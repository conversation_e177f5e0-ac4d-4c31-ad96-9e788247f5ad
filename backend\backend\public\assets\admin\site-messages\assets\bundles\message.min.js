"use strict";var AlphaMessages=function(){function n(n){this.format={monthFormat:"MMMM - yyyy",shortDateWithSlash_DatePicker:"MM/DD/YYYY",shortDateWithSlash_MonthPicker:"MM/dd/yyyy"};this.sort={none:0,ascending:1,descending:-1};this.$=n}return n}(),alphaMessages=new AlphaMessages($),Message=function(){function n(n){var t=this;this.registerEvents=function(){var n=t;t.$(".nav-tabs > li").on("click",function(){n.$.blockUI();n.$(this).find("span").val("")});t.$(".btn-submit").on("click",function(){t.$.blockUI()});t.initDateTimePicker("#fromDateContainer");t.initDateTimePicker("#toDateContainer")};this.initDateTimePicker=function(n){t.$(n).datetimepicker({allowInputToggle:!0,format:"MM/DD/YYYY"})};this.$=n}return n}();$(function(){var n=new Message($);n.registerEvents()});