/*!
 * Datetimepicker for Bootstrap 3
 * version : 4.17.42
 * https://github.com/Eonasdan/bootstrap-datetimepicker/
 */
.bootstrap-datetimepicker-widget{list-style:none}.bootstrap-datetimepicker-widget.dropdown-menu{margin:2px 0;padding:4px;width:19em}@media(min-width:768px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width:38em}}@media(min-width:992px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width:38em}}@media(min-width:1200px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width:38em}}.bootstrap-datetimepicker-widget.dropdown-menu:before,.bootstrap-datetimepicker-widget.dropdown-menu:after{content:'';display:inline-block;position:absolute}.bootstrap-datetimepicker-widget.dropdown-menu.bottom:before{border-left:7px solid transparent;border-right:7px solid transparent;border-bottom:7px solid #ccc;border-bottom-color:rgba(0,0,0,.2);top:-7px;left:7px}.bootstrap-datetimepicker-widget.dropdown-menu.bottom:after{border-left:6px solid transparent;border-right:6px solid transparent;border-bottom:6px solid #fff;top:-6px;left:8px}.bootstrap-datetimepicker-widget.dropdown-menu.top:before{border-left:7px solid transparent;border-right:7px solid transparent;border-top:7px solid #ccc;border-top-color:rgba(0,0,0,.2);bottom:-7px;left:6px}.bootstrap-datetimepicker-widget.dropdown-menu.top:after{border-left:6px solid transparent;border-right:6px solid transparent;border-top:6px solid #fff;bottom:-6px;left:7px}.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:before{left:auto;right:6px}.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:after{left:auto;right:7px}.bootstrap-datetimepicker-widget .list-unstyled{margin:0}.bootstrap-datetimepicker-widget a[data-action]{padding:6px 0}.bootstrap-datetimepicker-widget a[data-action]:active{box-shadow:none}.bootstrap-datetimepicker-widget .timepicker-hour,.bootstrap-datetimepicker-widget .timepicker-minute,.bootstrap-datetimepicker-widget .timepicker-second{width:54px;font-weight:bold;font-size:1.2em;margin:0}.bootstrap-datetimepicker-widget button[data-action]{padding:6px}.bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Increment Hours"}.bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Increment Minutes"}.bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Decrement Hours"}.bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Decrement Minutes"}.bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Show Hours"}.bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Show Minutes"}.bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Toggle AM/PM"}.bootstrap-datetimepicker-widget .btn[data-action="clear"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Clear the picker"}.bootstrap-datetimepicker-widget .btn[data-action="today"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Set the date to today"}.bootstrap-datetimepicker-widget .picker-switch{text-align:center}.bootstrap-datetimepicker-widget .picker-switch::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Toggle Date and Time Screens"}.bootstrap-datetimepicker-widget .picker-switch td{padding:0;margin:0;height:auto;width:auto;line-height:inherit}.bootstrap-datetimepicker-widget .picker-switch td span{line-height:2.5;height:2.5em;width:100%}.bootstrap-datetimepicker-widget table{width:100%;margin:0}.bootstrap-datetimepicker-widget table td,.bootstrap-datetimepicker-widget table th{text-align:center;border-radius:4px}.bootstrap-datetimepicker-widget table th{height:20px;line-height:20px;width:20px}.bootstrap-datetimepicker-widget table th.picker-switch{width:145px}.bootstrap-datetimepicker-widget table th.disabled,.bootstrap-datetimepicker-widget table th.disabled:hover{background:none;color:#777;cursor:not-allowed}.bootstrap-datetimepicker-widget table th.prev::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Previous Month"}.bootstrap-datetimepicker-widget table th.next::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Next Month"}.bootstrap-datetimepicker-widget table thead tr:first-child th{cursor:pointer}.bootstrap-datetimepicker-widget table thead tr:first-child th:hover{background:#eee}.bootstrap-datetimepicker-widget table td{height:54px;line-height:54px;width:54px}.bootstrap-datetimepicker-widget table td.cw{font-size:.8em;height:20px;line-height:20px;color:#777}.bootstrap-datetimepicker-widget table td.day{height:20px;line-height:20px;width:20px}.bootstrap-datetimepicker-widget table td.day:hover,.bootstrap-datetimepicker-widget table td.hour:hover,.bootstrap-datetimepicker-widget table td.minute:hover,.bootstrap-datetimepicker-widget table td.second:hover{background:#eee;cursor:pointer}.bootstrap-datetimepicker-widget table td.old,.bootstrap-datetimepicker-widget table td.new{color:#777}.bootstrap-datetimepicker-widget table td.today{position:relative}.bootstrap-datetimepicker-widget table td.today:before{content:'';display:inline-block;border:solid transparent;border-width:0 0 7px 7px;border-bottom-color:#337ab7;border-top-color:rgba(0,0,0,.2);position:absolute;bottom:4px;right:4px}.bootstrap-datetimepicker-widget table td.active,.bootstrap-datetimepicker-widget table td.active:hover{background-color:#337ab7;color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25)}.bootstrap-datetimepicker-widget table td.active.today:before{border-bottom-color:#fff}.bootstrap-datetimepicker-widget table td.disabled,.bootstrap-datetimepicker-widget table td.disabled:hover{background:none;color:#777;cursor:not-allowed}.bootstrap-datetimepicker-widget table td span{display:inline-block;width:54px;height:54px;line-height:54px;margin:2px 1.5px;cursor:pointer;border-radius:4px}.bootstrap-datetimepicker-widget table td span:hover{background:#eee}.bootstrap-datetimepicker-widget table td span.active{background-color:#337ab7;color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25)}.bootstrap-datetimepicker-widget table td span.old{color:#777}.bootstrap-datetimepicker-widget table td span.disabled,.bootstrap-datetimepicker-widget table td span.disabled:hover{background:none;color:#777;cursor:not-allowed}.bootstrap-datetimepicker-widget.usetwentyfour td.hour{height:27px;line-height:27px}.bootstrap-datetimepicker-widget.wider{width:21em}.bootstrap-datetimepicker-widget .datepicker-decades .decade{line-height:1.8em !important}.input-group.date .input-group-addon{cursor:pointer}.sr-only{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0}@font-face{font-family:'Roboto';src:url("../../styles/fonts/roboto/Roboto-Regular.woff2") format("woff2");src:url("../../styles/fonts/roboto/Roboto-Regular.woff") format("woff");font-weight:normal;font-style:normal}@font-face{font-family:'Roboto';src:url("../../styles/fonts/roboto/Roboto-Medium.woff2") format("woff2");src:url("../../styles/fonts/roboto/Roboto-Medium.woff") format("woff");font-weight:bold;font-style:normal}@font-face{font-family:'Roboto';src:url("../../styles/fonts/roboto/Roboto-Italic.ttf") format("truetype");font-weight:normal;font-style:italic}@font-face{font-family:'Roboto Bold';src:url("../../styles/fonts/roboto/Roboto-Bold.ttf") format("truetype");font-weight:bold;font-style:normal}.width-100per{width:100%}.tblRpt{border:1px solid #d1d1d1;border-collapse:collapse;background-color:#fff}.tblRpt.tblRpt-bordered>thead>tr>th,.tblRpt.tblRpt-bordered>tbody>tr>td,.tblRpt.tblRpt-bordered tfoot>tr>td{border:1px solid #d1d1d1}.tblRpt.tblRpt-striped tbody>tr:nth-of-type(2n){background-color:#eae9ee}.tblRpt.alternative-row-table tbody .even-row{background-color:#eae9ee}.tblRpt.tblRpt-hover>tbody>tr:hover>td{background-color:#f8eb95}.tblRpt thead th,.tblRpt tbody td,.tblRpt tfoot td{height:28px;padding:4px 8px;white-space:nowrap;vertical-align:middle;-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-ms-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box}.tblRpt thead th{background-color:#666;color:#fff;font-weight:400;text-align:center}.tblRpt thead tr.thead-row2-pt th{background-color:#f0f1c7;color:#333}.tblRpt thead tr.thead-row2-pt th.thead-row{background-color:#666;color:#fff}.tblRpt .thead-row2 th{background-color:#3f3f3f;color:#fff}.tblRpt tbody td.text-center{text-align:center}.tblRpt tbody td.group-league{background:#f4f5e0;text-transform:uppercase;color:#333;font-weight:bold;text-align:left}.tblRpt tbody .col-first{background-color:#ffefde}.tblRpt tbody .altercol{background-color:#f6f2d7;text-align:right}.tblRpt tbody .col-sma{background-color:#f8d7a2;font-weight:bold;text-transform:uppercase;text-align:center}.tblRpt .tbl-footer{background-color:#7f3d1d;color:#fff;font-weight:700}.tblRpt .tbl-footer .altercol{background-color:#432616}.tblRpt .product-row,.tblRpt .pt-row{background-color:#f3f5e0;color:#333;text-transform:uppercase}.tblRpt .product-row .altercol,.tblRpt .pt-row .altercol{background-color:#f3f5e0}.tblRpt .product-row:hover,.tblRpt .pt-row:hover{cursor:pointer}.tblRpt .product-row td:first-child,.tblRpt .pt-row td:first-child{width:20%}.tblRpt .product-row>td .icon-arrow-right,.tblRpt .pt-row>td .icon-arrow-right{-webkit-transform:rotate(0);-khtml-transform:rotate(0);-moz-transform:rotate(0);-ms-transform:rotate(0);-o-transform:rotate(0);transform:rotate(0);font-size:16px;position:relative;top:2px;-webkit-transition:transform .3s ease-out;-khtml-transition:transform .3s ease-out;-moz-transition:transform .3s ease-out;-ms-transition:transform .3s ease-out;-o-transition:transform .3s ease-out;transition:transform .3s ease-out}.tblRpt .product-row>td.active .icon-arrow-right,.tblRpt .pt-row>td.active .icon-arrow-right{-webkit-transform:rotate(90deg);-khtml-transform:rotate(90deg);-moz-transform:rotate(90deg);-ms-transform:rotate(90deg);-o-transform:rotate(90deg);transform:rotate(90deg)}.tblRpt .noInfo{background-color:#fff !important;text-align:center}.combined-fixed-pagination-bottom{margin-bottom:48px}.control{display:inline-block;width:200px;vertical-align:middle;margin:0 8px 0 5px}.control.date{width:130px}.input-group.date{position:initial}.input-group.date .icon-calendar{font-size:24px;color:#7f7f7f;display:inline-block;position:relative}.input-group.date .icon-calendar:after{position:relative;content:'';display:block}.input-group.date .icon-calendar:hover:after{position:absolute;left:0;top:-2px;width:36px;height:36px;margin-left:-6px;margin-top:-4px;background:#b1aea3;border-radius:100%;opacity:1;-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0);-moz-animation:ripple 1s ease-out;-o-animation:ripple 1s ease-out;-webkit-animation:ripple 1s ease-out;animation:ripple 1s ease-out}.input-group.date .input-group-addon{background:transparent;border:none;padding:0 8px;position:relative;top:3px}.input-group.date .form-control{border:none;font-size:12px;height:28px;background:#fff;margin-left:0;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-ms-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3)}.bootstrap-datetimepicker-widget{border:1px solid #dfdfdf;font-family:Roboto,Arial,Tahoma,sans-serif;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-khtml-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-moz-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-ms-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-o-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23)}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td,.bootstrap-datetimepicker-widget table td span.month,.bootstrap-datetimepicker-widget table td span.year,.bootstrap-datetimepicker-widget table td span.decade{position:relative;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active,.bootstrap-datetimepicker-widget table td span.active.month,.bootstrap-datetimepicker-widget table td span.active.year,.bootstrap-datetimepicker-widget table td span.active.decade{background-color:#337ab7;color:#fff;text-shadow:none;padding:3px 1px;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active:before,.bootstrap-datetimepicker-widget table td span.active.month:before,.bootstrap-datetimepicker-widget table td span.active.year:before,.bootstrap-datetimepicker-widget table td span.active.decade:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active:hover:after,.bootstrap-datetimepicker-widget table td span.active.month:hover:after,.bootstrap-datetimepicker-widget table td span.active.year:hover:after,.bootstrap-datetimepicker-widget table td span.active.decade:hover:after{animation:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active.today,.bootstrap-datetimepicker-widget table td span.active.today.month,.bootstrap-datetimepicker-widget table td span.active.today.year,.bootstrap-datetimepicker-widget table td span.active.today.decade{color:#fff}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active.today:hover,.bootstrap-datetimepicker-widget table td span.active.today.month:hover,.bootstrap-datetimepicker-widget table td span.active.today.year:hover,.bootstrap-datetimepicker-widget table td span.active.today.decade:hover{background-color:#337ab7;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled:before,.bootstrap-datetimepicker-widget table td span.disabled.month:before,.bootstrap-datetimepicker-widget table td span.disabled.year:before,.bootstrap-datetimepicker-widget table td span.disabled.decade:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled:hover:after,.bootstrap-datetimepicker-widget table td span.disabled.month:hover:after,.bootstrap-datetimepicker-widget table td span.disabled.year:hover:after,.bootstrap-datetimepicker-widget table td span.disabled.decade:hover:after{animation:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td:after,.bootstrap-datetimepicker-widget table td span.month:after,.bootstrap-datetimepicker-widget table td span.year:after,.bootstrap-datetimepicker-widget table td span.decade:after{content:"";display:block;position:relative}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td:hover:after,.bootstrap-datetimepicker-widget table td span.month:hover:after,.bootstrap-datetimepicker-widget table td span.year:hover:after,.bootstrap-datetimepicker-widget table td span.decade:hover:after{animation:1s ease-out 0s normal 1 ripple;background:#337ab7;left:0;opacity:1;position:absolute;transform:scale(0);top:0;right:0;bottom:0;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days table thead tr:first-child th:hover{background:none}.bootstrap-datetimepicker-widget .datepicker-days table th.picker-switch{height:36px;line-height:36px;color:#3e3e3e}.bootstrap-datetimepicker-widget .datepicker-days table th.dow{color:#9e9e9e;font-weight:normal}.bootstrap-datetimepicker-widget .datepicker-days table th .glyphicon{font-family:'Iconalpha' !important;font-size:24px}.bootstrap-datetimepicker-widget .datepicker-days table th .glyphicon.glyphicon-chevron-left:before{content:'';line-height:36px}.bootstrap-datetimepicker-widget .datepicker-days table th .glyphicon.glyphicon-chevron-right:before{content:'';line-height:36px}.bootstrap-datetimepicker-widget .datepicker-days table th.prev{color:#757575}.bootstrap-datetimepicker-widget .datepicker-days table td.day{height:30px;line-height:30px;color:#000;width:30px;transition:all .3s ease;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>thead>tr>th,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>th,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tfoot>tr>th,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>thead>tr>td,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tfoot>tr>td{padding:3px 1px}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed{border-collapse:separate}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td{color:#4c5246;font-size:13px}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled{color:#c7c7c7}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.today{color:#2491ef}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.today:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.today:hover{background:none}.bootstrap-datetimepicker-widget table td.day:hover,.bootstrap-datetimepicker-widget table td.hour:hover,.bootstrap-datetimepicker-widget table td.minute:hover,.bootstrap-datetimepicker-widget table td.second:hover{background:none}.bootstrap-datetimepicker-widget table td span:hover{background:none}@keyframes ripple{0%{transform:scale(0)}20%{transform:scale(1)}100%{opacity:0;transform:scale(1)}}@media only screen and (max-device-width:743px){.input-group.date .form-control{height:36px;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 3px rgba(0,0,0,.3);-moz-box-shadow:0 1px 3px rgba(0,0,0,.3);-ms-box-shadow:0 1px 3px rgba(0,0,0,.3);-o-box-shadow:0 1px 3px rgba(0,0,0,.3);box-shadow:0 1px 3px rgba(0,0,0,.3)}.input-group.date .icon-calendar{font-size:26px;position:relative}.input-group.date .icon-calendar:after{position:relative;content:'';display:block}.input-group.date .icon-calendar:hover:after{position:absolute;left:0;top:-4px;width:40px;height:40px;margin-left:-7px;margin-top:-4px;background:#b1aea3;border-radius:100%;opacity:1;-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0);-moz-animation:ripple 1s ease-out;-o-animation:ripple 1s ease-out;-webkit-animation:ripple 1s ease-out;animation:ripple 1s ease-out}}.nav-tabs>li.active>a,.nav-tabs>li.active>a:focus,.nav-tabs>li.active>a:hover,.nav-tabs>li>a:hover{color:#2267b6}.nav.nav-tabs>li.active:after,.nav.nav-tabs>li:hover:after{background:#314453}.nav-tabs>li>a{color:#333}.input-group.date .input-group-addon{padding-right:0}.daterange-picker{display:none}.monpickr-arrows input.monpickr-input{text-align:center !important;margin:0 !important;padding:1px 2px !important;border-radius:2px !important;cursor:pointer;height:28px !important;font-weight:normal}.monpickr-arrows input.monpickr-input.daypickr-selected-day{width:100%}.ui-widget input,.ui-widget select,.ui-widget textarea,.ui-widget button,.ui-monpickr li a{font-family:"Roboto",'Lucida Sans',Tahoma,Arial,Helvetica,sans-serif;font-size:13px}.monpickr-wrap-parent{cursor:pointer}.monpickr-wrap-parent .monpickr-wrap{z-index:99 !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-selected-month{width:154px !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-dropdown{-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-khtml-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-moz-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-ms-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-o-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);margin-left:1px !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-dropdown ul li:not(.year) a{font-size:100% !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-dropdown ul .ui-state-active{border-radius:2px !important;background:none !important;border:1px solid #fff !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-dropdown ul .ui-state-active a{color:#0062ad !important;font-size:100% !important;font-weight:bold !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-dropdown ul .ui-state-hover:not(.year) a{font-size:100% !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-dropdown ul .ui-state-hover{border-radius:2px !important;background:none !important;border:1px solid #fff !important}.monpickr-wrap-parent .monpickr-wrap .monpickr-dropdown ul .ui-state-hover a{color:#0062ad !important;font-weight:bold !important}.monpickr-wrap-parent .monpickr-wrap .monpickr{background:none !important;border:none !important;border-radius:2px !important;vertical-align:middle}.monpickr-wrap-parent .monpickr-wrap .monpickr .ui-state-focus,.monpickr-wrap-parent .monpickr-wrap .monpickr .ui-state-hover{background:none !important;border:none !important}.monpickr-wrap-parent .monpickr-wrap .monpickr .prev .ui-icon-circle-triangle-w{background:url("../../styles/vendors/monthpicker/images/sprites.png") 0 -127px !important;background-position:0 -127px !important;height:18px;width:15px;float:left}.monpickr-wrap-parent .monpickr-wrap .monpickr .next .ui-icon-circle-triangle-e{background:url("../../styles/vendors/monthpicker/images/sprites.png") -19px -127px !important;background-position:-19px -127px !important;height:18px;width:15px}.daypickr-wrap-parent .daypickr{border-radius:2px !important;background:none !important;border:0 solid #cecccd !important}.daypickr-wrap-parent .daypickr .monpickr-down{margin-right:-2px}.daypickr-wrap-parent .daypickr .ui-state-hover{background:none !important;border:none !important}.daypickr-wrap-parent .daypickr .daypickr-selected-day{text-align:left !important;padding-left:6px !important}.daypickr-wrap-parent .daypickr-dropdown{-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-khtml-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-moz-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-ms-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-o-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23)}.daypickr-wrap-parent .daypickr-dropdown .ui-state-hover{background:none repeat scroll 0 0 #fff;border:solid 1px transparent !important}.daypickr-wrap-parent .daypickr-dropdown .ui-state-hover>a{color:#0062ad !important;font-weight:bold}.daypickr-wrap-parent .ui-widget-header .ui-icon{background-image:url("../../styles/vendors/monthpicker/images/dropdown-icon.png") !important;background-position:39px 6px !important;width:47px !important}.daypickr-wrap-parent .ui-state-hover .ui-icon{background-position:40px 5px !important}.monpickr-arrows{padding:0 !important}.monpickr-arrows .monpickr-prev,.monpickr-arrows .monpickr-next,.monpickr-arrows .monpickr-down{top:6px;display:inline-block}.monpickr-arrows .monpickr-prev:hover,.monpickr-arrows .monpickr-next:hover,.monpickr-arrows .monpickr-down:hover{padding:0}.monpickr-arrows .monpickr-next,.monpickr-arrows .monpickr-down{right:5px}.monpickr-arrows .monpickr-prev{left:5px}.monpickr-super-wrapper .monpickr-label{font-size:100% !important;margin:3px 5px 3px 16px}