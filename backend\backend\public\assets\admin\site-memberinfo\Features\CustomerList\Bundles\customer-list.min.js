"use strict";
var SingleSelectionDropdownOption = function () {
    function n(n, t, i, r, u) {
        n === void 0 && (n = "dropdown-single");
        t === void 0 && (t = "UserSelectedId");
        i === void 0 && (i = null);
        r === void 0 && (r = !1);
        u === void 0 && (u = !1);
        this.dropdownId = n;
        this.inputId = t;
        this.onChange = i;
        this.enableClickableOptGroups = r;
        this.enableCollapsibleOptGroups = u
    }
    return n
}(),
    SingleSelectionDropdownComponent = function () {
        function n(n) {
            var t = this;
            this.options = new SingleSelectionDropdownOption;
            this.render = function (n) {
                var i, r, u;
                n === void 0 && (n = new SingleSelectionDropdownOption);
                t.options = t.$.extend({}, t.options, n);
                i = t.$("#" + t.options.dropdownId);
                t.$dropdown = i;
                r = t.$("#" + t.options.inputId);
                t.$hiddenInput = r;
                u = t;
                t.selectedValue = u.getSelectedItem();
                t.$dropdown.multiselect({
                    multiple: !1,
                    maxHeight: 300,
                    numberDisplayed: 1,
                    includeSelectAllOption: !1,
                    onChange: t.onchange,
                    enableClickableOptGroups: t.options.enableClickableOptGroups,
                    enableCollapsibleOptGroups: t.options.enableCollapsibleOptGroups,
                    optionClass: t.optionClass
                });
                t.options.enableClickableOptGroups && t.options.enableCollapsibleOptGroups && t.$dropdown.next().find(".multiselect-container li").removeClass("hidden")
            };
            this.getSelectedItem = function () {
                var n = [],
                    i = t.$dropdown.find("option:selected"),
                    r = t.$;
                return i.each(function () {
                    n.push([r(this).val()])
                }), n.length ? n[0].toString() : ""
            };
            this.updateSelectedValue = function () {
                t.$hiddenInput.val(t.selectedValue)
            };
            this.onchange = function () {
                t.selectedValue = t.getSelectedItem();
                t.options.onChange && t.options.onChange()
            };
            this.optionClass = function (n) {
                if (t.$(n).data("isnested") === 1) return "multiselect-nested-item"
            };
            this.changeSelection = function (n) {
                t.$dropdown.val(n);
                t.$hiddenInput.val(n);
                t.$dropdown.multiselect("select", [n]);
                t.$dropdown.multiselect("refresh")
            };
            this.$ = n
        }
        return n
    }(),
    CustomerListPopup, CustomerListServices, EditStatus, Unlock, EditSingleSetting, EditMultipleSetting, CustomerListDataTable, _this, CustomerListViewModel, CustomerLevel, CustomerList;
CustomerListPopup = function () {
    function n(n, t, i) {
        var r = this;
        this.cacheDom = function () {
            r.$statusPopupContainer = r.$("#status-popup");
            r.$productPopupContainer = r.$("#product-popup");
            r.$productIframe = r.$("#productIframe");
            r.$productIframeForm = r.$("form#productIframeForm")
        };
        this.openProductPopup = function (n, t) {
            var i, u, o, f, e;
            t === void 0 && (t = "GET");
            r.$.blockUI();
            i = r.$(n).data("url");
            r.$productIframe.attr("src", "");
            r.$productIframeForm.attr("action", i);
            r.$productIframeForm.attr("method", t);
            r.topWindow.productPopupUrl = i;
            r.topWindow[i] = function () {
                r.$productIframeForm.submit()
            };
            u = r.$(n).data("params");
            r.$productIframeForm.empty();
            u !== undefined && u !== null && (o = r.$.map(u, function (n, t) {
                return '<input type="hidden"  name=\'' + t + "' id='" + t + "' value='" + n + "'/>"
            }).join(" "), r.$productIframeForm.append(o));
            r.$productIframeForm.submit();
            f = "100%";
            r.isMobile || (e = r.topWindow.$("#main").width(), f = e < 1160 ? e - 50 + "px" : "1160px");
            !r.isMobile && i.indexOf("ParlayMaxPayout") > -1 && r.$productPopupContainer.addClass("parlay-max-payout-popup-new");
            var s = r.$(n).data("showGuideLine"),
                h = s === undefined || s === "" ? !1 : JSON.parse(r.$(n).data("showGuideLine")),
                c = h ? "<a class='icon-information-outline icon-info' href='" + r.$(n).data("guideLineUrl") + "' target='_blank'/>" : "";
            r.$productPopupContainer.find(".modal-title").html("<div>" + r.$(n).data("title") + c + "<\/div>");
            r.$productPopupContainer.css({
                height: "100%",
                width: f
            }).modal({
                show: !0,
                backdrop: !1
            });
            r.$productPopupContainer.draggable({
                containment: "window"
            });
            r.$.unblockUI()
        };
        this.clearDuplicateHeader = function () {
            r.$productIframe.on("load", function () {
                r.modifyContentPopup()
            })
        };
        this.forcusInputElement = function () {
            var n = r.$productIframe.contents().find("input[type=text],input[type=password],input[type=radio],input[type=checkbox],textarea,select").filter(":visible:first");
            n != null && n.focus()
        };
        this.modifyContentPopup = function () {
            r.$productIframe.contents().find(".setting-header-agency,#header_main,#race-book-positiontaking-header,#title_header,#button_hr").css("display", "none")
        };
        this.bindingBootstrapModalEvent = function () {
            r.$statusPopupContainer.unbind().bind("mouseleave ", function () {
                return r.$statusPopupContainer.modal("hide")
            });
            r.$productPopupContainer.on("hidden.bs.modal", function () {
                r.$productIframe.attr("src", "about:blank")
            });
            r.$productPopupContainer.on("show.bs.modal", function () {
                r.clearDuplicateHeader()
            });
            r.$productPopupContainer.on("shown.bs.modal", function () {
                r.forcusInputElement.apply(r.$productIframe.contents())
            })
        };
        this.closeStatusPopup = function () {
            return r.$statusPopupContainer.modal("hide")
        };
        this.closeProductPopup = function () {
            return r.$productPopupContainer.modal("hide")
        };
        this.reloadFrame = function () {
            r.$("div.modal-product.in").length > 0 && (r.$productIframeForm.submit(), r.clearDuplicateHeader())
        };
        this.successfulUpdatingCallback = function () {
            setTimeout(function () {
                r.reloadFrame()
            }, 1500)
        };
        this.openStatusSettingPopup = function (n, t, i, u) {
            var f;
            if (r.buildStatusSettingPopupHtml(t, i), u(n), f = r.$statusPopupContainer.find(".modal-dialog"), r.isMobile) f.css({
                height: "100%",
                width: "100%"
            }), r.$statusPopupContainer.css({
                top: 0,
                left: 0,
                height: "100%",
                width: "100%",
                paddingRight: "0px"
            }).modal({
                show: !0,
                backdrop: !1
            });
            else {
                r.$statusPopupContainer.css({
                    height: "auto",
                    width: "auto"
                }).modal({
                    show: !0,
                    backdrop: !1
                });
                f.css({
                    width: "inherit"
                });
                var o = f.outerHeight(),
                    h = f.outerWidth(),
                    c = r.$(r.topWindow.parent.frames.main).height(),
                    s = r.$(n).offset().top - r.$(document).scrollTop(),
                    e = r.$(n).offset().left + r.$(n).width();
                c - s < o && (s = c - o);
                r.$(window).width() - e < h && (e = e - h);
                r.$statusPopupContainer.css({
                    top: s,
                    left: e,
                    height: o
                })
            }
        };
        this.buildStatusSettingPopupHtml = function (n, t) {
            r.$statusPopupContainer.find(".modal-body").empty().append(n);
            !r.isMobile && r.$statusPopupContainer.find(".modal-title").empty().append(t)
        };
        this.addCursorProgress = function (n) {
            n.css("cursor", "progress");
            r.$("#tbl-customer-list").css("cursor", "progress")
        };
        this.removeCursorProgress = function (n) {
            n.css("cursor", "");
            r.$("#tbl-customer-list").css("cursor", "")
        };
        this.$ = n;
        this.isMobile = i;
        this.topWindow = t;
        this.cacheDom()
    }
    return n
}();
CustomerListServices = function () {
    function n(n, t) {
        var i = this;
        this.callAjax = function (n, t, r, u) {
            i.$.blockUI();
            var f = i.callAjaxWithoutBlocking(n, t, r, u);
            f.error = i.$.unblockUI
        };
        this.callAjaxError = function (n, t, r, u, e) {
            i.$.blockUI();
            var f = i.callAjaxWithoutBlockingError(n, t, r, u, e);
            f.error = i.$.unblockUI
        };
        this.callAjaxWithoutBlocking = function (n, t, r, u) {
            return i.$.ajax({
                url: n,
                dataType: "json",
                type: t,
                headers: {
                    __RequestVerificationToken: i.$("input[name='__RequestVerificationToken']").val(),
                    "X-Requested-With": "XMLHttpRequest",
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: r,
                success: u
            })
        };
        this.callAjaxWithoutBlockingError = function (n, t, r, u,e) {
            return i.$.ajax({
                url: n,
                dataType: "json",
                type: t,
                headers: {
                    __RequestVerificationToken: i.$("input[name='__RequestVerificationToken']").val(),
                    "X-Requested-With": "XMLHttpRequest",
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: r,
                success: u,
                error: e
            })
        };
        this.getCutomerList = function (n, t) {
            i.callAjax("GetCustomerListData?page="+ n.PageIndex, "POST", n, function (n) {
                i.$.unblockUI();
                i.$("#select-all").prop("checked", !1);
                t({
                    data: n.DownlinesList,
                    recordsTotal: n.TotalDownlines,
                    recordsFiltered: n.TotalDownlines
                })
            })
        };
        this.updateCustomerStatus = function (n, t, r) {
            var u = i.$(n),
                f = u.data("url"),
                e = u.data("params");
                
            // let data = JSON.parse(e);
            var o = {'status': e.status};
            $.ajax({
                headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')},
                url: '/portal/api/users/'+e.SearchAccountId+'/status',
                type: 'PUT',
                data: {
                    "status":e.status
                },
                success: function(result) {
                    window.location.reload();
                },
                error: function(XMLHttpRequest, textStatus, errorThrown){
                    if (XMLHttpRequest.responseJSON.redirect) {
                        window.open(XMLHttpRequest.responseJSON.redirect, '_self');
                    } else {
                        alert(XMLHttpRequest.responseJSON.msg);
                    }
                }
            });

            // i.callAjaxError(f, "PUT", e, function (u) {
                // i.$.unblockUI();
                // console.log(u);
                // r.customerListPopup.openProductPopup(n.currentTarget, r.$(n.currentTarget).data("method"))
            //     r(n, t, u, o)
            // },function(XMLHttpRequest, textStatus, errorThrown) {
                
                // return u.customerListPopup.openProductPopup(XMLHttpRequest.responseJSON.redirect)
            //  })
        };
        this.getSettings = function (n, t, r, u) {
            return r === void 0 && (r = "GET"), u === void 0 && (u = {}), i.callAjaxWithoutBlocking(n, r, u, function (n) {
                return typeof t == "function" && t.apply(i, [n])
            })
        };
        this.$ = n;
        this.viewModel = t
    }
    return n
}();
EditStatus = function () {
    function n(n, t, i, r, u) {
        var f = this;
        this.bindingEvent = function () {
            return f.$(".status-popup").unbind().bind("click", function (n) {
                return f.openPopup(f.$(n.currentTarget))
            })
        };
        this.openPopup = function (n) {
            var t = f.$(n),
                i = t.find(":first-child");
            f.customerListPopup.addCursorProgress(i);
            f.customerListServices.getSettings(t.data("url"), function (r) {
                var u = t.data("username"),
                    e = t.data("custid"),
                    o = f.buildPopupHtml(r.Status, u, e),
                    s = f.$("#editStatusText").val() + " - " + u;
                f.customerListPopup.openStatusSettingPopup(n, o, s, f.bindingPopupEvent);
                f.customerListPopup.removeCursorProgress(i)
            })
        };
        this.buildPopupHtml = function (n, t, i) {
            var r = ["<ul>"];
            return f.$.each(n, function (n, u) {
                if (u.ShowInCustomerList) {
                    var f = {
                        "class": "edit-status-popup " + (u.Disabled ? "grey-color" : ""),
                        "data-disabled": u.Disabled,
                        "data-custid": i,
                        "data-username": t,
                        "data-reloadcustomerlist": u.ReloadCustomerList,
                        "data-url": u.Url,
                        "data-ispopup": u.IsPopup,
                        "data-popupheadertext": u.PopupHeaderText,
                        "data-params": u.JsonParams
                    };
                    r.push('<li data-message="' + u.ConfirmMessage + '" ' + buildAttributes(f) + "><input " + (u.Disabled ? "disabled" : "") + ' type="checkbox" ' + (u.Checked ? "checked" : "") + "/> " + u.Text + "<\/li>")
                }
            }), r.join("")
        };
        this.bindingPopupEvent = function (n) {
            f.$(".edit-status-popup").click(function (t) {
                var u = JSON.parse(f.$(t.currentTarget).data("ispopup")),
                    i, r;
                u ? (i = f.$(t.currentTarget).data("popupheadertext"), r = f.$(t.currentTarget).data("username"), f.$(t.currentTarget).data("title", i.replace("<username>", r)), f.customerListPopup.openProductPopup(t.currentTarget)) : JSON.parse(f.$(t.currentTarget).data("disabled")) || f.topWindow.bootboxConfirm(f.$(t.currentTarget).data("message"), t.currentTarget, function () {
                    return f.customerListServices.updateCustomerStatus(t.currentTarget, f.$(n).context, f.updateCustomerStatusSuccess)
                })
            })
        };
        this.updateCustomerStatusSuccess = function (n, t, i, r) {
            i.IsSuccessful ? (r && f.topWindow.customerListNew ? f.topWindow.successfulUpdatingCallbackAndReloadMainFrame(!0) : f.$.unblockUI(), f.customerListPopup.closeStatusPopup()) : (f.$.unblockUI(), f.topWindow.bootboxAlert(i.Message, f.isMobile ? n : t))
        };
        this.$ = n;
        this.topWindow = t;
        this.isMobile = i;
        this.customerListServices = r;
        this.customerListPopup = u
    }
    return n
}();
Unlock = function () {
    function n(n, t, i, r) {
        var u = this;
        this.bindingEvent = function () {
            return u.$(".icon-locked").unbind().bind("click", u.openPopup)
        };
        this.openPopup = function (n) {
            var t = u.$(n.currentTarget).data("message");
            t && u.topWindow.bootboxConfirm(t, u.isMobile ? null : n.currentTarget, function () {
                return u.customerListServices.updateCustomerStatus(n.currentTarget, null, u.updateCustomerStatusSuccess)
            })
        };
        this.updateCustomerStatusSuccess = function (n, t, i, r) {
            i.IsSuccessful ? r && u.topWindow.customerListNew ? u.topWindow.successfulUpdatingCallbackAndReloadMainFrame(!0) : u.$.unblockUI() : (u.$.unblockUI(), u.topWindow.bootboxAlert(i.Message, u.isMobile ? n : t))
        };
        this.$ = n;
        this.topWindow = t;
        this.isMobile = i;
        this.customerListServices = r
    }
    return n
}();
EditSingleSetting = function () {
    function n(n, t, i) {
        var r = this;
        this.bindingEvent = function () {
            return r.$(".edit").unbind().bind("click", function (n) {
                return r.openPopup(r.$(n.currentTarget))
            })
        };
        this.openPopup = function (n, t) {
            t === void 0 && (t = null);
            var i = r.$(n);
            r.customerListPopup.addCursorProgress(t || i);
            r.$(".edit-multiple").data("custid", "");
            r.$(".edit-multiple").data("url", "");
            r.$("#arrayCustID").val("");
            r.$("#arrayUserName").val("");
            r.customerListServices.getSettings(i.data("url"), function (u) {
                var f = i.data("username"),
                    e = r.buildPopupHtml(u.Settings, f),
                    o = r.$("#editSettingText").val() + " - " + f;
                r.customerListPopup.openStatusSettingPopup(n, e, o, r.bindingPopupEvent);
                r.customerListPopup.removeCursorProgress(t || i)
            })
        };
        this.buildPopupHtml = function (n, t) {
            var i = ["<ul>"];
            return r.$.each(n, function (n, u) {
                var f = {
                    "class": "edit-popup " + u.Icon + " " + (u.Enabled ? "" : "grey-color"),
                    "data-method": u.Method,
                    "data-params": JSON.stringify(u.QueryStringParamaters),
                    "data-url": u.Url,
                    "data-show-guide-line": u.ShowGuideLine,
                    "data-enabled":u.Enabled,
                    "data-targetid":u.targetid,
                },
                    e = u.Title || (u.AddEditTextToPopupTitle ? r.$("#editText").val() : "") + " " + u.Text + " - " + t;
                i.push("<li " + buildAttributes(f) + ' data-title="' + e + '">' + u.Text + "<\/li>")
            }), i.join("")
        };
        this.bindingPopupEvent = function () {
            return r.$(".edit-popup").click(function (n) {
                if($(n.currentTarget).data("enabled")){
                    let param = $(n.currentTarget).data("params");
                    if($(n.currentTarget).data("targetid") == "delete"){
                        var $alert = confirm('Bạn có muốn xóa tài khoản ' + param.username + ' này?');
                        if ($alert) {
                            $.ajax({
                                headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')},
                                url: 'api/users/'+param.CustId+'/delete',
                                type: 'DELETE',
                                success: function(result) {
                                    window.location.reload();
                                }
                            });
                        }
                    }else if($(n.currentTarget).data("targetid") =="secure"){
                        var alert = confirm('Bạn có muốn reset mã bảo vệ của tài khoản ' + param.CustName + ' này?');
                        if (alert) {
                            return window.open('/portal/users/' +param.CustId+ '/secure_code', '_self');
                            //return r.customerListPopup.openProductPopup(n.currentTarget, r.$(n.currentTarget).data("method"))
                        }
                    }else{
                        return r.customerListPopup.openProductPopup(n.currentTarget, r.$(n.currentTarget).data("method"))
                    }
                }
            })
        };
        this.$ = n;
        this.customerListPopup = t;
        this.customerListServices = i
    }
    return n
}();
EditMultipleSetting = function () {
    function n(n, t, i, r, u) {
        var f = this;
        this.bindingEvent = function () {
            f.$(".edit-multiple").unbind().bind("click", function (n) {
                return f.openPopup(n.currentTarget)
            });
            f.$("#select-all").unbind().bind("click", function () {
                return f.$("tbody .checkbox").prop("checked", f.$("#select-all").is(":checked"))
            })
        };
        this.openPopup = function (n) {
            var r = [],
                t = [],
                u, i;
            f.$("input.checkbox:checkbox:checked").each(function (n, i) {
                r.push(f.$(i).data("username"));
                t.push(f.$(i).data("custid"))
            });
            u = {
                UplineId: f.viewModel.SearchAccountId,
                UplineName: f.viewModel.SearchAccountRoleId,
                UplineRoleId: f.viewModel.SearchAccountRoleId,
                SearchAccountRoleId: f.viewModel.SearchAccountRoleId - 1,
                custids: t.join(","),
                usernames: r.join(",")
            };
            t.length > 0 && (f.$(".edit-multiple").data("custid", t[0]), f.$(".edit-multiple").data("url", f.$("#setting-" + t[0]).data("url")), f.$("#arrayCustID").val(t.join("^")), f.$("#arrayUserName").val(r.join("^")), i = f.$(n), t.length === 1 ? f.editSingleSetting.openPopup(f.$("#setting-" + t[0]), i) : (f.customerListPopup.addCursorProgress(i), f.customerListServices.getSettings(f.viewModel.MemberInfoUrl + "/MultipleEditPopup", function (t) {
                f.customerListPopup.openStatusSettingPopup(n, f.buildPopupHtml(t), f.$("#editMultipleSettingText").val(), f.bindingPopupEvent);
                f.customerListPopup.removeCursorProgress(i)
            }, "POST", u)))
        };
        this.buildPopupHtml = function (n) {
            var t = ["<ul>"];
            return f.$.each(n, function (n, i) {
                i.ProductId === 6 && f.$("#arrayStatusRnCasino").val(i.QueryStringParamaters.syncedCasinoList);
                var r = {
                    "class": "edit-multiple-settings " + i.Icon,
                    "data-title": i.Title || i.Text,
                    "data-method": i.Method,
                    "data-params": JSON.stringify(i.QueryStringParamaters),
                    "data-url": i.Url,
                    "data-show-guide-line": i.ShowGuideLine,
                    "data-guide-line-url": i.GuideLineUrl
                };
                t.push("<li " + buildAttributes(r) + ">" + i.Text + "<\/li>")
            }), t.join("")
        };
        this.bindingPopupEvent = function () {
            return f.$(".edit-multiple-settings").click(function (n) {
                return f.customerListPopup.openProductPopup(n.currentTarget, f.$(n.currentTarget).data("method"))
            })
        };
        this.$ = n;
        this.viewModel = t;
        this.customerListPopup = i;
        this.customerListServices = r;
        this.editSingleSetting = u
    }
    return n
}();
CustomerListDataTable = function () {
    function n(n, t, i, r, u, f, e, o, s) {
        var h = this;
        this.bindingEvent = function () {
            h.editMultipleSetting.bindingEvent();
            h.initDataTable()
        };
        this.getRowClass = function (n) {
            if (n.Enabled) {
                if (n.Closed) return statusClasses.Closed.filter(function (t) {
                    return n.ClosedReason === t.Id
                })[0].Css;
                if (n.Suspended) return statusClasses.Suspended.Css;
                if (n.Locked) return statusClasses.Locked.filter(function (t) {
                    return n.Locked === t.Id
                })[0].Css
            } else return statusClasses.Disabled.Css;
            return n.Enabled.Css
        };
        this.initDataTable = function () {
            var n = h.viewModel.IsMobileView ? 200 : 500,
                t = {
                    type: "column",
                    target: -1,
                    minWidth: "100px",
                    auto: !1,
                    renderer: function (n, t) {
                        return h.renderDetailColumn(n, t)
                    },
                    bindingCallback: h.detailCallBack
                },
                i = h.viewModel.CustomerListResources.PageSize + ' <select><option value="10">10<\/option><option value="50">50<\/option><option value="100">100<\/option>' + ('<option value="' + n + '">' + n + "<\/option>") + "<\/select>",
                r = {
                    sFirst: "",
                    sPrevious: "",
                    sNext: "",
                    sLast: "",
                    info: h.viewModel.CustomerListResources.Page + " _INPUT_ " + h.viewModel.CustomerListResources.Of + " _TOTAL_"
                };
            h.$table = h.$("#tbl-customer-list").DataTable({
                serverSide: !0,
                ajax: function (n, t) {
                    if (h.viewModel.CustomerListData) t({
                        data: h.viewModel.CustomerListData.DownlinesList,
                        recordsTotal: h.viewModel.CustomerListData.TotalDownlines,
                        recordsFiltered: h.viewModel.CustomerListData.TotalDownlines
                    }), delete h.viewModel.CustomerListData;
                    else {
                        var i = {
                            CustName: h.viewModel.SearchAccountName,
                            CustId: $("#searchAccountId").val(),
                            CustLevelId: h.viewModel.SearchAccountRoleId,
                            PageIndex: n.start / n.length + 1,
                            PageSize: n.length,
                            Username: h.username,
                            Status: h.status
                        };
                        h.customerListServices.getCutomerList(i, t)
                    }
                },
                columns: h.buildDataTableColumns(),
                createdRow: function (n, t) {
                    if(t.status=="active"){
                        h.$(n).addClass("enabled")
                    }else if(t.status=="suspended"){
                        h.$(n).addClass("closed-byArchive")
                    }else{
                        h.$(n).addClass("closed-byUpline")
                    }
                },
                responsive: {
                    details: t
                },
                columnDefs: [{
                    className: "control",
                    data: null,
                    orderable: !1,
                    defaultContent: "",
                    targets: -1
                }],
                stripeClasses: [],
                ordering: !1,
                paging: !0,
                pagingType: "input",
                pageLength: 10,
                searching: !1,
                bInfo: !1,
                fixedHeader: !0,
                // processing: !0,
                dom: '<"top"iflp<"clear">>rt<"bottom"ifp<"clear">>',
                language: {
                    processing: "",
                    lengthMenu: i,
                    oPaginate: r,
                    emptyTable: h.viewModel.CustomerListResources.ThereIsNoData
                },
                drawCallback: h.drawCallback,
                isSubmit: !1
            });
            h.$("#tbl-customer-list").find("tbody").on("click", "td.control", function (n) {
                return h.doAdjustHeader(n)
            });
            h.$("thead tr th.control").unbind().bind("click", function (n) {
                return h.collapseExpandAll(n)
            })
        };
        this.collapseExpandAll = function (n) {
            // h.$.blockUI();
            // setTimeout(function () {
            //     var t = h.$(n.currentTarget).parent();
            //     t.hasClass("parent") ? (t.removeClass("parent"), h.triggerClickDetailWhenSwitchAll(h.$("tbody tr.parent")), h.$(n.currentTarget).attr("title", h.$("#expandAllText").val())) : (t.addClass("parent"), h.triggerClickDetailWhenSwitchAll(h.$("tbody tr").not(".parent")), h.$(n.currentTarget).attr("title", h.$("#collapseAllText").val()));
            //     h.$.unblockUI()
            // }, 100)
        };
        this.triggerClickDetailWhenSwitchAll = function (n) {
            n.each(function (n, t) {
                return h.$(t).find(".control").click()
            });
            h.$table.fixedHeader.adjust()
        };
        this.doAdjustHeader = function (n) {
            n.isTrigger || h.$table.fixedHeader.adjust()
        };
        this.buildDataTableColumns = function () {
            var n = [];
            return h.viewModel.IsMobileView || n.push({
                className: "center",
                data: "RowNumber",
                width: "50px;"
            }), n.push({
                className: "center",
                data: null,
                render: function (n) {
                    return '<input type="checkbox" class="checkbox" data-custid="' + n.id + '" data-username="' + n.username + '"/>'
                }
            }), n.push({
                className: "center",
                data: null,
                render: function (n) {
                    return h.renderEditColumn(n)
                }
            }, {
                className: "left",
                data: null,
                render: function (n) {
                    return h.renderUsernameColumn(n)
                }
            }, {
                className: "center",
                data: function (n) {
                    return h.renderStatusColumn(n)
                }
            }), n.push({
                className: "center",
                data: null,
                render: function (n) {
                    return "Đóng"
                }
            }), n.push({
                className: "left",
                data: null,
                render: function (n) {
                    return ''
                }
            }), n.push({
                className: "left",
                data: "first_name"
            }, {
                className: "left",
                data: "last_name"
            }, {
                className: "left",
                data: "group"
            }), n.push({
                className: "center",
                data: "discountAsian"
            }), n.push({
                className: "left",
                data: "discount1x2"
            }, {
                className: "left",
                data: "discountCS"
            }, {
                className: "left",
                data: "discountNumber"
            }), n.push({
                className: "center",
                data: "created_at"
            }), n.push({
                className: "center",
                data: null,
                render: function (n) {
                    return h.renderLoginIP(n)
                }
            }), n.push({
                className: "control",
                data: null,
                render: function (n) {
                    return ""
                }
            }), n
        };
        this.renderUsernameColumn = function (n) {
            i = "/portal/listMember?id=" + n.id;
            // return "<div title='" + n.username + "'><a href=\"" + i + '" >' + n.username + "<\/a><\/div>"
            if(n.current_type !== 'member'){
                return "<div style='cursor: pointer;color:#045ace !important' title='" + n.username + "' class='username-field' data-cusid='"+n.id+"'>" + n.username + "<\/div>"
            }else{
                return "<div title='" + n.username + "' >" + n.username + "<\/div>"
            }
        };
        this.renderLockedIcon = function (n) {
            var t, i;
            return h.viewModel.CanUpdateAccount ? (t = "", n.Locked === 1 ? t = h.$("#confirmUnlockInCaseFalseOTPSC").val() : n.Locked === 2 && (t = h.$("#confirmUnlockInCaseFalsePassword").val()), i = {
                SearchAccountId: n.CustId,
                SearchAccountName: n.Username,
                SearchAccountRoleId: n.RoleId,
                StatusName: "Unlock"
            }, "<span class='icon-locked' title='" + h.$("#unlockTooltipText").val() + "' data-message='" + t + "' data-url='UpdateCustomerStatus' data-reloadcustomerlist='true' data-params=" + JSON.stringify(i) + "><\/span>") : "<span class='icon-locked'><\/span>"
        };
        this.renderEditColumn = function (n) {
            var t = {
                "class": "edit icon-table-edit",
                id: "setting-" + n.id,
                "data-custid": n.id,
                "data-username": n.username,
                "data-url": h.buildGetAllStatusLink(n),
                title: h.$("#editSettingText").val()
            };
            return "<div " + buildAttributes(t) + "/>"
        };
        this.renderStatusColumn = function (n) {
            var t = {
                "class": "status-popup",
                id: "status-" + n.id,
                "data-custid": n.id,
                "data-username": n.username,
                "data-url": h.buildGetAllStatusLink(n),
                title: h.$("#editStatusText").val()
            };
            let s = n.status;
            if(s=="active"){
                s = 'Mở';
            }else if(s=="suspended"){
                s = 'Đình chỉ';
            }else{
                s = 'Bị đóng';
            }
            return "<a " + buildAttributes(t) + "><span>" + s + "<\/span><span class='icon-menu-down'/><\/a>"
        };
        this.renderStatementColumn = function (n) {
            var t = {
                CustId: n.CustId,
                CustName: n.Username,
                CustLevelId: n.RoleId
            },
                i = n.RoleId === CustomerLevel.Member ? "MemberStatement" : "SMAStatement",
                r = h.viewModel.MemberInfoUrl + "/" + i + "?" + h.$.param(t);
            return "<a data-url='" + r + "' title='" + h.$("#statementTooltipText").val() + "' class='icon-statement' />"
        };
        this.renderLoginIP = function (n) {
            if (n.last_ip_login === "" || n.last_ip_login === null) return "";
            return '<a class="login-ip" data-title="IP đăng nhập - '+n.username+'" data-url="//cqcounter.com/whois/?query= '+n.last_ip_login+
            'data-params="{\"ip\":\"**************\"}" data-width="800px" data-height="400px" '+
            '" title="Nhấn vào IP để xem thông tin IP">'+n.last_ip_login+'</a>'
        };
        this.renderDetailColumn = function (n, t) {
            var r = h,
                u = [],
                e = [],
                o = [],
                i = "enabled",
                f;
            return n.cells(t, ":hidden").eq(0).each(function (t) {
                var f = r.$(n.column(t.column).header()),
                    s = n.cell(t).index(),
                    c, h;
                if (f.hasClass("control") || f.hasClass("never")) return "";
                c = n.settings()[0];
                h = c.oApi._fnGetCellData(c, s.row, s.column, "display");
                f.hasClass("ugroup") ? o.push('<tr class="' + i + '" data-dtr-index="' + s.column + '">' + ('<td class="dtr-title">' + f.text() + "<\/td> ") + ('<td class="dtr-data">' + h + "<\/td><\/tr>")) : f.hasClass("commission") ? (f.hasClass("startGroup") && u.push('<tr class="' + i + "\"><td class='commission-wrapper' colspan='2'>" + ("<table class='commission-table'><tr class=\"" + i + '">') + ("<td class='commission-header' colspan=\"2\">" + r.$("#commissionText").val() + "<\/td><\/tr>")), u.push('<tr class="' + i + '" data-dtr-index="' + s.column + '">' + ('<td class="dtr-title">' + f.text() + "<\/td> ") + ('<td class="dtr-data">' + h + "<\/td><\/tr>")), f.hasClass("endGroup") && u.push("<\/table><\/td><\/tr>")) : e.push('<tr class="' + i + '" data-dtr-index="' + s.column + '">' + ('<td class="dtr-title">' + f.text() + "<\/td> ") + ('<td class="dtr-data">' + h + "<\/td><\/tr>"))
            }), f = e.concat(o).concat(u), f ? r.$('<table class="detail-table" data-dtr-index="' + t + '"/>').append(f.join("")) : !1
        };
        this.reloadData = function () {
            h.$.unblockUI();
            h.$table.ajax.reload()
        };
        this.detailCallBack = function () {
            h.$(".login-ip").unbind().bind("click", function (n) {
                return h.customerListPopup.openProductPopup(n.currentTarget)
            });
            h.$(".icon-statement").unbind().bind("click", function (n) {
                return h.viewStatement(n)
            });
            h.editSingleSetting.bindingEvent();
            h.editStatus.bindingEvent();
            h.unlock.bindingEvent();
            h.$("td.control").each(function (n, t) {
                h.$(t).attr("title", h.$(t).parent().hasClass("parent") ? h.$("#collapseText").val() : h.$("#expandText").val())
            })
        };
        this.drawCallback = function () {
            h.detailCallBack();
            var n = h.$("#select-all");
            h.$("#tbl-customer-list").removeClass("hidden");
            h.$(".btn-submit").unbind().bind("click", function () {
                $("#searchAccountId").val("");
                h.username = h.$("#txt-username").val();
                h.status = h.$("#statusFilter").val();
                n.prop("checked", !1);
                h.$table.ajax.reload()
            });
            h.$(".username-field").unbind().bind("click", function () {
                $("#searchAccountId").val(h.$(this).data("cusid"));
                h.$table.ajax.reload()
            });
            h.$("tbody .checkbox").unbind().bind("click", function () {
                n.prop("checked", h.$("tbody .checkbox").length === h.$("tbody .checkbox:checked").length)
            });
            h.$("td.control").each(function (n, t) {
                h.$(t).attr("title", h.$(t).parent().hasClass("parent") ? h.$("#collapseText").val() : h.$("#expandText").val())
            });
            h.$("thead tr th.control").parent().removeClass("parent")
        };
        this.viewStatement = function (n) {
            n.preventDefault();
            var t = h.topWindow.$("#main");
            h.topWindow.alpha.core.isIe() || (t.contents().find("head").append(h.$("#AssetCommonCssLink").val()), t.contents().find("body").empty().block());
            t.attr("src", h.$(n.currentTarget).data("url"))
        };
        this.buildGetAllStatusLink = function (n) {
            var t = {
                SearchAccountId: n.id,
                SearchAccountName: n.username,
                // SearchAccountRoleId: n.RoleId,
                // UplineId: h.viewModel.SearchAccountId,
                // UplineName: h.viewModel.SearchAccountName,
                // UplineRoleId: h.viewModel.SearchAccountRoleId
            };
            return "GetCustomerAllStatus?" + h.$.param(t)
        };
        this.$ = n;
        this.topWindow = t;
        this.viewModel = i;
        this.customerListPopup = r;
        this.customerListServices = u;
        this.editSingleSetting = f;
        this.editMultipleSetting = e;
        this.editStatus = o;
        this.unlock = s;
        this.topWindow.customerListNew = !0
    }
    return n
}();
_this = this;
CustomerListViewModel = function () {
    function n(n, t, i, r, u, f, e, o, s, h, c, l, a, v, y, p, w, b) {
        this.UplineRoleId = n;
        this.SearchAccountId = t;
        this.SearchAccountRoleId = r;
        this.SearchAccountName = i;
        this.DownlineLevelName = u;
        this.ShowEditMultiple = f;
        this.IsMobileView = e;
        this.BreadcrumbLevelJson = o;
        this.OldMainUrl = s;
        this.ReportsUrl = h;
        this.MemberInfoUrl = c;
        this.AccountInfoUrl = l;
        this.LimitedDateToShowInfo = a;
        this.IsShowMyanmarOdds = v;
        this.CanViewReport = y;
        this.CanUpdateAccount = p;
        this.CustomerListResources = w;
        this.CustomerListData = b
    }
    return n
}(),
    function (n) {
        n[n.Member = 1] = "Member";
        n[n.Agent = 2] = "Agent";
        n[n.Master = 3] = "Master";
        n[n.Super = 4] = "Super"
    }(CustomerLevel || (CustomerLevel = {}));
var ClosedReason = {
    ByAdmin: 1,
    ByUpline: 2,
    ByArchive: 4
},
    LockedAccess = {
        FalseOTPSC: 1,
        FalsePassword: 2
    },
    statusClasses = {
        Closed: [{
            Id: ClosedReason.ByAdmin,
            Css: "closed-byAdmin"
        }, {
            Id: ClosedReason.ByUpline,
            Css: "closed-byUpline"
        }, {
            Id: ClosedReason.ByArchive,
            Css: "closed-byArchive"
        }],
        Suspended: {
            Css: "RowBgSuspended"
        },
        Disabled: {
            Css: "RowBgDisabled"
        },
        Enabled: {
            Css: "enabled"
        },
        Locked: [{
            Id: LockedAccess.FalseOTPSC,
            Css: "locked-falseOTPSC"
        }, {
            Id: LockedAccess.FalsePassword,
            Css: "locked-falsePassword"
        }]
    },
    buildAttributes = function (n) {
        return _this.$.map(n, function (n, t) {
            return t + "='" + n + "'"
        }).join(" ")
    };
CustomerList = function () {
    function n(n, t, i, r, u, f) {
        var e = this;
        this.init = function () {
            e.statusDropdown.render(new SingleSelectionDropdownOption("statusFilter", "StatusId", function () { }, !0, !0));
            e.bindingTxtUsername();
            e.customerListDataTable.bindingEvent();
            e.customerListPopup.bindingBootstrapModalEvent()
        };
        this.bindingTxtUsername = function () {
            var t = {
                url: function (n) {
                    return e.viewModel.MemberInfoUrl + "customer/GetUsernamesSuggestion?SuggestionQuery=" + n + "&CustId=" + e.viewModel.SearchAccountId + "&DirectDownlines=true&IsAccInfo=false"
                },
                getValue: "UserName",
                requestDelay: 200,
                minCharNumber: 2,
                template: {
                    type: "custom",
                    method: function (n, t) {
                        var i = t.UserName2 === null || t.UserName2 === "" ? "" : "(" + t.UserName2 + ")";
                        return "<div class='suggestion'>" + n + " " + i + "<div>" + t.FirstName + " " + t.LastName + "<\/div><\/div>"
                    }
                },
                list: {
                    maxNumberOfElements: 10
                }
            },
                n = e.$("#txt-username");
            n.easyAutocomplete(t);
            n.bind("keypress", function (n) {
                n.which == 13 && e.$(".btn-submit").click()
            })
        };
        this.$ = n;
        this.viewModel = t;
        this.customerListDataTable = r;
        this.customerListPopup = i;
        this.statusDropdown = u;
    }
    return n
}();
$(function () {
    var t = new CustomerListViewModel(parseInt($("#uplineRoleId").val()), parseInt($("#searchAccountId").val()),
    $("#searchAccountName").val(), parseInt($("#searchAccountRoleId").val()), $("#downlineLevelName").val(),
    JSON.parse($("#showEditMultiple").val()), JSON.parse($("#isMobileView").val()), $("#breadcrumbLevelJson").val(),
    $("#oldMainUrl").val(), $("#reportsUrl").val(), $("#memberInfoUrl").val(), $("#accountInfoUrl").val(),
    JSON.parse($("#limitedDateToShowInfo").val()), JSON.parse($("#isShowMyanmarOdds").val()),
    JSON.parse($("#canViewReport").val()), JSON.parse($("#canUpdateAccount").val()),
    $.parseJSON($("#customerListResources").val()), window.customerListData),     n = new CustomerListPopup($, window.top, t.IsMobileView),
        i = new CustomerListServices($, t),
        r = new EditSingleSetting($, n, i),
        f = new EditMultipleSetting($, t, n, i, r),
        e = new EditStatus($, window.top, t.IsMobileView, i, n),
        o = new Unlock($, window.top, t.IsMobileView, i),
        u = new CustomerListDataTable($, window.top, t, n, i, r, f, e, o),
        s = new CustomerList($, t, n, u, new SingleSelectionDropdownComponent($), new SingleSelectionDropdownComponent($));
    window.top.customerListPopup = n;
    window.successfulUpdatingCallback = function () {
        return n.successfulUpdatingCallback()
    };
    window.actionAfterUpdatedLiveCasinoSettingMultiple = function () {
        return n.successfulUpdatingCallback()
    };
    window.actionAfterUpdatedLiveCasinoSettingSingle = function () {
        return n.successfulUpdatingCallback()
    };
    window.top.successfulUpdatingCallbackAndReloadMainFrame = function (t) {
        t === void 0 && (t = !1);
        $.blockUI();
        setTimeout(function () {
            $.unblockUI();
            u.reloadData();
            n.reloadFrame()
        }, t ? 3e3 : 1500)
    };
    s.init()
});