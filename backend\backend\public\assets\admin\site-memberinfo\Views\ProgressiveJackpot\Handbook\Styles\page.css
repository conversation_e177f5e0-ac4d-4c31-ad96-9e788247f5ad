﻿body {
    max-width: 750px;
    font-family: '<PERSON><PERSON>', Arial, Helvetica, sans-serif;
    font-size: 14px;
    line-height: 1.5;
}

.bold {
    font-weight: bold;
}

.mrg-t-4 {
    margin-top: 4px;
}

ol, ul {
    margin: 10px 0;
}

    ul li {
        margin-bottom: 8px;
    }

.responsive-image {
    width: 100%;
    height: auto;
}

.underline {
    text-decoration: underline;
}

.container-heading1 {
    counter-reset: item;
}

.heading1 {
    margin-top: 16px;
    margin-bottom: 0;
    counter-increment: item;
    color: #e36c0c;
    font-size: 16px;
}

    .heading1:before {
        margin-right: 4px;
        content: counters(item, ".") ". ";
        color: #e36c0c;
    }

.container-heading2 {
    padding-left: 20px;
    counter-reset: setting;
}

.heading2 {
    margin-top: 10px;
    margin-bottom: 0;
    counter-increment: setting;
    font-size: 14px;
    color: #588dd6;
}

    .heading2:before {
        margin-right: 4px;
        content: "2." counters(setting, "3")". ";
        color: #588dd6;
    }

/* Table */

.table {
    margin-top: 10px;
    border: 1px solid #9cc2e5;
    border-collapse: collapse;
}

td, th {
    min-height: 25px;
    padding-right: 14px;
    padding-left: 14px;
    border: 1px solid #9cc2e5;
    line-height: 25px;
}

.table th {
    white-space: nowrap;
}

.table ul {
    padding-left: 14px;
}
