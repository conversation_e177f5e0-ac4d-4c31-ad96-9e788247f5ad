function FormEnterKey(e){var t;return t=window.event?window.event.keyCode:e.which,13!=t}function showHelp(e){if("hidden"==document.getElementById(e).style.visibility){for(i=0;i<=HelpList.length-1;i+=1)document.getElementById(HelpList[i]).style.visibility="hidden";document.getElementById(e).style.visibility="visible"}else document.getElementById(e).style.visibility="hidden"}function SwitchUssInput(e){document.getElementById("uusSet").style.display="c"==e?"none":""}function RestorePreferences(){for("imbc"==document.getElementById("hidUSite").value&&(DefPreferencesSelectChildValue[1]="0"),i=0;i<=PreferencesSelect.length-1;i+=1)setSelecter(PreferencesSelect[i],this,DefPreferencesKey[i]),"OddsType"==PreferencesSelect[i]&&null==document.getElementById("OddsType")||(document.getElementById(PreferencesSelect[i]+"_Txt").innerHTML=document.getElementById(PreferencesSelect[i]+"_menu").children[DefPreferencesSelectChildValue[i]].innerHTML,$("#"+PreferencesSelect[i]+"_Txt").attr("title",document.getElementById(PreferencesSelect[i]+"_menu").children[DefPreferencesSelectChildValue[i]].innerHTML)),PreferencesSelect[3]&&(document.getElementById("uus").value="",SwitchUssInput("c"))}function callPreferencesSubmit(e){if("YES"==e){if(getSelecterValue("BetStake")>0||""==getSelecterValue("BetStake")){if(""==document.getElementById("uus").value)return alert(document.getElementById("hidBetStakeNotNullMessage").value),!1;if(isNaN(removeCommas(document.getElementById("uus").value)))return alert(document.getElementById("hidBetStakeNotNumericMessage").value),!1;if(document.getElementById("uus").value<=0)return alert(document.getElementById("hidBetStakeMoreThenZeroMessage").value),!1;if(-1!=document.getElementById("uus").value.indexOf("."))return alert(document.getElementById("hidBetSatkeNumbericValueMessage").value),!1}if("visible"==document.getElementById("ValidateDiv").style.visibility&&""==document.getElementById("txtCode").value)return alert(document.getElementById("hiderrlogin_enter_valid").value),!1;var t=new Object;t.Type="P",t.DefaultLanguage=getSelecterValue("DefaultLanguage"),t.OddsType=getSelecterValue("OddsType"),t.LayoutMode=getSelecterValue("LayoutMode"),t.BetStake=""!=getSelecterValue("BetStake")&&getSelecterValue("BetStake")>0||""==getSelecterValue("BetStake")?removeCommas(document.getElementById("uus").value):getSelecterValue("BetStake"),t.BetterOdds=getSelecterValue("BetterOdds"),t.AutoRefresh=getSelecterValue("AutoRefresh"),t.ShowScoreMap=getSelecterValue("ShowScoreMap"),t.OddsSort=getSelecterValue("OddsSort"),t.ShowLiveCasino=getSelecterValue("ShowLiveCasino"),t.MarketType=getSelecterValue("MarketType"),t.txtCode=document.getElementById("txtCode").value,$.ajax({url:"UserProfileUpdate.ashx",async:!1,cache:!1,type:"GET",dataType:"json",data:{Type:t.Type,DefaultLanguage:t.DefaultLanguage,OddsType:t.OddsType,LayoutMode:t.LayoutMode,BetStake:t.BetStake,BetterOdds:t.BetterOdds,AutoRefresh:t.AutoRefresh,ShowScoreMap:t.ShowScoreMap,OddsSort:t.OddsSort,ShowLiveCasino:t.ShowLiveCasino,MarketType:t.MarketType,ValidateCode:t.txtCode},success:function(e){var t=e.ValidateCheck;alert(e.Msg.replace("\\n","\n")),1==t?(document.getElementById("ValidateDiv").style.visibility="visible",$("#validateCode").click()):document.getElementById("ValidateDiv").style.visibility="hidden",document.getElementById("txtCode").value="",window.opener.UpdateSetting(e.AutoRefresh,e.OddsType,e.PageType,e.OddsSort,e.BetterOdds,e.MarketType)}})}return!0}function RestoreCSPriority(){var e=document.getElementById("SportCount").value,t=document.getElementById("DefaultSortType").value.split(","),n=document.getElementById("DefaultSportName").value.split(",");for(i=1;i<=e;i+=1)$(document.getElementById("SN"+i)).find("input").val(t[i-1]),$(document.getElementById("SN"+i)).find("div")[1].innerHTML=n[i-1],document.getElementById("SN"+i).style.color=""}function callCSPrioritySubmit(e){var t=new Object;if(t.Type="S","NO"==e)t.NewSportSort="NO";else{var n="",l=document.getElementById("SportCount").value;for(i=1;i<=l;i+=1)n+=$(document.getElementById("SN"+i)).find("input").val(),i<l&&(n+=",");t.NewSportSort=n}return $.ajax({url:"UserProfileUpdate.ashx",async:!1,cache:!1,type:"GET",dataType:"json",data:{Type:t.Type,NewSportSort:t.NewSportSort},success:function(e){alert(e.Msg.replace("\\n","\n")),window.top.location.href="UserProfile_CSPriority.aspx"}}),!0}function setMoveDataColor(e){var t=document.getElementById("SportCount").value;for(i=1;i<=t;i+=1)document.getElementById("SN"+i).style.color="";document.getElementById("SN"+e).style.color="#ff0000"}function moveUp(e){var t=e.offsetParent.id.replace("SN","");if(1==t)return!1;var n=t.replace("SN",""),l=parseInt(n)-1,d=e.parentNode.parentNode.parentNode,r=$(d).find("input").val(),i=$(d).find("div")[1].innerHTML,u=$(document.getElementById("SN"+l)).find("input").val(),a=$(document.getElementById("SN"+l)).find("div")[1].innerHTML;return"2"==t&&"252"==r?(alert($("#BingoTopMsg").val()),!1):($(document.getElementById("SN"+l)).find("input").val(r),$($(document.getElementById("SN"+l)).find("div")[1]).html(i),$(document.getElementById("SN"+n)).find("input").val(u),$($(document.getElementById("SN"+n)).find("div")[1]).html(a),void setMoveDataColor(l))}function moveDown(e){var t=e.offsetParent.id.replace("SN","");if(t==document.getElementById("SportCount").value)return!1;var n=t.replace("SN",""),l=parseInt(n)+1,d=e.parentNode.parentNode.parentNode,r=$(d).find("input").val(),i=$(d).find("div")[1].innerHTML,u=$(document.getElementById("SN"+l)).find("input").val(),a=$(document.getElementById("SN"+l)).find("div")[1].innerHTML;return"1"==t&&"252"==u?(alert($("#BingoTopMsg").val()),!1):($(document.getElementById("SN"+l)).find("input").val(r),$($(document.getElementById("SN"+l)).find("div")[1]).html(i),$(document.getElementById("SN"+n)).find("input").val(u),$($(document.getElementById("SN"+n)).find("div")[1]).html(a),void setMoveDataColor(l))}function moveUpToTop(e){var t,n=(document.getElementById("SportCount").value,e.offsetParent.id.replace("SN",""));if(1==n)return setMoveDataColor(1),!1;if(2==n&&"252"==$(document.getElementById("SN"+n)).find("input").val())return alert($("#BingoTopMsg").val()),!1;if("252"==$(document.getElementById("SN"+n)).find("input").val()){var l=parseInt(n);t=new Array(l-1)}else t=new Array(parseInt(n));var d=new Array(parseInt(t.length)),r=new Array(parseInt(t.length));if("252"!=$(document.getElementById("SN"+n)).find("input").val())for(i=0;i<=t.length-1;i+=1)0==i?(d[i]=$(document.getElementById("SN"+n)).find("input").val(),r[i]=$(document.getElementById("SN"+n)).find("div")[1].innerHTML):(d[i]=$(document.getElementById("SN"+i)).find("input").val(),r[i]=$(document.getElementById("SN"+i)).find("div")[1].innerHTML);else for(d[0]=$(document.getElementById("SN"+n)).find("input").val(),r[0]=$(document.getElementById("SN"+n)).find("div")[1].innerHTML,i=1;i<=t.length-1;i+=1)d[i]=$(document.getElementById("SN"+(i+1))).find("input").val(),r[i]=$(document.getElementById("SN"+(i+1))).find("div")[1].innerHTML;var u=1;for("252"==$(document.getElementById("SN"+n)).find("input").val()&&(u=2),j=0;j<=r.length-1;j+=1){var a=j+u;$(document.getElementById("SN"+a)).find("input").val(d[j]),$(document.getElementById("SN"+a)).find("div")[1].innerHTML=r[j]}setMoveDataColor(u)}function moveDownToLast(e){var t,n=document.getElementById("SportCount").value,l=e.offsetParent.id.replace("SN","");if(1==l)t=new Array(parseInt(n));else{var d=parseInt(n)-parseInt(l)+1;t=new Array(parseInt(d))}var r=new Array(parseInt(t.length)),u=new Array(parseInt(t.length));for(i=0;i<=t.length-1;i+=1)if(i==parseInt(t.length-1))r[i]=$(document.getElementById("SN"+l)).find("input").val(),u[i]=$(document.getElementById("SN"+l)).find("div")[1].innerHTML;else{var a=parseInt(l)+i+1;r[i]=$(document.getElementById("SN"+a)).find("input").val(),u[i]=$(document.getElementById("SN"+a)).find("div")[1].innerHTML}if(1==l&&"252"==r[0])return alert($("#BingoTopMsg").val()),!1;for(j=0;j<=u.length-1;j+=1){var a=parseInt(l)+j;$(document.getElementById("SN"+a)).find("input").val(r[j]),$(document.getElementById("SN"+a)).find("div")[1].innerHTML=u[j]}setMoveDataColor(n)}function callSecSubmit(e){if(""==document.getElementById("txtSecCode").value)return alert("{{lbl_EmptyError}}"),!1;if("true"==document.getElementById("hidCheckSecRule").value.toLowerCase()){if(6!=document.getElementById("txtSecCode").value.length)return alert("{{lbl_LengthError}}"),!1;var t=/^[0-9]*$/;if(!t.test(document.getElementById("txtSecCode").value))return alert("{{lbl_NumberError}}"),!1;var n=document.getElementById("txtSecCode").value,l="(\\d)(\\1{"+(n.length-1)+"})",d=new RegExp(l,"gi");if(d.test(n))return alert("{{lbl_2NumberDiff}}"),!1;var r="0123456789",i="9876543210";if(r.indexOf(document.getElementById("txtSecCode").value)>-1||i.indexOf(document.getElementById("txtSecCode").value)>-1)return alert("{{lbl_SeqError}}"),!1}if(document.getElementById("hidSubmit").value=e,document.getElementById("hidChangeMode").value="sec",confirm(document.getElementById("hidconfirm").value)){var u;return u=document.getElementById("txtSecCode"),document.getElementById("hidEncryptSecCode").value=CFS(u.value),u.value="",document.getElementById("frmChangePW").submit(),!0}return!1}function callSubmit(e){var t,n,l=new RegExp(/[a-zA-Z0-9]*/);if(""==document.getElementById("txtOldPW").value)return alert(document.getElementById("hidOldPW").value),!1;if(""==document.getElementById("txtPW").value)return alert(document.getElementById("hidPW").value),!1;if(""==document.getElementById("txtConPW").value)return alert(document.getElementById("hidConPW").value),!1;if(document.getElementById("txtPW").value.length>10)return alert(document.getElementById("hidPW").value+" "+document.getElementById("hidExecPW").value),!1;if(document.getElementById("txtConPW").value.length>10)return alert(document.getElementById("hidConPW").value+" "+document.getElementById("hidExecPW").value),!1;if(document.getElementById("txtPW").value.length<6)return alert(document.getElementById("hidPW").value+" "+document.getElementById("hidExecPW").value),!1;if(document.getElementById("txtConPW").value.length<6)return alert(document.getElementById("hidConPW").value+" "+document.getElementById("hidExecPW").value),!1;if(n=document.getElementById("txtPW").value,t=l.test(n),0==t)return alert(document.getElementById("hidSpecialCharactersNewPassword").value),document.getElementById("txtPW").focus(),!1;if(n=document.getElementById("txtConPW").value,t=l.test(n),0==t)return alert(document.getElementById("hidSpecialCharactersConfirmPassword").value),document.getElementById("txtConPW").focus(),!1;if(document.getElementById("txtPW").value!=document.getElementById("txtConPW").value)return alert(document.getElementById("hidPWdiff").value),!1;if(document.getElementById("txtPW").value==document.getElementById("txtOldPW").value)return alert(document.getElementById("hidpwdnotsame").value),!1;document.getElementById("hidSubmit").value=e,document.getElementById("hidChangeMode").value="pwd";var d,r;return d=document.getElementById("txtPW"),d.value=CFS(d.value),d=document.getElementById("txtConPW"),d.value=CFS(d.value),d=document.getElementById("txtOldPW"),r=d.value.toLowerCase(),d.value=CFS(d.value),d=document.getElementById("hidLowerCaseOldPW"),d.value=CFS(r),document.getElementById("frmChangePW").submit(),!0}function checkKeyForChPassword(e,t){document.all?t.keyCode:t.which;return 13==whichCode||8==whichCode||9==whichCode?!0:(key=whichCode,key>=48&&key<=57?!0:key>=65&&key<=90?!0:key>=96&&key<=122?!0:!1)}function userBrowser(){var e=navigator.userAgent.toLowerCase();return/msie/i.test(e)&&!/opera/.test(e)?"IE":/firefox/i.test(e)?"Firefox":/chrome/i.test(e)&&/webkit/i.test(e)&&/mozilla/i.test(e)?"Chrome":/opera/i.test(e)?"Opera":!/webkit/i.test(e)||/chrome/i.test(e)&&/webkit/i.test(e)&&/mozilla/i.test(e)?"unKnow":"Safari"}function resetPW(){document.getElementById("txtOldPW").value="",document.getElementById("txtPW").value="",document.getElementById("txtConPW").value="",$(".testresult").remove()}function resetSEC(){document.getElementById("txtSecCode").value=""}function showmessage(e){return""==e?!1:void alert(e)}function addCommas(e){for(var t=new RegExp("(-?[0-9]+)([0-9]{3})");t.test(e);)e=e.replace(t,"$1,$2");document.getElementById("uus").value=e}function removeCommas(e){var t=/,/g;return e.replace(t,"")}var PreferencesSelect=new Array(10),DefPreferencesKey=new Array(10),DefPreferencesSelectChildValue=new Array(10),HelpList=new Array(10);PreferencesSelect[0]="DefaultLanguage",PreferencesSelect[1]="OddsType",PreferencesSelect[2]="LayoutMode",PreferencesSelect[3]="BetStake",PreferencesSelect[4]="BetterOdds",PreferencesSelect[5]="AutoRefresh",PreferencesSelect[6]="ShowScoreMap",PreferencesSelect[7]="OddsSort",PreferencesSelect[8]="ShowLiveCasino",PreferencesSelect[9]="MarketType",DefPreferencesKey[0]="en",DefPreferencesKey[1]="4",DefPreferencesKey[2]="2",DefPreferencesKey[3]="-1",DefPreferencesKey[4]="1",DefPreferencesKey[5]="0",DefPreferencesKey[6]="0",DefPreferencesKey[7]="1",DefPreferencesKey[8]="1",DefPreferencesKey[9]="1",DefPreferencesSelectChildValue[0]=0,DefPreferencesSelectChildValue[1]=2,DefPreferencesSelectChildValue[2]=1,DefPreferencesSelectChildValue[3]=2,DefPreferencesSelectChildValue[4]=0,DefPreferencesSelectChildValue[5]=1,DefPreferencesSelectChildValue[6]=1,DefPreferencesSelectChildValue[7]=0,DefPreferencesSelectChildValue[8]=0,DefPreferencesSelectChildValue[9]=1,HelpList[0]="langHelp",HelpList[1]="otHelp",HelpList[2]="ptHelp",HelpList[3]="stHelp",HelpList[4]="boHelp",HelpList[5]="arHelp",HelpList[6]="smHelp",HelpList[7]="osHelp",HelpList[8]="lcHelp",HelpList[9]="mtHelp",$(document).ready(function(){$(".CSPriority input").on("click",function(){$(this).is(":checked")?$(this).closest(".CSPriority").removeClass("default"):$(this).closest(".CSPriority").addClass("default")})});