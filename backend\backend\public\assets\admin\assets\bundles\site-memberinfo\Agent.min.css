﻿@import url("/assets/styles/base/roboto-fonts.css");@import url("/assets/styles/base/icon-fonts.css");@-webkit-keyframes ripple{0%{-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0);}20%{-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}100%{opacity:0;-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}}@-moz-keyframes ripple{0%{-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0);}20%{-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}100%{opacity:0;-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}}@-o-keyframes ripple{0%{-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0);}20%{-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}100%{opacity:0;-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}}@keyframes ripple{0%{-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0);}20%{-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}100%{opacity:0;-webkit-transform:scale(1);-khtml-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1);}}::-webkit-scrollbar{height:16px;overflow:visible;width:16px;}::-webkit-scrollbar-button{height:0;width:0;}::-webkit-scrollbar-track{background-clip:padding-box;border:solid transparent;border-width:0 0 0 4px;}::-webkit-scrollbar:vertical{width:11px;}::-webkit-scrollbar:horizontal{height:11px;}::-webkit-scrollbar-track:horizontal{border-width:4px 0 0;}::-webkit-scrollbar-track:hover{background-color:rgba(0,0,0,.05);box-shadow:inset 1px 0 0 rgba(0,0,0,.1);}::-webkit-scrollbar-track:horizontal:hover{box-shadow:inset 0 1px 0 rgba(0,0,0,.1);}::-webkit-scrollbar-track:active{background-color:rgba(0,0,0,.05);box-shadow:inset 1px 0 0 rgba(0,0,0,.14),inset -1px 0 0 rgba(0,0,0,.07);}::-webkit-scrollbar-track:horizontal:active{box-shadow:inset 0 1px 0 rgba(0,0,0,.14),inset 0 -1px 0 rgba(0,0,0,.07);}::-webkit-scrollbar-thumb{background-color:rgba(0,0,0,.2);background-clip:padding-box;border:solid transparent;min-height:28px;padding:100px 0 0;box-shadow:inset 1px 1px 0 rgba(0,0,0,.1),inset 0 -1px 0 rgba(0,0,0,.07);}::-webkit-scrollbar-thumb:horizontal{border-width:6px 1px 1px;padding:0 0 0 100px;box-shadow:inset 1px 1px 0 rgba(0,0,0,.1),inset -1px 0 0 rgba(0,0,0,.07);}::-webkit-scrollbar-thumb:hover{background-color:rgba(0,0,0,.4);box-shadow:inset 1px 1px 1px rgba(0,0,0,.25);}::-webkit-scrollbar-thumb:active{background-color:rgba(0,0,0,.5);box-shadow:inset 1px 1px 3px rgba(0,0,0,.35);}::-webkit-scrollbar-corner{background:transparent;}body::-webkit-scrollbar-track-piece{background-clip:padding-box;background-color:#f5f5f5;border:solid #fff;border-width:0 0 0 3px;box-shadow:inset 1px 0 0 rgba(0,0,0,.14),inset -1px 0 0 rgba(0,0,0,.07);}body::-webkit-scrollbar-track-piece{border-width:3px 0 0;box-shadow:none;}body::-webkit-scrollbar-thumb{border-width:1px 1px 1px 1px;}body::-webkit-scrollbar-thumb:horizontal{border-width:1px 1px 1px;}body::-webkit-scrollbar-corner{background-clip:padding-box;background-color:#f5f5f5;border:solid #fff;border-width:3px 0 0 3px;-webkit-box-shadow:1px 1px 0 rgba(0,0,0,.14);-khtml-box-shadow:1px 1px 0 rgba(0,0,0,.14);-moz-box-shadow:1px 1px 0 rgba(0,0,0,.14);-o-box-shadow:1px 1px 0 rgba(0,0,0,.14);box-shadow:1px 1px 0 rgba(0,0,0,.14);}html{touch-action:manipulation;}body{background:none;color:#333;font-family:Roboto,Arial,Tahoma,helvetica,sans-serif;font-size:13px;margin:0;padding:0;}.hidden{display:none;}.nowrap{white-space:nowrap;}.l{text-align:left !important;}.r,.text-right{text-align:right;}.c,.text-center{text-align:center;}.b{font-weight:700 !important;}.weight-normal{font-weight:normal;}.bold{font-weight:bold !important;}.pull-left{float:left !important;}.pull-right{float:right !important;}a:link{color:#045ace;text-decoration:none;}input,select,button{font-size:inherit;font-family:inherit;outline:none;}input[type=text],input[type=password]{width:120px;height:28px;line-height:28px;border:none;padding-left:8px;padding-right:8px;margin-left:8px;-webkit-appearance:none;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3);-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}input[type=text].hasBorder,input[type=password].hasBorder{border:1px solid #dfdfdf;}input[type=text]::-ms-clear,input[type=password]::-ms-clear{display:none;}button[disabled]:active,button[disabled],input[type="reset"][disabled]:active,input[type="reset"][disabled],input[type="button"][disabled]:active,input[type="button"][disabled],input[type="submit"][disabled]:active,input[type="submit"][disabled]{opacity:.6;filter:alpha(opacity=60);cursor:pointer;}select{border:none;color:#333;height:28px;margin-right:7px;padding:4px 8px;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3);-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}select option{padding:4px;}select.hasBorder{border:1px solid #dfdfdf;}select:disabled{border-color:#a9a9a9;color:rgba(0,0,0,.3);}input[type=button],.btn,.btn-refresh,input[type=button]#dSubmit,input[type=button].btnSubmit,input[type=submit]#dSubmit,input[type=submit].btnSubmit,.buttonSubmit,.btnMain{border:none;cursor:pointer;height:28px;outline:none;margin-right:8px;padding-left:16px;padding-right:16px;white-space:nowrap;-webkit-appearance:none;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 2px 2px rgba(0,0,0,.3);-khtml-box-shadow:0 2px 2px rgba(0,0,0,.3);-moz-box-shadow:0 2px 2px rgba(0,0,0,.3);-o-box-shadow:0 2px 2px rgba(0,0,0,.3);box-shadow:0 2px 2px rgba(0,0,0,.3);-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}input[disabled][type=button],[disabled].btn,[disabled].btn-refresh,input[disabled][type=button]#dSubmit,input[disabled][type=submit]#dSubmit,input[disabled][type=submit].btnSubmit,[disabled].buttonSubmit,[disabled].btnMain{opacity:.4;cursor:default;}input[type=button],.btn{background-color:#e1e1e1;color:#212121;}input[type=button]:hover,.btn:hover{color:#212121;background-color:#d4d4d4;}input[type=button].btn-cancel,.btn.btn-cancel{background-color:#909090;color:#fff;}input[type=button].btn-cancel:hover,input[type=button].btn-cancel:active,input[type=button].btn-cancel:focus,.btn.btn-cancel:hover,.btn.btn-cancel:active,.btn.btn-cancel:focus{background-color:#838383;}.btn-refresh{background-color:#1bac69;color:#fff;}.btn-refresh:hover,.btn-refresh:active,.btn-refresh:focus{background-color:#18965c;color:#fff;}input[type=button]#dSubmit,input[type=button].btnSubmit,input[type=submit]#dSubmit,input[type=submit].btnSubmit,.buttonSubmit,.btnMain{background:#0b599c;color:#fff;}input[type=button]#dSubmit:hover,input[type=button]#dSubmit:active,input[type=button]#dSubmit:focus,input[type=button].btnSubmit:hover,input[type=button].btnSubmit:active,input[type=button].btnSubmit:focus,input[type=submit]#dSubmit:hover,input[type=submit]#dSubmit:active,input[type=submit]#dSubmit:focus,input[type=submit].btnSubmit:hover,input[type=submit].btnSubmit:active,input[type=submit].btnSubmit:focus,.buttonSubmit:hover,.buttonSubmit:active,.buttonSubmit:focus,.btnMain:hover,.btnMain:active,.btnMain:focus{background-color:#094b84;}input[type=reset],input[type=button].btn-reset{background-color:#f6731b;color:#fff;outline:none;}input[type=reset]:hover,input[type=button].btn-reset:hover{color:#fff;background-color:#ee6509;}table{border-spacing:0;border:0;}.tblRpt{background-color:#fff;border:1px solid #d1d1d1;}.tblRpt tfoot tr:first-child td{border-bottom:1px solid #d1d1d1;}.tblRpt thead tr th,.tblRpt tr th{font-weight:normal;}.tblRpt tr td,.tblRpt thead tr th,.tblRpt tr th{border-right-width:0;border-bottom-width:0;border-right:1px solid #d1d1d1;border-top:1px solid #d1d1d1;height:28px;padding:2px 8px;white-space:nowrap;vertical-align:middle;-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}.tblRpt tbody+tfoot tr:first-child td{border-top:1px solid #d1d1d1;}.tblRpt tr.RptFooter{background-color:#7f3d1d;color:#fff;font-weight:700;}.RptSubTitle{background-color:#ffefde;}.RptTitle{text-align:center;}.RptHeader{font-weight:400;text-align:center;}.RptHeader td,.RptHeader th{background-color:#666;color:#fff;}.RptHeader02{font-weight:400;text-align:center;}.RptHeader02 td,.RptHeader02 th{background-color:#3f3f3f;color:#fff;}.RptHeader01{background-color:#e8e8e8;font-weight:700;height:22px;text-align:center;}.text-red{color:#b50000;}.text-green{color:#18910b;}.w-order{width:20px;}.bl_time{font-size:8pt !important;text-align:center;}.bg_white{background-color:#fff;}.bg_eb{background-color:#f7f0e4;}.bg_eb2{background-color:#f7f0e4;}.MaskLoadingDiv{background:url(../Images/loader3.gif) no-repeat center center;}#page_main{overflow:visible;padding-bottom:12px;}#header_main,.title-page{color:#9d1c1c;font-weight:700;height:39px;line-height:40px;min-width:650px;text-transform:uppercase;border-bottom:1px solid #cecece;}#header_main.titleWinlossMaster,.title-page.titleWinlossMaster{width:765px;}#header_main .changeview,.title-page .changeview{color:#045ace;text-decoration:none;text-transform:capitalize;margin-left:12px;}#header_main .changeview:hover,.title-page .changeview:hover{color:#f60;}.positive{color:#036;font-weight:700;}.negative{color:#b50000;font-weight:700;}.iplink{color:#045ace;text-decoration:none;}.iplink:hover{color:#f60;text-decoration:none;}.ipnolink{text-decoration:none;}.firstCol{background-color:#f8d7a2;font-weight:bold;text-transform:uppercase;text-align:center;}#reportFilter input[type='checkbox']{position:relative;top:2px;}#reportFilter+.warning ul{margin-top:0;}.warning ul{color:#333;list-style:none;margin:16px 0 0;padding-left:2px;-webkit-text-size-adjust:none;-moz-text-size-adjust:none;-ms-text-size-adjust:none;text-size-adjust:none;}.warning ul li{background:url(../Images/more.gif) no-repeat 0 6px;padding-left:10px;}div.loading,.loading1{background:url(../Images/ajax-loader.gif) no-repeat center center;display:block;height:16px;width:16px;}#loading{height:16px;position:relative;top:8px;width:16px;}.style{cursor:pointer;}.loading2{background-image:url(../MemberInfo/P2P/Images/loading.gif);background-repeat:no-repeat;background-position:center center;}div.divBoxRight{float:right;padding-right:4px;margin-top:4px;}.AGEWnd{background-color:#fff;border:1px solid #dfdfdf;border-collapse:collapse;margin:0;padding:0 16px 16px;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-shadow:0 10px 20px rgba(0,0,0,.19),0 6px 6px rgba(0,0,0,.23);-moz-box-shadow:0 10px 20px rgba(0,0,0,.19),0 6px 6px rgba(0,0,0,.23);box-shadow:0 10px 20px rgba(0,0,0,.19),0 6px 6px rgba(0,0,0,.23);}.AGEWndMask{background-color:transparent;border:dashed 2px #dfdfdf;border-style:dotted;cursor:move;z-index:1002;}.AGEWndTitleMask{background-color:#727272;border:solid 0 #fff;border-style:none;cursor:move;filter:alpha(opacity=1);height:20px;left:0;margin:0;opacity:.01;padding:0;position:absolute;top:0;z-index:1002;}.AGEWndTable{border:solid 0 transparent;cursor:default;margin:0;padding:0;table-layout:fixed;width:100%;}.AGEWndTitle{-moz-user-select:none;color:#333;font-weight:bold;margin:0;border-bottom:1px solid #dfdfdf;}.AGEWndTitleDiv{font-size:13px;font-weight:bold;}.AGEWndTitleText{cursor:pointer;text-align:left;}.AGEWndTitleButton{height:20px;overflow:hidden;padding-right:1px;text-align:right;width:20px;}.AGEWndCloseButton{display:block;cursor:pointer;}.AGEWndCloseButton:after{content:'';font-family:'Iconalpha';font-size:14px;font-weight:bold;}.AGEWndNoPadding{margin:0;padding:0;}.successWarning,.errorWarning,.errorWarning2,.successWarning2{padding-left:5px;background-repeat:no-repeat;letter-spacing:10px;font-size:13px;}.successWarning{background-image:url("../Images/tick.png");}.errorWarning,.errorWarning2{background-image:url("../Images/x5.gif");}.successWarning2{background-image:url("../Images/tick.png");}.infoIco:before,.usernameIsValid:before,.PwdSuccess:before,.tick:before,.usernameIsNotValid:before,.PwdError:before,.crossError:before,.icon-print:before{font-family:'Iconalpha';font-size:18px;display:inline-block;}.infoIco{text-decoration:none;position:relative;}.infoIco:after{position:relative;content:'';}.infoIco:hover:after{position:absolute;left:0;top:-1px;width:26px;height:26px;margin-left:-4px;margin-top:-4px;background:#b1aea3;-webkit-border-radius:100%;-khtml-border-radius:100%;-moz-border-radius:100%;-o-border-radius:100%;border-radius:100%;-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0);-moz-animation:ripple 1s ease-out;-o-animation:ripple 1s ease-out;-webkit-animation:ripple 1s ease-out;animation:ripple 1s ease-out;}.infoIco:before{cursor:pointer;content:'';color:#0b599c;position:relative;font-weight:100;vertical-align:middle;}.usernameIsValid:before,.PwdSuccess:before,.tick:before{content:'';color:#52a220;vertical-align:middle;}.usernameIsNotValid:before,.PwdError:before,.crossError:before{content:'';color:#fe0000;vertical-align:middle;}.usernameIsValid:before{position:relative;top:4px;}.usernameIsNotValid:before{position:relative;top:4px;}.icon-print{display:inline-block;margin-left:16px;vertical-align:middle;position:relative;}.icon-print:after{position:relative;content:'';}.icon-print:hover:after{position:absolute;left:0;top:6px;width:26px;height:26px;margin-left:-4px;margin-top:-4px;background:#b1aea3;-webkit-border-radius:100%;-khtml-border-radius:100%;-moz-border-radius:100%;-o-border-radius:100%;border-radius:100%;-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0);-moz-animation:ripple 1s ease-out;-o-animation:ripple 1s ease-out;-webkit-animation:ripple 1s ease-out;animation:ripple 1s ease-out;}.icon-print:before{content:'';color:#888;font-weight:100;}#fdate_trigger,#tdate_trigger{margin:4px 12px 0 4px;}#tdToDateCal,#tdFromDateCal{padding-right:12px;}.selectDate{padding-left:5px;}#ddlSelectDate{position:relative;}.width-100per{width:100%;}.width-80per{width:100%;}.width-99per{width:99%;}.width-50per{width:50%;}.blue{color:#2673ce;}.bl_favorite{font-weight:bold !important;color:#b50000;}