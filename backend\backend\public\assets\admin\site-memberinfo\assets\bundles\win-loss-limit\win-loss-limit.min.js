"use strict";var SingleSelectionDropdownOption=function(){function n(n,t,i,r,u){n===void 0&&(n="dropdown-single");t===void 0&&(t="UserSelectedId");i===void 0&&(i=null);r===void 0&&(r=!1);u===void 0&&(u=!1);this.dropdownId=n;this.inputId=t;this.onChange=i;this.enableClickableOptGroups=r;this.enableCollapsibleOptGroups=u}return n}(),SingleSelectionDropdownComponent=function(){function n(n){var t=this;this.options=new SingleSelectionDropdownOption;this.render=function(n){var i,r,u;n===void 0&&(n=new SingleSelectionDropdownOption);t.options=t.$.extend({},t.options,n);i=t.$("#"+t.options.dropdownId);t.$dropdown=i;r=t.$("#"+t.options.inputId);t.$hiddenInput=r;u=t;t.selectedValue=u.getSelectedItem();t.$dropdown.multiselect({multiple:!1,maxHeight:300,numberDisplayed:1,includeSelectAllOption:!1,onChange:t.onchange,enableClickableOptGroups:t.options.enableClickableOptGroups,enableCollapsibleOptGroups:t.options.enableCollapsibleOptGroups,optionClass:t.optionClass});t.options.enableClickableOptGroups&&t.options.enableCollapsibleOptGroups&&t.$dropdown.next().find(".multiselect-container li").removeClass("hidden")};this.getSelectedItem=function(){var n=[],i=t.$dropdown.find("option:selected"),r=t.$;return i.each(function(){n.push([r(this).val()])}),n.length?n[0].toString():""};this.updateSelectedValue=function(){t.$hiddenInput.val(t.selectedValue)};this.onchange=function(){t.selectedValue=t.getSelectedItem();t.options.onChange&&t.options.onChange()};this.optionClass=function(n){if(t.$(n).data("isnested")===1)return"multiselect-nested-item"};this.changeSelection=function(n){t.$dropdown.val(n);t.$hiddenInput.val(n);t.$dropdown.multiselect("select",[n]);t.$dropdown.multiselect("refresh")};this.$=n}return n}(),PagerOption,PagerComponent,WinLossLimit,winLossLimit;PagerOption=function(){function n(n,t,i,r,u,f){n===void 0&&(n=null);t===void 0&&(t="report-pager");i===void 0&&(i="report-form");r===void 0&&(r="PageIndex");u===void 0&&(u="PageSize");f===void 0&&(f=[50,100,200,500]);this.onSelectPage=n;this.pagerId=t;this.formId=i;this.pageIndexId=r;this.pageSizeId=u;this.pageList=f}return n}();PagerComponent=function(){function n(n){var t=this;this.pageList=[];this.render=function(n){n===void 0&&(n=t.options);var u=t,i=t.$.extend({},t.options,n),r=t.$("#"+i.pagerId);return t.$reportForm=t.$("#"+i.formId),t.totalRecords=r.data("totalrecords"),t.pageSize=r.data("pagesize"),t.pageIndex=r.data("pageindex"),t.$pageIndex=t.$('<input type="hidden" id="'+i.pageIndexId+'" name="'+i.pageIndexId+'" value="'+t.pageIndex+'" />'),t.$pageSize=t.$('<input type="hidden" id="'+i.pageSizeId+'" name="'+i.pageSizeId+'" value="'+t.pageSize+'" />'),t.pageList=i.pageList,t.$reportForm.append(t.$pageIndex).append(t.$pageSize),t.isShow()?(r.pagination({pageList:t.pageList,total:t.totalRecords,pageSize:t.pageSize,pageNumber:t.pageIndex,showRefresh:!1,onSelectPage:function(n,t){u.updateHiddenFields(n,t);i.onSelectPage&&typeof i.onSelectPage=="function"&&i.onSelectPage()},beforePageText:r.data("labelpage"),afterPageText:r.data("labelof")+" {pages}",displayMsg:r.data("labeldisplayitems")}),r.removeClass("hide")):r.addClass("hide"),r};this.onChange=function(){t.$reportForm.trigger("submit")};this.updateHiddenFields=function(n,i){t.$pageIndex.val(n);t.$pageSize.val(i)};this.isShow=function(){return t.pageList[0]<=t.totalRecords};this.options=new PagerOption(this.onChange);this.$=n}return n}();WinLossLimit=function(){function n(n,t,i,r,u){var f=this;this.init=function(){f.cacheDOMElements();f.bindEvents();var n=f;f.productComponent.render(new SingleSelectionDropdownOption("dropdown-product","UserSelectedProductId",f.changeProduct,!0,!0));f.$masterList.length&&f.masterListComponent.render(new SingleSelectionDropdownOption("dropdown-master","MasterId",f.changeMaster));f.$agentList.length&&f.agentListComponent.render(new SingleSelectionDropdownOption("dropdown-agent","AgentId",f.changeAgent));f.$tableReport.stickyTableHeaders();f.pager.render(new PagerOption(f.submitForm,"report-pager","win-loss-limit-form"))};this.cacheDOMElements=function(){f.$productList=f.$("#dropdown-product");f.$sportType=f.$("#SportType");f.$betType=f.$("#BetType");f.$productId=f.$("#UserSelectedProductId");f.$winLossLimitForm=f.$("#win-loss-limit-form");f.$masterList=f.$("#dropdown-master");f.$agentList=f.$("#dropdown-agent");f.$masterId=f.$("#MasterId");f.$agentId=f.$("#AgentId");f.$username=f.$("#txt-username");f.$playerId=f.$("#txt-player-id");f.$tableReport=f.$("#tbl-report")};this.bindEvents=function(){f.$username.keyup(f.doneInputTextbox);f.$playerId.keyup(f.doneInputTextbox)};this.changeProduct=function(){var n=f.$productList.find(":selected"),t=n.attr("data-sport-type"),i=n.attr("data-bet-type"),r=n.attr("data-product-id");f.$sportType.val(t);f.$betType.val(i);f.$productId.val(r);f.submitForm()};this.changeMaster=function(){f.$masterId.val(f.$masterList.val());f.$agentId.val(0);f.submitForm()};this.changeAgent=function(){f.$agentId.val(f.$agentList.val());f.submitForm()};this.submitForm=function(){f.$.blockUI();f.$winLossLimitForm.submit()};this.doneInputTextbox=function(n){var t=n.keyCode||n.which;t===13&&f.submitForm()};this.$=n;this.productComponent=t;this.masterListComponent=i;this.agentListComponent=r;this.pager=u}return n}();winLossLimit=new WinLossLimit($,new SingleSelectionDropdownComponent($),new SingleSelectionDropdownComponent($),new SingleSelectionDropdownComponent($),new PagerComponent($));$(function(){winLossLimit.init()});