<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1.039" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#bebebe"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="1" y2="0" xlink:href="#linear-gradient"/>
  </defs>
  <g id="bet_time_btn" transform="translate(-51 -532)">
    <rect id="active" width="20" height="20" rx="10" transform="translate(51 532)" fill="#3d6e28" opacity="0"/>
    <g id="矩形_33" data-name="矩形 33" transform="translate(51 532)" stroke="#b5b5b5" stroke-width="1" fill="url(#linear-gradient)">
      <rect width="20" height="20" rx="10" stroke="none"/>
      <rect x="0.5" y="0.5" width="19" height="19" rx="9.5" fill="none"/>
    </g>
    <rect id="矩形_49" data-name="矩形 49" width="12" height="12" rx="6" transform="translate(55 536)" fill="url(#linear-gradient-2)"/>
  </g>
</svg>
