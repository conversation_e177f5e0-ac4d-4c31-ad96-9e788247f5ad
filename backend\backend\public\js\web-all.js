function bindEvent(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent&&e.attachEvent("on"+t,n)}function SwitchSport(e,t){return"161"==t?(top.mainFrame.location="/bingo",void top.leftFrame.gotoCurrentMenu("161_head")):"2"==t?OpenGame("NBA"):"8"==t?OpenGame("Baseball"):"4"==t?OpenGame("Hocky"):"5"==t?OpenGame("Tennis"):"6"==t?OpenGame("Volleyball"):"43"==t?OpenGame("E-Sports"):"11"==t?OpenGame("Motorsports"):"7"==t?OpenGame("Snooker/Pool"):"10"==t?OpenGame("Golf"):"50"==t?OpenGame("Cricket"):"1"==t?OpenGame("Winner"):"0"==t?OpenGame("WinnerLast"):"1X2"==t?OpenGame("1X2"):"25"==t?OpenGame("PhiTieu"):"26"==t?OpenGame("BAODUC"):"CorrectScore"==t?OpenGame("CorrectScore"):"Oe"==t?OpenGame("Oe"):"TG"==t?OpenGame("TG"):"HTFT"==t?OpenGame("HTFT"):"HTFTOE"==t?OpenGame("HTFTOE"):"FGLG"==t?OpenGame("FGLG"):void alert("Trò chơi tạm khoá, vui lòng liên hệ admin")}function ShowOdds(e,t){"P"==e?(top.mainFrame.location="/underoddsGroup",top.leftFrame.gotoCurrentMenu("1_P")):"A"==e?(top.mainFrame.location="/underodds",top.leftFrame.gotoCurrentMenu("1_A")):SwitchSport(e,t)}function OpenGame(e){e=e||"numberGame",top.mainFrame.loadSport?top.mainFrame.loadSport(e):top.mainFrame.location="/copy?type="+e}function ShowWhatIsNews(){top.mainFrame.showSlides&&top.mainFrame.showSlides()}function SwitchMenuType(e){}function SwitchVersion(){top.mainFrame.showTransition&&top.mainFrame.showTransition()}function offNewVersionPopup(){top.mainFrame.promoNewVersionPopup&&top.mainFrame.promoNewVersionPopup(!0)}function openMenu(e){}function OpenGDLiveCasino(e){alert("Trò chơi tạm khoá, vui lòng liên hệ admin")}function OpenAllBet(e){alert("Trò chơi tạm khoá, vui lòng liên hệ admin")}function gotoSportsTodayPage(){top.mainFrame.location="/underodds",top.leftFrame.gotoCurrentMenu("1_A")}function gotoParlay(){top.mainFrame.location="/underoddsGroup",top.leftFrame.gotoCurrentMenu("1_P")}function showSlides(){"hidden"==$("#MiniOddsNews").css("visibility")?$("#MiniOddsNews").css("visibility","visible").animate({opacity:1},200):$("#MiniOddsNews").css("visibility","hidden").animate({opacity:0},200)}function gotoCurrentMenu(e){$("#1_body>div a").removeClass("current"),$("#"+e+" a").addClass("current")}function convertMalayToDecimal(e){return e<0&&(e=-1/e),Math.round(100*(e+1))/100}function convertSaba(e){return void 0!=e?HKOdds(e):e}function FloatAdd(e,t){var n,r,i;try{n=e.toString().split(".")[1].length}catch(o){n=0}try{r=t.toString().split(".")[1].length}catch(o){r=0}return i=Math.pow(10,Math.max(n,r)),(FloatMul(e,i)+FloatMul(t,i))/i}function FloatMul(e,t){var n=0,r=e.toString(),i=t.toString();try{n+=r.split(".")[1].length}catch(o){}try{n+=i.split(".")[1].length}catch(o){}return Number(r.replace(".",""))*Number(i.replace(".",""))/Math.pow(10,n)}function HKOdds(e){return e<0&&(e=FloatDiv(-1,e),e=FloatDiv(Floor(FloatMul(e,100),0),100)),e}function FloatDiv(arg1,arg2){var t1=0,t2=0,r1,r2;try{t1=arg1.toString().split(".")[1].length}catch(n){}try{t2=arg2.toString().split(".")[1].length}catch(n){}with(Math)return r1=Number(arg1.toString().replace(".","")),r2=Number(arg2.toString().replace(".","")),r1/r2*pow(10,t2-t1)}function Floor(e,t){if(null==e)return!1;var n="^([-+]?[0-9]*.[0-9]{"+t+"})[0-9]*$";return parseFloat(e.toString().replace(new RegExp(n),function(e,t){return t}))}if(function(e){"use strict";function t(e,t){return t=t||Error,function(){var n,r,i=2,o=arguments,a=o[0],s="["+(e?e+":":"")+a+"] ",u=o[1];for(s+=u.replace(/\{\d+\}/g,function(e){var t=+e.slice(1,-1),n=t+i;return n<o.length?$e(o[n]):e}),s+="\nhttp://errors.angularjs.org/1.5.8/"+(e?e+"/":"")+a,r=i,n="?";r<o.length;r++,n="&")s+=n+"p"+(r-i)+"="+encodeURIComponent($e(o[r]));return new t(s)}}function n(e){if(null==e||S(e))return!1;if(Xr(e)||w(e)||Vr&&e instanceof Vr)return!0;var t="length"in Object(e)&&e.length;return x(t)&&(t>=0&&(t-1 in e||e instanceof Array)||"function"==typeof e.item)}function r(e,t,i){var o,a;if(e)if(T(e))for(o in e)"prototype"==o||"length"==o||"name"==o||e.hasOwnProperty&&!e.hasOwnProperty(o)||t.call(i,e[o],o,e);else if(Xr(e)||n(e)){var s="object"!=typeof e;for(o=0,a=e.length;o<a;o++)(s||o in e)&&t.call(i,e[o],o,e)}else if(e.forEach&&e.forEach!==r)e.forEach(t,i,e);else if(b(e))for(o in e)t.call(i,e[o],o,e);else if("function"==typeof e.hasOwnProperty)for(o in e)e.hasOwnProperty(o)&&t.call(i,e[o],o,e);else for(o in e)Dr.call(e,o)&&t.call(i,e[o],o,e);return e}function i(e,t,n){for(var r=Object.keys(e).sort(),i=0;i<r.length;i++)t.call(n,e[r[i]],r[i]);return r}function o(e){return function(t,n){e(n,t)}}function a(){return++Yr}function s(e,t){t?e.$$hashKey=t:delete e.$$hashKey}function u(e,t,n){for(var r=e.$$hashKey,i=0,o=t.length;i<o;++i){var a=t[i];if(y(a)||T(a))for(var l=Object.keys(a),c=0,f=l.length;c<f;c++){var d=l[c],p=a[d];n&&y(p)?E(p)?e[d]=new Date(p.valueOf()):C(p)?e[d]=new RegExp(p):p.nodeName?e[d]=p.cloneNode(!0):j(p)?e[d]=p.clone():(y(e[d])||(e[d]=Xr(p)?[]:{}),u(e[d],[p],!0)):e[d]=p}}return s(e,r),e}function l(e){return u(e,Hr.call(arguments,1),!1)}function c(e){return u(e,Hr.call(arguments,1),!0)}function f(e){return parseInt(e,10)}function d(e,t){return l(Object.create(e),t)}function p(){}function h(e){return e}function m(e){return function(){return e}}function g(e){return T(e.toString)&&e.toString!==Ur}function v(e){return"undefined"==typeof e}function $(e){return"undefined"!=typeof e}function y(e){return null!==e&&"object"==typeof e}function b(e){return null!==e&&"object"==typeof e&&!Wr(e)}function w(e){return"string"==typeof e}function x(e){return"number"==typeof e}function E(e){return"[object Date]"===Ur.call(e)}function T(e){return"function"==typeof e}function C(e){return"[object RegExp]"===Ur.call(e)}function S(e){return e&&e.window===e}function k(e){return e&&e.$evalAsync&&e.$watch}function _(e){return"[object File]"===Ur.call(e)}function A(e){return"[object FormData]"===Ur.call(e)}function N(e){return"[object Blob]"===Ur.call(e)}function O(e){return"boolean"==typeof e}function D(e){return e&&T(e.then)}function I(e){return e&&x(e.length)&&Jr.test(Ur.call(e))}function M(e){return"[object ArrayBuffer]"===Ur.call(e)}function j(e){return!(!e||!(e.nodeName||e.prop&&e.attr&&e.find))}function P(e){var t,n={},r=e.split(",");for(t=0;t<r.length;t++)n[r[t]]=!0;return n}function L(e){return Ir(e.nodeName||e[0]&&e[0].nodeName)}function V(e,t){var n=e.indexOf(t);return n>=0&&e.splice(n,1),n}function R(e,t){function n(e,t){var n,r=t.$$hashKey;if(Xr(e))for(var o=0,a=e.length;o<a;o++)t.push(i(e[o]));else if(b(e))for(n in e)t[n]=i(e[n]);else if(e&&"function"==typeof e.hasOwnProperty)for(n in e)e.hasOwnProperty(n)&&(t[n]=i(e[n]));else for(n in e)Dr.call(e,n)&&(t[n]=i(e[n]));return s(t,r),t}function i(e){if(!y(e))return e;var t=a.indexOf(e);if(t!==-1)return u[t];if(S(e)||k(e))throw zr("cpws","Can't copy! Making copies of Window or Scope instances is not supported.");var r=!1,i=o(e);return void 0===i&&(i=Xr(e)?[]:Object.create(Wr(e)),r=!0),a.push(e),u.push(i),r?n(e,i):i}function o(e){switch(Ur.call(e)){case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Float32Array]":case"[object Float64Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return new e.constructor(i(e.buffer),e.byteOffset,e.length);case"[object ArrayBuffer]":if(!e.slice){var t=new ArrayBuffer(e.byteLength);return new Uint8Array(t).set(new Uint8Array(e)),t}return e.slice(0);case"[object Boolean]":case"[object Number]":case"[object String]":case"[object Date]":return new e.constructor(e.valueOf());case"[object RegExp]":var n=new RegExp(e.source,e.toString().match(/[^\/]*$/)[0]);return n.lastIndex=e.lastIndex,n;case"[object Blob]":return new e.constructor([e],{type:e.type})}if(T(e.cloneNode))return e.cloneNode(!0)}var a=[],u=[];if(t){if(I(t)||M(t))throw zr("cpta","Can't copy! TypedArray destination cannot be mutated.");if(e===t)throw zr("cpi","Can't copy! Source and destination are identical.");return Xr(t)?t.length=0:r(t,function(e,n){"$$hashKey"!==n&&delete t[n]}),a.push(e),u.push(t),n(e,t)}return i(e)}function F(e,t){if(e===t)return!0;if(null===e||null===t)return!1;if(e!==e&&t!==t)return!0;var n,r,i,o=typeof e,a=typeof t;if(o==a&&"object"==o){if(!Xr(e)){if(E(e))return!!E(t)&&F(e.getTime(),t.getTime());if(C(e))return!!C(t)&&e.toString()==t.toString();if(k(e)||k(t)||S(e)||S(t)||Xr(t)||E(t)||C(t))return!1;i=he();for(r in e)if("$"!==r.charAt(0)&&!T(e[r])){if(!F(e[r],t[r]))return!1;i[r]=!0}for(r in t)if(!(r in i)&&"$"!==r.charAt(0)&&$(t[r])&&!T(t[r]))return!1;return!0}if(!Xr(t))return!1;if((n=e.length)==t.length){for(r=0;r<n;r++)if(!F(e[r],t[r]))return!1;return!0}}return!1}function H(e,t,n){return e.concat(Hr.call(t,n))}function B(e,t){return Hr.call(e,t||0)}function q(e,t){var n=arguments.length>2?B(arguments,2):[];return!T(t)||t instanceof RegExp?t:n.length?function(){return arguments.length?t.apply(e,H(n,arguments,0)):t.apply(e,n)}:function(){return arguments.length?t.apply(e,arguments):t.call(e)}}function U(t,n){var r=n;return"string"==typeof t&&"$"===t.charAt(0)&&"$"===t.charAt(1)?r=void 0:S(n)?r="$WINDOW":n&&e.document===n?r="$DOCUMENT":k(n)&&(r="$SCOPE"),r}function W(e,t){if(!v(e))return x(t)||(t=t?2:null),JSON.stringify(e,U,t)}function z(e){return w(e)?JSON.parse(e):e}function G(e,t){e=e.replace(ti,"");var n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function Y(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function X(e,t,n){n=n?-1:1;var r=e.getTimezoneOffset(),i=G(t,r);return Y(e,n*(i-r))}function J(e){e=Vr(e).clone();try{e.empty()}catch(t){}var n=Vr("<div>").append(e).html();try{return e[0].nodeType===si?Ir(n):n.match(/^(<[^>]+>)/)[1].replace(/^<([\w\-]+)/,function(e,t){return"<"+Ir(t)})}catch(t){return Ir(n)}}function Q(e){try{return decodeURIComponent(e)}catch(t){}}function K(e){var t={};return r((e||"").split("&"),function(e){var n,r,i;e&&(r=e=e.replace(/\+/g,"%20"),n=e.indexOf("="),n!==-1&&(r=e.substring(0,n),i=e.substring(n+1)),r=Q(r),$(r)&&(i=!$(i)||Q(i),Dr.call(t,r)?Xr(t[r])?t[r].push(i):t[r]=[t[r],i]:t[r]=i))}),t}function Z(e){var t=[];return r(e,function(e,n){Xr(e)?r(e,function(e){t.push(te(n,!0)+(e===!0?"":"="+te(e,!0)))}):t.push(te(n,!0)+(e===!0?"":"="+te(e,!0)))}),t.length?t.join("&"):""}function ee(e){return te(e,!0).replace(/%26/gi,"&").replace(/%3D/gi,"=").replace(/%2B/gi,"+")}function te(e,t){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%3B/gi,";").replace(/%20/g,t?"%20":"+")}function ne(e,t){var n,r,i=ni.length;for(r=0;r<i;++r)if(n=ni[r]+t,w(n=e.getAttribute(n)))return n;return null}function re(e,t){var n,i,o={};r(ni,function(t){var r=t+"app";!n&&e.hasAttribute&&e.hasAttribute(r)&&(n=e,i=e.getAttribute(r))}),r(ni,function(t){var r,o=t+"app";!n&&(r=e.querySelector("["+o.replace(":","\\:")+"]"))&&(n=r,i=r.getAttribute(o))}),n&&(o.strictDi=null!==ne(n,"strict-di"),t(n,i?[i]:[],o))}function ie(t,n,i){y(i)||(i={});var o={strictDi:!1};i=l(o,i);var a=function(){if(t=Vr(t),t.injector()){var r=t[0]===e.document?"document":J(t);throw zr("btstrpd","App already bootstrapped with this element '{0}'",r.replace(/</,"&lt;").replace(/>/,"&gt;"))}n=n||[],n.unshift(["$provide",function(e){e.value("$rootElement",t)}]),i.debugInfoEnabled&&n.push(["$compileProvider",function(e){e.debugInfoEnabled(!0)}]),n.unshift("ng");var o=rt(n,i.strictDi);return o.invoke(["$rootScope","$rootElement","$compile","$injector",function(e,t,n,r){e.$apply(function(){t.data("$injector",r),n(t)(e)})}]),o},s=/^NG_ENABLE_DEBUG_INFO!/,u=/^NG_DEFER_BOOTSTRAP!/;return e&&s.test(e.name)&&(i.debugInfoEnabled=!0,e.name=e.name.replace(s,"")),e&&!u.test(e.name)?a():(e.name=e.name.replace(u,""),Gr.resumeBootstrap=function(e){return r(e,function(e){n.push(e)}),a()},void(T(Gr.resumeDeferredBootstrap)&&Gr.resumeDeferredBootstrap()))}function oe(){e.name="NG_ENABLE_DEBUG_INFO!"+e.name,e.location.reload()}function ae(e){var t=Gr.element(e).injector();if(!t)throw zr("test","no injector found for element argument to getTestability");return t.get("$$testability")}function se(e,t){return t=t||"_",e.replace(ri,function(e,n){return(n?t:"")+e.toLowerCase()})}function ue(){var t;if(!ii){var n=ei();Rr=v(n)?e.jQuery:n?e[n]:void 0,Rr&&Rr.fn.on?(Vr=Rr,l(Rr.fn,{scope:Si.scope,isolateScope:Si.isolateScope,controller:Si.controller,injector:Si.injector,inheritedData:Si.inheritedData}),t=Rr.cleanData,Rr.cleanData=function(e){for(var n,r,i=0;null!=(r=e[i]);i++)n=Rr._data(r,"events"),n&&n.$destroy&&Rr(r).triggerHandler("$destroy");t(e)}):Vr=Ae,Gr.element=Vr,ii=!0}}function le(e,t,n){if(!e)throw zr("areq","Argument '{0}' is {1}",t||"?",n||"required");return e}function ce(e,t,n){return n&&Xr(e)&&(e=e[e.length-1]),le(T(e),t,"not a function, got "+(e&&"object"==typeof e?e.constructor.name||"Object":typeof e)),e}function fe(e,t){if("hasOwnProperty"===e)throw zr("badname","hasOwnProperty is not a valid {0} name",t)}function de(e,t,n){if(!t)return e;for(var r,i=t.split("."),o=e,a=i.length,s=0;s<a;s++)r=i[s],e&&(e=(o=e)[r]);return!n&&T(e)?q(o,e):e}function pe(e){for(var t,n=e[0],r=e[e.length-1],i=1;n!==r&&(n=n.nextSibling);i++)(t||e[i]!==n)&&(t||(t=Vr(Hr.call(e,0,i))),t.push(n));return t||e}function he(){return Object.create(null)}function me(e){function n(e,t,n){return e[t]||(e[t]=n())}var r=t("$injector"),i=t("ng"),o=n(e,"angular",Object);return o.$$minErr=o.$$minErr||t,n(o,"module",function(){var e={};return function(t,o,a){var s=function(e,t){if("hasOwnProperty"===e)throw i("badname","hasOwnProperty is not a valid {0} name",t)};return s(t,"module"),o&&e.hasOwnProperty(t)&&(e[t]=null),n(e,t,function(){function e(e,t,n,r){return r||(r=i),function(){return r[n||"push"]([e,t,arguments]),c}}function n(e,n){return function(r,o){return o&&T(o)&&(o.$$moduleName=t),i.push([e,n,arguments]),c}}if(!o)throw r("nomod","Module '{0}' is not available! You either misspelled the module name or forgot to load it. If registering a module ensure that you specify the dependencies as the second argument.",t);var i=[],s=[],u=[],l=e("$injector","invoke","push",s),c={_invokeQueue:i,_configBlocks:s,_runBlocks:u,requires:o,name:t,provider:n("$provide","provider"),factory:n("$provide","factory"),service:n("$provide","service"),value:e("$provide","value"),constant:e("$provide","constant","unshift"),decorator:n("$provide","decorator"),animation:n("$animateProvider","register"),filter:n("$filterProvider","register"),controller:n("$controllerProvider","register"),directive:n("$compileProvider","directive"),component:n("$compileProvider","component"),config:l,run:function(e){return u.push(e),this}};return a&&l(a),c})}})}function ge(e,t){if(Xr(e)){t=t||[];for(var n=0,r=e.length;n<r;n++)t[n]=e[n]}else if(y(e)){t=t||{};for(var i in e)"$"===i.charAt(0)&&"$"===i.charAt(1)||(t[i]=e[i])}return t||e}function ve(e){var t=[];return JSON.stringify(e,function(e,n){if(n=U(e,n),y(n)){if(t.indexOf(n)>=0)return"...";t.push(n)}return n})}function $e(e){return"function"==typeof e?e.toString().replace(/ \{[\s\S]*$/,""):v(e)?"undefined":"string"!=typeof e?ve(e):e}function ye(n){l(n,{bootstrap:ie,copy:R,extend:l,merge:c,equals:F,element:Vr,forEach:r,injector:rt,noop:p,bind:q,toJson:W,fromJson:z,identity:h,isUndefined:v,isDefined:$,isString:w,isFunction:T,isObject:y,isNumber:x,isElement:j,isArray:Xr,version:fi,isDate:E,lowercase:Ir,uppercase:Mr,callbacks:{$$counter:0},getTestability:ae,$$minErr:t,$$csp:Zr,reloadWithDebugInfo:oe}),(Fr=me(e))("ng",["ngLocale"],["$provide",function(e){e.provider({$$sanitizeUri:Cn}),e.provider("$compile",ht).directive({a:jo,input:ea,textarea:ea,form:Fo,script:Xa,select:Ka,style:es,option:Za,ngBind:ra,ngBindHtml:oa,ngBindTemplate:ia,ngClass:sa,ngClassEven:la,ngClassOdd:ua,ngCloak:ca,ngController:fa,ngForm:Ho,ngHide:Ba,ngIf:ha,ngInclude:ma,ngInit:va,ngNonBindable:Ia,ngPluralize:La,ngRepeat:Va,ngShow:Ha,ngStyle:qa,ngSwitch:Ua,ngSwitchWhen:Wa,ngSwitchDefault:za,ngOptions:Pa,ngTransclude:Ya,ngModel:Na,ngList:$a,ngChange:aa,pattern:ns,ngPattern:ns,required:ts,ngRequired:ts,minlength:is,ngMinlength:is,maxlength:rs,ngMaxlength:rs,ngValue:na,ngModelOptions:Da}).directive({ngInclude:ga}).directive(Po).directive(da),e.provider({$anchorScroll:it,$animate:Bi,$animateCss:Wi,$$animateJs:Fi,$$animateQueue:Hi,$$AnimateRunner:Ui,$$animateAsyncRun:qi,$browser:ct,$cacheFactory:ft,$controller:bt,$document:wt,$exceptionHandler:xt,$filter:Rn,$$forceReflow:Qi,$interpolate:Pt,$interval:Lt,$http:Dt,$httpParamSerializer:Tt,$httpParamSerializerJQLike:Ct,$httpBackend:Mt,$xhrFactory:It,$jsonpCallbacks:ao,$location:Kt,$log:Zt,$parse:yn,$rootScope:Tn,$q:bn,$$q:wn,$sce:An,$sceDelegate:_n,$sniffer:Nn,$templateCache:dt,$templateRequest:On,$$testability:Dn,$timeout:In,$window:Pn,$$rAF:En,$$jqLite:Je,$$HashMap:Ni,$$cookieReader:Vn})}])}function be(){return++pi}function we(e){return e.replace(gi,function(e,t,n,r){return r?n.toUpperCase():n}).replace(vi,"Moz$1")}function xe(e){return!wi.test(e)}function Ee(e){var t=e.nodeType;return t===oi||!t||t===li}function Te(e){for(var t in di[e.ng339])return!0;return!1}function Ce(e){for(var t=0,n=e.length;t<n;t++)Ie(e[t])}function Se(e,t){var n,i,o,a,s=t.createDocumentFragment(),u=[];if(xe(e))u.push(t.createTextNode(e));else{for(n=s.appendChild(t.createElement("div")),i=(xi.exec(e)||["",""])[1].toLowerCase(),o=Ti[i]||Ti._default,n.innerHTML=o[1]+e.replace(Ei,"<$1></$2>")+o[2],a=o[0];a--;)n=n.lastChild;u=H(u,n.childNodes),n=s.firstChild,n.textContent=""}return s.textContent="",s.innerHTML="",r(u,function(e){s.appendChild(e)}),s}function ke(t,n){n=n||e.document;var r;return(r=bi.exec(t))?[n.createElement(r[1])]:(r=Se(t,n))?r.childNodes:[]}function _e(e,t){var n=e.parentNode;n&&n.replaceChild(t,e),t.appendChild(e)}function Ae(e){if(e instanceof Ae)return e;var t;if(w(e)&&(e=Qr(e),t=!0),!(this instanceof Ae)){if(t&&"<"!=e.charAt(0))throw yi("nosel","Looking up elements via selectors is not supported by jqLite! See: http://docs.angularjs.org/api/angular.element");return new Ae(e)}t?Re(this,ke(e)):Re(this,e)}function Ne(e){return e.cloneNode(!0)}function Oe(e,t){if(t||Ie(e),e.querySelectorAll)for(var n=e.querySelectorAll("*"),r=0,i=n.length;r<i;r++)Ie(n[r])}function De(e,t,n,i){if($(i))throw yi("offargs","jqLite#off() does not support the `selector` argument");var o=Me(e),a=o&&o.events,s=o&&o.handle;if(s)if(t){var u=function(t){var r=a[t];$(n)&&V(r||[],n),$(n)&&r&&r.length>0||(mi(e,t,s),delete a[t])};r(t.split(" "),function(e){u(e),$i[e]&&u($i[e])})}else for(t in a)"$destroy"!==t&&mi(e,t,s),delete a[t]}function Ie(e,t){var n=e.ng339,r=n&&di[n];if(r){if(t)return void delete r.data[t];r.handle&&(r.events.$destroy&&r.handle({},"$destroy"),De(e)),delete di[n],e.ng339=void 0}}function Me(e,t){var n=e.ng339,r=n&&di[n];return t&&!r&&(e.ng339=n=be(),r=di[n]={events:{},data:{},handle:void 0}),r}function je(e,t,n){if(Ee(e)){var r=$(n),i=!r&&t&&!y(t),o=!t,a=Me(e,!i),s=a&&a.data;if(r)s[t]=n;else{if(o)return s;if(i)return s&&s[t];l(s,t)}}}function Pe(e,t){return!!e.getAttribute&&(" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ").indexOf(" "+t+" ")>-1}function Le(e,t){t&&e.setAttribute&&r(t.split(" "),function(t){e.setAttribute("class",Qr((" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ").replace(" "+Qr(t)+" "," ")))})}function Ve(e,t){if(t&&e.setAttribute){var n=(" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ");r(t.split(" "),function(e){e=Qr(e),n.indexOf(" "+e+" ")===-1&&(n+=e+" ")}),e.setAttribute("class",Qr(n))}}function Re(e,t){if(t)if(t.nodeType)e[e.length++]=t;else{var n=t.length;if("number"==typeof n&&t.window!==t){if(n)for(var r=0;r<n;r++)e[e.length++]=t[r]}else e[e.length++]=t}}function Fe(e,t){return He(e,"$"+(t||"ngController")+"Controller")}function He(e,t,n){e.nodeType==li&&(e=e.documentElement);for(var r=Xr(t)?t:[t];e;){for(var i=0,o=r.length;i<o;i++)if($(n=Vr.data(e,r[i])))return n;e=e.parentNode||e.nodeType===ci&&e.host}}function Be(e){for(Oe(e,!0);e.firstChild;)e.removeChild(e.firstChild)}function qe(e,t){t||Oe(e);var n=e.parentNode;n&&n.removeChild(e)}function Ue(t,n){n=n||e,"complete"===n.document.readyState?n.setTimeout(t):Vr(n).on("load",t)}function We(e,t){var n=ki[t.toLowerCase()];return n&&_i[L(e)]&&n}function ze(e){return Ai[e]}function Ge(e,t){var n=function(n,r){n.isDefaultPrevented=function(){return n.defaultPrevented};var i=t[r||n.type],o=i?i.length:0;if(o){if(v(n.immediatePropagationStopped)){var a=n.stopImmediatePropagation;n.stopImmediatePropagation=function(){n.immediatePropagationStopped=!0,n.stopPropagation&&n.stopPropagation(),a&&a.call(n)}}n.isImmediatePropagationStopped=function(){return n.immediatePropagationStopped===!0};var s=i.specialHandlerWrapper||Ye;o>1&&(i=ge(i));for(var u=0;u<o;u++)n.isImmediatePropagationStopped()||s(e,n,i[u])}};return n.elem=e,n}function Ye(e,t,n){n.call(e,t)}function Xe(e,t,n){var r=t.relatedTarget;r&&(r===e||Ci.call(e,r))||n.call(e,t)}function Je(){this.$get=function(){return l(Ae,{hasClass:function(e,t){return e.attr&&(e=e[0]),Pe(e,t)},addClass:function(e,t){return e.attr&&(e=e[0]),Ve(e,t)},removeClass:function(e,t){return e.attr&&(e=e[0]),Le(e,t)}})}}function Qe(e,t){var n=e&&e.$$hashKey;if(n)return"function"==typeof n&&(n=e.$$hashKey()),n;var r=typeof e;return n="function"==r||"object"==r&&null!==e?e.$$hashKey=r+":"+(t||a)():r+":"+e}function Ke(e,t){if(t){var n=0;this.nextUid=function(){return++n}}r(e,this.put,this)}function Ze(e){return Function.prototype.toString.call(e)+" "}function et(e){var t=Ze(e).replace(ji,""),n=t.match(Oi)||t.match(Di);return n}function tt(e){var t=et(e);return t?"function("+(t[1]||"").replace(/[\s\r\n]+/," ")+")":"fn"}function nt(e,t,n){var i,o,a;if("function"==typeof e){if(!(i=e.$inject)){if(i=[],e.length){if(t)throw w(n)&&n||(n=e.name||tt(e)),Pi("strictdi","{0} is not using explicit annotation and cannot be invoked in strict mode",n);o=et(e),r(o[1].split(Ii),function(e){e.replace(Mi,function(e,t,n){i.push(n)})})}e.$inject=i}}else Xr(e)?(a=e.length-1,ce(e[a],"fn"),i=e.slice(0,a)):ce(e,"fn",!0);return i}function rt(e,t){function n(e){return function(t,n){return y(t)?void r(t,o(e)):e(t,n)}}function i(e,t){if(fe(e,"service"),(T(t)||Xr(t))&&(t=E.instantiate(t)),!t.$get)throw Pi("pget","Provider '{0}' must define $get factory method.",e);return x[e+g]=t}function a(e,t){return function(){var n=k.invoke(t,this);if(v(n))throw Pi("undef","Provider '{0}' must return a value from $get factory method.",e);return n}}function s(e,t,n){return i(e,{$get:n!==!1?a(e,t):t})}function u(e,t){return s(e,["$injector",function(e){return e.instantiate(t)}])}function l(e,t){return s(e,m(t),!1)}function c(e,t){fe(e,"constant"),x[e]=t,C[e]=t}function f(e,t){var n=E.get(e+g),r=n.$get;n.$get=function(){var e=k.invoke(r,n);return k.invoke(t,null,{$delegate:e})}}function d(e){le(v(e)||Xr(e),"modulesToLoad","not an array");var t,n=[];return r(e,function(e){function r(e){var t,n;for(t=0,n=e.length;t<n;t++){var r=e[t],i=E.get(r[0]);i[r[1]].apply(i,r[2])}}if(!b.get(e)){b.put(e,!0);try{w(e)?(t=Fr(e),n=n.concat(d(t.requires)).concat(t._runBlocks),r(t._invokeQueue),r(t._configBlocks)):T(e)?n.push(E.invoke(e)):Xr(e)?n.push(E.invoke(e)):ce(e,"module")}catch(i){throw Xr(e)&&(e=e[e.length-1]),i.message&&i.stack&&i.stack.indexOf(i.message)==-1&&(i=i.message+"\n"+i.stack),Pi("modulerr","Failed to instantiate module {0} due to:\n{1}",e,i.stack||i.message||i)}}}),n}function p(e,n){function r(t,r){if(e.hasOwnProperty(t)){if(e[t]===h)throw Pi("cdep","Circular dependency found: {0}",t+" <- "+$.join(" <- "));return e[t]}try{return $.unshift(t),e[t]=h,e[t]=n(t,r)}catch(i){throw e[t]===h&&delete e[t],i}finally{$.shift()}}function i(e,n,i){for(var o=[],a=rt.$$annotate(e,t,i),s=0,u=a.length;s<u;s++){var l=a[s];if("string"!=typeof l)throw Pi("itkn","Incorrect injection token! Expected service name as string, got {0}",l);o.push(n&&n.hasOwnProperty(l)?n[l]:r(l,i))}return o}function o(e){return!(Lr<=11)&&("function"==typeof e&&/^(?:class\b|constructor\()/.test(Ze(e)))}function a(e,t,n,r){"string"==typeof n&&(r=n,n=null);var a=i(e,n,r);return Xr(e)&&(e=e[e.length-1]),o(e)?(a.unshift(null),new(Function.prototype.bind.apply(e,a))):e.apply(t,a)}function s(e,t,n){var r=Xr(e)?e[e.length-1]:e,o=i(e,t,n);return o.unshift(null),new(Function.prototype.bind.apply(r,o))}return{invoke:a,instantiate:s,get:r,annotate:rt.$$annotate,has:function(t){return x.hasOwnProperty(t+g)||e.hasOwnProperty(t)}}}t=t===!0;var h={},g="Provider",$=[],b=new Ke([],(!0)),x={$provide:{provider:n(i),factory:n(s),service:n(u),value:n(l),constant:n(c),decorator:f}},E=x.$injector=p(x,function(e,t){throw Gr.isString(t)&&$.push(t),Pi("unpr","Unknown provider: {0}",$.join(" <- "))}),C={},S=p(C,function(e,t){var n=E.get(e+g,t);return k.invoke(n.$get,n,void 0,e)}),k=S;x["$injector"+g]={$get:m(S)};var _=d(e);return k=S.get("$injector"),k.strictDi=t,r(_,function(e){e&&k.invoke(e)}),k}function it(){var e=!0;this.disableAutoScrolling=function(){e=!1},this.$get=["$window","$location","$rootScope",function(t,n,r){function i(e){var t=null;return Array.prototype.some.call(e,function(e){if("a"===L(e))return t=e,!0}),t}function o(){var e=s.yOffset;if(T(e))e=e();else if(j(e)){var n=e[0],r=t.getComputedStyle(n);e="fixed"!==r.position?0:n.getBoundingClientRect().bottom}else x(e)||(e=0);return e}function a(e){if(e){e.scrollIntoView();var n=o();if(n){var r=e.getBoundingClientRect().top;t.scrollBy(0,r-n)}}else t.scrollTo(0,0)}function s(e){e=w(e)?e:n.hash();var t;e?(t=u.getElementById(e))?a(t):(t=i(u.getElementsByName(e)))?a(t):"top"===e&&a(null):a(null)}var u=t.document;return e&&r.$watch(function(){return n.hash()},function(e,t){e===t&&""===e||Ue(function(){r.$evalAsync(s)})}),s}]}function ot(e,t){return e||t?e?t?(Xr(e)&&(e=e.join(" ")),Xr(t)&&(t=t.join(" ")),e+" "+t):e:t:""}function at(e){for(var t=0;t<e.length;t++){var n=e[t];if(n.nodeType===Vi)return n}}function st(e){w(e)&&(e=e.split(" "));var t=he();return r(e,function(e){e.length&&(t[e]=!0)}),t}function ut(e){return y(e)?e:{}}function lt(e,t,n,i){function o(e){try{e.apply(null,B(arguments,1))}finally{if($--,0===$)for(;y.length;)try{y.pop()()}catch(t){n.error(t)}}}function a(e){var t=e.indexOf("#");return t===-1?"":e.substr(t)}function s(){T=null,u(),l()}function u(){b=C(),b=v(b)?null:b,F(b,_)&&(b=_),_=b}function l(){x===c.url()&&w===b||(x=c.url(),w=b,r(S,function(e){e(c.url(),b)}))}var c=this,f=e.location,d=e.history,h=e.setTimeout,m=e.clearTimeout,g={};c.isMock=!1;var $=0,y=[];c.$$completeOutstandingRequest=o,c.$$incOutstandingRequestCount=function(){$++},c.notifyWhenNoOutstandingRequests=function(e){0===$?e():y.push(e)};var b,w,x=f.href,E=t.find("base"),T=null,C=i.history?function(){try{return d.state}catch(e){}}:p;u(),w=b,c.url=function(t,n,r){if(v(r)&&(r=null),f!==e.location&&(f=e.location),d!==e.history&&(d=e.history),t){var o=w===r;if(x===t&&(!i.history||o))return c;var s=x&&qt(x)===qt(t);return x=t,w=r,!i.history||s&&o?(s||(T=t),n?f.replace(t):s?f.hash=a(t):f.href=t,f.href!==t&&(T=t)):(d[n?"replaceState":"pushState"](r,"",t),u(),w=b),T&&(T=t),c}return T||f.href.replace(/%27/g,"'")},c.state=function(){return b};var S=[],k=!1,_=null;c.onUrlChange=function(t){return k||(i.history&&Vr(e).on("popstate",s),Vr(e).on("hashchange",s),k=!0),S.push(t),t},c.$$applicationDestroyed=function(){Vr(e).off("hashchange popstate",s)},c.$$checkUrlChange=l,c.baseHref=function(){var e=E.attr("href");return e?e.replace(/^(https?\:)?\/\/[^\/]*/,""):""},c.defer=function(e,t){var n;return $++,n=h(function(){delete g[n],o(e)},t||0),g[n]=!0,n},c.defer.cancel=function(e){return!!g[e]&&(delete g[e],m(e),o(p),!0)}}function ct(){this.$get=["$window","$log","$sniffer","$document",function(e,t,n,r){return new lt(e,r,t,n)}]}function ft(){this.$get=function(){function e(e,r){function i(e){e!=d&&(p?p==e&&(p=e.n):p=e,o(e.n,e.p),o(e,d),d=e,d.n=null)}function o(e,t){e!=t&&(e&&(e.p=t),t&&(t.n=e))}if(e in n)throw t("$cacheFactory")("iid","CacheId '{0}' is already taken!",e);var a=0,s=l({},r,{id:e}),u=he(),c=r&&r.capacity||Number.MAX_VALUE,f=he(),d=null,p=null;return n[e]={put:function(e,t){if(!v(t)){if(c<Number.MAX_VALUE){var n=f[e]||(f[e]={key:e});i(n)}return e in u||a++,u[e]=t,a>c&&this.remove(p.key),t}},get:function(e){if(c<Number.MAX_VALUE){var t=f[e];if(!t)return;i(t)}return u[e]},remove:function(e){if(c<Number.MAX_VALUE){var t=f[e];if(!t)return;t==d&&(d=t.p),t==p&&(p=t.n),o(t.n,t.p),delete f[e]}e in u&&(delete u[e],a--)},removeAll:function(){u=he(),a=0,f=he(),d=p=null},destroy:function(){u=null,s=null,f=null,delete n[e]},info:function(){return l({},s,{size:a})}}}var n={};return e.info=function(){var e={};return r(n,function(t,n){e[n]=t.info()}),e},e.get=function(e){return n[e]},e}}function dt(){this.$get=["$cacheFactory",function(e){return e("templates")}]}function pt(){}function ht(t,n){function i(e,t,n){var i=/^\s*([@&<]|=(\*?))(\??)\s*(\w*)\s*$/,o=he();return r(e,function(e,r){if(e in S)return void(o[r]=S[e]);var a=e.match(i);if(!a)throw zi("iscp","Invalid {3} for directive '{0}'. Definition: {... {1}: '{2}' ...}",t,r,e,n?"controller bindings definition":"isolate scope definition");o[r]={mode:a[1][0],collection:"*"===a[2],optional:"?"===a[3],attrName:a[4]||r},a[4]&&(S[e]=o[r])}),o}function a(e,t){var n={isolateScope:null,bindToController:null};if(y(e.scope)&&(e.bindToController===!0?(n.bindToController=i(e.scope,t,!0),n.isolateScope={}):n.isolateScope=i(e.scope,t,!1)),y(e.bindToController)&&(n.bindToController=i(e.bindToController,t,!0)),y(n.bindToController)){var r=e.controller,o=e.controllerAs;if(!r)throw zi("noctrl","Cannot bind to controller without directive '{0}'s controller.",t);if(!yt(r,o))throw zi("noident","Cannot bind to controller without identifier for directive '{0}'.",t)}return n}function s(e){var t=e.charAt(0);if(!t||t!==Ir(t))throw zi("baddir","Directive/Component name '{0}' is invalid. The first character must be a lowercase letter",e);if(e!==e.trim())throw zi("baddir","Directive/Component name '{0}' is invalid. The name should not contain leading or trailing whitespaces",e)}function u(e){var t=e.require||e.controller&&e.name;return!Xr(t)&&y(t)&&r(t,function(e,n){var r=e.match(E),i=e.substring(r[0].length);i||(t[n]=r[0]+n)}),t}var c={},f="Directive",g=/^\s*directive\:\s*([\w\-]+)\s+(.*)$/,b=/(([\w\-]+)(?:\:([^;]+))?;?)/,x=P("ngSrc,ngSrcset,src,srcset"),E=/^(?:(\^\^?)?(\?)?(\^\^?)?)?/,C=/^(on[a-z]+|formaction)$/,S=he();this.directive=function N(e,n){return fe(e,"directive"),w(e)?(s(e),le(n,"directiveFactory"),c.hasOwnProperty(e)||(c[e]=[],t.factory(e+f,["$injector","$exceptionHandler",function(t,n){var i=[];return r(c[e],function(r,o){try{var a=t.invoke(r);T(a)?a={compile:m(a)}:!a.compile&&a.link&&(a.compile=m(a.link)),a.priority=a.priority||0,a.index=o,a.name=a.name||e,a.require=u(a),a.restrict=a.restrict||"EA",a.$$moduleName=r.$$moduleName,i.push(a)}catch(s){n(s)}}),i}])),c[e].push(n)):r(e,o(N)),this},this.component=function(e,t){function n(e){function n(t){return T(t)||Xr(t)?function(n,r){return e.invoke(t,this,{$element:n,$attrs:r})}:t}var o=t.template||t.templateUrl?t.template:"",a={controller:i,controllerAs:yt(t.controller)||t.controllerAs||"$ctrl",template:n(o),templateUrl:n(t.templateUrl),transclude:t.transclude,scope:{},bindToController:t.bindings||{},restrict:"E",require:t.require};return r(t,function(e,t){"$"===t.charAt(0)&&(a[t]=e)}),a}var i=t.controller||function(){};return r(t,function(e,t){"$"===t.charAt(0)&&(n[t]=e,T(i)&&(i[t]=e))}),n.$inject=["$injector"],this.directive(e,n)},this.aHrefSanitizationWhitelist=function(e){return $(e)?(n.aHrefSanitizationWhitelist(e),this):n.aHrefSanitizationWhitelist()},this.imgSrcSanitizationWhitelist=function(e){return $(e)?(n.imgSrcSanitizationWhitelist(e),this):n.imgSrcSanitizationWhitelist()};var _=!0;this.debugInfoEnabled=function(e){return $(e)?(_=e,this):_};var A=10;this.onChangesTtl=function(e){return arguments.length?(A=e,this):A},this.$get=["$injector","$interpolate","$exceptionHandler","$templateRequest","$parse","$controller","$rootScope","$sce","$animate","$$sanitizeUri",function(t,n,i,o,s,u,m,S,N,D){function I(){try{if(!--Ee)throw ye=void 0,
zi("infchng","{0} $onChanges() iterations reached. Aborting!\n",A);m.$apply(function(){for(var e=[],t=0,n=ye.length;t<n;++t)try{ye[t]()}catch(r){e.push(r)}if(ye=void 0,e.length)throw e})}finally{Ee++}}function M(e,t){if(t){var n,r,i,o=Object.keys(t);for(n=0,r=o.length;n<r;n++)i=o[n],this[i]=t[i]}else this.$attr={};this.$$element=e}function j(e,t,n){we.innerHTML="<span "+t+">";var r=we.firstChild.attributes,i=r[0];r.removeNamedItem(i.name),i.value=n,e.attributes.setNamedItem(i)}function P(e,t){try{e.addClass(t)}catch(n){}}function R(t,n,r,i,o){t instanceof Vr||(t=Vr(t));for(var a=/\S+/,s=0,u=t.length;s<u;s++){var l=t[s];l.nodeType===si&&l.nodeValue.match(a)&&_e(l,t[s]=e.document.createElement("span"))}var c=U(t,n,t,r,i,o);R.$$addScopeClass(t);var f=null;return function(e,n,r){le(e,"scope"),o&&o.needsNewScope&&(e=e.$parent.$new()),r=r||{};var i=r.parentBoundTranscludeFn,a=r.transcludeControllers,s=r.futureParentElement;i&&i.$$boundTransclude&&(i=i.$$boundTransclude),f||(f=H(s));var u;if(u="html"!==f?Vr(fe(f,Vr("<div>").append(t).html())):n?Si.clone.call(t):t,a)for(var l in a)u.data("$"+l+"Controller",a[l].instance);return R.$$addScopeInfo(u,e),n&&n(u,e),c&&c(e,u,u,i),u}}function H(e){var t=e&&e[0];return t&&"foreignobject"!==L(t)&&Ur.call(t).match(/SVG/)?"svg":"html"}function U(e,t,n,r,i,o){function a(e,n,r,i){var o,a,s,u,l,c,f,d,m;if(p){var g=n.length;for(m=new Array(g),l=0;l<h.length;l+=3)f=h[l],m[f]=n[f]}else m=n;for(l=0,c=h.length;l<c;)s=m[h[l++]],o=h[l++],a=h[l++],o?(o.scope?(u=e.$new(),R.$$addScopeInfo(Vr(s),u)):u=e,d=o.transcludeOnThisElement?W(e,o.transclude,i):!o.templateOnThisElement&&i?i:!i&&t?W(e,t):null,o(a,u,s,r,d)):a&&a(e,s.childNodes,void 0,i)}for(var s,u,l,c,f,d,p,h=[],m=0;m<e.length;m++)s=new M,u=z(e[m],[],s,0===m?r:void 0,i),l=u.length?K(u,e[m],s,t,n,null,[],[],o):null,l&&l.scope&&R.$$addScopeClass(s.$$element),f=l&&l.terminal||!(c=e[m].childNodes)||!c.length?null:U(c,l?(l.transcludeOnThisElement||!l.templateOnThisElement)&&l.transclude:t),(l||f)&&(h.push(m,l,f),d=!0,p=p||l),o=null;return d?a:null}function W(e,t,n){function r(r,i,o,a,s){return r||(r=e.$new(!1,s),r.$$transcluded=!0),t(r,i,{parentBoundTranscludeFn:n,transcludeControllers:o,futureParentElement:a})}var i=r.$$slots=he();for(var o in t.$$slots)t.$$slots[o]?i[o]=W(e,t.$$slots[o],n):i[o]=null;return r}function z(e,t,n,r,i){var o,a,s=e.nodeType,u=n.$attr;switch(s){case oi:ne(t,gt(L(e)),"E",r,i);for(var l,c,f,d,p,h,m=e.attributes,g=0,v=m&&m.length;g<v;g++){var $=!1,x=!1;l=m[g],c=l.name,p=Qr(l.value),d=gt(c),(h=ke.test(d))&&(c=c.replace(Yi,"").substr(8).replace(/_(.)/g,function(e,t){return t.toUpperCase()}));var E=d.match(Ae);E&&re(E[1])&&($=c,x=c.substr(0,c.length-5)+"end",c=c.substr(0,c.length-6)),f=gt(c.toLowerCase()),u[f]=c,!h&&n.hasOwnProperty(f)||(n[f]=p,We(e,f)&&(n[f]=!0)),pe(e,t,p,f,h),ne(t,f,"A",r,i,$,x)}if(a=e.className,y(a)&&(a=a.animVal),w(a)&&""!==a)for(;o=b.exec(a);)f=gt(o[2]),ne(t,f,"C",r,i)&&(n[f]=Qr(o[3])),a=a.substr(o.index+o[0].length);break;case si:if(11===Lr)for(;e.parentNode&&e.nextSibling&&e.nextSibling.nodeType===si;)e.nodeValue=e.nodeValue+e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);ce(t,e.nodeValue);break;case ui:G(e,t,n,r,i)}return t.sort(ae),t}function G(e,t,n,r,i){try{var o=g.exec(e.nodeValue);if(o){var a=gt(o[1]);ne(t,a,"M",r,i)&&(n[a]=Qr(o[2]))}}catch(s){}}function Y(e,t,n){var r=[],i=0;if(t&&e.hasAttribute&&e.hasAttribute(t)){do{if(!e)throw zi("uterdir","Unterminated attribute, found '{0}' but no matching '{1}' found.",t,n);e.nodeType==oi&&(e.hasAttribute(t)&&i++,e.hasAttribute(n)&&i--),r.push(e),e=e.nextSibling}while(i>0)}else r.push(e);return Vr(r)}function X(e,t,n){return function(r,i,o,a,s){return i=Y(i[0],t,n),e(r,i,o,a,s)}}function Q(e,t,n,r,i,o){var a;return e?R(t,n,r,i,o):function(){return a||(a=R(t,n,r,i,o),t=n=o=null),a.apply(this,arguments)}}function K(e,t,n,o,a,s,u,c,f){function d(e,t,n,r){e&&(n&&(e=X(e,n,r)),e.require=h.require,e.directiveName=m,(C===h||h.$$isolateScope)&&(e=ge(e,{isolateScope:!0})),u.push(e)),t&&(n&&(t=X(t,n,r)),t.require=h.require,t.directiveName=m,(C===h||h.$$isolateScope)&&(t=ge(t,{isolateScope:!0})),c.push(t))}function p(e,o,a,s,f){function d(e,t,n,r){var i;if(k(e)||(r=n,n=t,t=e,e=void 0),O&&(i=b),n||(n=O?_.parent():_),!r)return f(e,t,i,n,V);var o=f.$$slots[r];if(o)return o(e,t,i,n,V);if(v(o))throw zi("noslot",'No parent directive that requires a transclusion with slot name "{0}". Element: {1}',r,J(_))}var p,h,m,g,$,b,w,_,A,N;t===a?(A=n,_=n.$$element):(_=Vr(a),A=new M(_,n)),$=o,C?g=o.$new(!0):x&&($=o.$parent),f&&(w=d,w.$$boundTransclude=f,w.isSlotFilled=function(e){return!!f.$$slots[e]}),E&&(b=ee(_,A,w,E,g,o,C)),C&&(R.$$addScopeInfo(_,g,!0,!(S&&(S===C||S===C.$$originalDirective))),R.$$addScopeClass(_,!0),g.$$isolateBindings=C.$$isolateBindings,N=$e(o,A,g,g.$$isolateBindings,C),N.removeWatches&&g.$on("$destroy",N.removeWatches));for(var D in b){var I=E[D],j=b[D],P=I.$$bindings.bindToController;j.identifier&&P?j.bindingInfo=$e($,A,j.instance,P,I):j.bindingInfo={};var L=j();L!==j.instance&&(j.instance=L,_.data("$"+I.name+"Controller",L),j.bindingInfo.removeWatches&&j.bindingInfo.removeWatches(),j.bindingInfo=$e($,A,j.instance,P,I))}for(r(E,function(e,t){var n=e.require;e.bindToController&&!Xr(n)&&y(n)&&l(b[t].instance,Z(t,n,_,b))}),r(b,function(e){var t=e.instance;if(T(t.$onChanges))try{t.$onChanges(e.bindingInfo.initialChanges)}catch(n){i(n)}if(T(t.$onInit))try{t.$onInit()}catch(n){i(n)}T(t.$doCheck)&&($.$watch(function(){t.$doCheck()}),t.$doCheck()),T(t.$onDestroy)&&$.$on("$destroy",function(){t.$onDestroy()})}),p=0,h=u.length;p<h;p++)m=u[p],ve(m,m.isolateScope?g:o,_,A,m.require&&Z(m.directiveName,m.require,_,b),w);var V=o;for(C&&(C.template||null===C.templateUrl)&&(V=g),e&&e(V,a.childNodes,void 0,f),p=c.length-1;p>=0;p--)m=c[p],ve(m,m.isolateScope?g:o,_,A,m.require&&Z(m.directiveName,m.require,_,b),w);r(b,function(e){var t=e.instance;T(t.$postLink)&&t.$postLink()})}f=f||{};for(var h,m,g,$,b,w=-Number.MAX_VALUE,x=f.newScopeDirective,E=f.controllerDirectives,C=f.newIsolateScopeDirective,S=f.templateDirective,_=f.nonTlbTranscludeDirective,A=!1,N=!1,O=f.hasElementTranscludeDirective,D=n.$$element=Vr(t),I=s,j=o,P=!1,V=!1,F=0,H=e.length;F<H;F++){h=e[F];var U=h.$$start,W=h.$$end;if(U&&(D=Y(t,U,W)),g=void 0,w>h.priority)break;if((b=h.scope)&&(h.templateUrl||(y(b)?(ue("new/isolated scope",C||x,h,D),C=h):ue("new/isolated scope",C,h,D)),x=x||h),m=h.name,!P&&(h.replace&&(h.templateUrl||h.template)||h.transclude&&!h.$$tlb)){for(var G,K=F+1;G=e[K++];)if(G.transclude&&!G.$$tlb||G.replace&&(G.templateUrl||G.template)){V=!0;break}P=!0}if(!h.templateUrl&&h.controller&&(b=h.controller,E=E||he(),ue("'"+m+"' controller",E[m],h,D),E[m]=h),b=h.transclude)if(A=!0,h.$$tlb||(ue("transclusion",_,h,D),_=h),"element"==b)O=!0,w=h.priority,g=D,D=n.$$element=Vr(R.$$createComment(m,n[m])),t=D[0],me(a,B(g),t),g[0].$$parentNode=g[0].parentNode,j=Q(V,g,o,w,I&&I.name,{nonTlbTranscludeDirective:_});else{var ne=he();if(g=Vr(Ne(t)).contents(),y(b)){g=[];var re=he(),ae=he();r(b,function(e,t){var n="?"===e.charAt(0);e=n?e.substring(1):e,re[e]=t,ne[t]=null,ae[t]=n}),r(D.contents(),function(e){var t=re[gt(L(e))];t?(ae[t]=!0,ne[t]=ne[t]||[],ne[t].push(e)):g.push(e)}),r(ae,function(e,t){if(!e)throw zi("reqslot","Required transclusion slot `{0}` was not filled.",t)});for(var se in ne)ne[se]&&(ne[se]=Q(V,ne[se],o))}D.empty(),j=Q(V,g,o,void 0,void 0,{needsNewScope:h.$$isolateScope||h.$$newScope}),j.$$slots=ne}if(h.template)if(N=!0,ue("template",S,h,D),S=h,b=T(h.template)?h.template(D,n):h.template,b=Se(b),h.replace){if(I=h,g=xe(b)?[]:$t(fe(h.templateNamespace,Qr(b))),t=g[0],1!=g.length||t.nodeType!==oi)throw zi("tplrt","Template for directive '{0}' must have exactly one root element. {1}",m,"");me(a,D,t);var le={$attr:{}},ce=z(t,[],le),de=e.splice(F+1,e.length-(F+1));(C||x)&&te(ce,C,x),e=e.concat(ce).concat(de),ie(n,le),H=e.length}else D.html(b);if(h.templateUrl)N=!0,ue("template",S,h,D),S=h,h.replace&&(I=h),p=oe(e.splice(F,e.length-F),D,n,a,A&&j,u,c,{controllerDirectives:E,newScopeDirective:x!==h&&x,newIsolateScopeDirective:C,templateDirective:S,nonTlbTranscludeDirective:_}),H=e.length;else if(h.compile)try{$=h.compile(D,n,j);var pe=h.$$originalDirective||h;T($)?d(null,q(pe,$),U,W):$&&d(q(pe,$.pre),q(pe,$.post),U,W)}catch(ye){i(ye,J(D))}h.terminal&&(p.terminal=!0,w=Math.max(w,h.priority))}return p.scope=x&&x.scope===!0,p.transcludeOnThisElement=A,p.templateOnThisElement=N,p.transclude=j,f.hasElementTranscludeDirective=O,p}function Z(e,t,n,i){var o;if(w(t)){var a=t.match(E),s=t.substring(a[0].length),u=a[1]||a[3],l="?"===a[2];if("^^"===u?n=n.parent():(o=i&&i[s],o=o&&o.instance),!o){var c="$"+s+"Controller";o=u?n.inheritedData(c):n.data(c)}if(!o&&!l)throw zi("ctreq","Controller '{0}', required by directive '{1}', can't be found!",s,e)}else if(Xr(t)){o=[];for(var f=0,d=t.length;f<d;f++)o[f]=Z(e,t[f],n,i)}else y(t)&&(o={},r(t,function(t,r){o[r]=Z(e,t,n,i)}));return o||null}function ee(e,t,n,r,i,o,a){var s=he();for(var l in r){var c=r[l],f={$scope:c===a||c.$$isolateScope?i:o,$element:e,$attrs:t,$transclude:n},d=c.controller;"@"==d&&(d=t[c.name]);var p=u(d,f,!0,c.controllerAs);s[c.name]=p,e.data("$"+c.name+"Controller",p.instance)}return s}function te(e,t,n){for(var r=0,i=e.length;r<i;r++)e[r]=d(e[r],{$$isolateScope:t,$$newScope:n})}function ne(e,n,r,o,s,u,l){if(n===s)return null;var p=null;if(c.hasOwnProperty(n))for(var h,m=t.get(n+f),g=0,$=m.length;g<$;g++)try{if(h=m[g],(v(o)||o>h.priority)&&h.restrict.indexOf(r)!=-1){if(u&&(h=d(h,{$$start:u,$$end:l})),!h.$$bindings){var b=h.$$bindings=a(h,h.name);y(b.isolateScope)&&(h.$$isolateBindings=b.isolateScope)}e.push(h),p=h}}catch(w){i(w)}return p}function re(e){if(c.hasOwnProperty(e))for(var n,r=t.get(e+f),i=0,o=r.length;i<o;i++)if(n=r[i],n.multiElement)return!0;return!1}function ie(e,t){var n=t.$attr,i=e.$attr;e.$$element;r(e,function(r,i){"$"!=i.charAt(0)&&(t[i]&&t[i]!==r&&(r+=("style"===i?";":" ")+t[i]),e.$set(i,r,!0,n[i]))}),r(t,function(t,r){e.hasOwnProperty(r)||"$"===r.charAt(0)||(e[r]=t,"class"!==r&&"style"!==r&&(i[r]=n[r]))})}function oe(e,t,n,i,a,s,u,l){var c,f,p=[],h=t[0],m=e.shift(),g=d(m,{templateUrl:null,transclude:null,replace:null,$$originalDirective:m}),v=T(m.templateUrl)?m.templateUrl(t,n):m.templateUrl,$=m.templateNamespace;return t.empty(),o(v).then(function(o){var d,b,w,x;if(o=Se(o),m.replace){if(w=xe(o)?[]:$t(fe($,Qr(o))),d=w[0],1!=w.length||d.nodeType!==oi)throw zi("tplrt","Template for directive '{0}' must have exactly one root element. {1}",m.name,v);b={$attr:{}},me(i,t,d);var E=z(d,[],b);y(m.scope)&&te(E,!0),e=E.concat(e),ie(n,b)}else d=h,t.html(o);for(e.unshift(g),c=K(e,d,n,a,t,m,s,u,l),r(i,function(e,n){e==d&&(i[n]=t[0])}),f=U(t[0].childNodes,a);p.length;){var T=p.shift(),C=p.shift(),S=p.shift(),k=p.shift(),_=t[0];if(!T.$$destroyed){if(C!==h){var A=C.className;l.hasElementTranscludeDirective&&m.replace||(_=Ne(d)),me(S,Vr(C),_),P(Vr(_),A)}x=c.transcludeOnThisElement?W(T,c.transclude,k):k,c(f,T,_,i,x)}}p=null}),function(e,t,n,r,i){var o=i;t.$$destroyed||(p?p.push(t,n,r,o):(c.transcludeOnThisElement&&(o=W(t,c.transclude,i)),c(f,t,n,r,o)))}}function ae(e,t){var n=t.priority-e.priority;return 0!==n?n:e.name!==t.name?e.name<t.name?-1:1:e.index-t.index}function ue(e,t,n,r){function i(e){return e?" (module: "+e+")":""}if(t)throw zi("multidir","Multiple directives [{0}{1}, {2}{3}] asking for {4} on: {5}",t.name,i(t.$$moduleName),n.name,i(n.$$moduleName),e,J(r))}function ce(e,t){var r=n(t,!0);r&&e.push({priority:0,compile:function(e){var t=e.parent(),n=!!t.length;return n&&R.$$addBindingClass(t),function(e,t){var i=t.parent();n||R.$$addBindingClass(i),R.$$addBindingInfo(i,r.expressions),e.$watch(r,function(e){t[0].nodeValue=e})}}})}function fe(t,n){switch(t=Ir(t||"html")){case"svg":case"math":var r=e.document.createElement("div");return r.innerHTML="<"+t+">"+n+"</"+t+">",r.childNodes[0].childNodes;default:return n}}function de(e,t){if("srcdoc"==t)return S.HTML;var n=L(e);return"xlinkHref"==t||"form"==n&&"action"==t||"img"!=n&&("src"==t||"ngSrc"==t)?S.RESOURCE_URL:void 0}function pe(e,t,r,i,o){var a=de(e,i);o=x[i]||o;var s=n(r,!0,a,o);if(s){if("multiple"===i&&"select"===L(e))throw zi("selmulti","Binding to the 'multiple' attribute is not supported. Element: {0}",J(e));t.push({priority:100,compile:function(){return{pre:function(e,t,u){var l=u.$$observers||(u.$$observers=he());if(C.test(i))throw zi("nodomevents","Interpolations for HTML DOM event attributes are disallowed.  Please use the ng- versions (such as ng-click instead of onclick) instead.");var c=u[i];c!==r&&(s=c&&n(c,!0,a,o),r=c),s&&(u[i]=s(e),(l[i]||(l[i]=[])).$$inter=!0,(u.$$observers&&u.$$observers[i].$$scope||e).$watch(s,function(e,t){"class"===i&&e!=t?u.$updateClass(e,t):u.$set(i,e)}))}}}})}}function me(t,n,r){var i,o,a=n[0],s=n.length,u=a.parentNode;if(t)for(i=0,o=t.length;i<o;i++)if(t[i]==a){t[i++]=r;for(var l=i,c=l+s-1,f=t.length;l<f;l++,c++)c<f?t[l]=t[c]:delete t[l];t.length-=s-1,t.context===a&&(t.context=r);break}u&&u.replaceChild(r,a);var d=e.document.createDocumentFragment();for(i=0;i<s;i++)d.appendChild(n[i]);for(Vr.hasData(a)&&(Vr.data(r,Vr.data(a)),Vr(a).off("$destroy")),Vr.cleanData(d.querySelectorAll("*")),i=1;i<s;i++)delete n[i];n[0]=r,n.length=1}function ge(e,t){return l(function(){return e.apply(null,arguments)},e,t)}function ve(e,t,n,r,o,a){try{e(t,n,r,o,a)}catch(s){i(s,J(n))}}function $e(e,t,i,o,a){function u(t,n,r){T(i.$onChanges)&&n!==r&&(ye||(e.$$postDigest(I),ye=[]),c||(c={},ye.push(l)),c[t]&&(r=c[t].previousValue),c[t]=new mt(r,n))}function l(){i.$onChanges(c),c=void 0}var c,f=[],d={};return r(o,function(r,o){var l,c,h,m,g,v=r.attrName,$=r.optional,y=r.mode;switch(y){case"@":$||Dr.call(t,v)||(i[o]=t[v]=void 0),t.$observe(v,function(e){if(w(e)||O(e)){var t=i[o];u(o,e,t),i[o]=e}}),t.$$observers[v].$$scope=e,l=t[v],w(l)?i[o]=n(l)(e):O(l)&&(i[o]=l),d[o]=new mt(Gi,i[o]);break;case"=":if(!Dr.call(t,v)){if($)break;t[v]=void 0}if($&&!t[v])break;c=s(t[v]),m=c.literal?F:function(e,t){return e===t||e!==e&&t!==t},h=c.assign||function(){throw l=i[o]=c(e),zi("nonassign","Expression '{0}' in attribute '{1}' used with directive '{2}' is non-assignable!",t[v],v,a.name)},l=i[o]=c(e);var b=function(t){return m(t,i[o])||(m(t,l)?h(e,t=i[o]):i[o]=t),l=t};b.$stateful=!0,g=r.collection?e.$watchCollection(t[v],b):e.$watch(s(t[v],b),null,c.literal),f.push(g);break;case"<":if(!Dr.call(t,v)){if($)break;t[v]=void 0}if($&&!t[v])break;c=s(t[v]);var x=i[o]=c(e);d[o]=new mt(Gi,i[o]),g=e.$watch(c,function(e,t){if(t===e){if(t===x)return;t=x}u(o,e,t),i[o]=e},c.literal),f.push(g);break;case"&":if(c=t.hasOwnProperty(v)?s(t[v]):p,c===p&&$)break;i[o]=function(t){return c(e,t)}}}),{initialChanges:d,removeWatches:f.length&&function(){for(var e=0,t=f.length;e<t;++e)f[e]()}}}var ye,be=/^\w/,we=e.document.createElement("div"),Ee=A;M.prototype={$normalize:gt,$addClass:function(e){e&&e.length>0&&N.addClass(this.$$element,e)},$removeClass:function(e){e&&e.length>0&&N.removeClass(this.$$element,e)},$updateClass:function(e,t){var n=vt(e,t);n&&n.length&&N.addClass(this.$$element,n);var r=vt(t,e);r&&r.length&&N.removeClass(this.$$element,r)},$set:function(e,t,n,o){var a,s=this.$$element[0],u=We(s,e),l=ze(e),c=e;if(u?(this.$$element.prop(e,t),o=u):l&&(this[l]=t,c=l),this[e]=t,o?this.$attr[e]=o:(o=this.$attr[e],o||(this.$attr[e]=o=se(e,"-"))),a=L(this.$$element),"a"===a&&("href"===e||"xlinkHref"===e)||"img"===a&&"src"===e)this[e]=t=D(t,"src"===e);else if("img"===a&&"srcset"===e&&$(t)){for(var f="",d=Qr(t),p=/(\s+\d+x\s*,|\s+\d+w\s*,|\s+,|,\s+)/,h=/\s/.test(d)?p:/(,)/,m=d.split(h),g=Math.floor(m.length/2),y=0;y<g;y++){var b=2*y;f+=D(Qr(m[b]),!0),f+=" "+Qr(m[b+1])}var w=Qr(m[2*y]).split(/\s/);f+=D(Qr(w[0]),!0),2===w.length&&(f+=" "+Qr(w[1])),this[e]=t=f}n!==!1&&(null===t||v(t)?this.$$element.removeAttr(o):be.test(o)?this.$$element.attr(o,t):j(this.$$element[0],o,t));var x=this.$$observers;x&&r(x[c],function(e){try{e(t)}catch(n){i(n)}})},$observe:function(e,t){var n=this,r=n.$$observers||(n.$$observers=he()),i=r[e]||(r[e]=[]);return i.push(t),m.$evalAsync(function(){i.$$inter||!n.hasOwnProperty(e)||v(n[e])||t(n[e])}),function(){V(i,t)}}};var Te=n.startSymbol(),Ce=n.endSymbol(),Se="{{"==Te&&"}}"==Ce?h:function(e){return e.replace(/\{\{/g,Te).replace(/}}/g,Ce)},ke=/^ngAttr[A-Z]/,Ae=/^(.+)Start$/;return R.$$addBindingInfo=_?function(e,t){var n=e.data("$binding")||[];Xr(t)?n=n.concat(t):n.push(t),e.data("$binding",n)}:p,R.$$addBindingClass=_?function(e){P(e,"ng-binding")}:p,R.$$addScopeInfo=_?function(e,t,n,r){var i=n?r?"$isolateScopeNoTemplate":"$isolateScope":"$scope";e.data(i,t)}:p,R.$$addScopeClass=_?function(e,t){P(e,t?"ng-isolate-scope":"ng-scope")}:p,R.$$createComment=function(t,n){var r="";return _&&(r=" "+(t||"")+": ",n&&(r+=n+" ")),e.document.createComment(r)},R}]}function mt(e,t){this.previousValue=e,this.currentValue=t}function gt(e){return we(e.replace(Yi,""))}function vt(e,t){var n="",r=e.split(/\s+/),i=t.split(/\s+/);e:for(var o=0;o<r.length;o++){for(var a=r[o],s=0;s<i.length;s++)if(a==i[s])continue e;n+=(n.length>0?" ":"")+a}return n}function $t(e){e=Vr(e);var t=e.length;if(t<=1)return e;for(;t--;){var n=e[t];n.nodeType===ui&&Br.call(e,t,1)}return e}function yt(e,t){if(t&&w(t))return t;if(w(e)){var n=Ji.exec(e);if(n)return n[3]}}function bt(){var e={},n=!1;this.has=function(t){return e.hasOwnProperty(t)},this.register=function(t,n){fe(t,"controller"),y(t)?l(e,t):e[t]=n},this.allowGlobals=function(){n=!0},this.$get=["$injector","$window",function(r,i){function o(e,n,r,i){if(!e||!y(e.$scope))throw t("$controller")("noscp","Cannot export controller '{0}' as '{1}'! No $scope object provided via `locals`.",i,n);e.$scope[n]=r}return function(t,a,s,u){var c,f,d,p;if(s=s===!0,u&&w(u)&&(p=u),w(t)){if(f=t.match(Ji),!f)throw Xi("ctrlfmt","Badly formed controller string '{0}'. Must match `__name__ as __id__` or `__name__`.",t);d=f[1],p=p||f[3],t=e.hasOwnProperty(d)?e[d]:de(a.$scope,d,!0)||(n?de(i,d,!0):void 0),ce(t,d,!0)}if(s){var h=(Xr(t)?t[t.length-1]:t).prototype;c=Object.create(h||null),p&&o(a,p,c,d||t.name);var m;return m=l(function(){var e=r.invoke(t,c,a,d);return e!==c&&(y(e)||T(e))&&(c=e,p&&o(a,p,c,d||t.name)),c},{instance:c,identifier:p})}return c=r.instantiate(t,a,d),p&&o(a,p,c,d||t.name),c}}]}function wt(){this.$get=["$window",function(e){return Vr(e.document)}]}function xt(){this.$get=["$log",function(e){return function(t,n){e.error.apply(e,arguments)}}]}function Et(e){return y(e)?E(e)?e.toISOString():W(e):e}function Tt(){this.$get=function(){return function(e){if(!e)return"";var t=[];return i(e,function(e,n){null===e||v(e)||(Xr(e)?r(e,function(e){t.push(te(n)+"="+te(Et(e)))}):t.push(te(n)+"="+te(Et(e))))}),t.join("&")}}}function Ct(){this.$get=function(){return function(e){function t(e,o,a){null===e||v(e)||(Xr(e)?r(e,function(e,n){t(e,o+"["+(y(e)?n:"")+"]")}):y(e)&&!E(e)?i(e,function(e,n){t(e,o+(a?"":"[")+n+(a?"":"]"))}):n.push(te(o)+"="+te(Et(e))))}if(!e)return"";var n=[];return t(e,"",!0),n.join("&")}}}function St(e,t){if(w(e)){var n=e.replace(no,"").trim();if(n){var r=t("Content-Type");(r&&0===r.indexOf(Ki)||kt(n))&&(e=z(n))}}return e}function kt(e){var t=e.match(eo);return t&&to[t[0]].test(e)}function _t(e){function t(e,t){e&&(i[e]=i[e]?i[e]+", "+t:t)}var n,i=he();return w(e)?r(e.split("\n"),function(e){n=e.indexOf(":"),t(Ir(Qr(e.substr(0,n))),Qr(e.substr(n+1)))}):y(e)&&r(e,function(e,n){t(Ir(n),Qr(e))}),i}function At(e){var t;return function(n){if(t||(t=_t(e)),n){var r=t[Ir(n)];return void 0===r&&(r=null),r}return t}}function Nt(e,t,n,i){return T(i)?i(e,t,n):(r(i,function(r){e=r(e,t,n)}),e)}function Ot(e){return 200<=e&&e<300}function Dt(){var e=this.defaults={transformResponse:[St],transformRequest:[function(e){return!y(e)||_(e)||N(e)||A(e)?e:W(e)}],headers:{common:{Accept:"application/json, text/plain, */*"},post:ge(Zi),put:ge(Zi),patch:ge(Zi)},xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",paramSerializer:"$httpParamSerializer"},n=!1;this.useApplyAsync=function(e){return $(e)?(n=!!e,this):n};var i=!0;this.useLegacyPromiseExtensions=function(e){return $(e)?(i=!!e,this):i};var o=this.interceptors=[];this.$get=["$httpBackend","$$cookieReader","$cacheFactory","$rootScope","$q","$injector",function(a,s,u,c,f,d){function p(n){function o(e,t){for(var n=0,r=t.length;n<r;){var i=t[n++],o=t[n++];e=e.then(i,o)}return t.length=0,e}function a(e,t){var n,i={};return r(e,function(e,r){T(e)?(n=e(t),null!=n&&(i[r]=n)):i[r]=e}),i}function s(t){var n,r,i,o=e.headers,s=l({},t.headers);o=l({},o.common,o[Ir(t.method)]);e:for(n in o){r=Ir(n);for(i in s)if(Ir(i)===r)continue e;s[n]=o[n]}return a(s,ge(t))}function u(t){var n=t.headers,i=Nt(t.data,At(n),void 0,t.transformRequest);return v(i)&&r(n,function(e,t){"content-type"===Ir(t)&&delete n[t]}),v(t.withCredentials)&&!v(e.withCredentials)&&(t.withCredentials=e.withCredentials),g(t,i).then(c,c)}function c(e){var t=l({},e);return t.data=Nt(e.data,e.headers,e.status,p.transformResponse),Ot(e.status)?t:f.reject(t)}if(!y(n))throw t("$http")("badreq","Http request configuration must be an object.  Received: {0}",n);if(!w(n.url))throw t("$http")("badreq","Http request configuration url must be a string.  Received: {0}",n.url);var p=l({method:"get",transformRequest:e.transformRequest,transformResponse:e.transformResponse,paramSerializer:e.paramSerializer},n);p.headers=s(n),p.method=Mr(p.method),p.paramSerializer=w(p.paramSerializer)?d.get(p.paramSerializer):p.paramSerializer;var h=[],m=[],$=f.when(p);return r(E,function(e){(e.request||e.requestError)&&h.unshift(e.request,e.requestError),(e.response||e.responseError)&&m.push(e.response,e.responseError)}),$=o($,h),$=$.then(u),$=o($,m),i?($.success=function(e){return ce(e,"fn"),$.then(function(t){e(t.data,t.status,t.headers,p)}),$},$.error=function(e){return ce(e,"fn"),$.then(null,function(t){e(t.data,t.status,t.headers,p)}),$}):($.success=io("success"),$.error=io("error")),$}function h(e){r(arguments,function(e){p[e]=function(t,n){return p(l({},n||{},{method:e,url:t}))}})}function m(e){r(arguments,function(e){p[e]=function(t,n,r){return p(l({},r||{},{method:e,url:t,data:n}))}})}function g(t,i){function o(e){if(e){var t={};return r(e,function(e,r){t[r]=function(t){function r(){e(t)}n?c.$applyAsync(r):c.$$phase?r():c.$apply(r)}}),t}}function u(e,t,r,i){function o(){l(t,e,r,i)}m&&(Ot(e)?m.put(C,[e,t,_t(r),i]):m.remove(C)),n?c.$applyAsync(o):(o(),c.$$phase||c.$apply())}function l(e,n,r,i){n=n>=-1?n:0,(Ot(n)?w.resolve:w.reject)({data:e,status:n,headers:At(r),config:t,statusText:i})}function d(e){l(e.data,e.status,ge(e.headers()),e.statusText)}function h(){var e=p.pendingRequests.indexOf(t);e!==-1&&p.pendingRequests.splice(e,1)}var m,g,w=f.defer(),E=w.promise,T=t.headers,C=b(t.url,t.paramSerializer(t.params));if(p.pendingRequests.push(t),E.then(h,h),!t.cache&&!e.cache||t.cache===!1||"GET"!==t.method&&"JSONP"!==t.method||(m=y(t.cache)?t.cache:y(e.cache)?e.cache:x),m&&(g=m.get(C),$(g)?D(g)?g.then(d,d):Xr(g)?l(g[1],g[0],ge(g[2]),g[3]):l(g,200,{},"OK"):m.put(C,E)),v(g)){var S=jn(t.url)?s()[t.xsrfCookieName||e.xsrfCookieName]:void 0;S&&(T[t.xsrfHeaderName||e.xsrfHeaderName]=S),a(t.method,C,i,u,T,t.timeout,t.withCredentials,t.responseType,o(t.eventHandlers),o(t.uploadEventHandlers))}return E}function b(e,t){return t.length>0&&(e+=(e.indexOf("?")==-1?"?":"&")+t),e}var x=u("$http");e.paramSerializer=w(e.paramSerializer)?d.get(e.paramSerializer):e.paramSerializer;var E=[];return r(o,function(e){E.unshift(w(e)?d.get(e):d.invoke(e))}),p.pendingRequests=[],h("get","delete","head","jsonp"),m("post","put","patch"),p.defaults=e,p}]}function It(){this.$get=function(){return function(){return new e.XMLHttpRequest}}}function Mt(){this.$get=["$browser","$jsonpCallbacks","$document","$xhrFactory",function(e,t,n,r){return jt(e,r,e.defer,t,n[0])}]}function jt(e,t,n,i,o){function a(e,t,n){e=e.replace("JSON_CALLBACK",t);var r=o.createElement("script"),a=null;return r.type="text/javascript",r.src=e,r.async=!0,a=function(e){mi(r,"load",a),mi(r,"error",a),o.body.removeChild(r),r=null;var s=-1,u="unknown";e&&("load"!==e.type||i.wasCalled(t)||(e={type:"error"}),u=e.type,s="error"===e.type?404:200),n&&n(s,u)},hi(r,"load",a),hi(r,"error",a),o.body.appendChild(r),a}return function(o,s,u,l,c,f,d,h,m,g){function y(){x&&x(),E&&E.abort()}function b(t,r,i,o,a){$(S)&&n.cancel(S),x=E=null,t(r,i,o,a),e.$$completeOutstandingRequest(p)}if(e.$$incOutstandingRequestCount(),s=s||e.url(),"jsonp"===Ir(o))var w=i.createCallback(s),x=a(s,w,function(e,t){var n=200===e&&i.getResponse(w);b(l,e,n,"",t),i.removeCallback(w)});else{var E=t(o,s);E.open(o,s,!0),r(c,function(e,t){$(e)&&E.setRequestHeader(t,e)}),E.onload=function(){var e=E.statusText||"",t="response"in E?E.response:E.responseText,n=1223===E.status?204:E.status;0===n&&(n=t?200:"file"==Mn(s).protocol?404:0),b(l,n,t,E.getAllResponseHeaders(),e)};var T=function(){b(l,-1,null,null,"")};if(E.onerror=T,E.onabort=T,r(m,function(e,t){E.addEventListener(t,e)}),r(g,function(e,t){E.upload.addEventListener(t,e)}),d&&(E.withCredentials=!0),h)try{E.responseType=h}catch(C){if("json"!==h)throw C}E.send(v(u)?null:u)}if(f>0)var S=n(y,f);else D(f)&&f.then(y)}}function Pt(){var e="{{",t="}}";this.startSymbol=function(t){return t?(e=t,this):e},this.endSymbol=function(e){return e?(t=e,this):t},this.$get=["$parse","$exceptionHandler","$sce",function(n,r,i){function o(e){return"\\\\\\"+e}function a(n){return n.replace(p,e).replace(h,t)}function s(e){if(null==e)return"";switch(typeof e){case"string":break;case"number":e=""+e;break;default:e=W(e)}return e}function u(e,t,n,r){var i;return i=e.$watch(function(e){return i(),r(e)},t,n)}function c(o,c,p,h){function g(e){try{return e=D(e),h&&!$(e)?e:s(e)}catch(t){r(oo.interr(o,t))}}if(!o.length||o.indexOf(e)===-1){var y;if(!c){var b=a(o);y=m(b),y.exp=o,y.expressions=[],y.$$watchDelegate=u}return y}h=!!h;for(var w,x,E,C=0,S=[],k=[],_=o.length,A=[],N=[];C<_;){if((w=o.indexOf(e,C))==-1||(x=o.indexOf(t,w+f))==-1){C!==_&&A.push(a(o.substring(C)));break}C!==w&&A.push(a(o.substring(C,w))),E=o.substring(w+f,x),S.push(E),k.push(n(E,g)),C=x+d,N.push(A.length),A.push("")}if(p&&A.length>1&&oo.throwNoconcat(o),!c||S.length){var O=function(e){for(var t=0,n=S.length;t<n;t++){if(h&&v(e[t]))return;A[N[t]]=e[t]}return A.join("")},D=function(e){return p?i.getTrusted(p,e):i.valueOf(e)};return l(function(e){var t=0,n=S.length,i=new Array(n);try{for(;t<n;t++)i[t]=k[t](e);return O(i)}catch(a){r(oo.interr(o,a))}},{exp:o,expressions:S,$$watchDelegate:function(e,t){var n;return e.$watchGroup(k,function(r,i){var o=O(r);T(t)&&t.call(this,o,r!==i?n:o,e),n=o})}})}}var f=e.length,d=t.length,p=new RegExp(e.replace(/./g,o),"g"),h=new RegExp(t.replace(/./g,o),"g");return c.startSymbol=function(){return e},c.endSymbol=function(){return t},c}]}function Lt(){this.$get=["$rootScope","$window","$q","$$q","$browser",function(e,t,n,r,i){function o(o,s,u,l){function c(){f?o.apply(null,d):o(m)}var f=arguments.length>4,d=f?B(arguments,4):[],p=t.setInterval,h=t.clearInterval,m=0,g=$(l)&&!l,v=(g?r:n).defer(),y=v.promise;return u=$(u)?u:0,y.$$intervalId=p(function(){g?i.defer(c):e.$evalAsync(c),v.notify(m++),u>0&&m>=u&&(v.resolve(m),h(y.$$intervalId),delete a[y.$$intervalId]),g||e.$apply()},s),a[y.$$intervalId]=v,y}var a={};return o.cancel=function(e){return!!(e&&e.$$intervalId in a)&&(a[e.$$intervalId].reject("canceled"),t.clearInterval(e.$$intervalId),delete a[e.$$intervalId],!0)},o}]}function Vt(e){for(var t=e.split("/"),n=t.length;n--;)t[n]=ee(t[n]);return t.join("/")}function Rt(e,t){var n=Mn(e);t.$$protocol=n.protocol,t.$$host=n.hostname,t.$$port=f(n.port)||uo[n.protocol]||null}function Ft(e,t){var n="/"!==e.charAt(0);n&&(e="/"+e);var r=Mn(e);t.$$path=decodeURIComponent(n&&"/"===r.pathname.charAt(0)?r.pathname.substring(1):r.pathname),t.$$search=K(r.search),t.$$hash=decodeURIComponent(r.hash),t.$$path&&"/"!=t.$$path.charAt(0)&&(t.$$path="/"+t.$$path)}function Ht(e,t){return 0===e.lastIndexOf(t,0)}function Bt(e,t){if(Ht(t,e))return t.substr(e.length)}function qt(e){var t=e.indexOf("#");return t==-1?e:e.substr(0,t)}function Ut(e){return e.replace(/(#.+)|#$/,"$1")}function Wt(e){return e.substr(0,qt(e).lastIndexOf("/")+1)}function zt(e){return e.substring(0,e.indexOf("/",e.indexOf("//")+2))}function Gt(e,t,n){this.$$html5=!0,n=n||"",Rt(e,this),this.$$parse=function(e){var n=Bt(t,e);if(!w(n))throw lo("ipthprfx",'Invalid url "{0}", missing path prefix "{1}".',e,t);Ft(n,this),this.$$path||(this.$$path="/"),this.$$compose()},this.$$compose=function(){var e=Z(this.$$search),n=this.$$hash?"#"+ee(this.$$hash):"";this.$$url=Vt(this.$$path)+(e?"?"+e:"")+n,this.$$absUrl=t+this.$$url.substr(1)},this.$$parseLinkUrl=function(r,i){if(i&&"#"===i[0])return this.hash(i.slice(1)),!0;var o,a,s;return $(o=Bt(e,r))?(a=o,s=$(o=Bt(n,o))?t+(Bt("/",o)||o):e+a):$(o=Bt(t,r))?s=t+o:t==r+"/"&&(s=t),s&&this.$$parse(s),!!s}}function Yt(e,t,n){Rt(e,this),this.$$parse=function(r){function i(e,t,n){var r,i=/^\/[A-Z]:(\/.*)/;return Ht(t,n)&&(t=t.replace(n,"")),i.exec(t)?e:(r=i.exec(e),r?r[1]:e)}var o,a=Bt(e,r)||Bt(t,r);v(a)||"#"!==a.charAt(0)?this.$$html5?o=a:(o="",v(a)&&(e=r,this.replace())):(o=Bt(n,a),v(o)&&(o=a)),Ft(o,this),this.$$path=i(this.$$path,o,e),this.$$compose()},this.$$compose=function(){var t=Z(this.$$search),r=this.$$hash?"#"+ee(this.$$hash):"";this.$$url=Vt(this.$$path)+(t?"?"+t:"")+r,this.$$absUrl=e+(this.$$url?n+this.$$url:"")},this.$$parseLinkUrl=function(t,n){return qt(e)==qt(t)&&(this.$$parse(t),!0)}}function Xt(e,t,n){this.$$html5=!0,Yt.apply(this,arguments),this.$$parseLinkUrl=function(r,i){if(i&&"#"===i[0])return this.hash(i.slice(1)),!0;var o,a;return e==qt(r)?o=r:(a=Bt(t,r))?o=e+n+a:t===r+"/"&&(o=t),o&&this.$$parse(o),!!o},this.$$compose=function(){var t=Z(this.$$search),r=this.$$hash?"#"+ee(this.$$hash):"";this.$$url=Vt(this.$$path)+(t?"?"+t:"")+r,this.$$absUrl=e+n+this.$$url}}function Jt(e){return function(){return this[e]}}function Qt(e,t){return function(n){return v(n)?this[e]:(this[e]=t(n),this.$$compose(),this)}}function Kt(){var e="",t={enabled:!1,requireBase:!0,rewriteLinks:!0};this.hashPrefix=function(t){return $(t)?(e=t,this):e},this.html5Mode=function(e){return O(e)?(t.enabled=e,this):y(e)?(O(e.enabled)&&(t.enabled=e.enabled),O(e.requireBase)&&(t.requireBase=e.requireBase),O(e.rewriteLinks)&&(t.rewriteLinks=e.rewriteLinks),this):t},this.$get=["$rootScope","$browser","$sniffer","$rootElement","$window",function(n,r,i,o,a){function s(e,t,n){var i=l.url(),o=l.$$state;try{r.url(e,t,n),l.$$state=r.state()}catch(a){throw l.url(i),l.$$state=o,a}}function u(e,t){n.$broadcast("$locationChangeSuccess",l.absUrl(),e,l.$$state,t)}var l,c,f,d=r.baseHref(),p=r.url();if(t.enabled){if(!d&&t.requireBase)throw lo("nobase","$location in HTML5 mode requires a <base> tag to be present!");f=zt(p)+(d||"/"),c=i.history?Gt:Xt}else f=qt(p),c=Yt;var h=Wt(f);l=new c(f,h,"#"+e),l.$$parseLinkUrl(p,p),l.$$state=r.state();var m=/^\s*(javascript|mailto):/i;o.on("click",function(e){if(t.rewriteLinks&&!e.ctrlKey&&!e.metaKey&&!e.shiftKey&&2!=e.which&&2!=e.button){for(var i=Vr(e.target);"a"!==L(i[0]);)if(i[0]===o[0]||!(i=i.parent())[0])return;var s=i.prop("href"),u=i.attr("href")||i.attr("xlink:href");y(s)&&"[object SVGAnimatedString]"===s.toString()&&(s=Mn(s.animVal).href),m.test(s)||!s||i.attr("target")||e.isDefaultPrevented()||l.$$parseLinkUrl(s,u)&&(e.preventDefault(),l.absUrl()!=r.url()&&(n.$apply(),a.angular["ff-684208-preventDefault"]=!0))}}),Ut(l.absUrl())!=Ut(p)&&r.url(l.absUrl(),!0);var g=!0;return r.onUrlChange(function(e,t){return v(Bt(h,e))?void(a.location.href=e):(n.$evalAsync(function(){var r,i=l.absUrl(),o=l.$$state;e=Ut(e),l.$$parse(e),l.$$state=t,r=n.$broadcast("$locationChangeStart",e,i,t,o).defaultPrevented,l.absUrl()===e&&(r?(l.$$parse(i),l.$$state=o,s(i,!1,o)):(g=!1,u(i,o)))}),void(n.$$phase||n.$digest()))}),n.$watch(function(){var e=Ut(r.url()),t=Ut(l.absUrl()),o=r.state(),a=l.$$replace,c=e!==t||l.$$html5&&i.history&&o!==l.$$state;(g||c)&&(g=!1,n.$evalAsync(function(){var t=l.absUrl(),r=n.$broadcast("$locationChangeStart",t,e,l.$$state,o).defaultPrevented;
l.absUrl()===t&&(r?(l.$$parse(e),l.$$state=o):(c&&s(t,a,o===l.$$state?null:l.$$state),u(e,o)))})),l.$$replace=!1}),l}]}function Zt(){var e=!0,t=this;this.debugEnabled=function(t){return $(t)?(e=t,this):e},this.$get=["$window",function(n){function i(e){return e instanceof Error&&(e.stack?e=e.message&&e.stack.indexOf(e.message)===-1?"Error: "+e.message+"\n"+e.stack:e.stack:e.sourceURL&&(e=e.message+"\n"+e.sourceURL+":"+e.line)),e}function o(e){var t=n.console||{},o=t[e]||t.log||p,a=!1;try{a=!!o.apply}catch(s){}return a?function(){var e=[];return r(arguments,function(t){e.push(i(t))}),o.apply(t,e)}:function(e,t){o(e,null==t?"":t)}}return{log:o("log"),info:o("info"),warn:o("warn"),error:o("error"),debug:function(){var n=o("debug");return function(){e&&n.apply(t,arguments)}}()}}]}function en(e,t){if("__defineGetter__"===e||"__defineSetter__"===e||"__lookupGetter__"===e||"__lookupSetter__"===e||"__proto__"===e)throw fo("isecfld","Attempting to access a disallowed field in Angular expressions! Expression: {0}",t);return e}function tn(e){return e+""}function nn(e,t){if(e){if(e.constructor===e)throw fo("isecfn","Referencing Function in Angular expressions is disallowed! Expression: {0}",t);if(e.window===e)throw fo("isecwindow","Referencing the Window in Angular expressions is disallowed! Expression: {0}",t);if(e.children&&(e.nodeName||e.prop&&e.attr&&e.find))throw fo("isecdom","Referencing DOM nodes in Angular expressions is disallowed! Expression: {0}",t);if(e===Object)throw fo("isecobj","Referencing Object in Angular expressions is disallowed! Expression: {0}",t)}return e}function rn(e,t){if(e){if(e.constructor===e)throw fo("isecfn","Referencing Function in Angular expressions is disallowed! Expression: {0}",t);if(e===po||e===ho||e===mo)throw fo("isecff","Referencing call, apply or bind in Angular expressions is disallowed! Expression: {0}",t)}}function on(e,t){if(e&&(e===(0).constructor||e===(!1).constructor||e==="".constructor||e==={}.constructor||e===[].constructor||e===Function.constructor))throw fo("isecaf","Assigning to a constructor is disallowed! Expression: {0}",t)}function an(e,t){return"undefined"!=typeof e?e:t}function sn(e,t){return"undefined"==typeof e?t:"undefined"==typeof t?e:e+t}function un(e,t){var n=e(t);return!n.$stateful}function ln(e,t){var n,i;switch(e.type){case yo.Program:n=!0,r(e.body,function(e){ln(e.expression,t),n=n&&e.expression.constant}),e.constant=n;break;case yo.Literal:e.constant=!0,e.toWatch=[];break;case yo.UnaryExpression:ln(e.argument,t),e.constant=e.argument.constant,e.toWatch=e.argument.toWatch;break;case yo.BinaryExpression:ln(e.left,t),ln(e.right,t),e.constant=e.left.constant&&e.right.constant,e.toWatch=e.left.toWatch.concat(e.right.toWatch);break;case yo.LogicalExpression:ln(e.left,t),ln(e.right,t),e.constant=e.left.constant&&e.right.constant,e.toWatch=e.constant?[]:[e];break;case yo.ConditionalExpression:ln(e.test,t),ln(e.alternate,t),ln(e.consequent,t),e.constant=e.test.constant&&e.alternate.constant&&e.consequent.constant,e.toWatch=e.constant?[]:[e];break;case yo.Identifier:e.constant=!1,e.toWatch=[e];break;case yo.MemberExpression:ln(e.object,t),e.computed&&ln(e.property,t),e.constant=e.object.constant&&(!e.computed||e.property.constant),e.toWatch=[e];break;case yo.CallExpression:n=!!e.filter&&un(t,e.callee.name),i=[],r(e.arguments,function(e){ln(e,t),n=n&&e.constant,e.constant||i.push.apply(i,e.toWatch)}),e.constant=n,e.toWatch=e.filter&&un(t,e.callee.name)?i:[e];break;case yo.AssignmentExpression:ln(e.left,t),ln(e.right,t),e.constant=e.left.constant&&e.right.constant,e.toWatch=[e];break;case yo.ArrayExpression:n=!0,i=[],r(e.elements,function(e){ln(e,t),n=n&&e.constant,e.constant||i.push.apply(i,e.toWatch)}),e.constant=n,e.toWatch=i;break;case yo.ObjectExpression:n=!0,i=[],r(e.properties,function(e){ln(e.value,t),n=n&&e.value.constant&&!e.computed,e.value.constant||i.push.apply(i,e.value.toWatch)}),e.constant=n,e.toWatch=i;break;case yo.ThisExpression:e.constant=!1,e.toWatch=[];break;case yo.LocalsExpression:e.constant=!1,e.toWatch=[]}}function cn(e){if(1==e.length){var t=e[0].expression,n=t.toWatch;return 1!==n.length?n:n[0]!==t?n:void 0}}function fn(e){return e.type===yo.Identifier||e.type===yo.MemberExpression}function dn(e){if(1===e.body.length&&fn(e.body[0].expression))return{type:yo.AssignmentExpression,left:e.body[0].expression,right:{type:yo.NGValueParameter},operator:"="}}function pn(e){return 0===e.body.length||1===e.body.length&&(e.body[0].expression.type===yo.Literal||e.body[0].expression.type===yo.ArrayExpression||e.body[0].expression.type===yo.ObjectExpression)}function hn(e){return e.constant}function mn(e,t){this.astBuilder=e,this.$filter=t}function gn(e,t){this.astBuilder=e,this.$filter=t}function vn(e){return"constructor"==e}function $n(e){return T(e.valueOf)?e.valueOf():wo.call(e)}function yn(){var e,t,n=he(),i=he(),o={"true":!0,"false":!1,"null":null,undefined:void 0};this.addLiteral=function(e,t){o[e]=t},this.setIdentifierFns=function(n,r){return e=n,t=r,this},this.$get=["$filter",function(a){function s(e,t,r){var o,s,l;switch(r=r||b,typeof e){case"string":e=e.trim(),l=e;var g=r?i:n;if(o=g[l],!o){":"===e.charAt(0)&&":"===e.charAt(1)&&(s=!0,e=e.substring(2));var $=r?y:v,w=new $o($),x=new bo(w,a,$);o=x.parse(e),o.constant?o.$$watchDelegate=h:s?o.$$watchDelegate=o.literal?d:f:o.inputs&&(o.$$watchDelegate=c),r&&(o=u(o)),g[l]=o}return m(o,t);case"function":return m(e,t);default:return m(p,t)}}function u(e){function t(t,n,r,i){var o=b;b=!0;try{return e(t,n,r,i)}finally{b=o}}if(!e)return e;t.$$watchDelegate=e.$$watchDelegate,t.assign=u(e.assign),t.constant=e.constant,t.literal=e.literal;for(var n=0;e.inputs&&n<e.inputs.length;++n)e.inputs[n]=u(e.inputs[n]);return t.inputs=e.inputs,t}function l(e,t){return null==e||null==t?e===t:("object"!=typeof e||(e=$n(e),"object"!=typeof e))&&(e===t||e!==e&&t!==t)}function c(e,t,n,r,i){var o,a=r.inputs;if(1===a.length){var s=l;return a=a[0],e.$watch(function(e){var t=a(e);return l(t,s)||(o=r(e,void 0,void 0,[t]),s=t&&$n(t)),o},t,n,i)}for(var u=[],c=[],f=0,d=a.length;f<d;f++)u[f]=l,c[f]=null;return e.$watch(function(e){for(var t=!1,n=0,i=a.length;n<i;n++){var s=a[n](e);(t||(t=!l(s,u[n])))&&(c[n]=s,u[n]=s&&$n(s))}return t&&(o=r(e,void 0,void 0,c)),o},t,n,i)}function f(e,t,n,r){var i,o;return i=e.$watch(function(e){return r(e)},function(e,n,r){o=e,T(t)&&t.apply(this,arguments),$(e)&&r.$$postDigest(function(){$(o)&&i()})},n)}function d(e,t,n,i){function o(e){var t=!0;return r(e,function(e){$(e)||(t=!1)}),t}var a,s;return a=e.$watch(function(e){return i(e)},function(e,n,r){s=e,T(t)&&t.call(this,e,n,r),o(e)&&r.$$postDigest(function(){o(s)&&a()})},n)}function h(e,t,n,r){var i;return i=e.$watch(function(e){return i(),r(e)},t,n)}function m(e,t){if(!t)return e;var n=e.$$watchDelegate,r=!1,i=n!==d&&n!==f,o=i?function(n,i,o,a){var s=r&&a?a[0]:e(n,i,o,a);return t(s,n,i)}:function(n,r,i,o){var a=e(n,r,i,o),s=t(a,n,r);return $(a)?s:a};return e.$$watchDelegate&&e.$$watchDelegate!==c?o.$$watchDelegate=e.$$watchDelegate:t.$stateful||(o.$$watchDelegate=c,r=!e.inputs,o.inputs=e.inputs?e.inputs:[e]),o}var g=Zr().noUnsafeEval,v={csp:g,expensiveChecks:!1,literals:R(o),isIdentifierStart:T(e)&&e,isIdentifierContinue:T(t)&&t},y={csp:g,expensiveChecks:!0,literals:R(o),isIdentifierStart:T(e)&&e,isIdentifierContinue:T(t)&&t},b=!1;return s.$$runningExpensiveChecks=function(){return b},s}]}function bn(){this.$get=["$rootScope","$exceptionHandler",function(e,t){return xn(function(t){e.$evalAsync(t)},t)}]}function wn(){this.$get=["$browser","$exceptionHandler",function(e,t){return xn(function(t){e.defer(t)},t)}]}function xn(e,n){function i(){this.$$state={status:0}}function o(e,t){return function(n){t.call(e,n)}}function a(e){var t,r,i;i=e.pending,e.processScheduled=!1,e.pending=void 0;for(var o=0,a=i.length;o<a;++o){r=i[o][0],t=i[o][e.status];try{T(t)?r.resolve(t(e.value)):1===e.status?r.resolve(e.value):r.reject(e.value)}catch(s){r.reject(s),n(s)}}}function s(t){!t.processScheduled&&t.pending&&(t.processScheduled=!0,e(function(){a(t)}))}function u(){this.promise=new i}function c(e){var t=new u,n=0,i=Xr(e)?[]:{};return r(e,function(e,r){n++,$(e).then(function(e){i.hasOwnProperty(r)||(i[r]=e,--n||t.resolve(i))},function(e){i.hasOwnProperty(r)||t.reject(e)})}),0===n&&t.resolve(i),t.promise}function f(e){var t=p();return r(e,function(e){$(e).then(t.resolve,t.reject)}),t.promise}var d=t("$q",TypeError),p=function(){var e=new u;return e.resolve=o(e,e.resolve),e.reject=o(e,e.reject),e.notify=o(e,e.notify),e};l(i.prototype,{then:function(e,t,n){if(v(e)&&v(t)&&v(n))return this;var r=new u;return this.$$state.pending=this.$$state.pending||[],this.$$state.pending.push([r,e,t,n]),this.$$state.status>0&&s(this.$$state),r.promise},"catch":function(e){return this.then(null,e)},"finally":function(e,t){return this.then(function(t){return g(t,!0,e)},function(t){return g(t,!1,e)},t)}}),l(u.prototype,{resolve:function(e){this.promise.$$state.status||(e===this.promise?this.$$reject(d("qcycle","Expected promise to be resolved with value other than itself '{0}'",e)):this.$$resolve(e))},$$resolve:function(e){function t(e){u||(u=!0,a.$$resolve(e))}function r(e){u||(u=!0,a.$$reject(e))}var i,a=this,u=!1;try{(y(e)||T(e))&&(i=e&&e.then),T(i)?(this.promise.$$state.status=-1,i.call(e,t,r,o(this,this.notify))):(this.promise.$$state.value=e,this.promise.$$state.status=1,s(this.promise.$$state))}catch(l){r(l),n(l)}},reject:function(e){this.promise.$$state.status||this.$$reject(e)},$$reject:function(e){this.promise.$$state.value=e,this.promise.$$state.status=2,s(this.promise.$$state)},notify:function(t){var r=this.promise.$$state.pending;this.promise.$$state.status<=0&&r&&r.length&&e(function(){for(var e,i,o=0,a=r.length;o<a;o++){i=r[o][0],e=r[o][3];try{i.notify(T(e)?e(t):t)}catch(s){n(s)}}})}});var h=function(e){var t=new u;return t.reject(e),t.promise},m=function(e,t){var n=new u;return t?n.resolve(e):n.reject(e),n.promise},g=function(e,t,n){var r=null;try{T(n)&&(r=n())}catch(i){return m(i,!1)}return D(r)?r.then(function(){return m(e,t)},function(e){return m(e,!1)}):m(e,t)},$=function(e,t,n,r){var i=new u;return i.resolve(e),i.promise.then(t,n,r)},b=$,w=function(e){function t(e){r.resolve(e)}function n(e){r.reject(e)}if(!T(e))throw d("norslvr","Expected resolverFn, got '{0}'",e);var r=new u;return e(t,n),r.promise};return w.prototype=i.prototype,w.defer=p,w.reject=h,w.when=$,w.resolve=b,w.all=c,w.race=f,w}function En(){this.$get=["$window","$timeout",function(e,t){var n=e.requestAnimationFrame||e.webkitRequestAnimationFrame,r=e.cancelAnimationFrame||e.webkitCancelAnimationFrame||e.webkitCancelRequestAnimationFrame,i=!!n,o=i?function(e){var t=n(e);return function(){r(t)}}:function(e){var n=t(e,16.66,!1);return function(){t.cancel(n)}};return o.supported=i,o}]}function Tn(){function e(e){function t(){this.$$watchers=this.$$nextSibling=this.$$childHead=this.$$childTail=null,this.$$listeners={},this.$$listenerCount={},this.$$watchersCount=0,this.$id=a(),this.$$ChildScope=null}return t.prototype=e,t}var i=10,o=t("$rootScope"),s=null,u=null;this.digestTtl=function(e){return arguments.length&&(i=e),i},this.$get=["$exceptionHandler","$parse","$browser",function(t,l,c){function f(e){e.currentScope.$$destroyed=!0}function d(e){9===Lr&&(e.$$childHead&&d(e.$$childHead),e.$$nextSibling&&d(e.$$nextSibling)),e.$parent=e.$$nextSibling=e.$$prevSibling=e.$$childHead=e.$$childTail=e.$root=e.$$watchers=null}function h(){this.$id=a(),this.$$phase=this.$parent=this.$$watchers=this.$$nextSibling=this.$$prevSibling=this.$$childHead=this.$$childTail=null,this.$root=this,this.$$destroyed=!1,this.$$listeners={},this.$$listenerCount={},this.$$watchersCount=0,this.$$isolateBindings=null}function m(e){if(C.$$phase)throw o("inprog","{0} already in progress",C.$$phase);C.$$phase=e}function g(){C.$$phase=null}function $(e,t){do e.$$watchersCount+=t;while(e=e.$parent)}function b(e,t,n){do e.$$listenerCount[n]-=t,0===e.$$listenerCount[n]&&delete e.$$listenerCount[n];while(e=e.$parent)}function w(){}function x(){for(;_.length;)try{_.shift()()}catch(e){t(e)}u=null}function E(){null===u&&(u=c.defer(function(){C.$apply(x)}))}h.prototype={constructor:h,$new:function(t,n){var r;return n=n||this,t?(r=new h,r.$root=this.$root):(this.$$ChildScope||(this.$$ChildScope=e(this)),r=new this.$$ChildScope),r.$parent=n,r.$$prevSibling=n.$$childTail,n.$$childHead?(n.$$childTail.$$nextSibling=r,n.$$childTail=r):n.$$childHead=n.$$childTail=r,(t||n!=this)&&r.$on("$destroy",f),r},$watch:function(e,t,n,r){var i=l(e);if(i.$$watchDelegate)return i.$$watchDelegate(this,t,n,i,e);var o=this,a=o.$$watchers,u={fn:t,last:w,get:i,exp:r||e,eq:!!n};return s=null,T(t)||(u.fn=p),a||(a=o.$$watchers=[]),a.unshift(u),$(this,1),function(){V(a,u)>=0&&$(o,-1),s=null}},$watchGroup:function(e,t){function n(){u=!1,l?(l=!1,t(o,o,s)):t(o,i,s)}var i=new Array(e.length),o=new Array(e.length),a=[],s=this,u=!1,l=!0;if(!e.length){var c=!0;return s.$evalAsync(function(){c&&t(o,o,s)}),function(){c=!1}}return 1===e.length?this.$watch(e[0],function(e,n,r){o[0]=e,i[0]=n,t(o,e===n?o:i,r)}):(r(e,function(e,t){var r=s.$watch(e,function(e,r){o[t]=e,i[t]=r,u||(u=!0,s.$evalAsync(n))});a.push(r)}),function(){for(;a.length;)a.shift()()})},$watchCollection:function(e,t){function r(e){o=e;var t,r,i,s,u;if(!v(o)){if(y(o))if(n(o)){a!==p&&(a=p,g=a.length=0,f++),t=o.length,g!==t&&(f++,a.length=g=t);for(var l=0;l<t;l++)u=a[l],s=o[l],i=u!==u&&s!==s,i||u===s||(f++,a[l]=s)}else{a!==h&&(a=h={},g=0,f++),t=0;for(r in o)Dr.call(o,r)&&(t++,s=o[r],u=a[r],r in a?(i=u!==u&&s!==s,i||u===s||(f++,a[r]=s)):(g++,a[r]=s,f++));if(g>t){f++;for(r in a)Dr.call(o,r)||(g--,delete a[r])}}else a!==o&&(a=o,f++);return f}}function i(){if(m?(m=!1,t(o,o,u)):t(o,s,u),c)if(y(o))if(n(o)){s=new Array(o.length);for(var e=0;e<o.length;e++)s[e]=o[e]}else{s={};for(var r in o)Dr.call(o,r)&&(s[r]=o[r])}else s=o}r.$stateful=!0;var o,a,s,u=this,c=t.length>1,f=0,d=l(e,r),p=[],h={},m=!0,g=0;return this.$watch(d,i)},$digest:function(){var e,n,r,a,l,f,d,p,h,v,$,y,b=i,E=this,_=[];m("$digest"),c.$$checkUrlChange(),this===C&&null!==u&&(c.defer.cancel(u),x()),s=null;do{p=!1,v=E;for(var N=0;N<S.length;N++){try{y=S[N],y.scope.$eval(y.expression,y.locals)}catch(O){t(O)}s=null}S.length=0;e:do{if(f=v.$$watchers)for(d=f.length;d--;)try{if(e=f[d])if(l=e.get,(n=l(v))===(r=e.last)||(e.eq?F(n,r):"number"==typeof n&&"number"==typeof r&&isNaN(n)&&isNaN(r))){if(e===s){p=!1;break e}}else p=!0,s=e,e.last=e.eq?R(n,null):n,a=e.fn,a(n,r===w?n:r,v),b<5&&($=4-b,_[$]||(_[$]=[]),_[$].push({msg:T(e.exp)?"fn: "+(e.exp.name||e.exp.toString()):e.exp,newVal:n,oldVal:r}))}catch(O){t(O)}if(!(h=v.$$watchersCount&&v.$$childHead||v!==E&&v.$$nextSibling))for(;v!==E&&!(h=v.$$nextSibling);)v=v.$parent}while(v=h);if((p||S.length)&&!b--)throw g(),o("infdig","{0} $digest() iterations reached. Aborting!\nWatchers fired in the last 5 iterations: {1}",i,_)}while(p||S.length);for(g();A<k.length;)try{k[A++]()}catch(O){t(O)}k.length=A=0},$destroy:function(){if(!this.$$destroyed){var e=this.$parent;this.$broadcast("$destroy"),this.$$destroyed=!0,this===C&&c.$$applicationDestroyed(),$(this,-this.$$watchersCount);for(var t in this.$$listenerCount)b(this,this.$$listenerCount[t],t);e&&e.$$childHead==this&&(e.$$childHead=this.$$nextSibling),e&&e.$$childTail==this&&(e.$$childTail=this.$$prevSibling),this.$$prevSibling&&(this.$$prevSibling.$$nextSibling=this.$$nextSibling),this.$$nextSibling&&(this.$$nextSibling.$$prevSibling=this.$$prevSibling),this.$destroy=this.$digest=this.$apply=this.$evalAsync=this.$applyAsync=p,this.$on=this.$watch=this.$watchGroup=function(){return p},this.$$listeners={},this.$$nextSibling=null,d(this)}},$eval:function(e,t){return l(e)(this,t)},$evalAsync:function(e,t){C.$$phase||S.length||c.defer(function(){S.length&&C.$digest()}),S.push({scope:this,expression:l(e),locals:t})},$$postDigest:function(e){k.push(e)},$apply:function(e){try{m("$apply");try{return this.$eval(e)}finally{g()}}catch(n){t(n)}finally{try{C.$digest()}catch(n){throw t(n),n}}},$applyAsync:function(e){function t(){n.$eval(e)}var n=this;e&&_.push(t),e=l(e),E()},$on:function(e,t){var n=this.$$listeners[e];n||(this.$$listeners[e]=n=[]),n.push(t);var r=this;do r.$$listenerCount[e]||(r.$$listenerCount[e]=0),r.$$listenerCount[e]++;while(r=r.$parent);var i=this;return function(){var r=n.indexOf(t);r!==-1&&(n[r]=null,b(i,1,e))}},$emit:function(e,n){var r,i,o,a=[],s=this,u=!1,l={name:e,targetScope:s,stopPropagation:function(){u=!0},preventDefault:function(){l.defaultPrevented=!0},defaultPrevented:!1},c=H([l],arguments,1);do{for(r=s.$$listeners[e]||a,l.currentScope=s,i=0,o=r.length;i<o;i++)if(r[i])try{r[i].apply(null,c)}catch(f){t(f)}else r.splice(i,1),i--,o--;if(u)return l.currentScope=null,l;s=s.$parent}while(s);return l.currentScope=null,l},$broadcast:function(e,n){var r=this,i=r,o=r,a={name:e,targetScope:r,preventDefault:function(){a.defaultPrevented=!0},defaultPrevented:!1};if(!r.$$listenerCount[e])return a;for(var s,u,l,c=H([a],arguments,1);i=o;){for(a.currentScope=i,s=i.$$listeners[e]||[],u=0,l=s.length;u<l;u++)if(s[u])try{s[u].apply(null,c)}catch(f){t(f)}else s.splice(u,1),u--,l--;if(!(o=i.$$listenerCount[e]&&i.$$childHead||i!==r&&i.$$nextSibling))for(;i!==r&&!(o=i.$$nextSibling);)i=i.$parent}return a.currentScope=null,a}};var C=new h,S=C.$$asyncQueue=[],k=C.$$postDigestQueue=[],_=C.$$applyAsyncQueue=[],A=0;return C}]}function Cn(){var e=/^\s*(https?|ftp|mailto|tel|file):/,t=/^\s*((https?|ftp|file|blob):|data:image\/)/;this.aHrefSanitizationWhitelist=function(t){return $(t)?(e=t,this):e},this.imgSrcSanitizationWhitelist=function(e){return $(e)?(t=e,this):t},this.$get=function(){return function(n,r){var i,o=r?t:e;return i=Mn(n).href,""===i||i.match(o)?n:"unsafe:"+i}}}function Sn(e){if("self"===e)return e;if(w(e)){if(e.indexOf("***")>-1)throw xo("iwcard","Illegal sequence *** in string matcher.  String: {0}",e);return e=Kr(e).replace("\\*\\*",".*").replace("\\*","[^:/.?&;]*"),new RegExp("^"+e+"$")}if(C(e))return new RegExp("^"+e.source+"$");throw xo("imatcher",'Matchers may only be "self", string patterns or RegExp objects')}function kn(e){var t=[];return $(e)&&r(e,function(e){t.push(Sn(e))}),t}function _n(){this.SCE_CONTEXTS=Eo;var e=["self"],t=[];this.resourceUrlWhitelist=function(t){return arguments.length&&(e=kn(t)),e},this.resourceUrlBlacklist=function(e){return arguments.length&&(t=kn(e)),t},this.$get=["$injector",function(n){function r(e,t){return"self"===e?jn(t):!!e.exec(t.href)}function i(n){var i,o,a=Mn(n.toString()),s=!1;for(i=0,o=e.length;i<o;i++)if(r(e[i],a)){s=!0;break}if(s)for(i=0,o=t.length;i<o;i++)if(r(t[i],a)){s=!1;break}return s}function o(e){var t=function(e){this.$$unwrapTrustedValue=function(){return e}};return e&&(t.prototype=new e),t.prototype.valueOf=function(){return this.$$unwrapTrustedValue()},t.prototype.toString=function(){return this.$$unwrapTrustedValue().toString()},t}function a(e,t){var n=f.hasOwnProperty(e)?f[e]:null;if(!n)throw xo("icontext","Attempted to trust a value in invalid context. Context: {0}; Value: {1}",e,t);if(null===t||v(t)||""===t)return t;if("string"!=typeof t)throw xo("itype","Attempted to trust a non-string value in a content requiring a string: Context: {0}",e);return new n(t)}function s(e){return e instanceof c?e.$$unwrapTrustedValue():e}function u(e,t){if(null===t||v(t)||""===t)return t;var n=f.hasOwnProperty(e)?f[e]:null;if(n&&t instanceof n)return t.$$unwrapTrustedValue();if(e===Eo.RESOURCE_URL){if(i(t))return t;throw xo("insecurl","Blocked loading resource from url not allowed by $sceDelegate policy.  URL: {0}",t.toString())}if(e===Eo.HTML)return l(t);throw xo("unsafe","Attempting to use an unsafe value in a safe context.")}var l=function(e){throw xo("unsafe","Attempting to use an unsafe value in a safe context.")};n.has("$sanitize")&&(l=n.get("$sanitize"));var c=o(),f={};return f[Eo.HTML]=o(c),f[Eo.CSS]=o(c),f[Eo.URL]=o(c),f[Eo.JS]=o(c),f[Eo.RESOURCE_URL]=o(f[Eo.URL]),{trustAs:a,getTrusted:u,valueOf:s}}]}function An(){var e=!0;this.enabled=function(t){return arguments.length&&(e=!!t),e},this.$get=["$parse","$sceDelegate",function(t,n){if(e&&Lr<8)throw xo("iequirks","Strict Contextual Escaping does not support Internet Explorer version < 11 in quirks mode.  You can fix this by adding the text <!doctype html> to the top of your HTML document.  See http://docs.angularjs.org/api/ng.$sce for more information.");var i=ge(Eo);i.isEnabled=function(){return e},i.trustAs=n.trustAs,i.getTrusted=n.getTrusted,i.valueOf=n.valueOf,e||(i.trustAs=i.getTrusted=function(e,t){return t},i.valueOf=h),i.parseAs=function(e,n){var r=t(n);return r.literal&&r.constant?r:t(n,function(t){return i.getTrusted(e,t)})};var o=i.parseAs,a=i.getTrusted,s=i.trustAs;return r(Eo,function(e,t){var n=Ir(t);i[we("parse_as_"+n)]=function(t){return o(e,t)},i[we("get_trusted_"+n)]=function(t){return a(e,t)},i[we("trust_as_"+n)]=function(t){return s(e,t)}}),i}]}function Nn(){this.$get=["$window","$document",function(e,t){var n,r,i={},o=e.chrome&&e.chrome.app&&e.chrome.app.runtime,a=!o&&e.history&&e.history.pushState,s=f((/android (\d+)/.exec(Ir((e.navigator||{}).userAgent))||[])[1]),u=/Boxee/i.test((e.navigator||{}).userAgent),l=t[0]||{},c=/^(Moz|webkit|ms)(?=[A-Z])/,d=l.body&&l.body.style,p=!1,h=!1;if(d){for(var m in d)if(r=c.exec(m)){n=r[0],n=n[0].toUpperCase()+n.substr(1);break}n||(n="WebkitOpacity"in d&&"webkit"),p=!!("transition"in d||n+"Transition"in d),h=!!("animation"in d||n+"Animation"in d),!s||p&&h||(p=w(d.webkitTransition),h=w(d.webkitAnimation))}return{history:!(!a||s<4||u),hasEvent:function(e){if("input"===e&&Lr<=11)return!1;if(v(i[e])){var t=l.createElement("div");i[e]="on"+e in t}return i[e]},csp:Zr(),vendorPrefix:n,transitions:p,animations:h,android:s}}]}function On(){var e;this.httpOptions=function(t){return t?(e=t,this):e},this.$get=["$templateCache","$http","$q","$sce",function(t,n,r,i){function o(a,s){function u(e){if(!s)throw To("tpload","Failed to load template: {0} (HTTP status: {1} {2})",a,e.status,e.statusText);return r.reject(e)}o.totalPendingRequests++,w(a)&&!v(t.get(a))||(a=i.getTrustedResourceUrl(a));var c=n.defaults&&n.defaults.transformResponse;return Xr(c)?c=c.filter(function(e){return e!==St}):c===St&&(c=null),n.get(a,l({cache:t,transformResponse:c},e))["finally"](function(){o.totalPendingRequests--}).then(function(e){return t.put(a,e.data),e.data},u)}return o.totalPendingRequests=0,o}]}function Dn(){this.$get=["$rootScope","$browser","$location",function(e,t,n){var i={};return i.findBindings=function(e,t,n){var i=e.getElementsByClassName("ng-binding"),o=[];return r(i,function(e){var i=Gr.element(e).data("$binding");i&&r(i,function(r){if(n){var i=new RegExp("(^|\\s)"+Kr(t)+"(\\s|\\||$)");i.test(r)&&o.push(e)}else r.indexOf(t)!=-1&&o.push(e)})}),o},i.findModels=function(e,t,n){for(var r=["ng-","data-ng-","ng\\:"],i=0;i<r.length;++i){var o=n?"=":"*=",a="["+r[i]+"model"+o+'"'+t+'"]',s=e.querySelectorAll(a);if(s.length)return s}},i.getLocation=function(){return n.url()},i.setLocation=function(t){t!==n.url()&&(n.url(t),e.$digest())},i.whenStable=function(e){t.notifyWhenNoOutstandingRequests(e)},i}]}function In(){this.$get=["$rootScope","$browser","$q","$$q","$exceptionHandler",function(e,t,n,r,i){function o(o,s,u){T(o)||(u=s,s=o,o=p);var l,c=B(arguments,3),f=$(u)&&!u,d=(f?r:n).defer(),h=d.promise;return l=t.defer(function(){try{d.resolve(o.apply(null,c))}catch(t){d.reject(t),i(t)}finally{delete a[h.$$timeoutId]}f||e.$apply()},s),h.$$timeoutId=l,a[l]=d,h}var a={};return o.cancel=function(e){return!!(e&&e.$$timeoutId in a)&&(a[e.$$timeoutId].reject("canceled"),delete a[e.$$timeoutId],t.defer.cancel(e.$$timeoutId))},o}]}function Mn(e){var t=e;return Lr&&(Co.setAttribute("href",t),t=Co.href),Co.setAttribute("href",t),{href:Co.href,protocol:Co.protocol?Co.protocol.replace(/:$/,""):"",host:Co.host,search:Co.search?Co.search.replace(/^\?/,""):"",hash:Co.hash?Co.hash.replace(/^#/,""):"",hostname:Co.hostname,port:Co.port,pathname:"/"===Co.pathname.charAt(0)?Co.pathname:"/"+Co.pathname}}function jn(e){var t=w(e)?Mn(e):e;return t.protocol===So.protocol&&t.host===So.host}function Pn(){this.$get=m(e)}function Ln(e){function t(e){try{return decodeURIComponent(e)}catch(t){return e}}var n=e[0]||{},r={},i="";return function(){var e,o,a,s,u,l=n.cookie||"";if(l!==i)for(i=l,e=i.split("; "),r={},a=0;a<e.length;a++)o=e[a],s=o.indexOf("="),s>0&&(u=t(o.substring(0,s)),v(r[u])&&(r[u]=t(o.substring(s+1))));return r}}function Vn(){this.$get=Ln}function Rn(e){function t(i,o){if(y(i)){var a={};return r(i,function(e,n){a[n]=t(n,e)}),a}return e.factory(i+n,o)}var n="Filter";this.register=t,this.$get=["$injector",function(e){return function(t){return e.get(t+n)}}],t("currency",Un),t("date",or),t("filter",Fn),t("json",ar),t("limitTo",sr),t("lowercase",Io),t("number",Wn),t("orderBy",lr),t("uppercase",Mo)}function Fn(){return function(e,r,i,o){if(!n(e)){if(null==e)return e;throw t("filter")("notarray","Expected array but received: {0}",e)}o=o||"$";var a,s,u=qn(r);switch(u){case"function":a=r;break;case"boolean":case"null":case"number":case"string":s=!0;case"object":a=Hn(r,i,o,s);break;default:return e}return Array.prototype.filter.call(e,a)}}function Hn(e,t,n,r){var i,o=y(e)&&n in e;return t===!0?t=F:T(t)||(t=function(e,t){return!v(e)&&(null===e||null===t?e===t:!(y(t)||y(e)&&!g(e))&&(e=Ir(""+e),t=Ir(""+t),e.indexOf(t)!==-1))}),i=function(i){return o&&!y(i)?Bn(i,e[n],t,n,!1):Bn(i,e,t,n,r)}}function Bn(e,t,n,r,i,o){var a=qn(e),s=qn(t);if("string"===s&&"!"===t.charAt(0))return!Bn(e,t.substring(1),n,r,i);if(Xr(e))return e.some(function(e){return Bn(e,t,n,r,i)});switch(a){case"object":var u;if(i){for(u in e)if("$"!==u.charAt(0)&&Bn(e[u],t,n,r,!0))return!0;return!o&&Bn(e,t,n,r,!1)}if("object"===s){for(u in t){var l=t[u];if(!T(l)&&!v(l)){var c=u===r,f=c?e:e[u];if(!Bn(f,l,n,r,c,c))return!1}}return!0}return n(e,t);case"function":return!1;default:return n(e,t)}}function qn(e){return null===e?"null":typeof e}function Un(e){var t=e.NUMBER_FORMATS;return function(e,n,r){return v(n)&&(n=t.CURRENCY_SYM),v(r)&&(r=t.PATTERNS[1].maxFrac),null==e?e:Yn(e,t.PATTERNS[1],t.GROUP_SEP,t.DECIMAL_SEP,r).replace(/\u00A4/g,n)}}function Wn(e){var t=e.NUMBER_FORMATS;return function(e,n){return null==e?e:Yn(e,t.PATTERNS[0],t.GROUP_SEP,t.DECIMAL_SEP,n)}}function zn(e){var t,n,r,i,o,a=0;for((n=e.indexOf(_o))>-1&&(e=e.replace(_o,"")),(r=e.search(/e/i))>0?(n<0&&(n=r),n+=+e.slice(r+1),e=e.substring(0,r)):n<0&&(n=e.length),r=0;e.charAt(r)==Ao;r++);if(r==(o=e.length))t=[0],n=1;else{for(o--;e.charAt(o)==Ao;)o--;for(n-=r,t=[],i=0;r<=o;r++,i++)t[i]=+e.charAt(r)}return n>ko&&(t=t.splice(0,ko-1),a=n-1,n=1),{d:t,e:a,i:n}}function Gn(e,t,n,r){var i=e.d,o=i.length-e.i;t=v(t)?Math.min(Math.max(n,o),r):+t;var a=t+e.i,s=i[a];if(a>0){i.splice(Math.max(e.i,a));for(var u=a;u<i.length;u++)i[u]=0}else{o=Math.max(0,o),e.i=1,i.length=Math.max(1,a=t+1),i[0]=0;for(var l=1;l<a;l++)i[l]=0}if(s>=5)if(a-1<0){for(var c=0;c>a;c--)i.unshift(0),e.i++;i.unshift(1),e.i++}else i[a-1]++;for(;o<Math.max(0,t);o++)i.push(0);var f=i.reduceRight(function(e,t,n,r){return t+=e,r[n]=t%10,Math.floor(t/10)},0);f&&(i.unshift(f),e.i++)}function Yn(e,t,n,r,i){if(!w(e)&&!x(e)||isNaN(e))return"";var o,a=!isFinite(e),s=!1,u=Math.abs(e)+"",l="";if(a)l="∞";else{o=zn(u),Gn(o,i,t.minFrac,t.maxFrac);var c=o.d,f=o.i,d=o.e,p=[];for(s=c.reduce(function(e,t){return e&&!t},!0);f<0;)c.unshift(0),f++;f>0?p=c.splice(f,c.length):(p=c,c=[0]);var h=[];for(c.length>=t.lgSize&&h.unshift(c.splice(-t.lgSize,c.length).join(""));c.length>t.gSize;)h.unshift(c.splice(-t.gSize,c.length).join(""));c.length&&h.unshift(c.join("")),l=h.join(n),p.length&&(l+=r+p.join("")),d&&(l+="e+"+d)}return e<0&&!s?t.negPre+l+t.negSuf:t.posPre+l+t.posSuf}function Xn(e,t,n,r){var i="";for((e<0||r&&e<=0)&&(r?e=-e+1:(e=-e,i="-")),e=""+e;e.length<t;)e=Ao+e;return n&&(e=e.substr(e.length-t)),i+e}function Jn(e,t,n,r,i){return n=n||0,function(o){var a=o["get"+e]();return(n>0||a>-n)&&(a+=n),0===a&&n==-12&&(a=12),Xn(a,t,r,i)}}function Qn(e,t,n){return function(r,i){var o=r["get"+e](),a=(n?"STANDALONE":"")+(t?"SHORT":""),s=Mr(a+e);return i[s][o]}}function Kn(e,t,n){var r=-1*n,i=r>=0?"+":"";return i+=Xn(Math[r>0?"floor":"ceil"](r/60),2)+Xn(Math.abs(r%60),2)}function Zn(e){var t=new Date(e,0,1).getDay();return new Date(e,0,(t<=4?5:12)-t)}function er(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate()+(4-e.getDay()))}function tr(e){return function(t){var n=Zn(t.getFullYear()),r=er(t),i=+r-+n,o=1+Math.round(i/6048e5);return Xn(o,e)}}function nr(e,t){return e.getHours()<12?t.AMPMS[0]:t.AMPMS[1]}function rr(e,t){return e.getFullYear()<=0?t.ERAS[0]:t.ERAS[1]}function ir(e,t){return e.getFullYear()<=0?t.ERANAMES[0]:t.ERANAMES[1]}function or(e){function t(e){var t;if(t=e.match(n)){var r=new Date(0),i=0,o=0,a=t[8]?r.setUTCFullYear:r.setFullYear,s=t[8]?r.setUTCHours:r.setHours;t[9]&&(i=f(t[9]+t[10]),o=f(t[9]+t[11])),a.call(r,f(t[1]),f(t[2])-1,f(t[3]));var u=f(t[4]||0)-i,l=f(t[5]||0)-o,c=f(t[6]||0),d=Math.round(1e3*parseFloat("0."+(t[7]||0)));return s.call(r,u,l,c,d),r}return e}var n=/^(\d{4})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/;return function(n,i,o){var a,s,u="",l=[];if(i=i||"mediumDate",i=e.DATETIME_FORMATS[i]||i,w(n)&&(n=Do.test(n)?f(n):t(n)),x(n)&&(n=new Date(n)),!E(n)||!isFinite(n.getTime()))return n;for(;i;)s=Oo.exec(i),s?(l=H(l,s,1),i=l.pop()):(l.push(i),i=null);var c=n.getTimezoneOffset();return o&&(c=G(o,c),n=X(n,o,!0)),r(l,function(t){a=No[t],u+=a?a(n,e.DATETIME_FORMATS,c):"''"===t?"'":t.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}}function ar(){return function(e,t){return v(t)&&(t=2),W(e,t)}}function sr(){return function(e,t,r){return t=Math.abs(Number(t))===1/0?Number(t):f(t),isNaN(t)?e:(x(e)&&(e=e.toString()),n(e)?(r=!r||isNaN(r)?0:f(r),r=r<0?Math.max(0,e.length+r):r,t>=0?ur(e,r,r+t):0===r?ur(e,t,e.length):ur(e,Math.max(0,r+t),r)):e)}}function ur(e,t,n){return w(e)?e.slice(t,n):Hr.call(e,t,n)}function lr(e){function r(t){return t.map(function(t){var n=1,r=h;if(T(t))r=t;else if(w(t)&&("+"!=t.charAt(0)&&"-"!=t.charAt(0)||(n="-"==t.charAt(0)?-1:1,t=t.substring(1)),""!==t&&(r=e(t),r.constant))){var i=r();r=function(e){return e[i]}}return{get:r,descending:n}})}function i(e){switch(typeof e){case"number":case"boolean":case"string":return!0;default:return!1}}function o(e){return T(e.valueOf)&&(e=e.valueOf(),i(e))?e:g(e)&&(e=e.toString(),i(e))?e:e}function a(e,t){var n=typeof e;return null===e?(n="string",e="null"):"object"===n&&(e=o(e)),{value:e,type:n,index:t}}function s(e,t){var n=0,r=e.type,i=t.type;if(r===i){var o=e.value,a=t.value;"string"===r?(o=o.toLowerCase(),a=a.toLowerCase()):"object"===r&&(y(o)&&(o=e.index),y(a)&&(a=t.index)),o!==a&&(n=o<a?-1:1)}else n=r<i?-1:1;return n}return function(e,i,o,u){function l(e,t){return{value:e,tieBreaker:{value:t,type:"number",index:t},predicateValues:f.map(function(n){return a(n.get(e),t)})}}function c(e,t){for(var n=0,r=f.length;n<r;n++){var i=p(e.predicateValues[n],t.predicateValues[n]);if(i)return i*f[n].descending*d}return p(e.tieBreaker,t.tieBreaker)*d}if(null==e)return e;if(!n(e))throw t("orderBy")("notarray","Expected array but received: {0}",e);Xr(i)||(i=[i]),0===i.length&&(i=["+"]);var f=r(i),d=o?-1:1,p=T(u)?u:s,h=Array.prototype.map.call(e,l);return h.sort(c),e=h.map(function(e){return e.value})}}function cr(e){return T(e)&&(e={link:e}),e.restrict=e.restrict||"AC",m(e)}function fr(e,t){e.$name=t}function dr(e,t,n,i,o){var a=this,s=[];a.$error={},a.$$success={},a.$pending=void 0,a.$name=o(t.name||t.ngForm||"")(n),a.$dirty=!1,a.$pristine=!0,a.$valid=!0,a.$invalid=!1,a.$submitted=!1,a.$$parentForm=Lo,a.$rollbackViewValue=function(){r(s,function(e){e.$rollbackViewValue()})},a.$commitViewValue=function(){r(s,function(e){e.$commitViewValue()})},a.$addControl=function(e){fe(e.$name,"input"),s.push(e),e.$name&&(a[e.$name]=e),e.$$parentForm=a},a.$$renameControl=function(e,t){var n=e.$name;a[n]===e&&delete a[n],a[t]=e,e.$name=t},a.$removeControl=function(e){e.$name&&a[e.$name]===e&&delete a[e.$name],
r(a.$pending,function(t,n){a.$setValidity(n,null,e)}),r(a.$error,function(t,n){a.$setValidity(n,null,e)}),r(a.$$success,function(t,n){a.$setValidity(n,null,e)}),V(s,e),e.$$parentForm=Lo},kr({ctrl:this,$element:e,set:function(e,t,n){var r=e[t];if(r){var i=r.indexOf(n);i===-1&&r.push(n)}else e[t]=[n]},unset:function(e,t,n){var r=e[t];r&&(V(r,n),0===r.length&&delete e[t])},$animate:i}),a.$setDirty=function(){i.removeClass(e,wa),i.addClass(e,xa),a.$dirty=!0,a.$pristine=!1,a.$$parentForm.$setDirty()},a.$setPristine=function(){i.setClass(e,wa,xa+" "+Vo),a.$dirty=!1,a.$pristine=!0,a.$submitted=!1,r(s,function(e){e.$setPristine()})},a.$setUntouched=function(){r(s,function(e){e.$setUntouched()})},a.$setSubmitted=function(){i.addClass(e,Vo),a.$submitted=!0,a.$$parentForm.$setSubmitted()}}function pr(e){e.$formatters.push(function(t){return e.$isEmpty(t)?t:t.toString()})}function hr(e,t,n,r,i,o){mr(e,t,n,r,i,o),pr(r)}function mr(e,t,n,r,i,o){var a=Ir(t[0].type);if(!i.android){var s=!1;t.on("compositionstart",function(){s=!0}),t.on("compositionend",function(){s=!1,l()})}var u,l=function(e){if(u&&(o.defer.cancel(u),u=null),!s){var i=t.val(),l=e&&e.type;"password"===a||n.ngTrim&&"false"===n.ngTrim||(i=Qr(i)),(r.$viewValue!==i||""===i&&r.$$hasNativeValidators)&&r.$setViewValue(i,l)}};if(i.hasEvent("input"))t.on("input",l);else{var c=function(e,t,n){u||(u=o.defer(function(){u=null,t&&t.value===n||l(e)}))};t.on("keydown",function(e){var t=e.keyCode;91===t||15<t&&t<19||37<=t&&t<=40||c(e,this,this.value)}),i.hasEvent("paste")&&t.on("paste cut",c)}t.on("change",l),Ko[a]&&r.$$hasNativeValidators&&a===n.type&&t.on(Qo,function(e){if(!u){var t=this[Or],n=t.badInput,r=t.typeMismatch;u=o.defer(function(){u=null,t.badInput===n&&t.typeMismatch===r||l(e)})}}),r.$render=function(){var e=r.$isEmpty(r.$viewValue)?"":r.$viewValue;t.val()!==e&&t.val(e)}}function gr(e,t){if(E(e))return e;if(w(e)){Yo.lastIndex=0;var n=Yo.exec(e);if(n){var r=+n[1],i=+n[2],o=0,a=0,s=0,u=0,l=Zn(r),c=7*(i-1);return t&&(o=t.getHours(),a=t.getMinutes(),s=t.getSeconds(),u=t.getMilliseconds()),new Date(r,0,l.getDate()+c,o,a,s,u)}}return NaN}function vr(e,t){return function(n,i){var o,a;if(E(n))return n;if(w(n)){if('"'==n.charAt(0)&&'"'==n.charAt(n.length-1)&&(n=n.substring(1,n.length-1)),Bo.test(n))return new Date(n);if(e.lastIndex=0,o=e.exec(n))return o.shift(),a=i?{yyyy:i.getFullYear(),MM:i.getMonth()+1,dd:i.getDate(),HH:i.getHours(),mm:i.getMinutes(),ss:i.getSeconds(),sss:i.getMilliseconds()/1e3}:{yyyy:1970,MM:1,dd:1,HH:0,mm:0,ss:0,sss:0},r(o,function(e,n){n<t.length&&(a[t[n]]=+e)}),new Date(a.yyyy,a.MM-1,a.dd,a.HH,a.mm,a.ss||0,1e3*a.sss||0)}return NaN}}function $r(e,t,n,r){return function(i,o,a,s,u,l,c){function f(e){return e&&!(e.getTime&&e.getTime()!==e.getTime())}function d(e){return $(e)&&!E(e)?n(e)||void 0:e}yr(i,o,a,s),mr(i,o,a,s,u,l);var p,h=s&&s.$options&&s.$options.timezone;if(s.$$parserName=e,s.$parsers.push(function(e){if(s.$isEmpty(e))return null;if(t.test(e)){var r=n(e,p);return h&&(r=X(r,h)),r}}),s.$formatters.push(function(e){if(e&&!E(e))throw _a("datefmt","Expected `{0}` to be a date",e);return f(e)?(p=e,p&&h&&(p=X(p,h,!0)),c("date")(e,r,h)):(p=null,"")}),$(a.min)||a.ngMin){var m;s.$validators.min=function(e){return!f(e)||v(m)||n(e)>=m},a.$observe("min",function(e){m=d(e),s.$validate()})}if($(a.max)||a.ngMax){var g;s.$validators.max=function(e){return!f(e)||v(g)||n(e)<=g},a.$observe("max",function(e){g=d(e),s.$validate()})}}}function yr(e,t,n,r){var i=t[0],o=r.$$hasNativeValidators=y(i.validity);o&&r.$parsers.push(function(e){var n=t.prop(Or)||{};return n.badInput||n.typeMismatch?void 0:e})}function br(e,t,n,r,i,o){if(yr(e,t,n,r),mr(e,t,n,r,i,o),r.$$parserName="number",r.$parsers.push(function(e){return r.$isEmpty(e)?null:Wo.test(e)?parseFloat(e):void 0}),r.$formatters.push(function(e){if(!r.$isEmpty(e)){if(!x(e))throw _a("numfmt","Expected `{0}` to be a number",e);e=e.toString()}return e}),$(n.min)||n.ngMin){var a;r.$validators.min=function(e){return r.$isEmpty(e)||v(a)||e>=a},n.$observe("min",function(e){$(e)&&!x(e)&&(e=parseFloat(e)),a=x(e)&&!isNaN(e)?e:void 0,r.$validate()})}if($(n.max)||n.ngMax){var s;r.$validators.max=function(e){return r.$isEmpty(e)||v(s)||e<=s},n.$observe("max",function(e){$(e)&&!x(e)&&(e=parseFloat(e)),s=x(e)&&!isNaN(e)?e:void 0,r.$validate()})}}function wr(e,t,n,r,i,o){mr(e,t,n,r,i,o),pr(r),r.$$parserName="url",r.$validators.url=function(e,t){var n=e||t;return r.$isEmpty(n)||qo.test(n)}}function xr(e,t,n,r,i,o){mr(e,t,n,r,i,o),pr(r),r.$$parserName="email",r.$validators.email=function(e,t){var n=e||t;return r.$isEmpty(n)||Uo.test(n)}}function Er(e,t,n,r){v(n.name)&&t.attr("name",a());var i=function(e){t[0].checked&&r.$setViewValue(n.value,e&&e.type)};t.on("click",i),r.$render=function(){var e=n.value;t[0].checked=e==r.$viewValue},n.$observe("value",r.$render)}function Tr(e,t,n,r,i){var o;if($(r)){if(o=e(r),!o.constant)throw _a("constexpr","Expected constant expression for `{0}`, but saw `{1}`.",n,r);return o(t)}return i}function Cr(e,t,n,r,i,o,a,s){var u=Tr(s,e,"ngTrueValue",n.ngTrueValue,!0),l=Tr(s,e,"ngFalseValue",n.ngFalseValue,!1),c=function(e){r.$setViewValue(t[0].checked,e&&e.type)};t.on("click",c),r.$render=function(){t[0].checked=r.$viewValue},r.$isEmpty=function(e){return e===!1},r.$formatters.push(function(e){return F(e,u)}),r.$parsers.push(function(e){return e?u:l})}function Sr(e,t){return e="ngClass"+e,["$animate",function(n){function i(e,t){var n=[];e:for(var r=0;r<e.length;r++){for(var i=e[r],o=0;o<t.length;o++)if(i==t[o])continue e;n.push(i)}return n}function o(e){var t=[];return Xr(e)?(r(e,function(e){t=t.concat(o(e))}),t):w(e)?e.split(" "):y(e)?(r(e,function(e,n){e&&(t=t.concat(n.split(" ")))}),t):e}return{restrict:"AC",link:function(a,s,u){function l(e){var t=f(e,1);u.$addClass(t)}function c(e){var t=f(e,-1);u.$removeClass(t)}function f(e,t){var n=s.data("$classCounts")||he(),i=[];return r(e,function(e){(t>0||n[e])&&(n[e]=(n[e]||0)+t,n[e]===+(t>0)&&i.push(e))}),s.data("$classCounts",n),i.join(" ")}function d(e,t){var r=i(t,e),o=i(e,t);r=f(r,1),o=f(o,-1),r&&r.length&&n.addClass(s,r),o&&o.length&&n.removeClass(s,o)}function p(e){if(t===!0||(1&a.$index)===t){var n=o(e||[]);if(h){if(!F(e,h)){var r=o(h);d(r,n)}}else l(n)}h=Xr(e)?e.map(function(e){return ge(e)}):ge(e)}var h;a.$watch(u[e],p,!0),u.$observe("class",function(t){p(a.$eval(u[e]))}),"ngClass"!==e&&a.$watch("$index",function(n,r){var i=1&n;if(i!==(1&r)){var s=o(a.$eval(u[e]));i===t?l(s):c(s)}})}}}]}function kr(e){function t(e,t,s){v(t)?n("$pending",e,s):r("$pending",e,s),O(t)?t?(c(a.$error,e,s),l(a.$$success,e,s)):(l(a.$error,e,s),c(a.$$success,e,s)):(c(a.$error,e,s),c(a.$$success,e,s)),a.$pending?(i(Ca,!0),a.$valid=a.$invalid=void 0,o("",null)):(i(Ca,!1),a.$valid=_r(a.$error),a.$invalid=!a.$valid,o("",a.$valid));var u;u=a.$pending&&a.$pending[e]?void 0:!a.$error[e]&&(!!a.$$success[e]||null),o(e,u),a.$$parentForm.$setValidity(e,u,a)}function n(e,t,n){a[e]||(a[e]={}),l(a[e],t,n)}function r(e,t,n){a[e]&&c(a[e],t,n),_r(a[e])&&(a[e]=void 0)}function i(e,t){t&&!u[e]?(f.addClass(s,e),u[e]=!0):!t&&u[e]&&(f.removeClass(s,e),u[e]=!1)}function o(e,t){e=e?"-"+se(e,"-"):"",i(ya+e,t===!0),i(ba+e,t===!1)}var a=e.ctrl,s=e.$element,u={},l=e.set,c=e.unset,f=e.$animate;u[ba]=!(u[ya]=s.hasClass(ya)),a.$setValidity=t}function _r(e){if(e)for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}function Ar(e){e[0].hasAttribute("selected")&&(e[0].selected=!0)}var Nr=/^\/(.+)\/([a-z]*)$/,Or="validity",Dr=Object.prototype.hasOwnProperty,Ir=function(e){return w(e)?e.toLowerCase():e},Mr=function(e){return w(e)?e.toUpperCase():e},jr=function(e){return w(e)?e.replace(/[A-Z]/g,function(e){return String.fromCharCode(32|e.charCodeAt(0))}):e},Pr=function(e){return w(e)?e.replace(/[a-z]/g,function(e){return String.fromCharCode(e.charCodeAt(0)&-33)}):e};"i"!=="I".toLowerCase()&&(Ir=jr,Mr=Pr);var Lr,Vr,Rr,Fr,Hr=[].slice,Br=[].splice,qr=[].push,Ur=Object.prototype.toString,Wr=Object.getPrototypeOf,zr=t("ng"),Gr=e.angular||(e.angular={}),Yr=0;Lr=e.document.documentMode,p.$inject=[],h.$inject=[];var Xr=Array.isArray,Jr=/^\[object (?:Uint8|Uint8Clamped|Uint16|Uint32|Int8|Int16|Int32|Float32|Float64)Array\]$/,Qr=function(e){return w(e)?e.trim():e},Kr=function(e){return e.replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")},Zr=function(){function t(){try{return new Function(""),!1}catch(e){return!0}}if(!$(Zr.rules)){var n=e.document.querySelector("[ng-csp]")||e.document.querySelector("[data-ng-csp]");if(n){var r=n.getAttribute("ng-csp")||n.getAttribute("data-ng-csp");Zr.rules={noUnsafeEval:!r||r.indexOf("no-unsafe-eval")!==-1,noInlineStyle:!r||r.indexOf("no-inline-style")!==-1}}else Zr.rules={noUnsafeEval:t(),noInlineStyle:!1}}return Zr.rules},ei=function(){if($(ei.name_))return ei.name_;var t,n,r,i,o=ni.length;for(n=0;n<o;++n)if(r=ni[n],t=e.document.querySelector("["+r.replace(":","\\:")+"jq]")){i=t.getAttribute(r+"jq");break}return ei.name_=i},ti=/:/g,ni=["ng-","data-ng-","ng:","x-ng-"],ri=/[A-Z]/g,ii=!1,oi=1,ai=2,si=3,ui=8,li=9,ci=11,fi={full:"1.5.8",major:1,minor:5,dot:8,codeName:"arbitrary-fallbacks"};Ae.expando="ng339";var di=Ae.cache={},pi=1,hi=function(e,t,n){e.addEventListener(t,n,!1)},mi=function(e,t,n){e.removeEventListener(t,n,!1)};Ae._data=function(e){return this.cache[e[this.expando]]||{}};var gi=/([\:\-\_]+(.))/g,vi=/^moz([A-Z])/,$i={mouseleave:"mouseout",mouseenter:"mouseover"},yi=t("jqLite"),bi=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,wi=/<|&#?\w+;/,xi=/<([\w:-]+)/,Ei=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,Ti={option:[1,'<select multiple="multiple">',"</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};Ti.optgroup=Ti.option,Ti.tbody=Ti.tfoot=Ti.colgroup=Ti.caption=Ti.thead,Ti.th=Ti.td;var Ci=e.Node.prototype.contains||function(e){return!!(16&this.compareDocumentPosition(e))},Si=Ae.prototype={ready:function(t){function n(){r||(r=!0,t())}var r=!1;"complete"===e.document.readyState?e.setTimeout(n):(this.on("DOMContentLoaded",n),Ae(e).on("load",n))},toString:function(){var e=[];return r(this,function(t){e.push(""+t)}),"["+e.join(", ")+"]"},eq:function(e){return Vr(e>=0?this[e]:this[this.length+e])},length:0,push:qr,sort:[].sort,splice:[].splice},ki={};r("multiple,selected,checked,disabled,readOnly,required,open".split(","),function(e){ki[Ir(e)]=e});var _i={};r("input,select,option,textarea,button,form,details".split(","),function(e){_i[e]=!0});var Ai={ngMinlength:"minlength",ngMaxlength:"maxlength",ngMin:"min",ngMax:"max",ngPattern:"pattern"};r({data:je,removeData:Ie,hasData:Te,cleanData:Ce},function(e,t){Ae[t]=e}),r({data:je,inheritedData:He,scope:function(e){return Vr.data(e,"$scope")||He(e.parentNode||e,["$isolateScope","$scope"])},isolateScope:function(e){return Vr.data(e,"$isolateScope")||Vr.data(e,"$isolateScopeNoTemplate")},controller:Fe,injector:function(e){return He(e,"$injector")},removeAttr:function(e,t){e.removeAttribute(t)},hasClass:Pe,css:function(e,t,n){return t=we(t),$(n)?void(e.style[t]=n):e.style[t]},attr:function(e,t,n){var r=e.nodeType;if(r!==si&&r!==ai&&r!==ui){var i=Ir(t);if(ki[i]){if(!$(n))return e[t]||(e.attributes.getNamedItem(t)||p).specified?i:void 0;n?(e[t]=!0,e.setAttribute(t,i)):(e[t]=!1,e.removeAttribute(i))}else if($(n))e.setAttribute(t,n);else if(e.getAttribute){var o=e.getAttribute(t,2);return null===o?void 0:o}}},prop:function(e,t,n){return $(n)?void(e[t]=n):e[t]},text:function(){function e(e,t){if(v(t)){var n=e.nodeType;return n===oi||n===si?e.textContent:""}e.textContent=t}return e.$dv="",e}(),val:function(e,t){if(v(t)){if(e.multiple&&"select"===L(e)){var n=[];return r(e.options,function(e){e.selected&&n.push(e.value||e.text)}),0===n.length?null:n}return e.value}e.value=t},html:function(e,t){return v(t)?e.innerHTML:(Oe(e,!0),void(e.innerHTML=t))},empty:Be},function(e,t){Ae.prototype[t]=function(t,n){var r,i,o=this.length;if(e!==Be&&v(2==e.length&&e!==Pe&&e!==Fe?t:n)){if(y(t)){for(r=0;r<o;r++)if(e===je)e(this[r],t);else for(i in t)e(this[r],i,t[i]);return this}for(var a=e.$dv,s=v(a)?Math.min(o,1):o,u=0;u<s;u++){var l=e(this[u],t,n);a=a?a+l:l}return a}for(r=0;r<o;r++)e(this[r],t,n);return this}}),r({removeData:Ie,on:function(e,t,n,r){if($(r))throw yi("onargs","jqLite#on() does not support the `selector` or `eventData` parameters");if(Ee(e)){var i=Me(e,!0),o=i.events,a=i.handle;a||(a=i.handle=Ge(e,o));for(var s=t.indexOf(" ")>=0?t.split(" "):[t],u=s.length,l=function(t,r,i){var s=o[t];s||(s=o[t]=[],s.specialHandlerWrapper=r,"$destroy"===t||i||hi(e,t,a)),s.push(n)};u--;)t=s[u],$i[t]?(l($i[t],Xe),l(t,void 0,!0)):l(t)}},off:De,one:function(e,t,n){e=Vr(e),e.on(t,function r(){e.off(t,n),e.off(t,r)}),e.on(t,n)},replaceWith:function(e,t){var n,i=e.parentNode;Oe(e),r(new Ae(t),function(t){n?i.insertBefore(t,n.nextSibling):i.replaceChild(t,e),n=t})},children:function(e){var t=[];return r(e.childNodes,function(e){e.nodeType===oi&&t.push(e)}),t},contents:function(e){return e.contentDocument||e.childNodes||[]},append:function(e,t){var n=e.nodeType;if(n===oi||n===ci){t=new Ae(t);for(var r=0,i=t.length;r<i;r++){var o=t[r];e.appendChild(o)}}},prepend:function(e,t){if(e.nodeType===oi){var n=e.firstChild;r(new Ae(t),function(t){e.insertBefore(t,n)})}},wrap:function(e,t){_e(e,Vr(t).eq(0).clone()[0])},remove:qe,detach:function(e){qe(e,!0)},after:function(e,t){var n=e,r=e.parentNode;t=new Ae(t);for(var i=0,o=t.length;i<o;i++){var a=t[i];r.insertBefore(a,n.nextSibling),n=a}},addClass:Ve,removeClass:Le,toggleClass:function(e,t,n){t&&r(t.split(" "),function(t){var r=n;v(r)&&(r=!Pe(e,t)),(r?Ve:Le)(e,t)})},parent:function(e){var t=e.parentNode;return t&&t.nodeType!==ci?t:null},next:function(e){return e.nextElementSibling},find:function(e,t){return e.getElementsByTagName?e.getElementsByTagName(t):[]},clone:Ne,triggerHandler:function(e,t,n){var i,o,a,s=t.type||t,u=Me(e),c=u&&u.events,f=c&&c[s];f&&(i={preventDefault:function(){this.defaultPrevented=!0},isDefaultPrevented:function(){return this.defaultPrevented===!0},stopImmediatePropagation:function(){this.immediatePropagationStopped=!0},isImmediatePropagationStopped:function(){return this.immediatePropagationStopped===!0},stopPropagation:p,type:s,target:e},t.type&&(i=l(i,t)),o=ge(f),a=n?[i].concat(n):[i],r(o,function(t){i.isImmediatePropagationStopped()||t.apply(e,a)}))}},function(e,t){Ae.prototype[t]=function(t,n,r){for(var i,o=0,a=this.length;o<a;o++)v(i)?(i=e(this[o],t,n,r),$(i)&&(i=Vr(i))):Re(i,e(this[o],t,n,r));return $(i)?i:this},Ae.prototype.bind=Ae.prototype.on,Ae.prototype.unbind=Ae.prototype.off}),Ke.prototype={put:function(e,t){this[Qe(e,this.nextUid)]=t},get:function(e){return this[Qe(e,this.nextUid)]},remove:function(e){var t=this[e=Qe(e,this.nextUid)];return delete this[e],t}};var Ni=[function(){this.$get=[function(){return Ke}]}],Oi=/^([^\(]+?)=>/,Di=/^[^\(]*\(\s*([^\)]*)\)/m,Ii=/,/,Mi=/^\s*(_?)(\S+?)\1\s*$/,ji=/((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm,Pi=t("$injector");rt.$$annotate=nt;var Li=t("$animate"),Vi=1,Ri="ng-animate",Fi=function(){this.$get=p},Hi=function(){var e=new Ke,t=[];this.$get=["$$AnimateRunner","$rootScope",function(n,i){function o(e,t,n){var i=!1;return t&&(t=w(t)?t.split(" "):Xr(t)?t:[],r(t,function(t){t&&(i=!0,e[t]=n)})),i}function a(){r(t,function(t){var n=e.get(t);if(n){var i=st(t.attr("class")),o="",a="";r(n,function(e,t){var n=!!i[t];e!==n&&(e?o+=(o.length?" ":"")+t:a+=(a.length?" ":"")+t)}),r(t,function(e){o&&Ve(e,o),a&&Le(e,a)}),e.remove(t)}}),t.length=0}function s(n,r,s){var u=e.get(n)||{},l=o(u,r,!0),c=o(u,s,!1);(l||c)&&(e.put(n,u),t.push(n),1===t.length&&i.$$postDigest(a))}return{enabled:p,on:p,off:p,pin:p,push:function(e,t,r,i){i&&i(),r=r||{},r.from&&e.css(r.from),r.to&&e.css(r.to),(r.addClass||r.removeClass)&&s(e,r.addClass,r.removeClass);var o=new n;return o.complete(),o}}}]},Bi=["$provide",function(e){var t=this;this.$$registeredAnimations=Object.create(null),this.register=function(n,r){if(n&&"."!==n.charAt(0))throw Li("notcsel","Expecting class selector starting with '.' got '{0}'.",n);var i=n+"-animation";t.$$registeredAnimations[n.substr(1)]=i,e.factory(i,r)},this.classNameFilter=function(e){if(1===arguments.length&&(this.$$classNameFilter=e instanceof RegExp?e:null,this.$$classNameFilter)){var t=new RegExp("(\\s+|\\/)"+Ri+"(\\s+|\\/)");if(t.test(this.$$classNameFilter.toString()))throw Li("nongcls",'$animateProvider.classNameFilter(regex) prohibits accepting a regex value which matches/contains the "{0}" CSS class.',Ri)}return this.$$classNameFilter},this.$get=["$$animateQueue",function(e){function t(e,t,n){if(n){var r=at(n);!r||r.parentNode||r.previousElementSibling||(n=null)}n?n.after(e):t.prepend(e)}return{on:e.on,off:e.off,pin:e.pin,enabled:e.enabled,cancel:function(e){e.end&&e.end()},enter:function(n,r,i,o){return r=r&&Vr(r),i=i&&Vr(i),r=r||i.parent(),t(n,r,i),e.push(n,"enter",ut(o))},move:function(n,r,i,o){return r=r&&Vr(r),i=i&&Vr(i),r=r||i.parent(),t(n,r,i),e.push(n,"move",ut(o))},leave:function(t,n){return e.push(t,"leave",ut(n),function(){t.remove()})},addClass:function(t,n,r){return r=ut(r),r.addClass=ot(r.addclass,n),e.push(t,"addClass",r)},removeClass:function(t,n,r){return r=ut(r),r.removeClass=ot(r.removeClass,n),e.push(t,"removeClass",r)},setClass:function(t,n,r,i){return i=ut(i),i.addClass=ot(i.addClass,n),i.removeClass=ot(i.removeClass,r),e.push(t,"setClass",i)},animate:function(t,n,r,i,o){return o=ut(o),o.from=o.from?l(o.from,n):n,o.to=o.to?l(o.to,r):r,i=i||"ng-inline-animate",o.tempClasses=ot(o.tempClasses,i),e.push(t,"animate",o)}}}]}],qi=function(){this.$get=["$$rAF",function(e){function t(t){n.push(t),n.length>1||e(function(){for(var e=0;e<n.length;e++)n[e]();n=[]})}var n=[];return function(){var e=!1;return t(function(){e=!0}),function(n){e?n():t(n)}}}]},Ui=function(){this.$get=["$q","$sniffer","$$animateAsyncRun","$document","$timeout",function(e,t,n,i,o){function a(e){this.setHost(e);var t=n(),r=function(e){o(e,0,!1)};this._doneCallbacks=[],this._tick=function(e){var n=i[0];n&&n.hidden?r(e):t(e)},this._state=0}var s=0,u=1,l=2;return a.chain=function(e,t){function n(){return r===e.length?void t(!0):void e[r](function(e){return e===!1?void t(!1):(r++,void n())})}var r=0;n()},a.all=function(e,t){function n(n){o=o&&n,++i===e.length&&t(o)}var i=0,o=!0;r(e,function(e){e.done(n)})},a.prototype={setHost:function(e){this.host=e||{}},done:function(e){this._state===l?e():this._doneCallbacks.push(e)},progress:p,getPromise:function(){if(!this.promise){var t=this;this.promise=e(function(e,n){t.done(function(t){t===!1?n():e()})})}return this.promise},then:function(e,t){return this.getPromise().then(e,t)},"catch":function(e){return this.getPromise()["catch"](e)},"finally":function(e){return this.getPromise()["finally"](e)},pause:function(){this.host.pause&&this.host.pause()},resume:function(){this.host.resume&&this.host.resume()},end:function(){this.host.end&&this.host.end(),this._resolve(!0)},cancel:function(){this.host.cancel&&this.host.cancel(),this._resolve(!1)},complete:function(e){var t=this;t._state===s&&(t._state=u,t._tick(function(){t._resolve(e)}))},_resolve:function(e){this._state!==l&&(r(this._doneCallbacks,function(t){t(e)}),this._doneCallbacks.length=0,this._state=l)}},a}]},Wi=function(){this.$get=["$$rAF","$q","$$AnimateRunner",function(e,t,n){return function(t,r){function i(){return e(function(){o(),s||u.complete(),s=!0}),u}function o(){a.addClass&&(t.addClass(a.addClass),a.addClass=null),a.removeClass&&(t.removeClass(a.removeClass),a.removeClass=null),a.to&&(t.css(a.to),a.to=null)}var a=r||{};a.$$prepared||(a=R(a)),a.cleanupStyles&&(a.from=a.to=null),a.from&&(t.css(a.from),a.from=null);var s,u=new n;return{start:i,end:i}}}]},zi=t("$compile"),Gi=new pt;ht.$inject=["$provide","$$sanitizeUriProvider"],mt.prototype.isFirstChange=function(){return this.previousValue===Gi};var Yi=/^((?:x|data)[\:\-_])/i,Xi=t("$controller"),Ji=/^(\S+)(\s+as\s+([\w$]+))?$/,Qi=function(){this.$get=["$document",function(e){return function(t){return t?!t.nodeType&&t instanceof Vr&&(t=t[0]):t=e[0].body,t.offsetWidth+1}}]},Ki="application/json",Zi={"Content-Type":Ki+";charset=utf-8"},eo=/^\[|^\{(?!\{)/,to={"[":/]$/,"{":/}$/},no=/^\)\]\}',?\n/,ro=t("$http"),io=function(e){return function(){throw ro("legacy","The method `{0}` on the promise returned from `$http` has been disabled.",e)}},oo=Gr.$interpolateMinErr=t("$interpolate");oo.throwNoconcat=function(e){throw oo("noconcat","Error while interpolating: {0}\nStrict Contextual Escaping disallows interpolations that concatenate multiple expressions when a trusted value is required.  See http://docs.angularjs.org/api/ng.$sce",e)},oo.interr=function(e,t){return oo("interr","Can't interpolate: {0}\n{1}",e,t.toString())};var ao=function(){this.$get=["$window",function(e){function t(e){var t=function(e){t.data=e,t.called=!0};return t.id=e,t}var n=e.angular.callbacks,r={};return{createCallback:function(e){var i="_"+(n.$$counter++).toString(36),o="angular.callbacks."+i,a=t(i);return r[o]=n[i]=a,o},wasCalled:function(e){return r[e].called},getResponse:function(e){return r[e].data},removeCallback:function(e){var t=r[e];delete n[t.id],delete r[e]}}}]},so=/^([^\?#]*)(\?([^#]*))?(#(.*))?$/,uo={http:80,https:443,ftp:21},lo=t("$location"),co={$$absUrl:"",$$html5:!1,$$replace:!1,absUrl:Jt("$$absUrl"),url:function(e){if(v(e))return this.$$url;var t=so.exec(e);return(t[1]||""===e)&&this.path(decodeURIComponent(t[1])),(t[2]||t[1]||""===e)&&this.search(t[3]||""),this.hash(t[5]||""),this},protocol:Jt("$$protocol"),host:Jt("$$host"),port:Jt("$$port"),path:Qt("$$path",function(e){return e=null!==e?e.toString():"","/"==e.charAt(0)?e:"/"+e}),search:function(e,t){switch(arguments.length){case 0:return this.$$search;case 1:if(w(e)||x(e))e=e.toString(),this.$$search=K(e);else{if(!y(e))throw lo("isrcharg","The first argument of the `$location#search()` call must be a string or an object.");e=R(e,{}),r(e,function(t,n){null==t&&delete e[n]}),this.$$search=e}break;default:v(t)||null===t?delete this.$$search[e]:this.$$search[e]=t}return this.$$compose(),this},hash:Qt("$$hash",function(e){return null!==e?e.toString():""}),replace:function(){return this.$$replace=!0,this}};r([Xt,Yt,Gt],function(e){e.prototype=Object.create(co),e.prototype.state=function(t){if(!arguments.length)return this.$$state;if(e!==Gt||!this.$$html5)throw lo("nostate","History API state support is available only in HTML5 mode and only in browsers supporting HTML5 History API");return this.$$state=v(t)?null:t,this}});var fo=t("$parse"),po=Function.prototype.call,ho=Function.prototype.apply,mo=Function.prototype.bind,go=he();r("+ - * / % === !== == != < > <= >= && || ! = |".split(" "),function(e){go[e]=!0});var vo={n:"\n",f:"\f",r:"\r",t:"\t",v:"\x0B","'":"'",'"':'"'},$o=function(e){this.options=e};$o.prototype={constructor:$o,lex:function(e){for(this.text=e,this.index=0,this.tokens=[];this.index<this.text.length;){var t=this.text.charAt(this.index);if('"'===t||"'"===t)this.readString(t);else if(this.isNumber(t)||"."===t&&this.isNumber(this.peek()))this.readNumber();else if(this.isIdentifierStart(this.peekMultichar()))this.readIdent();else if(this.is(t,"(){}[].,;:?"))this.tokens.push({index:this.index,text:t}),this.index++;else if(this.isWhitespace(t))this.index++;else{var n=t+this.peek(),r=n+this.peek(2),i=go[t],o=go[n],a=go[r];if(i||o||a){var s=a?r:o?n:t;this.tokens.push({index:this.index,text:s,operator:!0}),this.index+=s.length}else this.throwError("Unexpected next character ",this.index,this.index+1)}}return this.tokens},is:function(e,t){return t.indexOf(e)!==-1},peek:function(e){var t=e||1;return this.index+t<this.text.length&&this.text.charAt(this.index+t)},isNumber:function(e){return"0"<=e&&e<="9"&&"string"==typeof e},isWhitespace:function(e){return" "===e||"\r"===e||"\t"===e||"\n"===e||"\x0B"===e||" "===e},isIdentifierStart:function(e){return this.options.isIdentifierStart?this.options.isIdentifierStart(e,this.codePointAt(e)):this.isValidIdentifierStart(e)},isValidIdentifierStart:function(e){return"a"<=e&&e<="z"||"A"<=e&&e<="Z"||"_"===e||"$"===e},isIdentifierContinue:function(e){return this.options.isIdentifierContinue?this.options.isIdentifierContinue(e,this.codePointAt(e)):this.isValidIdentifierContinue(e)},isValidIdentifierContinue:function(e,t){return this.isValidIdentifierStart(e,t)||this.isNumber(e)},codePointAt:function(e){return 1===e.length?e.charCodeAt(0):(e.charCodeAt(0)<<10)+e.charCodeAt(1)-56613888},peekMultichar:function(){var e=this.text.charAt(this.index),t=this.peek();if(!t)return e;var n=e.charCodeAt(0),r=t.charCodeAt(0);return n>=55296&&n<=56319&&r>=56320&&r<=57343?e+t:e},isExpOperator:function(e){return"-"===e||"+"===e||this.isNumber(e)},throwError:function(e,t,n){n=n||this.index;var r=$(t)?"s "+t+"-"+this.index+" ["+this.text.substring(t,n)+"]":" "+n;throw fo("lexerr","Lexer Error: {0} at column{1} in expression [{2}].",e,r,this.text)},readNumber:function(){for(var e="",t=this.index;this.index<this.text.length;){var n=Ir(this.text.charAt(this.index));if("."==n||this.isNumber(n))e+=n;else{var r=this.peek();if("e"==n&&this.isExpOperator(r))e+=n;else if(this.isExpOperator(n)&&r&&this.isNumber(r)&&"e"==e.charAt(e.length-1))e+=n;else{if(!this.isExpOperator(n)||r&&this.isNumber(r)||"e"!=e.charAt(e.length-1))break;this.throwError("Invalid exponent")}}this.index++}this.tokens.push({index:t,text:e,constant:!0,value:Number(e)})},readIdent:function(){var e=this.index;for(this.index+=this.peekMultichar().length;this.index<this.text.length;){var t=this.peekMultichar();if(!this.isIdentifierContinue(t))break;this.index+=t.length}this.tokens.push({index:e,text:this.text.slice(e,this.index),identifier:!0})},readString:function(e){var t=this.index;this.index++;for(var n="",r=e,i=!1;this.index<this.text.length;){var o=this.text.charAt(this.index);if(r+=o,i){if("u"===o){var a=this.text.substring(this.index+1,this.index+5);a.match(/[\da-f]{4}/i)||this.throwError("Invalid unicode escape [\\u"+a+"]"),this.index+=4,n+=String.fromCharCode(parseInt(a,16))}else{var s=vo[o];n+=s||o}i=!1}else if("\\"===o)i=!0;else{if(o===e)return this.index++,void this.tokens.push({index:t,text:r,constant:!0,value:n});n+=o}this.index++}this.throwError("Unterminated quote",t)}};var yo=function(e,t){this.lexer=e,this.options=t};yo.Program="Program",yo.ExpressionStatement="ExpressionStatement",yo.AssignmentExpression="AssignmentExpression",yo.ConditionalExpression="ConditionalExpression",yo.LogicalExpression="LogicalExpression",yo.BinaryExpression="BinaryExpression",yo.UnaryExpression="UnaryExpression",yo.CallExpression="CallExpression",yo.MemberExpression="MemberExpression",yo.Identifier="Identifier",yo.Literal="Literal",yo.ArrayExpression="ArrayExpression",yo.Property="Property",yo.ObjectExpression="ObjectExpression",yo.ThisExpression="ThisExpression",yo.LocalsExpression="LocalsExpression",yo.NGValueParameter="NGValueParameter",yo.prototype={ast:function(e){this.text=e,this.tokens=this.lexer.lex(e);var t=this.program();return 0!==this.tokens.length&&this.throwError("is an unexpected token",this.tokens[0]),t},program:function(){for(var e=[];;)if(this.tokens.length>0&&!this.peek("}",")",";","]")&&e.push(this.expressionStatement()),!this.expect(";"))return{type:yo.Program,body:e}},expressionStatement:function(){return{type:yo.ExpressionStatement,expression:this.filterChain()}},filterChain:function(){for(var e,t=this.expression();e=this.expect("|");)t=this.filter(t);return t},expression:function(){return this.assignment()},assignment:function(){var e=this.ternary();return this.expect("=")&&(e={type:yo.AssignmentExpression,left:e,right:this.assignment(),operator:"="}),e},ternary:function(){var e,t,n=this.logicalOR();return this.expect("?")&&(e=this.expression(),this.consume(":"))?(t=this.expression(),{type:yo.ConditionalExpression,test:n,alternate:e,consequent:t}):n},logicalOR:function(){for(var e=this.logicalAND();this.expect("||");)e={type:yo.LogicalExpression,operator:"||",left:e,right:this.logicalAND()};return e},logicalAND:function(){for(var e=this.equality();this.expect("&&");)e={type:yo.LogicalExpression,operator:"&&",left:e,right:this.equality()};return e},equality:function(){for(var e,t=this.relational();e=this.expect("==","!=","===","!==");)t={type:yo.BinaryExpression,operator:e.text,left:t,right:this.relational()};return t},relational:function(){for(var e,t=this.additive();e=this.expect("<",">","<=",">=");)t={type:yo.BinaryExpression,operator:e.text,left:t,right:this.additive()};return t},additive:function(){for(var e,t=this.multiplicative();e=this.expect("+","-");)t={type:yo.BinaryExpression,operator:e.text,left:t,right:this.multiplicative()};return t},multiplicative:function(){for(var e,t=this.unary();e=this.expect("*","/","%");)t={type:yo.BinaryExpression,operator:e.text,left:t,right:this.unary()};return t},unary:function(){var e;return(e=this.expect("+","-","!"))?{type:yo.UnaryExpression,operator:e.text,prefix:!0,argument:this.unary()}:this.primary()},primary:function(){var e;this.expect("(")?(e=this.filterChain(),this.consume(")")):this.expect("[")?e=this.arrayDeclaration():this.expect("{")?e=this.object():this.selfReferential.hasOwnProperty(this.peek().text)?e=R(this.selfReferential[this.consume().text]):this.options.literals.hasOwnProperty(this.peek().text)?e={type:yo.Literal,value:this.options.literals[this.consume().text]}:this.peek().identifier?e=this.identifier():this.peek().constant?e=this.constant():this.throwError("not a primary expression",this.peek());for(var t;t=this.expect("(","[",".");)"("===t.text?(e={type:yo.CallExpression,callee:e,arguments:this.parseArguments()},this.consume(")")):"["===t.text?(e={type:yo.MemberExpression,object:e,property:this.expression(),computed:!0},this.consume("]")):"."===t.text?e={type:yo.MemberExpression,object:e,property:this.identifier(),computed:!1}:this.throwError("IMPOSSIBLE");return e},filter:function(e){for(var t=[e],n={type:yo.CallExpression,callee:this.identifier(),arguments:t,filter:!0};this.expect(":");)t.push(this.expression());return n},parseArguments:function(){var e=[];if(")"!==this.peekToken().text)do e.push(this.filterChain());while(this.expect(","));return e},identifier:function(){var e=this.consume();return e.identifier||this.throwError("is not a valid identifier",e),{type:yo.Identifier,name:e.text}},constant:function(){return{type:yo.Literal,value:this.consume().value}},arrayDeclaration:function(){var e=[];if("]"!==this.peekToken().text)do{if(this.peek("]"))break;e.push(this.expression())}while(this.expect(","));return this.consume("]"),{type:yo.ArrayExpression,elements:e}},object:function(){var e,t=[];if("}"!==this.peekToken().text)do{if(this.peek("}"))break;e={type:yo.Property,kind:"init"},this.peek().constant?(e.key=this.constant(),e.computed=!1,this.consume(":"),e.value=this.expression()):this.peek().identifier?(e.key=this.identifier(),e.computed=!1,this.peek(":")?(this.consume(":"),e.value=this.expression()):e.value=e.key):this.peek("[")?(this.consume("["),e.key=this.expression(),this.consume("]"),e.computed=!0,this.consume(":"),e.value=this.expression()):this.throwError("invalid key",this.peek()),t.push(e)}while(this.expect(","));return this.consume("}"),{type:yo.ObjectExpression,properties:t}},throwError:function(e,t){throw fo("syntax","Syntax Error: Token '{0}' {1} at column {2} of the expression [{3}] starting at [{4}].",t.text,e,t.index+1,this.text,this.text.substring(t.index))},consume:function(e){if(0===this.tokens.length)throw fo("ueoe","Unexpected end of expression: {0}",this.text);var t=this.expect(e);return t||this.throwError("is unexpected, expecting ["+e+"]",this.peek()),t},peekToken:function(){if(0===this.tokens.length)throw fo("ueoe","Unexpected end of expression: {0}",this.text);return this.tokens[0]},peek:function(e,t,n,r){return this.peekAhead(0,e,t,n,r)},peekAhead:function(e,t,n,r,i){if(this.tokens.length>e){var o=this.tokens[e],a=o.text;if(a===t||a===n||a===r||a===i||!t&&!n&&!r&&!i)return o;
}return!1},expect:function(e,t,n,r){var i=this.peek(e,t,n,r);return!!i&&(this.tokens.shift(),i)},selfReferential:{"this":{type:yo.ThisExpression},$locals:{type:yo.LocalsExpression}}},mn.prototype={compile:function(e,t){var n=this,i=this.astBuilder.ast(e);this.state={nextId:0,filters:{},expensiveChecks:t,fn:{vars:[],body:[],own:{}},assign:{vars:[],body:[],own:{}},inputs:[]},ln(i,n.$filter);var o,a="";if(this.stage="assign",o=dn(i)){this.state.computing="assign";var s=this.nextId();this.recurse(o,s),this.return_(s),a="fn.assign="+this.generateFunction("assign","s,v,l")}var u=cn(i.body);n.stage="inputs",r(u,function(e,t){var r="fn"+t;n.state[r]={vars:[],body:[],own:{}},n.state.computing=r;var i=n.nextId();n.recurse(e,i),n.return_(i),n.state.inputs.push(r),e.watchId=t}),this.state.computing="fn",this.stage="main",this.recurse(i);var l='"'+this.USE+" "+this.STRICT+'";\n'+this.filterPrefix()+"var fn="+this.generateFunction("fn","s,l,a,i")+a+this.watchFns()+"return fn;",c=new Function("$filter","ensureSafeMemberName","ensureSafeObject","ensureSafeFunction","getStringValue","ensureSafeAssignContext","ifDefined","plus","text",l)(this.$filter,en,nn,rn,tn,on,an,sn,e);return this.state=this.stage=void 0,c.literal=pn(i),c.constant=hn(i),c},USE:"use",STRICT:"strict",watchFns:function(){var e=[],t=this.state.inputs,n=this;return r(t,function(t){e.push("var "+t+"="+n.generateFunction(t,"s"))}),t.length&&e.push("fn.inputs=["+t.join(",")+"];"),e.join("")},generateFunction:function(e,t){return"function("+t+"){"+this.varsPrefix(e)+this.body(e)+"};"},filterPrefix:function(){var e=[],t=this;return r(this.state.filters,function(n,r){e.push(n+"=$filter("+t.escape(r)+")")}),e.length?"var "+e.join(",")+";":""},varsPrefix:function(e){return this.state[e].vars.length?"var "+this.state[e].vars.join(",")+";":""},body:function(e){return this.state[e].body.join("")},recurse:function(e,t,n,i,o,a){var s,u,l,c,f,d=this;if(i=i||p,!a&&$(e.watchId))return t=t||this.nextId(),void this.if_("i",this.lazyAssign(t,this.computedMember("i",e.watchId)),this.lazyRecurse(e,t,n,i,o,!0));switch(e.type){case yo.Program:r(e.body,function(t,n){d.recurse(t.expression,void 0,void 0,function(e){u=e}),n!==e.body.length-1?d.current().body.push(u,";"):d.return_(u)});break;case yo.Literal:c=this.escape(e.value),this.assign(t,c),i(c);break;case yo.UnaryExpression:this.recurse(e.argument,void 0,void 0,function(e){u=e}),c=e.operator+"("+this.ifDefined(u,0)+")",this.assign(t,c),i(c);break;case yo.BinaryExpression:this.recurse(e.left,void 0,void 0,function(e){s=e}),this.recurse(e.right,void 0,void 0,function(e){u=e}),c="+"===e.operator?this.plus(s,u):"-"===e.operator?this.ifDefined(s,0)+e.operator+this.ifDefined(u,0):"("+s+")"+e.operator+"("+u+")",this.assign(t,c),i(c);break;case yo.LogicalExpression:t=t||this.nextId(),d.recurse(e.left,t),d.if_("&&"===e.operator?t:d.not(t),d.lazyRecurse(e.right,t)),i(t);break;case yo.ConditionalExpression:t=t||this.nextId(),d.recurse(e.test,t),d.if_(t,d.lazyRecurse(e.alternate,t),d.lazyRecurse(e.consequent,t)),i(t);break;case yo.Identifier:t=t||this.nextId(),n&&(n.context="inputs"===d.stage?"s":this.assign(this.nextId(),this.getHasOwnProperty("l",e.name)+"?l:s"),n.computed=!1,n.name=e.name),en(e.name),d.if_("inputs"===d.stage||d.not(d.getHasOwnProperty("l",e.name)),function(){d.if_("inputs"===d.stage||"s",function(){o&&1!==o&&d.if_(d.not(d.nonComputedMember("s",e.name)),d.lazyAssign(d.nonComputedMember("s",e.name),"{}")),d.assign(t,d.nonComputedMember("s",e.name))})},t&&d.lazyAssign(t,d.nonComputedMember("l",e.name))),(d.state.expensiveChecks||vn(e.name))&&d.addEnsureSafeObject(t),i(t);break;case yo.MemberExpression:s=n&&(n.context=this.nextId())||this.nextId(),t=t||this.nextId(),d.recurse(e.object,s,void 0,function(){d.if_(d.notNull(s),function(){o&&1!==o&&d.addEnsureSafeAssignContext(s),e.computed?(u=d.nextId(),d.recurse(e.property,u),d.getStringValue(u),d.addEnsureSafeMemberName(u),o&&1!==o&&d.if_(d.not(d.computedMember(s,u)),d.lazyAssign(d.computedMember(s,u),"{}")),c=d.ensureSafeObject(d.computedMember(s,u)),d.assign(t,c),n&&(n.computed=!0,n.name=u)):(en(e.property.name),o&&1!==o&&d.if_(d.not(d.nonComputedMember(s,e.property.name)),d.lazyAssign(d.nonComputedMember(s,e.property.name),"{}")),c=d.nonComputedMember(s,e.property.name),(d.state.expensiveChecks||vn(e.property.name))&&(c=d.ensureSafeObject(c)),d.assign(t,c),n&&(n.computed=!1,n.name=e.property.name))},function(){d.assign(t,"undefined")}),i(t)},!!o);break;case yo.CallExpression:t=t||this.nextId(),e.filter?(u=d.filter(e.callee.name),l=[],r(e.arguments,function(e){var t=d.nextId();d.recurse(e,t),l.push(t)}),c=u+"("+l.join(",")+")",d.assign(t,c),i(t)):(u=d.nextId(),s={},l=[],d.recurse(e.callee,u,s,function(){d.if_(d.notNull(u),function(){d.addEnsureSafeFunction(u),r(e.arguments,function(e){d.recurse(e,d.nextId(),void 0,function(e){l.push(d.ensureSafeObject(e))})}),s.name?(d.state.expensiveChecks||d.addEnsureSafeObject(s.context),c=d.member(s.context,s.name,s.computed)+"("+l.join(",")+")"):c=u+"("+l.join(",")+")",c=d.ensureSafeObject(c),d.assign(t,c)},function(){d.assign(t,"undefined")}),i(t)}));break;case yo.AssignmentExpression:if(u=this.nextId(),s={},!fn(e.left))throw fo("lval","Trying to assign a value to a non l-value");this.recurse(e.left,void 0,s,function(){d.if_(d.notNull(s.context),function(){d.recurse(e.right,u),d.addEnsureSafeObject(d.member(s.context,s.name,s.computed)),d.addEnsureSafeAssignContext(s.context),c=d.member(s.context,s.name,s.computed)+e.operator+u,d.assign(t,c),i(t||c)})},1);break;case yo.ArrayExpression:l=[],r(e.elements,function(e){d.recurse(e,d.nextId(),void 0,function(e){l.push(e)})}),c="["+l.join(",")+"]",this.assign(t,c),i(c);break;case yo.ObjectExpression:l=[],f=!1,r(e.properties,function(e){e.computed&&(f=!0)}),f?(t=t||this.nextId(),this.assign(t,"{}"),r(e.properties,function(e){e.computed?(s=d.nextId(),d.recurse(e.key,s)):s=e.key.type===yo.Identifier?e.key.name:""+e.key.value,u=d.nextId(),d.recurse(e.value,u),d.assign(d.member(t,s,e.computed),u)})):(r(e.properties,function(t){d.recurse(t.value,e.constant?void 0:d.nextId(),void 0,function(e){l.push(d.escape(t.key.type===yo.Identifier?t.key.name:""+t.key.value)+":"+e)})}),c="{"+l.join(",")+"}",this.assign(t,c)),i(t||c);break;case yo.ThisExpression:this.assign(t,"s"),i("s");break;case yo.LocalsExpression:this.assign(t,"l"),i("l");break;case yo.NGValueParameter:this.assign(t,"v"),i("v")}},getHasOwnProperty:function(e,t){var n=e+"."+t,r=this.current().own;return r.hasOwnProperty(n)||(r[n]=this.nextId(!1,e+"&&("+this.escape(t)+" in "+e+")")),r[n]},assign:function(e,t){if(e)return this.current().body.push(e,"=",t,";"),e},filter:function(e){return this.state.filters.hasOwnProperty(e)||(this.state.filters[e]=this.nextId(!0)),this.state.filters[e]},ifDefined:function(e,t){return"ifDefined("+e+","+this.escape(t)+")"},plus:function(e,t){return"plus("+e+","+t+")"},return_:function(e){this.current().body.push("return ",e,";")},if_:function(e,t,n){if(e===!0)t();else{var r=this.current().body;r.push("if(",e,"){"),t(),r.push("}"),n&&(r.push("else{"),n(),r.push("}"))}},not:function(e){return"!("+e+")"},notNull:function(e){return e+"!=null"},nonComputedMember:function(e,t){var n=/[$_a-zA-Z][$_a-zA-Z0-9]*/,r=/[^$_a-zA-Z0-9]/g;return n.test(t)?e+"."+t:e+'["'+t.replace(r,this.stringEscapeFn)+'"]'},computedMember:function(e,t){return e+"["+t+"]"},member:function(e,t,n){return n?this.computedMember(e,t):this.nonComputedMember(e,t)},addEnsureSafeObject:function(e){this.current().body.push(this.ensureSafeObject(e),";")},addEnsureSafeMemberName:function(e){this.current().body.push(this.ensureSafeMemberName(e),";")},addEnsureSafeFunction:function(e){this.current().body.push(this.ensureSafeFunction(e),";")},addEnsureSafeAssignContext:function(e){this.current().body.push(this.ensureSafeAssignContext(e),";")},ensureSafeObject:function(e){return"ensureSafeObject("+e+",text)"},ensureSafeMemberName:function(e){return"ensureSafeMemberName("+e+",text)"},ensureSafeFunction:function(e){return"ensureSafeFunction("+e+",text)"},getStringValue:function(e){this.assign(e,"getStringValue("+e+")")},ensureSafeAssignContext:function(e){return"ensureSafeAssignContext("+e+",text)"},lazyRecurse:function(e,t,n,r,i,o){var a=this;return function(){a.recurse(e,t,n,r,i,o)}},lazyAssign:function(e,t){var n=this;return function(){n.assign(e,t)}},stringEscapeRegex:/[^ a-zA-Z0-9]/g,stringEscapeFn:function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)},escape:function(e){if(w(e))return"'"+e.replace(this.stringEscapeRegex,this.stringEscapeFn)+"'";if(x(e))return e.toString();if(e===!0)return"true";if(e===!1)return"false";if(null===e)return"null";if("undefined"==typeof e)return"undefined";throw fo("esc","IMPOSSIBLE")},nextId:function(e,t){var n="v"+this.state.nextId++;return e||this.current().vars.push(n+(t?"="+t:"")),n},current:function(){return this.state[this.state.computing]}},gn.prototype={compile:function(e,t){var n=this,i=this.astBuilder.ast(e);this.expression=e,this.expensiveChecks=t,ln(i,n.$filter);var o,a;(o=dn(i))&&(a=this.recurse(o));var s,u=cn(i.body);u&&(s=[],r(u,function(e,t){var r=n.recurse(e);e.input=r,s.push(r),e.watchId=t}));var l=[];r(i.body,function(e){l.push(n.recurse(e.expression))});var c=0===i.body.length?p:1===i.body.length?l[0]:function(e,t){var n;return r(l,function(r){n=r(e,t)}),n};return a&&(c.assign=function(e,t,n){return a(e,n,t)}),s&&(c.inputs=s),c.literal=pn(i),c.constant=hn(i),c},recurse:function(e,t,n){var i,o,a,s=this;if(e.input)return this.inputs(e.input,e.watchId);switch(e.type){case yo.Literal:return this.value(e.value,t);case yo.UnaryExpression:return o=this.recurse(e.argument),this["unary"+e.operator](o,t);case yo.BinaryExpression:return i=this.recurse(e.left),o=this.recurse(e.right),this["binary"+e.operator](i,o,t);case yo.LogicalExpression:return i=this.recurse(e.left),o=this.recurse(e.right),this["binary"+e.operator](i,o,t);case yo.ConditionalExpression:return this["ternary?:"](this.recurse(e.test),this.recurse(e.alternate),this.recurse(e.consequent),t);case yo.Identifier:return en(e.name,s.expression),s.identifier(e.name,s.expensiveChecks||vn(e.name),t,n,s.expression);case yo.MemberExpression:return i=this.recurse(e.object,!1,!!n),e.computed||(en(e.property.name,s.expression),o=e.property.name),e.computed&&(o=this.recurse(e.property)),e.computed?this.computedMember(i,o,t,n,s.expression):this.nonComputedMember(i,o,s.expensiveChecks,t,n,s.expression);case yo.CallExpression:return a=[],r(e.arguments,function(e){a.push(s.recurse(e))}),e.filter&&(o=this.$filter(e.callee.name)),e.filter||(o=this.recurse(e.callee,!0)),e.filter?function(e,n,r,i){for(var s=[],u=0;u<a.length;++u)s.push(a[u](e,n,r,i));var l=o.apply(void 0,s,i);return t?{context:void 0,name:void 0,value:l}:l}:function(e,n,r,i){var u,l=o(e,n,r,i);if(null!=l.value){nn(l.context,s.expression),rn(l.value,s.expression);for(var c=[],f=0;f<a.length;++f)c.push(nn(a[f](e,n,r,i),s.expression));u=nn(l.value.apply(l.context,c),s.expression)}return t?{value:u}:u};case yo.AssignmentExpression:return i=this.recurse(e.left,!0,1),o=this.recurse(e.right),function(e,n,r,a){var u=i(e,n,r,a),l=o(e,n,r,a);return nn(u.value,s.expression),on(u.context),u.context[u.name]=l,t?{value:l}:l};case yo.ArrayExpression:return a=[],r(e.elements,function(e){a.push(s.recurse(e))}),function(e,n,r,i){for(var o=[],s=0;s<a.length;++s)o.push(a[s](e,n,r,i));return t?{value:o}:o};case yo.ObjectExpression:return a=[],r(e.properties,function(e){e.computed?a.push({key:s.recurse(e.key),computed:!0,value:s.recurse(e.value)}):a.push({key:e.key.type===yo.Identifier?e.key.name:""+e.key.value,computed:!1,value:s.recurse(e.value)})}),function(e,n,r,i){for(var o={},s=0;s<a.length;++s)a[s].computed?o[a[s].key(e,n,r,i)]=a[s].value(e,n,r,i):o[a[s].key]=a[s].value(e,n,r,i);return t?{value:o}:o};case yo.ThisExpression:return function(e){return t?{value:e}:e};case yo.LocalsExpression:return function(e,n){return t?{value:n}:n};case yo.NGValueParameter:return function(e,n,r){return t?{value:r}:r}}},"unary+":function(e,t){return function(n,r,i,o){var a=e(n,r,i,o);return a=$(a)?+a:0,t?{value:a}:a}},"unary-":function(e,t){return function(n,r,i,o){var a=e(n,r,i,o);return a=$(a)?-a:0,t?{value:a}:a}},"unary!":function(e,t){return function(n,r,i,o){var a=!e(n,r,i,o);return t?{value:a}:a}},"binary+":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a),u=t(r,i,o,a),l=sn(s,u);return n?{value:l}:l}},"binary-":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a),u=t(r,i,o,a),l=($(s)?s:0)-($(u)?u:0);return n?{value:l}:l}},"binary*":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)*t(r,i,o,a);return n?{value:s}:s}},"binary/":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)/t(r,i,o,a);return n?{value:s}:s}},"binary%":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)%t(r,i,o,a);return n?{value:s}:s}},"binary===":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)===t(r,i,o,a);return n?{value:s}:s}},"binary!==":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)!==t(r,i,o,a);return n?{value:s}:s}},"binary==":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)==t(r,i,o,a);return n?{value:s}:s}},"binary!=":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)!=t(r,i,o,a);return n?{value:s}:s}},"binary<":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)<t(r,i,o,a);return n?{value:s}:s}},"binary>":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)>t(r,i,o,a);return n?{value:s}:s}},"binary<=":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)<=t(r,i,o,a);return n?{value:s}:s}},"binary>=":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)>=t(r,i,o,a);return n?{value:s}:s}},"binary&&":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)&&t(r,i,o,a);return n?{value:s}:s}},"binary||":function(e,t,n){return function(r,i,o,a){var s=e(r,i,o,a)||t(r,i,o,a);return n?{value:s}:s}},"ternary?:":function(e,t,n,r){return function(i,o,a,s){var u=e(i,o,a,s)?t(i,o,a,s):n(i,o,a,s);return r?{value:u}:u}},value:function(e,t){return function(){return t?{context:void 0,name:void 0,value:e}:e}},identifier:function(e,t,n,r,i){return function(o,a,s,u){var l=a&&e in a?a:o;r&&1!==r&&l&&!l[e]&&(l[e]={});var c=l?l[e]:void 0;return t&&nn(c,i),n?{context:l,name:e,value:c}:c}},computedMember:function(e,t,n,r,i){return function(o,a,s,u){var l,c,f=e(o,a,s,u);return null!=f&&(l=t(o,a,s,u),l=tn(l),en(l,i),r&&1!==r&&(on(f),f&&!f[l]&&(f[l]={})),c=f[l],nn(c,i)),n?{context:f,name:l,value:c}:c}},nonComputedMember:function(e,t,n,r,i,o){return function(a,s,u,l){var c=e(a,s,u,l);i&&1!==i&&(on(c),c&&!c[t]&&(c[t]={}));var f=null!=c?c[t]:void 0;return(n||vn(t))&&nn(f,o),r?{context:c,name:t,value:f}:f}},inputs:function(e,t){return function(n,r,i,o){return o?o[t]:e(n,r,i)}}};var bo=function(e,t,n){this.lexer=e,this.$filter=t,this.options=n,this.ast=new yo(e,n),this.astCompiler=n.csp?new gn(this.ast,t):new mn(this.ast,t)};bo.prototype={constructor:bo,parse:function(e){return this.astCompiler.compile(e,this.options.expensiveChecks)}};var wo=Object.prototype.valueOf,xo=t("$sce"),Eo={HTML:"html",CSS:"css",URL:"url",RESOURCE_URL:"resourceUrl",JS:"js"},To=t("$compile"),Co=e.document.createElement("a"),So=Mn(e.location.href);Ln.$inject=["$document"],Rn.$inject=["$provide"];var ko=22,_o=".",Ao="0";Un.$inject=["$locale"],Wn.$inject=["$locale"];var No={yyyy:Jn("FullYear",4,0,!1,!0),yy:Jn("FullYear",2,0,!0,!0),y:Jn("FullYear",1,0,!1,!0),MMMM:Qn("Month"),MMM:Qn("Month",!0),MM:Jn("Month",2,1),M:Jn("Month",1,1),LLLL:Qn("Month",!1,!0),dd:Jn("Date",2),d:Jn("Date",1),HH:Jn("Hours",2),H:Jn("Hours",1),hh:Jn("Hours",2,-12),h:Jn("Hours",1,-12),mm:Jn("Minutes",2),m:Jn("Minutes",1),ss:Jn("Seconds",2),s:Jn("Seconds",1),sss:Jn("Milliseconds",3),EEEE:Qn("Day"),EEE:Qn("Day",!0),a:nr,Z:Kn,ww:tr(2),w:tr(1),G:rr,GG:rr,GGG:rr,GGGG:ir},Oo=/((?:[^yMLdHhmsaZEwG']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|m+|s+|a|Z|G+|w+))(.*)/,Do=/^\-?\d+$/;or.$inject=["$locale"];var Io=m(Ir),Mo=m(Mr);lr.$inject=["$parse"];var jo=m({restrict:"E",compile:function(e,t){if(!t.href&&!t.xlinkHref)return function(e,t){if("a"===t[0].nodeName.toLowerCase()){var n="[object SVGAnimatedString]"===Ur.call(t.prop("href"))?"xlink:href":"href";t.on("click",function(e){t.attr(n)||e.preventDefault()})}}}}),Po={};r(ki,function(e,t){function n(e,n,i){e.$watch(i[r],function(e){i.$set(t,!!e)})}if("multiple"!=e){var r=gt("ng-"+t),i=n;"checked"===e&&(i=function(e,t,i){i.ngModel!==i[r]&&n(e,t,i)}),Po[r]=function(){return{restrict:"A",priority:100,link:i}}}}),r(Ai,function(e,t){Po[t]=function(){return{priority:100,link:function(e,n,r){if("ngPattern"===t&&"/"==r.ngPattern.charAt(0)){var i=r.ngPattern.match(Nr);if(i)return void r.$set("ngPattern",new RegExp(i[1],i[2]))}e.$watch(r[t],function(e){r.$set(t,e)})}}}}),r(["src","srcset","href"],function(e){var t=gt("ng-"+e);Po[t]=function(){return{priority:99,link:function(n,r,i){var o=e,a=e;"href"===e&&"[object SVGAnimatedString]"===Ur.call(r.prop("href"))&&(a="xlinkHref",i.$attr[a]="xlink:href",o=null),i.$observe(t,function(t){return t?(i.$set(a,t),void(Lr&&o&&r.prop(o,i[a]))):void("href"===e&&i.$set(a,null))})}}}});var Lo={$addControl:p,$$renameControl:fr,$removeControl:p,$setValidity:p,$setDirty:p,$setPristine:p,$setSubmitted:p},Vo="ng-submitted";dr.$inject=["$element","$attrs","$scope","$animate","$interpolate"];var Ro=function(e){return["$timeout","$parse",function(t,n){function r(e){return""===e?n('this[""]').assign:n(e).assign||p}var i={name:"form",restrict:e?"EAC":"E",require:["form","^^?form"],controller:dr,compile:function(n,i){n.addClass(wa).addClass(ya);var o=i.name?"name":!(!e||!i.ngForm)&&"ngForm";return{pre:function(e,n,i,a){var s=a[0];if(!("action"in i)){var u=function(t){e.$apply(function(){s.$commitViewValue(),s.$setSubmitted()}),t.preventDefault()};hi(n[0],"submit",u),n.on("$destroy",function(){t(function(){mi(n[0],"submit",u)},0,!1)})}var c=a[1]||s.$$parentForm;c.$addControl(s);var f=o?r(s.$name):p;o&&(f(e,s),i.$observe(o,function(t){s.$name!==t&&(f(e,void 0),s.$$parentForm.$$renameControl(s,t),(f=r(s.$name))(e,s))})),n.on("$destroy",function(){s.$$parentForm.$removeControl(s),f(e,void 0),l(s,Lo)})}}}};return i}]},Fo=Ro(),Ho=Ro(!0),Bo=/^\d{4,}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+(?:[+-][0-2]\d:[0-5]\d|Z)$/,qo=/^[a-z][a-z\d.+-]*:\/*(?:[^:@]+(?::[^@]+)?@)?(?:[^\s:/?#]+|\[[a-f\d:]+\])(?::\d+)?(?:\/[^?#]*)?(?:\?[^#]*)?(?:#.*)?$/i,Uo=/^(?=.{1,254}$)(?=.{1,64}@)[-!#$%&'*+\/0-9=?A-Z^_`a-z{|}~]+(\.[-!#$%&'*+\/0-9=?A-Z^_`a-z{|}~]+)*@[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?(\.[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*$/,Wo=/^\s*(\-|\+)?(\d+|(\d*(\.\d*)))([eE][+-]?\d+)?\s*$/,zo=/^(\d{4,})-(\d{2})-(\d{2})$/,Go=/^(\d{4,})-(\d\d)-(\d\d)T(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,Yo=/^(\d{4,})-W(\d\d)$/,Xo=/^(\d{4,})-(\d\d)$/,Jo=/^(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,Qo="keydown wheel mousedown",Ko=he();r("date,datetime-local,month,time,week".split(","),function(e){Ko[e]=!0});var Zo={text:hr,date:$r("date",zo,vr(zo,["yyyy","MM","dd"]),"yyyy-MM-dd"),"datetime-local":$r("datetimelocal",Go,vr(Go,["yyyy","MM","dd","HH","mm","ss","sss"]),"yyyy-MM-ddTHH:mm:ss.sss"),time:$r("time",Jo,vr(Jo,["HH","mm","ss","sss"]),"HH:mm:ss.sss"),week:$r("week",Yo,gr,"yyyy-Www"),month:$r("month",Xo,vr(Xo,["yyyy","MM"]),"yyyy-MM"),number:br,url:wr,email:xr,radio:Er,checkbox:Cr,hidden:p,button:p,submit:p,reset:p,file:p},ea=["$browser","$sniffer","$filter","$parse",function(e,t,n,r){return{restrict:"E",require:["?ngModel"],link:{pre:function(i,o,a,s){s[0]&&(Zo[Ir(a.type)]||Zo.text)(i,o,a,s[0],t,e,n,r)}}}}],ta=/^(true|false|\d+)$/,na=function(){return{restrict:"A",priority:100,compile:function(e,t){return ta.test(t.ngValue)?function(e,t,n){n.$set("value",e.$eval(n.ngValue))}:function(e,t,n){e.$watch(n.ngValue,function(e){n.$set("value",e)})}}}},ra=["$compile",function(e){return{restrict:"AC",compile:function(t){return e.$$addBindingClass(t),function(t,n,r){e.$$addBindingInfo(n,r.ngBind),n=n[0],t.$watch(r.ngBind,function(e){n.textContent=v(e)?"":e})}}}}],ia=["$interpolate","$compile",function(e,t){return{compile:function(n){return t.$$addBindingClass(n),function(n,r,i){var o=e(r.attr(i.$attr.ngBindTemplate));t.$$addBindingInfo(r,o.expressions),r=r[0],i.$observe("ngBindTemplate",function(e){r.textContent=v(e)?"":e})}}}}],oa=["$sce","$parse","$compile",function(e,t,n){return{restrict:"A",compile:function(r,i){var o=t(i.ngBindHtml),a=t(i.ngBindHtml,function(t){return e.valueOf(t)});return n.$$addBindingClass(r),function(t,r,i){n.$$addBindingInfo(r,i.ngBindHtml),t.$watch(a,function(){var n=o(t);r.html(e.getTrustedHtml(n)||"")})}}}}],aa=m({restrict:"A",require:"ngModel",link:function(e,t,n,r){r.$viewChangeListeners.push(function(){e.$eval(n.ngChange)})}}),sa=Sr("",!0),ua=Sr("Odd",0),la=Sr("Even",1),ca=cr({compile:function(e,t){t.$set("ngCloak",void 0),e.removeClass("ng-cloak")}}),fa=[function(){return{restrict:"A",scope:!0,controller:"@",priority:500}}],da={},pa={blur:!0,focus:!0};r("click dblclick mousedown mouseup mouseover mouseout mousemove mouseenter mouseleave keydown keyup keypress submit focus blur copy cut paste".split(" "),function(e){var t=gt("ng-"+e);da[t]=["$parse","$rootScope",function(n,r){return{restrict:"A",compile:function(i,o){var a=n(o[t],null,!0);return function(t,n){n.on(e,function(n){var i=function(){a(t,{$event:n})};pa[e]&&r.$$phase?t.$evalAsync(i):t.$apply(i)})}}}}]});var ha=["$animate","$compile",function(e,t){return{multiElement:!0,transclude:"element",priority:600,terminal:!0,restrict:"A",$$tlb:!0,link:function(n,r,i,o,a){var s,u,l;n.$watch(i.ngIf,function(n){n?u||a(function(n,o){u=o,n[n.length++]=t.$$createComment("end ngIf",i.ngIf),s={clone:n},e.enter(n,r.parent(),r)}):(l&&(l.remove(),l=null),u&&(u.$destroy(),u=null),s&&(l=pe(s.clone),e.leave(l).then(function(){l=null}),s=null))})}}}],ma=["$templateRequest","$anchorScroll","$animate",function(e,t,n){return{restrict:"ECA",priority:400,terminal:!0,transclude:"element",controller:Gr.noop,compile:function(r,i){var o=i.ngInclude||i.src,a=i.onload||"",s=i.autoscroll;return function(r,i,u,l,c){var f,d,p,h=0,m=function(){d&&(d.remove(),d=null),f&&(f.$destroy(),f=null),p&&(n.leave(p).then(function(){d=null}),d=p,p=null)};r.$watch(o,function(o){var u=function(){!$(s)||s&&!r.$eval(s)||t()},d=++h;o?(e(o,!0).then(function(e){if(!r.$$destroyed&&d===h){var t=r.$new();l.template=e;var s=c(t,function(e){m(),n.enter(e,null,i).then(u)});f=t,p=s,f.$emit("$includeContentLoaded",o),r.$eval(a)}},function(){r.$$destroyed||d===h&&(m(),r.$emit("$includeContentError",o))}),r.$emit("$includeContentRequested",o)):(m(),l.template=null)})}}}}],ga=["$compile",function(t){return{restrict:"ECA",priority:-400,require:"ngInclude",link:function(n,r,i,o){return Ur.call(r[0]).match(/SVG/)?(r.empty(),void t(Se(o.template,e.document).childNodes)(n,function(e){r.append(e)},{futureParentElement:r})):(r.html(o.template),void t(r.contents())(n))}}}],va=cr({priority:450,compile:function(){return{pre:function(e,t,n){e.$eval(n.ngInit)}}}}),$a=function(){return{restrict:"A",priority:100,require:"ngModel",link:function(e,t,n,i){var o=t.attr(n.$attr.ngList)||", ",a="false"!==n.ngTrim,s=a?Qr(o):o,u=function(e){if(!v(e)){var t=[];return e&&r(e.split(s),function(e){e&&t.push(a?Qr(e):e)}),t}};i.$parsers.push(u),i.$formatters.push(function(e){if(Xr(e))return e.join(o)}),i.$isEmpty=function(e){return!e||!e.length}}}},ya="ng-valid",ba="ng-invalid",wa="ng-pristine",xa="ng-dirty",Ea="ng-untouched",Ta="ng-touched",Ca="ng-pending",Sa="ng-empty",ka="ng-not-empty",_a=t("ngModel"),Aa=["$scope","$exceptionHandler","$attrs","$element","$parse","$animate","$timeout","$rootScope","$q","$interpolate",function(e,t,n,i,o,a,s,u,l,c){this.$viewValue=Number.NaN,this.$modelValue=Number.NaN,this.$$rawModelValue=void 0,this.$validators={},this.$asyncValidators={},this.$parsers=[],this.$formatters=[],this.$viewChangeListeners=[],this.$untouched=!0,this.$touched=!1,this.$pristine=!0,this.$dirty=!1,this.$valid=!0,this.$invalid=!1,this.$error={},this.$$success={},this.$pending=void 0,this.$name=c(n.name||"",!1)(e),this.$$parentForm=Lo;var f,d=o(n.ngModel),h=d.assign,m=d,g=h,y=null,b=this;this.$$setOptions=function(e){if(b.$options=e,e&&e.getterSetter){var t=o(n.ngModel+"()"),r=o(n.ngModel+"($$$p)");m=function(e){var n=d(e);return T(n)&&(n=t(e)),n},g=function(e,t){T(d(e))?r(e,{$$$p:t}):h(e,t)}}else if(!d.assign)throw _a("nonassign","Expression '{0}' is non-assignable. Element: {1}",n.ngModel,J(i))},this.$render=p,this.$isEmpty=function(e){return v(e)||""===e||null===e||e!==e},this.$$updateEmptyClasses=function(e){b.$isEmpty(e)?(a.removeClass(i,ka),a.addClass(i,Sa)):(a.removeClass(i,Sa),a.addClass(i,ka))};var w=0;kr({ctrl:this,$element:i,set:function(e,t){e[t]=!0},unset:function(e,t){delete e[t]},$animate:a}),this.$setPristine=function(){b.$dirty=!1,b.$pristine=!0,a.removeClass(i,xa),a.addClass(i,wa)},this.$setDirty=function(){b.$dirty=!0,b.$pristine=!1,a.removeClass(i,wa),a.addClass(i,xa),b.$$parentForm.$setDirty()},this.$setUntouched=function(){b.$touched=!1,b.$untouched=!0,a.setClass(i,Ea,Ta)},this.$setTouched=function(){b.$touched=!0,b.$untouched=!1,a.setClass(i,Ta,Ea)},this.$rollbackViewValue=function(){s.cancel(y),b.$viewValue=b.$$lastCommittedViewValue,b.$render()},this.$validate=function(){if(!x(b.$modelValue)||!isNaN(b.$modelValue)){var e=b.$$lastCommittedViewValue,t=b.$$rawModelValue,n=b.$valid,r=b.$modelValue,i=b.$options&&b.$options.allowInvalid;b.$$runValidators(t,e,function(e){i||n===e||(b.$modelValue=e?t:void 0,b.$modelValue!==r&&b.$$writeModelToScope())})}},this.$$runValidators=function(e,t,n){function i(){var e=b.$$parserName||"parse";return v(f)?(s(e,null),!0):(f||(r(b.$validators,function(e,t){s(t,null)}),r(b.$asyncValidators,function(e,t){s(t,null)})),s(e,f),f)}function o(){var n=!0;return r(b.$validators,function(r,i){var o=r(e,t);n=n&&o,s(i,o)}),!!n||(r(b.$asyncValidators,function(e,t){s(t,null)}),!1)}function a(){var n=[],i=!0;r(b.$asyncValidators,function(r,o){var a=r(e,t);if(!D(a))throw _a("nopromise","Expected asynchronous validator to return a promise but got '{0}' instead.",a);s(o,void 0),n.push(a.then(function(){s(o,!0)},function(){i=!1,s(o,!1)}))}),n.length?l.all(n).then(function(){u(i)},p):u(!0)}function s(e,t){c===w&&b.$setValidity(e,t)}function u(e){c===w&&n(e)}w++;var c=w;return i()&&o()?void a():void u(!1)},this.$commitViewValue=function(){var e=b.$viewValue;s.cancel(y),(b.$$lastCommittedViewValue!==e||""===e&&b.$$hasNativeValidators)&&(b.$$updateEmptyClasses(e),b.$$lastCommittedViewValue=e,b.$pristine&&this.$setDirty(),this.$$parseAndValidate())},this.$$parseAndValidate=function(){function t(){b.$modelValue!==o&&b.$$writeModelToScope()}var n=b.$$lastCommittedViewValue,r=n;if(f=!v(r)||void 0)for(var i=0;i<b.$parsers.length;i++)if(r=b.$parsers[i](r),v(r)){f=!1;break}x(b.$modelValue)&&isNaN(b.$modelValue)&&(b.$modelValue=m(e));var o=b.$modelValue,a=b.$options&&b.$options.allowInvalid;b.$$rawModelValue=r,a&&(b.$modelValue=r,t()),b.$$runValidators(r,b.$$lastCommittedViewValue,function(e){a||(b.$modelValue=e?r:void 0,t())})},this.$$writeModelToScope=function(){g(e,b.$modelValue),r(b.$viewChangeListeners,function(e){try{e()}catch(n){t(n)}})},this.$setViewValue=function(e,t){b.$viewValue=e,b.$options&&!b.$options.updateOnDefault||b.$$debounceViewValueCommit(t)},this.$$debounceViewValueCommit=function(t){var n,r=0,i=b.$options;i&&$(i.debounce)&&(n=i.debounce,x(n)?r=n:x(n[t])?r=n[t]:x(n["default"])&&(r=n["default"])),s.cancel(y),r?y=s(function(){b.$commitViewValue()},r):u.$$phase?b.$commitViewValue():e.$apply(function(){b.$commitViewValue()})},e.$watch(function(){var t=m(e);if(t!==b.$modelValue&&(b.$modelValue===b.$modelValue||t===t)){b.$modelValue=b.$$rawModelValue=t,f=void 0;for(var n=b.$formatters,r=n.length,i=t;r--;)i=n[r](i);b.$viewValue!==i&&(b.$$updateEmptyClasses(i),b.$viewValue=b.$$lastCommittedViewValue=i,b.$render(),b.$$runValidators(t,i,p))}return t})}],Na=["$rootScope",function(e){return{restrict:"A",require:["ngModel","^?form","^?ngModelOptions"],controller:Aa,priority:1,compile:function(t){return t.addClass(wa).addClass(Ea).addClass(ya),{pre:function(e,t,n,r){var i=r[0],o=r[1]||i.$$parentForm;i.$$setOptions(r[2]&&r[2].$options),o.$addControl(i),n.$observe("name",function(e){i.$name!==e&&i.$$parentForm.$$renameControl(i,e)}),e.$on("$destroy",function(){i.$$parentForm.$removeControl(i)})},post:function(t,n,r,i){var o=i[0];o.$options&&o.$options.updateOn&&n.on(o.$options.updateOn,function(e){o.$$debounceViewValueCommit(e&&e.type)}),n.on("blur",function(){o.$touched||(e.$$phase?t.$evalAsync(o.$setTouched):t.$apply(o.$setTouched))})}}}}}],Oa=/(\s+|^)default(\s+|$)/,Da=function(){return{restrict:"A",controller:["$scope","$attrs",function(e,t){var n=this;this.$options=R(e.$eval(t.ngModelOptions)),$(this.$options.updateOn)?(this.$options.updateOnDefault=!1,this.$options.updateOn=Qr(this.$options.updateOn.replace(Oa,function(){return n.$options.updateOnDefault=!0," "}))):this.$options.updateOnDefault=!0}]}},Ia=cr({terminal:!0,priority:1e3}),Ma=t("ngOptions"),ja=/^\s*([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+group\s+by\s+([\s\S]+?))?(?:\s+disable\s+when\s+([\s\S]+?))?\s+for\s+(?:([\$\w][\$\w]*)|(?:\(\s*([\$\w][\$\w]*)\s*,\s*([\$\w][\$\w]*)\s*\)))\s+in\s+([\s\S]+?)(?:\s+track\s+by\s+([\s\S]+?))?$/,Pa=["$compile","$document","$parse",function(t,i,o){function a(e,t,r){function i(e,t,n,r,i){this.selectValue=e,this.viewValue=t,this.label=n,this.group=r,this.disabled=i}function a(e){var t;if(!l&&n(e))t=e;else{t=[];for(var r in e)e.hasOwnProperty(r)&&"$"!==r.charAt(0)&&t.push(r)}return t}var s=e.match(ja);if(!s)throw Ma("iexp","Expected expression in form of '_select_ (as _label_)? for (_key_,)?_value_ in _collection_' but got '{0}'. Element: {1}",e,J(t));var u=s[5]||s[7],l=s[6],c=/ as /.test(s[0])&&s[1],f=s[9],d=o(s[2]?s[1]:u),p=c&&o(c),h=p||d,m=f&&o(f),g=f?function(e,t){return m(r,t)}:function(e){return Qe(e)},v=function(e,t){return g(e,E(e,t))},$=o(s[2]||s[1]),y=o(s[3]||""),b=o(s[4]||""),w=o(s[8]),x={},E=l?function(e,t){return x[l]=t,x[u]=e,x}:function(e){return x[u]=e,x};return{trackBy:f,getTrackByValue:v,getWatchables:o(w,function(e){var t=[];e=e||[];for(var n=a(e),i=n.length,o=0;o<i;o++){var u=e===n?o:n[o],l=e[u],c=E(l,u),f=g(l,c);if(t.push(f),s[2]||s[1]){var d=$(r,c);t.push(d)}if(s[4]){var p=b(r,c);t.push(p)}}return t}),getOptions:function(){for(var e=[],t={},n=w(r)||[],o=a(n),s=o.length,u=0;u<s;u++){var l=n===o?u:o[u],c=n[l],d=E(c,l),p=h(r,d),m=g(p,d),x=$(r,d),T=y(r,d),C=b(r,d),S=new i(m,p,x,T,C);e.push(S),t[m]=S}return{items:e,selectValueMap:t,getOptionFromViewValue:function(e){return t[v(e)]},getViewValueFromOption:function(e){return f?Gr.copy(e.viewValue):e.viewValue}}}}}function s(e,n,o,s){function c(e,t){var n=u.cloneNode(!1);t.appendChild(n),f(e,n)}function f(e,t){e.element=t,t.disabled=e.disabled,e.label!==t.label&&(t.label=e.label,t.textContent=e.label),e.value!==t.value&&(t.value=e.selectValue)}function d(){var e=E&&h.readValue();if(E)for(var t=E.items.length-1;t>=0;t--){var r=E.items[t];qe($(r.group)?r.element.parentNode:r.element)}E=T.getOptions();var i={};if(w&&n.prepend(p),E.items.forEach(function(e){var t;$(e.group)?(t=i[e.group],t||(t=l.cloneNode(!1),C.appendChild(t),t.label=null===e.group?"null":e.group,i[e.group]=t),c(e,t)):c(e,C)}),n[0].appendChild(C),m.$render(),!m.$isEmpty(e)){var o=h.readValue(),a=T.trackBy||g;(a?F(e,o):e===o)||(m.$setViewValue(o),m.$render())}}for(var p,h=s[0],m=s[1],g=o.multiple,v=0,y=n.children(),b=y.length;v<b;v++)if(""===y[v].value){p=y.eq(v);break}var w=!!p,x=Vr(u.cloneNode(!1));x.val("?");var E,T=a(o.ngOptions,n,e),C=i[0].createDocumentFragment(),S=function(){w||n.prepend(p),n.val(""),p.prop("selected",!0),p.attr("selected",!0)},k=function(){w||p.remove()},_=function(){n.prepend(x),n.val("?"),x.prop("selected",!0),x.attr("selected",!0)},A=function(){x.remove()};g?(m.$isEmpty=function(e){return!e||0===e.length},h.writeValue=function(e){E.items.forEach(function(e){e.element.selected=!1}),e&&e.forEach(function(e){var t=E.getOptionFromViewValue(e);
t&&(t.element.selected=!0)})},h.readValue=function(){var e=n.val()||[],t=[];return r(e,function(e){var n=E.selectValueMap[e];n&&!n.disabled&&t.push(E.getViewValueFromOption(n))}),t},T.trackBy&&e.$watchCollection(function(){if(Xr(m.$viewValue))return m.$viewValue.map(function(e){return T.getTrackByValue(e)})},function(){m.$render()})):(h.writeValue=function(e){var t=E.getOptionFromViewValue(e);t?(n[0].value!==t.selectValue&&(A(),k(),n[0].value=t.selectValue,t.element.selected=!0),t.element.setAttribute("selected","selected")):null===e||w?(A(),S()):(k(),_())},h.readValue=function(){var e=E.selectValueMap[n.val()];return e&&!e.disabled?(k(),A(),E.getViewValueFromOption(e)):null},T.trackBy&&e.$watch(function(){return T.getTrackByValue(m.$viewValue)},function(){m.$render()})),w?(p.remove(),t(p)(e),p.removeClass("ng-scope")):p=Vr(u.cloneNode(!1)),n.empty(),d(),e.$watchCollection(T.getWatchables,d)}var u=e.document.createElement("option"),l=e.document.createElement("optgroup");return{restrict:"A",terminal:!0,require:["select","ngModel"],link:{pre:function(e,t,n,r){r[0].registerOption=p},post:s}}}],La=["$locale","$interpolate","$log",function(e,t,n){var i=/{}/g,o=/^when(Minus)?(.+)$/;return{link:function(a,s,u){function l(e){s.text(e||"")}var c,f=u.count,d=u.$attr.when&&s.attr(u.$attr.when),h=u.offset||0,m=a.$eval(d)||{},g={},$=t.startSymbol(),y=t.endSymbol(),b=$+f+"-"+h+y,w=Gr.noop;r(u,function(e,t){var n=o.exec(t);if(n){var r=(n[1]?"-":"")+Ir(n[2]);m[r]=s.attr(u.$attr[t])}}),r(m,function(e,n){g[n]=t(e.replace(i,b))}),a.$watch(f,function(t){var r=parseFloat(t),i=isNaN(r);if(i||r in m||(r=e.pluralCat(r-h)),r!==c&&!(i&&x(c)&&isNaN(c))){w();var o=g[r];v(o)?(null!=t&&n.debug("ngPluralize: no rule defined for '"+r+"' in "+d),w=p,l()):w=a.$watch(o,l),c=r}})}}}],Va=["$parse","$animate","$compile",function(e,i,o){var a="$$NG_REMOVED",s=t("ngRepeat"),u=function(e,t,n,r,i,o,a){e[n]=r,i&&(e[i]=o),e.$index=t,e.$first=0===t,e.$last=t===a-1,e.$middle=!(e.$first||e.$last),e.$odd=!(e.$even=0===(1&t))},l=function(e){return e.clone[0]},c=function(e){return e.clone[e.clone.length-1]};return{restrict:"A",multiElement:!0,transclude:"element",priority:1e3,terminal:!0,$$tlb:!0,compile:function(t,f){var d=f.ngRepeat,p=o.$$createComment("end ngRepeat",d),h=d.match(/^\s*([\s\S]+?)\s+in\s+([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+track\s+by\s+([\s\S]+?))?\s*$/);if(!h)throw s("iexp","Expected expression in form of '_item_ in _collection_[ track by _id_]' but got '{0}'.",d);var m=h[1],g=h[2],v=h[3],$=h[4];if(h=m.match(/^(?:(\s*[\$\w]+)|\(\s*([\$\w]+)\s*,\s*([\$\w]+)\s*\))$/),!h)throw s("iidexp","'_item_' in '_item_ in _collection_' should be an identifier or '(_key_, _value_)' expression, but got '{0}'.",m);var y=h[3]||h[1],b=h[2];if(v&&(!/^[$a-zA-Z_][$a-zA-Z0-9_]*$/.test(v)||/^(null|undefined|this|\$index|\$first|\$middle|\$last|\$even|\$odd|\$parent|\$root|\$id)$/.test(v)))throw s("badident","alias '{0}' is invalid --- must be a valid JS identifier which is not a reserved name.",v);var w,x,E,T,C={$id:Qe};return $?w=e($):(E=function(e,t){return Qe(t)},T=function(e){return e}),function(e,t,o,f,h){w&&(x=function(t,n,r){return b&&(C[b]=t),C[y]=n,C.$index=r,w(e,C)});var m=he();e.$watchCollection(g,function(o){var f,g,$,w,C,S,k,_,A,N,O,D,I=t[0],M=he();if(v&&(e[v]=o),n(o))A=o,_=x||E;else{_=x||T,A=[];for(var j in o)Dr.call(o,j)&&"$"!==j.charAt(0)&&A.push(j)}for(w=A.length,O=new Array(w),f=0;f<w;f++)if(C=o===A?f:A[f],S=o[C],k=_(C,S,f),m[k])N=m[k],delete m[k],M[k]=N,O[f]=N;else{if(M[k])throw r(O,function(e){e&&e.scope&&(m[e.id]=e)}),s("dupes","Duplicates in a repeater are not allowed. Use 'track by' expression to specify unique keys. Repeater: {0}, Duplicate key: {1}, Duplicate value: {2}",d,k,S);O[f]={id:k,scope:void 0,clone:void 0},M[k]=!0}for(var P in m){if(N=m[P],D=pe(N.clone),i.leave(D),D[0].parentNode)for(f=0,g=D.length;f<g;f++)D[f][a]=!0;N.scope.$destroy()}for(f=0;f<w;f++)if(C=o===A?f:A[f],S=o[C],N=O[f],N.scope){$=I;do $=$.nextSibling;while($&&$[a]);l(N)!=$&&i.move(pe(N.clone),null,I),I=c(N),u(N.scope,f,y,S,b,C,w)}else h(function(e,t){N.scope=t;var n=p.cloneNode(!1);e[e.length++]=n,i.enter(e,null,I),I=n,N.clone=e,M[N.id]=N,u(N.scope,f,y,S,b,C,w)});m=M})}}}}],Ra="ng-hide",Fa="ng-hide-animate",Ha=["$animate",function(e){return{restrict:"A",multiElement:!0,link:function(t,n,r){t.$watch(r.ngShow,function(t){e[t?"removeClass":"addClass"](n,Ra,{tempClasses:Fa})})}}}],Ba=["$animate",function(e){return{restrict:"A",multiElement:!0,link:function(t,n,r){t.$watch(r.ngHide,function(t){e[t?"addClass":"removeClass"](n,Ra,{tempClasses:Fa})})}}}],qa=cr(function(e,t,n){e.$watch(n.ngStyle,function(e,n){n&&e!==n&&r(n,function(e,n){t.css(n,"")}),e&&t.css(e)},!0)}),Ua=["$animate","$compile",function(e,t){return{require:"ngSwitch",controller:["$scope",function(){this.cases={}}],link:function(n,i,o,a){var s=o.ngSwitch||o.on,u=[],l=[],c=[],f=[],d=function(e,t){return function(){e.splice(t,1)}};n.$watch(s,function(n){var i,o;for(i=0,o=c.length;i<o;++i)e.cancel(c[i]);for(c.length=0,i=0,o=f.length;i<o;++i){var s=pe(l[i].clone);f[i].$destroy();var p=c[i]=e.leave(s);p.then(d(c,i))}l.length=0,f.length=0,(u=a.cases["!"+n]||a.cases["?"])&&r(u,function(n){n.transclude(function(r,i){f.push(i);var o=n.element;r[r.length++]=t.$$createComment("end ngSwitchWhen");var a={clone:r};l.push(a),e.enter(r,o.parent(),o)})})})}}}],Wa=cr({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(e,t,n,r,i){r.cases["!"+n.ngSwitchWhen]=r.cases["!"+n.ngSwitchWhen]||[],r.cases["!"+n.ngSwitchWhen].push({transclude:i,element:t})}}),za=cr({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(e,t,n,r,i){r.cases["?"]=r.cases["?"]||[],r.cases["?"].push({transclude:i,element:t})}}),Ga=t("ngTransclude"),Ya=["$compile",function(e){return{restrict:"EAC",terminal:!0,compile:function(t){var n=e(t.contents());return t.empty(),function(e,t,r,i,o){function a(e,n){e.length?t.append(e):(s(),n.$destroy())}function s(){n(e,function(e){t.append(e)})}if(!o)throw Ga("orphan","Illegal use of ngTransclude directive in the template! No parent directive that requires a transclusion found. Element: {0}",J(t));r.ngTransclude===r.$attr.ngTransclude&&(r.ngTransclude="");var u=r.ngTransclude||r.ngTranscludeSlot;o(a,null,u),u&&!o.isSlotFilled(u)&&s()}}}}],Xa=["$templateCache",function(e){return{restrict:"E",terminal:!0,compile:function(t,n){if("text/ng-template"==n.type){var r=n.id,i=t[0].text;e.put(r,i)}}}}],Ja={$setViewValue:p,$render:p},Qa=["$element","$scope",function(t,n){var r=this,i=new Ke;r.ngModelCtrl=Ja,r.unknownOption=Vr(e.document.createElement("option")),r.renderUnknownOption=function(e){var n="? "+Qe(e)+" ?";r.unknownOption.val(n),t.prepend(r.unknownOption),t.val(n)},n.$on("$destroy",function(){r.renderUnknownOption=p}),r.removeUnknownOption=function(){r.unknownOption.parent()&&r.unknownOption.remove()},r.readValue=function(){return r.removeUnknownOption(),t.val()},r.writeValue=function(e){r.hasOption(e)?(r.removeUnknownOption(),t.val(e),""===e&&r.emptyOption.prop("selected",!0)):null==e&&r.emptyOption?(r.removeUnknownOption(),t.val("")):r.renderUnknownOption(e)},r.addOption=function(e,t){if(t[0].nodeType!==ui){fe(e,'"option value"'),""===e&&(r.emptyOption=t);var n=i.get(e)||0;i.put(e,n+1),r.ngModelCtrl.$render(),Ar(t)}},r.removeOption=function(e){var t=i.get(e);t&&(1===t?(i.remove(e),""===e&&(r.emptyOption=void 0)):i.put(e,t-1))},r.hasOption=function(e){return!!i.get(e)},r.registerOption=function(e,t,n,i,o){if(i){var a;n.$observe("value",function(e){$(a)&&r.removeOption(a),a=e,r.addOption(e,t)})}else o?e.$watch(o,function(e,i){n.$set("value",e),i!==e&&r.removeOption(i),r.addOption(e,t)}):r.addOption(n.value,t);t.on("$destroy",function(){r.removeOption(n.value),r.ngModelCtrl.$render()})}}],Ka=function(){function e(e,t,n,i){var o=i[1];if(o){var a=i[0];if(a.ngModelCtrl=o,t.on("change",function(){e.$apply(function(){o.$setViewValue(a.readValue())})}),n.multiple){a.readValue=function(){var e=[];return r(t.find("option"),function(t){t.selected&&e.push(t.value)}),e},a.writeValue=function(e){var n=new Ke(e);r(t.find("option"),function(e){e.selected=$(n.get(e.value))})};var s,u=NaN;e.$watch(function(){u!==o.$viewValue||F(s,o.$viewValue)||(s=ge(o.$viewValue),o.$render()),u=o.$viewValue}),o.$isEmpty=function(e){return!e||0===e.length}}}}function t(e,t,n,r){var i=r[1];if(i){var o=r[0];i.$render=function(){o.writeValue(i.$viewValue)}}}return{restrict:"E",require:["select","?ngModel"],controller:Qa,priority:1,link:{pre:e,post:t}}},Za=["$interpolate",function(e){return{restrict:"E",priority:100,compile:function(t,n){if($(n.value))var r=e(n.value,!0);else{var i=e(t.text(),!0);i||n.$set("value",t.text())}return function(e,t,n){var o="$selectController",a=t.parent(),s=a.data(o)||a.parent().data(o);s&&s.registerOption(e,t,n,r,i)}}}}],es=m({restrict:"E",terminal:!1}),ts=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,r){r&&(n.required=!0,r.$validators.required=function(e,t){return!n.required||!r.$isEmpty(t)},n.$observe("required",function(){r.$validate()}))}}},ns=function(){return{restrict:"A",require:"?ngModel",link:function(e,n,r,i){if(i){var o,a=r.ngPattern||r.pattern;r.$observe("pattern",function(e){if(w(e)&&e.length>0&&(e=new RegExp("^"+e+"$")),e&&!e.test)throw t("ngPattern")("noregexp","Expected {0} to be a RegExp but was {1}. Element: {2}",a,e,J(n));o=e||void 0,i.$validate()}),i.$validators.pattern=function(e,t){return i.$isEmpty(t)||v(o)||o.test(t)}}}}},rs=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,r){if(r){var i=-1;n.$observe("maxlength",function(e){var t=f(e);i=isNaN(t)?-1:t,r.$validate()}),r.$validators.maxlength=function(e,t){return i<0||r.$isEmpty(t)||t.length<=i}}}}},is=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,r){if(r){var i=0;n.$observe("minlength",function(e){i=f(e)||0,r.$validate()}),r.$validators.minlength=function(e,t){return r.$isEmpty(t)||t.length>=i}}}}};return e.angular.bootstrap?void(e.console&&console.log("WARNING: Tried to load angular more than once.")):(ue(),ye(Gr),Gr.module("ngLocale",[],["$provide",function(e){function t(e){e+="";var t=e.indexOf(".");return t==-1?0:e.length-t-1}function n(e,n){var r=n;void 0===r&&(r=Math.min(t(e),3));var i=Math.pow(10,r),o=(e*i|0)%i;return{v:r,f:o}}var r={ZERO:"zero",ONE:"one",TWO:"two",FEW:"few",MANY:"many",OTHER:"other"};e.value("$locale",{DATETIME_FORMATS:{AMPMS:["AM","PM"],DAY:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],ERANAMES:["Before Christ","Anno Domini"],ERAS:["BC","AD"],FIRSTDAYOFWEEK:6,MONTH:["January","February","March","April","May","June","July","August","September","October","November","December"],SHORTDAY:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],SHORTMONTH:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],STANDALONEMONTH:["January","February","March","April","May","June","July","August","September","October","November","December"],WEEKENDRANGE:[5,6],fullDate:"EEEE, MMMM d, y",longDate:"MMMM d, y",medium:"MMM d, y h:mm:ss a",mediumDate:"MMM d, y",mediumTime:"h:mm:ss a","short":"M/d/yy h:mm a",shortDate:"M/d/yy",shortTime:"h:mm a"},NUMBER_FORMATS:{CURRENCY_SYM:"$",DECIMAL_SEP:".",GROUP_SEP:",",PATTERNS:[{gSize:3,lgSize:3,maxFrac:3,minFrac:0,minInt:1,negPre:"-",negSuf:"",posPre:"",posSuf:""},{gSize:3,lgSize:3,maxFrac:2,minFrac:2,minInt:1,negPre:"-¤",negSuf:"",posPre:"¤",posSuf:""}]},id:"en-us",localeID:"en_US",pluralCat:function(e,t){var i=0|e,o=n(e,t);return 1==i&&0==o.v?r.ONE:r.OTHER}})}]),void Vr(e.document).ready(function(){re(e.document,ie)}))}(window),!window.angular.$$csp().noInlineStyle&&window.angular.element(document.head).prepend('<style type="text/css">@charset "UTF-8";[ng\\:cloak],[ng-cloak],[data-ng-cloak],[x-ng-cloak],.ng-cloak,.x-ng-cloak,.ng-hide:not(.ng-hide-animate){display:none !important;}ng\\:form{display:block;}.ng-animate-shim{visibility:hidden;}.ng-anchor{position:absolute;}</style>'),function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){function n(e){var t=e.length,n=ie.type(e);return"function"!==n&&!ie.isWindow(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e))}function r(e,t,n){if(ie.isFunction(t))return ie.grep(e,function(e,r){return!!t.call(e,r,e)!==n});if(t.nodeType)return ie.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(de.test(t))return ie.filter(t,e,n);t=ie.filter(t,e)}return ie.grep(e,function(e){return ie.inArray(e,t)>=0!==n})}function i(e,t){do e=e[t];while(e&&1!==e.nodeType);return e}function o(e){var t=be[e]={};return ie.each(e.match(ye)||[],function(e,n){t[n]=!0}),t}function a(){he.addEventListener?(he.removeEventListener("DOMContentLoaded",s,!1),e.removeEventListener("load",s,!1)):(he.detachEvent("onreadystatechange",s),e.detachEvent("onload",s))}function s(){(he.addEventListener||"load"===event.type||"complete"===he.readyState)&&(a(),ie.ready())}function u(e,t,n){if(void 0===n&&1===e.nodeType){var r="data-"+t.replace(Ce,"-$1").toLowerCase();if(n=e.getAttribute(r),"string"==typeof n){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:Te.test(n)?ie.parseJSON(n):n)}catch(i){}ie.data(e,t,n)}else n=void 0}return n}function l(e){var t;for(t in e)if(("data"!==t||!ie.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function c(e,t,n,r){if(ie.acceptData(e)){var i,o,a=ie.expando,s=e.nodeType,u=s?ie.cache:e,l=s?e[a]:e[a]&&a;if(l&&u[l]&&(r||u[l].data)||void 0!==n||"string"!=typeof t)return l||(l=s?e[a]=Y.pop()||ie.guid++:a),u[l]||(u[l]=s?{}:{toJSON:ie.noop}),"object"!=typeof t&&"function"!=typeof t||(r?u[l]=ie.extend(u[l],t):u[l].data=ie.extend(u[l].data,t)),o=u[l],r||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[ie.camelCase(t)]=n),"string"==typeof t?(i=o[t],null==i&&(i=o[ie.camelCase(t)])):i=o,i}}function f(e,t,n){if(ie.acceptData(e)){var r,i,o=e.nodeType,a=o?ie.cache:e,s=o?e[ie.expando]:ie.expando;if(a[s]){if(t&&(r=n?a[s]:a[s].data)){ie.isArray(t)?t=t.concat(ie.map(t,ie.camelCase)):t in r?t=[t]:(t=ie.camelCase(t),t=t in r?[t]:t.split(" ")),i=t.length;for(;i--;)delete r[t[i]];if(n?!l(r):!ie.isEmptyObject(r))return}(n||(delete a[s].data,l(a[s])))&&(o?ie.cleanData([e],!0):ne.deleteExpando||a!=a.window?delete a[s]:a[s]=null)}}}function d(){return!0}function p(){return!1}function h(){try{return he.activeElement}catch(e){}}function m(e){var t=Pe.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}function g(e,t){var n,r,i=0,o=typeof e.getElementsByTagName!==Ee?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!==Ee?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(r=n[i]);i++)!t||ie.nodeName(r,t)?o.push(r):ie.merge(o,g(r,t));return void 0===t||t&&ie.nodeName(e,t)?ie.merge([e],o):o}function v(e){Ne.test(e.type)&&(e.defaultChecked=e.checked)}function $(e,t){return ie.nodeName(e,"table")&&ie.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function y(e){return e.type=(null!==ie.find.attr(e,"type"))+"/"+e.type,e}function b(e){var t=Ge.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function w(e,t){for(var n,r=0;null!=(n=e[r]);r++)ie._data(n,"globalEval",!t||ie._data(t[r],"globalEval"))}function x(e,t){if(1===t.nodeType&&ie.hasData(e)){var n,r,i,o=ie._data(e),a=ie._data(t,o),s=o.events;if(s){delete a.handle,a.events={};for(n in s)for(r=0,i=s[n].length;r<i;r++)ie.event.add(t,n,s[n][r])}a.data&&(a.data=ie.extend({},a.data))}}function E(e,t){var n,r,i;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!ne.noCloneEvent&&t[ie.expando]){i=ie._data(t);for(r in i.events)ie.removeEvent(t,r,i.handle);t.removeAttribute(ie.expando)}"script"===n&&t.text!==e.text?(y(t).text=e.text,b(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),ne.html5Clone&&e.innerHTML&&!ie.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&Ne.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}}function T(t,n){var r,i=ie(n.createElement(t)).appendTo(n.body),o=e.getDefaultComputedStyle&&(r=e.getDefaultComputedStyle(i[0]))?r.display:ie.css(i[0],"display");return i.detach(),o}function C(e){var t=he,n=Ze[e];return n||(n=T(e,t),"none"!==n&&n||(Ke=(Ke||ie("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=(Ke[0].contentWindow||Ke[0].contentDocument).document,t.write(),t.close(),n=T(e,t),Ke.detach()),Ze[e]=n),n}function S(e,t){return{get:function(){var n=e();if(null!=n)return n?void delete this.get:(this.get=t).apply(this,arguments)}}}function k(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=dt.length;i--;)if(t=dt[i]+n,t in e)return t;return r}function _(e,t){for(var n,r,i,o=[],a=0,s=e.length;a<s;a++)r=e[a],r.style&&(o[a]=ie._data(r,"olddisplay"),n=r.style.display,t?(o[a]||"none"!==n||(r.style.display=""),""===r.style.display&&_e(r)&&(o[a]=ie._data(r,"olddisplay",C(r.nodeName)))):(i=_e(r),(n&&"none"!==n||!i)&&ie._data(r,"olddisplay",i?n:ie.css(r,"display"))));for(a=0;a<s;a++)r=e[a],r.style&&(t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?o[a]||"":"none"));return e}function A(e,t,n){var r=ut.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function N(e,t,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===t?1:0,a=0;o<4;o+=2)"margin"===n&&(a+=ie.css(e,n+ke[o],!0,i)),r?("content"===n&&(a-=ie.css(e,"padding"+ke[o],!0,i)),"margin"!==n&&(a-=ie.css(e,"border"+ke[o]+"Width",!0,i))):(a+=ie.css(e,"padding"+ke[o],!0,i),"padding"!==n&&(a+=ie.css(e,"border"+ke[o]+"Width",!0,i)));return a}function O(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,o=et(e),a=ne.boxSizing&&"border-box"===ie.css(e,"boxSizing",!1,o);if(i<=0||null==i){if(i=tt(e,t,o),(i<0||null==i)&&(i=e.style[t]),rt.test(i))return i;r=a&&(ne.boxSizingReliable()||i===e.style[t]),i=parseFloat(i)||0}return i+N(e,t,n||(a?"border":"content"),r,o)+"px"}function D(e,t,n,r,i){return new D.prototype.init(e,t,n,r,i)}function I(){return setTimeout(function(){pt=void 0}),pt=ie.now()}function M(e,t){var n,r={height:e},i=0;for(t=t?1:0;i<4;i+=2-t)n=ke[i],r["margin"+n]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function j(e,t,n){for(var r,i=(yt[t]||[]).concat(yt["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function P(e,t,n){var r,i,o,a,s,u,l,c,f=this,d={},p=e.style,h=e.nodeType&&_e(e),m=ie._data(e,"fxshow");n.queue||(s=ie._queueHooks(e,"fx"),null==s.unqueued&&(s.unqueued=0,u=s.empty.fire,s.empty.fire=function(){s.unqueued||u()}),s.unqueued++,f.always(function(){f.always(function(){s.unqueued--,ie.queue(e,"fx").length||s.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],l=ie.css(e,"display"),c="none"===l?ie._data(e,"olddisplay")||C(e.nodeName):l,"inline"===c&&"none"===ie.css(e,"float")&&(ne.inlineBlockNeedsLayout&&"inline"!==C(e.nodeName)?p.zoom=1:p.display="inline-block")),n.overflow&&(p.overflow="hidden",ne.shrinkWrapBlocks()||f.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(r in t)if(i=t[r],mt.exec(i)){if(delete t[r],o=o||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;h=!0}d[r]=m&&m[r]||ie.style(e,r)}else l=void 0;if(ie.isEmptyObject(d))"inline"===("none"===l?C(e.nodeName):l)&&(p.display=l);else{m?"hidden"in m&&(h=m.hidden):m=ie._data(e,"fxshow",{}),o&&(m.hidden=!h),h?ie(e).show():f.done(function(){ie(e).hide()}),f.done(function(){var t;ie._removeData(e,"fxshow");for(t in d)ie.style(e,t,d[t])});for(r in d)a=j(h?m[r]:0,r,f),r in m||(m[r]=a.start,h&&(a.end=a.start,a.start="width"===r||"height"===r?1:0))}}function L(e,t){var n,r,i,o,a;for(n in e)if(r=ie.camelCase(n),i=t[r],o=e[n],ie.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),a=ie.cssHooks[r],a&&"expand"in a){o=a.expand(o),delete e[r];for(n in o)n in e||(e[n]=o[n],t[n]=i)}else t[r]=i}function V(e,t,n){var r,i,o=0,a=$t.length,s=ie.Deferred().always(function(){delete u.elem}),u=function(){if(i)return!1;for(var t=pt||I(),n=Math.max(0,l.startTime+l.duration-t),r=n/l.duration||0,o=1-r,a=0,u=l.tweens.length;a<u;a++)l.tweens[a].run(o);return s.notifyWith(e,[l,o,n]),o<1&&u?n:(s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:ie.extend({},t),opts:ie.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:pt||I(),duration:n.duration,tweens:[],createTween:function(t,n){var r=ie.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return t?s.resolveWith(e,[l,t]):s.rejectWith(e,[l,t]),this}}),c=l.props;for(L(c,l.opts.specialEasing);o<a;o++)if(r=$t[o].call(l,e,c,l.opts))return r;return ie.map(c,j,l),ie.isFunction(l.opts.start)&&l.opts.start.call(e,l),ie.fx.timer(ie.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}function R(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(ye)||[];if(ie.isFunction(n))for(;r=o[i++];)"+"===r.charAt(0)?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function F(e,t,n,r){function i(s){var u;return o[s]=!0,ie.each(e[s]||[],function(e,s){var l=s(t,n,r);return"string"!=typeof l||a||o[l]?a?!(u=l):void 0:(t.dataTypes.unshift(l),i(l),!1)}),u}var o={},a=e===qt;return i(t.dataTypes[0])||!o["*"]&&i("*")}function H(e,t){var n,r,i=ie.ajaxSettings.flatOptions||{};for(r in t)void 0!==t[r]&&((i[r]?e:n||(n={}))[r]=t[r]);return n&&ie.extend(!0,e,n),e}function B(e,t,n){for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(a in s)if(s[a]&&s[a].test(i)){u.unshift(a);break}if(u[0]in n)o=u[0];else{for(a in n){if(!u[0]||e.converters[a+" "+u[0]]){o=a;break}r||(r=a)}o=o||r}if(o)return o!==u[0]&&u.unshift(o),n[o]}function q(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(a=l[u+" "+o]||l["* "+o],!a)for(i in l)if(s=i.split(" "),s[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){a===!0?a=l[i]:l[i]!==!0&&(o=s[0],c.unshift(s[1]));break}if(a!==!0)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(f){return{state:"parsererror",error:a?f:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}function U(e,t,n,r){var i;if(ie.isArray(t))ie.each(t,function(t,i){n||Gt.test(e)?r(e,i):U(e+"["+("object"==typeof i?t:"")+"]",i,n,r)});else if(n||"object"!==ie.type(t))r(e,t);else for(i in t)U(e+"["+i+"]",t[i],n,r)}function W(){try{return new e.XMLHttpRequest}catch(t){}}function z(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}function G(e){return ie.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}var Y=[],X=Y.slice,J=Y.concat,Q=Y.push,K=Y.indexOf,Z={},ee=Z.toString,te=Z.hasOwnProperty,ne={},re="1.11.2",ie=function(e,t){return new ie.fn.init(e,t)},oe=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,ae=/^-ms-/,se=/-([\da-z])/gi,ue=function(e,t){return t.toUpperCase()};ie.fn=ie.prototype={jquery:re,constructor:ie,selector:"",length:0,toArray:function(){return X.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:X.call(this)},pushStack:function(e){var t=ie.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return ie.each(this,e,t)},map:function(e){return this.pushStack(ie.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(X.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:Q,sort:Y.sort,splice:Y.splice},ie.extend=ie.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||ie.isFunction(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(i=arguments[s]))for(r in i)e=a[r],n=i[r],a!==n&&(l&&n&&(ie.isPlainObject(n)||(t=ie.isArray(n)))?(t?(t=!1,o=e&&ie.isArray(e)?e:[]):o=e&&ie.isPlainObject(e)?e:{},a[r]=ie.extend(l,o,n)):void 0!==n&&(a[r]=n));return a},ie.extend({expando:"jQuery"+(re+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===ie.type(e)},isArray:Array.isArray||function(e){return"array"===ie.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!ie.isArray(e)&&e-parseFloat(e)+1>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==ie.type(e)||e.nodeType||ie.isWindow(e))return!1;try{if(e.constructor&&!te.call(e,"constructor")&&!te.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(n){return!1}if(ne.ownLast)for(t in e)return te.call(e,t);for(t in e);return void 0===t||te.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?Z[ee.call(e)]||"object":typeof e},globalEval:function(t){t&&ie.trim(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(ae,"ms-").replace(se,ue)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,r){var i,o=0,a=e.length,s=n(e);if(r){if(s)for(;o<a&&(i=t.apply(e[o],r),i!==!1);o++);else for(o in e)if(i=t.apply(e[o],r),i===!1)break}else if(s)for(;o<a&&(i=t.call(e[o],o,e[o]),i!==!1);o++);else for(o in e)if(i=t.call(e[o],o,e[o]),i===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(oe,"")},makeArray:function(e,t){var r=t||[];return null!=e&&(n(Object(e))?ie.merge(r,"string"==typeof e?[e]:e):Q.call(r,e)),r},inArray:function(e,t,n){var r;if(t){if(K)return K.call(t,e,n);for(r=t.length,n=n?n<0?Math.max(0,r+n):n:0;n<r;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;)e[i++]=t[r++];if(n!==n)for(;void 0!==t[r];)e[i++]=t[r++];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,a=e.length,s=!n;o<a;o++)r=!t(e[o],o),r!==s&&i.push(e[o]);return i},map:function(e,t,r){var i,o=0,a=e.length,s=n(e),u=[];if(s)for(;o<a;o++)i=t(e[o],o,r),null!=i&&u.push(i);else for(o in e)i=t(e[o],o,r),null!=i&&u.push(i);return J.apply([],u)},guid:1,proxy:function(e,t){var n,r,i;if("string"==typeof t&&(i=e[t],t=e,e=i),ie.isFunction(e))return n=X.call(arguments,2),r=function(){return e.apply(t||this,n.concat(X.call(arguments)))},r.guid=e.guid=e.guid||ie.guid++,r},now:function(){return+new Date},support:ne}),ie.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){Z["[object "+t+"]"]=t.toLowerCase()});var le=function(e){function t(e,t,n,r){var i,o,a,s,u,l,f,p,h,m;if((t?t.ownerDocument||t:F)!==D&&O(t),t=t||D,n=n||[],s=t.nodeType,"string"!=typeof e||!e||1!==s&&9!==s&&11!==s)return n;if(!r&&M){if(11!==s&&(i=$e.exec(e)))if(a=i[1]){if(9===s){if(o=t.getElementById(a),!o||!o.parentNode)return n;if(o.id===a)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(a))&&V(t,o)&&o.id===a)return n.push(o),n}else{if(i[2])return K.apply(n,t.getElementsByTagName(e)),n;if((a=i[3])&&w.getElementsByClassName)return K.apply(n,t.getElementsByClassName(a)),n}if(w.qsa&&(!j||!j.test(e))){if(p=f=R,h=t,m=1!==s&&e,1===s&&"object"!==t.nodeName.toLowerCase()){for(l=C(e),(f=t.getAttribute("id"))?p=f.replace(be,"\\$&"):t.setAttribute("id",p),p="[id='"+p+"'] ",u=l.length;u--;)l[u]=p+d(l[u]);h=ye.test(e)&&c(t.parentNode)||t,m=l.join(",")}if(m)try{return K.apply(n,h.querySelectorAll(m)),n}catch(g){}finally{f||t.removeAttribute("id")}}}return k(e.replace(ue,"$1"),t,n,r)}function n(){function e(n,r){return t.push(n+" ")>x.cacheLength&&delete e[t.shift()],e[n+" "]=r}var t=[];return e}function r(e){return e[R]=!0,e}function i(e){var t=D.createElement("div");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),r=e.length;r--;)x.attrHandle[n[r]]=t}function a(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||G)-(~e.sourceIndex||G);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function s(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function u(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function l(e){return r(function(t){return t=+t,r(function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))})})}function c(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function f(){}function d(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function p(e,t,n){var r=t.dir,i=n&&"parentNode"===r,o=B++;return t.first?function(t,n,o){for(;t=t[r];)if(1===t.nodeType||i)return e(t,n,o)}:function(t,n,a){var s,u,l=[H,o];if(a){for(;t=t[r];)if((1===t.nodeType||i)&&e(t,n,a))return!0}else for(;t=t[r];)if(1===t.nodeType||i){if(u=t[R]||(t[R]={}),(s=u[r])&&s[0]===H&&s[1]===o)return l[2]=s[2];if(u[r]=l,l[2]=e(t,n,a))return!0}}}function h(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function m(e,n,r){for(var i=0,o=n.length;i<o;i++)t(e,n[i],r);return r}function g(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function v(e,t,n,i,o,a){return i&&!i[R]&&(i=v(i)),o&&!o[R]&&(o=v(o,a)),r(function(r,a,s,u){var l,c,f,d=[],p=[],h=a.length,v=r||m(t||"*",s.nodeType?[s]:s,[]),$=!e||!r&&t?v:g(v,d,e,s,u),y=n?o||(r?e:h||i)?[]:a:$;if(n&&n($,y,s,u),i)for(l=g(y,p),i(l,[],s,u),c=l.length;c--;)(f=l[c])&&(y[p[c]]=!($[p[c]]=f));if(r){if(o||e){if(o){for(l=[],c=y.length;c--;)(f=y[c])&&l.push($[c]=f);o(null,y=[],l,u)}for(c=y.length;c--;)(f=y[c])&&(l=o?ee(r,f):d[c])>-1&&(r[l]=!(a[l]=f))}}else y=g(y===a?y.splice(h,y.length):y),o?o(null,a,y,u):K.apply(a,y)})}function $(e){for(var t,n,r,i=e.length,o=x.relative[e[0].type],a=o||x.relative[" "],s=o?1:0,u=p(function(e){return e===t},a,!0),l=p(function(e){return ee(t,e)>-1},a,!0),c=[function(e,n,r){var i=!o&&(r||n!==_)||((t=n).nodeType?u(e,n,r):l(e,n,r));return t=null,i}];s<i;s++)if(n=x.relative[e[s].type])c=[p(h(c),n)];else{if(n=x.filter[e[s].type].apply(null,e[s].matches),n[R]){for(r=++s;r<i&&!x.relative[e[r].type];r++);return v(s>1&&h(c),s>1&&d(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(ue,"$1"),n,s<r&&$(e.slice(s,r)),r<i&&$(e=e.slice(r)),r<i&&d(e))}c.push(n)}return h(c)}function y(e,n){var i=n.length>0,o=e.length>0,a=function(r,a,s,u,l){var c,f,d,p=0,h="0",m=r&&[],v=[],$=_,y=r||o&&x.find.TAG("*",l),b=H+=null==$?1:Math.random()||.1,w=y.length;for(l&&(_=a!==D&&a);h!==w&&null!=(c=y[h]);h++){if(o&&c){for(f=0;d=e[f++];)if(d(c,a,s)){u.push(c);break}l&&(H=b)}i&&((c=!d&&c)&&p--,r&&m.push(c))}if(p+=h,i&&h!==p){for(f=0;d=n[f++];)d(m,v,a,s);if(r){if(p>0)for(;h--;)m[h]||v[h]||(v[h]=J.call(u));v=g(v)}K.apply(u,v),l&&!r&&v.length>0&&p+n.length>1&&t.uniqueSort(u)}return l&&(H=b,_=$),m};return i?r(a):a}var b,w,x,E,T,C,S,k,_,A,N,O,D,I,M,j,P,L,V,R="sizzle"+1*new Date,F=e.document,H=0,B=0,q=n(),U=n(),W=n(),z=function(e,t){return e===t&&(N=!0),0},G=1<<31,Y={}.hasOwnProperty,X=[],J=X.pop,Q=X.push,K=X.push,Z=X.slice,ee=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;
return-1},te="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ne="[\\x20\\t\\r\\n\\f]",re="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ie=re.replace("w","w#"),oe="\\["+ne+"*("+re+")(?:"+ne+"*([*^$|!~]?=)"+ne+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+ie+"))|)"+ne+"*\\]",ae=":("+re+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+oe+")*)|.*)\\)|)",se=new RegExp(ne+"+","g"),ue=new RegExp("^"+ne+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ne+"+$","g"),le=new RegExp("^"+ne+"*,"+ne+"*"),ce=new RegExp("^"+ne+"*([>+~]|"+ne+")"+ne+"*"),fe=new RegExp("="+ne+"*([^\\]'\"]*?)"+ne+"*\\]","g"),de=new RegExp(ae),pe=new RegExp("^"+ie+"$"),he={ID:new RegExp("^#("+re+")"),CLASS:new RegExp("^\\.("+re+")"),TAG:new RegExp("^("+re.replace("w","w*")+")"),ATTR:new RegExp("^"+oe),PSEUDO:new RegExp("^"+ae),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ne+"*(even|odd|(([+-]|)(\\d*)n|)"+ne+"*(?:([+-]|)"+ne+"*(\\d+)|))"+ne+"*\\)|)","i"),bool:new RegExp("^(?:"+te+")$","i"),needsContext:new RegExp("^"+ne+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ne+"*((?:-\\d)?\\d*)"+ne+"*\\)|)(?=[^-]|$)","i")},me=/^(?:input|select|textarea|button)$/i,ge=/^h\d$/i,ve=/^[^{]+\{\s*\[native \w/,$e=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ye=/[+~]/,be=/'|\\/g,we=new RegExp("\\\\([\\da-f]{1,6}"+ne+"?|("+ne+")|.)","ig"),xe=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},Ee=function(){O()};try{K.apply(X=Z.call(F.childNodes),F.childNodes),X[F.childNodes.length].nodeType}catch(Te){K={apply:X.length?function(e,t){Q.apply(e,Z.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}w=t.support={},T=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},O=t.setDocument=function(e){var t,n,r=e?e.ownerDocument||e:F;return r!==D&&9===r.nodeType&&r.documentElement?(D=r,I=r.documentElement,n=r.defaultView,n&&n!==n.top&&(n.addEventListener?n.addEventListener("unload",Ee,!1):n.attachEvent&&n.attachEvent("onunload",Ee)),M=!T(r),w.attributes=i(function(e){return e.className="i",!e.getAttribute("className")}),w.getElementsByTagName=i(function(e){return e.appendChild(r.createComment("")),!e.getElementsByTagName("*").length}),w.getElementsByClassName=ve.test(r.getElementsByClassName),w.getById=i(function(e){return I.appendChild(e).id=R,!r.getElementsByName||!r.getElementsByName(R).length}),w.getById?(x.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&M){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},x.filter.ID=function(e){var t=e.replace(we,xe);return function(e){return e.getAttribute("id")===t}}):(delete x.find.ID,x.filter.ID=function(e){var t=e.replace(we,xe);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),x.find.TAG=w.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):w.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},x.find.CLASS=w.getElementsByClassName&&function(e,t){if(M)return t.getElementsByClassName(e)},P=[],j=[],(w.qsa=ve.test(r.querySelectorAll))&&(i(function(e){I.appendChild(e).innerHTML="<a id='"+R+"'></a><select id='"+R+"-\f]' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&j.push("[*^$]="+ne+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||j.push("\\["+ne+"*(?:value|"+te+")"),e.querySelectorAll("[id~="+R+"-]").length||j.push("~="),e.querySelectorAll(":checked").length||j.push(":checked"),e.querySelectorAll("a#"+R+"+*").length||j.push(".#.+[+~]")}),i(function(e){var t=r.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&j.push("name"+ne+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||j.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),j.push(",.*:")})),(w.matchesSelector=ve.test(L=I.matches||I.webkitMatchesSelector||I.mozMatchesSelector||I.oMatchesSelector||I.msMatchesSelector))&&i(function(e){w.disconnectedMatch=L.call(e,"div"),L.call(e,"[s!='']:x"),P.push("!=",ae)}),j=j.length&&new RegExp(j.join("|")),P=P.length&&new RegExp(P.join("|")),t=ve.test(I.compareDocumentPosition),V=t||ve.test(I.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},z=t?function(e,t){if(e===t)return N=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n?n:(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!w.sortDetached&&t.compareDocumentPosition(e)===n?e===r||e.ownerDocument===F&&V(F,e)?-1:t===r||t.ownerDocument===F&&V(F,t)?1:A?ee(A,e)-ee(A,t):0:4&n?-1:1)}:function(e,t){if(e===t)return N=!0,0;var n,i=0,o=e.parentNode,s=t.parentNode,u=[e],l=[t];if(!o||!s)return e===r?-1:t===r?1:o?-1:s?1:A?ee(A,e)-ee(A,t):0;if(o===s)return a(e,t);for(n=e;n=n.parentNode;)u.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);for(;u[i]===l[i];)i++;return i?a(u[i],l[i]):u[i]===F?-1:l[i]===F?1:0},r):D},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==D&&O(e),n=n.replace(fe,"='$1']"),w.matchesSelector&&M&&(!P||!P.test(n))&&(!j||!j.test(n)))try{var r=L.call(e,n);if(r||w.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(i){}return t(n,D,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==D&&O(e),V(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==D&&O(e);var n=x.attrHandle[t.toLowerCase()],r=n&&Y.call(x.attrHandle,t.toLowerCase())?n(e,t,!M):void 0;return void 0!==r?r:w.attributes||!M?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],r=0,i=0;if(N=!w.detectDuplicates,A=!w.sortStable&&e.slice(0),e.sort(z),N){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return A=null,e},E=t.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=E(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=E(t);return n},x=t.selectors={cacheLength:50,createPseudo:r,match:he,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(we,xe),e[3]=(e[3]||e[4]||e[5]||"").replace(we,xe),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return he.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&de.test(n)&&(t=C(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(we,xe).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=q[e+" "];return t||(t=new RegExp("(^|"+ne+")"+e+"("+ne+"|$)"))&&q(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,n,r){return function(i){var o=t.attr(i,e);return null==o?"!="===n:!n||(o+="","="===n?o===r:"!="===n?o!==r:"^="===n?r&&0===o.indexOf(r):"*="===n?r&&o.indexOf(r)>-1:"$="===n?r&&o.slice(-r.length)===r:"~="===n?(" "+o.replace(se," ")+" ").indexOf(r)>-1:"|="===n&&(o===r||o.slice(0,r.length+1)===r+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,f,d,p,h,m=o!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),$=!u&&!s;if(g){if(o){for(;m;){for(f=t;f=f[m];)if(s?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;h=m="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?g.firstChild:g.lastChild],a&&$){for(c=g[R]||(g[R]={}),l=c[e]||[],p=l[0]===H&&l[1],d=l[0]===H&&l[2],f=p&&g.childNodes[p];f=++p&&f&&f[m]||(d=p=0)||h.pop();)if(1===f.nodeType&&++d&&f===t){c[e]=[H,p,d];break}}else if($&&(l=(t[R]||(t[R]={}))[e])&&l[0]===H)d=l[1];else for(;(f=++p&&f&&f[m]||(d=p=0)||h.pop())&&((s?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++d||($&&((f[R]||(f[R]={}))[e]=[H,d]),f!==t)););return d-=i,d===r||d%r===0&&d/r>=0}}},PSEUDO:function(e,n){var i,o=x.pseudos[e]||x.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[R]?o(n):o.length>1?(i=[e,e,"",n],x.setFilters.hasOwnProperty(e.toLowerCase())?r(function(e,t){for(var r,i=o(e,n),a=i.length;a--;)r=ee(e,i[a]),e[r]=!(t[r]=i[a])}):function(e){return o(e,0,i)}):o}},pseudos:{not:r(function(e){var t=[],n=[],i=S(e.replace(ue,"$1"));return i[R]?r(function(e,t,n,r){for(var o,a=i(e,null,r,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}}),has:r(function(e){return function(n){return t(e,n).length>0}}),contains:r(function(e){return e=e.replace(we,xe),function(t){return(t.textContent||t.innerText||E(t)).indexOf(e)>-1}}),lang:r(function(e){return pe.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(we,xe).toLowerCase(),function(t){var n;do if(n=M?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===I},focus:function(e){return e===D.activeElement&&(!D.hasFocus||D.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!x.pseudos.empty(e)},header:function(e){return ge.test(e.nodeName)},input:function(e){return me.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:l(function(){return[0]}),last:l(function(e,t){return[t-1]}),eq:l(function(e,t,n){return[n<0?n+t:n]}),even:l(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:l(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:l(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:l(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},x.pseudos.nth=x.pseudos.eq;for(b in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[b]=s(b);for(b in{submit:!0,reset:!0})x.pseudos[b]=u(b);return f.prototype=x.filters=x.pseudos,x.setFilters=new f,C=t.tokenize=function(e,n){var r,i,o,a,s,u,l,c=U[e+" "];if(c)return n?0:c.slice(0);for(s=e,u=[],l=x.preFilter;s;){r&&!(i=le.exec(s))||(i&&(s=s.slice(i[0].length)||s),u.push(o=[])),r=!1,(i=ce.exec(s))&&(r=i.shift(),o.push({value:r,type:i[0].replace(ue," ")}),s=s.slice(r.length));for(a in x.filter)!(i=he[a].exec(s))||l[a]&&!(i=l[a](i))||(r=i.shift(),o.push({value:r,type:a,matches:i}),s=s.slice(r.length));if(!r)break}return n?s.length:s?t.error(e):U(e,u).slice(0)},S=t.compile=function(e,t){var n,r=[],i=[],o=W[e+" "];if(!o){for(t||(t=C(e)),n=t.length;n--;)o=$(t[n]),o[R]?r.push(o):i.push(o);o=W(e,y(i,r)),o.selector=e}return o},k=t.select=function(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,f=!r&&C(e=l.selector||e);if(n=n||[],1===f.length){if(o=f[0]=f[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&w.getById&&9===t.nodeType&&M&&x.relative[o[1].type]){if(t=(x.find.ID(a.matches[0].replace(we,xe),t)||[])[0],!t)return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=he.needsContext.test(e)?0:o.length;i--&&(a=o[i],!x.relative[s=a.type]);)if((u=x.find[s])&&(r=u(a.matches[0].replace(we,xe),ye.test(o[0].type)&&c(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&d(o),!e)return K.apply(n,r),n;break}}return(l||S(e,f))(r,t,!M,n,ye.test(e)&&c(t.parentNode)||t),n},w.sortStable=R.split("").sort(z).join("")===R,w.detectDuplicates=!!N,O(),w.sortDetached=i(function(e){return 1&e.compareDocumentPosition(D.createElement("div"))}),i(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),w.attributes&&i(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),i(function(e){return null==e.getAttribute("disabled")})||o(te,function(e,t,n){var r;if(!n)return e[t]===!0?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),t}(e);ie.find=le,ie.expr=le.selectors,ie.expr[":"]=ie.expr.pseudos,ie.unique=le.uniqueSort,ie.text=le.getText,ie.isXMLDoc=le.isXML,ie.contains=le.contains;var ce=ie.expr.match.needsContext,fe=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,de=/^.[^:#\[\.,]*$/;ie.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?ie.find.matchesSelector(r,e)?[r]:[]:ie.find.matches(e,ie.grep(t,function(e){return 1===e.nodeType}))},ie.fn.extend({find:function(e){var t,n=[],r=this,i=r.length;if("string"!=typeof e)return this.pushStack(ie(e).filter(function(){for(t=0;t<i;t++)if(ie.contains(r[t],this))return!0}));for(t=0;t<i;t++)ie.find(e,r[t],n);return n=this.pushStack(i>1?ie.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(r(this,e||[],!1))},not:function(e){return this.pushStack(r(this,e||[],!0))},is:function(e){return!!r(this,"string"==typeof e&&ce.test(e)?ie(e):e||[],!1).length}});var pe,he=e.document,me=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,ge=ie.fn.init=function(e,t){var n,r;if(!e)return this;if("string"==typeof e){if(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:me.exec(e),!n||!n[1]&&t)return!t||t.jquery?(t||pe).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof ie?t[0]:t,ie.merge(this,ie.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:he,!0)),fe.test(n[1])&&ie.isPlainObject(t))for(n in t)ie.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if(r=he.getElementById(n[2]),r&&r.parentNode){if(r.id!==n[2])return pe.find(e);this.length=1,this[0]=r}return this.context=he,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):ie.isFunction(e)?"undefined"!=typeof pe.ready?pe.ready(e):e(ie):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),ie.makeArray(e,this))};ge.prototype=ie.fn,pe=ie(he);var ve=/^(?:parents|prev(?:Until|All))/,$e={children:!0,contents:!0,next:!0,prev:!0};ie.extend({dir:function(e,t,n){for(var r=[],i=e[t];i&&9!==i.nodeType&&(void 0===n||1!==i.nodeType||!ie(i).is(n));)1===i.nodeType&&r.push(i),i=i[t];return r},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),ie.fn.extend({has:function(e){var t,n=ie(e,this),r=n.length;return this.filter(function(){for(t=0;t<r;t++)if(ie.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,r=0,i=this.length,o=[],a=ce.test(e)||"string"!=typeof e?ie(e,t||this.context):0;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&ie.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?ie.unique(o):o)},index:function(e){return e?"string"==typeof e?ie.inArray(this[0],ie(e)):ie.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(ie.unique(ie.merge(this.get(),ie(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),ie.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return ie.dir(e,"parentNode")},parentsUntil:function(e,t,n){return ie.dir(e,"parentNode",n)},next:function(e){return i(e,"nextSibling")},prev:function(e){return i(e,"previousSibling")},nextAll:function(e){return ie.dir(e,"nextSibling")},prevAll:function(e){return ie.dir(e,"previousSibling")},nextUntil:function(e,t,n){return ie.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return ie.dir(e,"previousSibling",n)},siblings:function(e){return ie.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return ie.sibling(e.firstChild)},contents:function(e){return ie.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:ie.merge([],e.childNodes)}},function(e,t){ie.fn[e]=function(n,r){var i=ie.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=ie.filter(r,i)),this.length>1&&($e[e]||(i=ie.unique(i)),ve.test(e)&&(i=i.reverse())),this.pushStack(i)}});var ye=/\S+/g,be={};ie.Callbacks=function(e){e="string"==typeof e?be[e]||o(e):ie.extend({},e);var t,n,r,i,a,s,u=[],l=!e.once&&[],c=function(o){for(n=e.memory&&o,r=!0,a=s||0,s=0,i=u.length,t=!0;u&&a<i;a++)if(u[a].apply(o[0],o[1])===!1&&e.stopOnFalse){n=!1;break}t=!1,u&&(l?l.length&&c(l.shift()):n?u=[]:f.disable())},f={add:function(){if(u){var r=u.length;!function o(t){ie.each(t,function(t,n){var r=ie.type(n);"function"===r?e.unique&&f.has(n)||u.push(n):n&&n.length&&"string"!==r&&o(n)})}(arguments),t?i=u.length:n&&(s=r,c(n))}return this},remove:function(){return u&&ie.each(arguments,function(e,n){for(var r;(r=ie.inArray(n,u,r))>-1;)u.splice(r,1),t&&(r<=i&&i--,r<=a&&a--)}),this},has:function(e){return e?ie.inArray(e,u)>-1:!(!u||!u.length)},empty:function(){return u=[],i=0,this},disable:function(){return u=l=n=void 0,this},disabled:function(){return!u},lock:function(){return l=void 0,n||f.disable(),this},locked:function(){return!l},fireWith:function(e,n){return!u||r&&!l||(n=n||[],n=[e,n.slice?n.slice():n],t?l.push(n):c(n)),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!r}};return f},ie.extend({Deferred:function(e){var t=[["resolve","done",ie.Callbacks("once memory"),"resolved"],["reject","fail",ie.Callbacks("once memory"),"rejected"],["notify","progress",ie.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return ie.Deferred(function(n){ie.each(t,function(t,o){var a=ie.isFunction(e[t])&&e[t];i[o[1]](function(){var e=a&&a.apply(this,arguments);e&&ie.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o[0]+"With"](this===r?n.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?ie.extend(e,r):r}},i={};return r.pipe=r.then,ie.each(t,function(e,o){var a=o[2],s=o[3];r[o[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),i[o[0]]=function(){return i[o[0]+"With"](this===i?r:this,arguments),this},i[o[0]+"With"]=a.fireWith}),r.promise(i),e&&e.call(i,i),i},when:function(e){var t,n,r,i=0,o=X.call(arguments),a=o.length,s=1!==a||e&&ie.isFunction(e.promise)?a:0,u=1===s?e:ie.Deferred(),l=function(e,n,r){return function(i){n[e]=this,r[e]=arguments.length>1?X.call(arguments):i,r===t?u.notifyWith(n,r):--s||u.resolveWith(n,r)}};if(a>1)for(t=new Array(a),n=new Array(a),r=new Array(a);i<a;i++)o[i]&&ie.isFunction(o[i].promise)?o[i].promise().done(l(i,r,o)).fail(u.reject).progress(l(i,n,t)):--s;return s||u.resolveWith(r,o),u.promise()}});var we;ie.fn.ready=function(e){return ie.ready.promise().done(e),this},ie.extend({isReady:!1,readyWait:1,holdReady:function(e){e?ie.readyWait++:ie.ready(!0)},ready:function(e){if(e===!0?!--ie.readyWait:!ie.isReady){if(!he.body)return setTimeout(ie.ready);ie.isReady=!0,e!==!0&&--ie.readyWait>0||(we.resolveWith(he,[ie]),ie.fn.triggerHandler&&(ie(he).triggerHandler("ready"),ie(he).off("ready")))}}}),ie.ready.promise=function(t){if(!we)if(we=ie.Deferred(),"complete"===he.readyState)setTimeout(ie.ready);else if(he.addEventListener)he.addEventListener("DOMContentLoaded",s,!1),e.addEventListener("load",s,!1);else{he.attachEvent("onreadystatechange",s),e.attachEvent("onload",s);var n=!1;try{n=null==e.frameElement&&he.documentElement}catch(r){}n&&n.doScroll&&!function i(){if(!ie.isReady){try{n.doScroll("left")}catch(e){return setTimeout(i,50)}a(),ie.ready()}}()}return we.promise(t)};var xe,Ee="undefined";for(xe in ie(ne))break;ne.ownLast="0"!==xe,ne.inlineBlockNeedsLayout=!1,ie(function(){var e,t,n,r;n=he.getElementsByTagName("body")[0],n&&n.style&&(t=he.createElement("div"),r=he.createElement("div"),r.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(t),typeof t.style.zoom!==Ee&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",ne.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(r))}),function(){var e=he.createElement("div");if(null==ne.deleteExpando){ne.deleteExpando=!0;try{delete e.test}catch(t){ne.deleteExpando=!1}}e=null}(),ie.acceptData=function(e){var t=ie.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||t!==!0&&e.getAttribute("classid")===t)};var Te=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ce=/([A-Z])/g;ie.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return e=e.nodeType?ie.cache[e[ie.expando]]:e[ie.expando],!!e&&!l(e)},data:function(e,t,n){return c(e,t,n)},removeData:function(e,t){return f(e,t)},_data:function(e,t,n){return c(e,t,n,!0)},_removeData:function(e,t){return f(e,t,!0)}}),ie.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=ie.data(o),1===o.nodeType&&!ie._data(o,"parsedAttrs"))){for(n=a.length;n--;)a[n]&&(r=a[n].name,0===r.indexOf("data-")&&(r=ie.camelCase(r.slice(5)),u(o,r,i[r])));ie._data(o,"parsedAttrs",!0)}return i}return"object"==typeof e?this.each(function(){ie.data(this,e)}):arguments.length>1?this.each(function(){ie.data(this,e,t)}):o?u(o,e,ie.data(o,e)):void 0},removeData:function(e){return this.each(function(){ie.removeData(this,e)})}}),ie.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=ie._data(e,t),n&&(!r||ie.isArray(n)?r=ie._data(e,t,ie.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=ie.queue(e,t),r=n.length,i=n.shift(),o=ie._queueHooks(e,t),a=function(){ie.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,a,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ie._data(e,n)||ie._data(e,n,{empty:ie.Callbacks("once memory").add(function(){ie._removeData(e,t+"queue"),ie._removeData(e,n)})})}}),ie.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?ie.queue(this[0],e):void 0===t?this:this.each(function(){var n=ie.queue(this,e,t);ie._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&ie.dequeue(this,e)})},dequeue:function(e){return this.each(function(){ie.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=ie.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)n=ie._data(o[a],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var Se=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ke=["Top","Right","Bottom","Left"],_e=function(e,t){return e=t||e,"none"===ie.css(e,"display")||!ie.contains(e.ownerDocument,e)},Ae=ie.access=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===ie.type(n)){i=!0;for(s in n)ie.access(e,t,s,n[s],!0,o,a)}else if(void 0!==r&&(i=!0,ie.isFunction(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(ie(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},Ne=/^(?:checkbox|radio)$/i;!function(){var e=he.createElement("input"),t=he.createElement("div"),n=he.createDocumentFragment();if(t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",ne.leadingWhitespace=3===t.firstChild.nodeType,ne.tbody=!t.getElementsByTagName("tbody").length,ne.htmlSerialize=!!t.getElementsByTagName("link").length,ne.html5Clone="<:nav></:nav>"!==he.createElement("nav").cloneNode(!0).outerHTML,e.type="checkbox",e.checked=!0,n.appendChild(e),ne.appendChecked=e.checked,t.innerHTML="<textarea>x</textarea>",ne.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,n.appendChild(t),t.innerHTML="<input type='radio' checked='checked' name='t'/>",ne.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,ne.noCloneEvent=!0,t.attachEvent&&(t.attachEvent("onclick",function(){ne.noCloneEvent=!1}),t.cloneNode(!0).click()),null==ne.deleteExpando){ne.deleteExpando=!0;try{delete t.test}catch(r){ne.deleteExpando=!1}}}(),function(){var t,n,r=he.createElement("div");for(t in{submit:!0,change:!0,focusin:!0})n="on"+t,(ne[t+"Bubbles"]=n in e)||(r.setAttribute(n,"t"),ne[t+"Bubbles"]=r.attributes[n].expando===!1);r=null}();var Oe=/^(?:input|select|textarea)$/i,De=/^key/,Ie=/^(?:mouse|pointer|contextmenu)|click/,Me=/^(?:focusinfocus|focusoutblur)$/,je=/^([^.]*)(?:\.(.+)|)$/;ie.event={global:{},add:function(e,t,n,r,i){var o,a,s,u,l,c,f,d,p,h,m,g=ie._data(e);if(g){for(n.handler&&(u=n,n=u.handler,i=u.selector),n.guid||(n.guid=ie.guid++),(a=g.events)||(a=g.events={}),(c=g.handle)||(c=g.handle=function(e){return typeof ie===Ee||e&&ie.event.triggered===e.type?void 0:ie.event.dispatch.apply(c.elem,arguments)},c.elem=e),t=(t||"").match(ye)||[""],s=t.length;s--;)o=je.exec(t[s])||[],p=m=o[1],h=(o[2]||"").split(".").sort(),p&&(l=ie.event.special[p]||{},p=(i?l.delegateType:l.bindType)||p,l=ie.event.special[p]||{},f=ie.extend({type:p,origType:m,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&ie.expr.match.needsContext.test(i),namespace:h.join(".")},u),(d=a[p])||(d=a[p]=[],d.delegateCount=0,l.setup&&l.setup.call(e,r,h,c)!==!1||(e.addEventListener?e.addEventListener(p,c,!1):e.attachEvent&&e.attachEvent("on"+p,c))),l.add&&(l.add.call(e,f),f.handler.guid||(f.handler.guid=n.guid)),i?d.splice(d.delegateCount++,0,f):d.push(f),ie.event.global[p]=!0);e=null}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,d,p,h,m,g=ie.hasData(e)&&ie._data(e);if(g&&(c=g.events)){for(t=(t||"").match(ye)||[""],l=t.length;l--;)if(s=je.exec(t[l])||[],p=m=s[1],h=(s[2]||"").split(".").sort(),p){for(f=ie.event.special[p]||{},p=(r?f.delegateType:f.bindType)||p,d=c[p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),u=o=d.length;o--;)a=d[o],!i&&m!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||r&&r!==a.selector&&("**"!==r||!a.selector)||(d.splice(o,1),a.selector&&d.delegateCount--,f.remove&&f.remove.call(e,a));u&&!d.length&&(f.teardown&&f.teardown.call(e,h,g.handle)!==!1||ie.removeEvent(e,p,g.handle),delete c[p])}else for(p in c)ie.event.remove(e,p+t[l],n,r,!0);ie.isEmptyObject(c)&&(delete g.handle,ie._removeData(e,"events"))}},trigger:function(t,n,r,i){var o,a,s,u,l,c,f,d=[r||he],p=te.call(t,"type")?t.type:t,h=te.call(t,"namespace")?t.namespace.split("."):[];if(s=c=r=r||he,3!==r.nodeType&&8!==r.nodeType&&!Me.test(p+ie.event.triggered)&&(p.indexOf(".")>=0&&(h=p.split("."),p=h.shift(),h.sort()),a=p.indexOf(":")<0&&"on"+p,t=t[ie.expando]?t:new ie.Event(p,"object"==typeof t&&t),t.isTrigger=i?2:3,t.namespace=h.join("."),t.namespace_re=t.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:ie.makeArray(n,[t]),l=ie.event.special[p]||{},i||!l.trigger||l.trigger.apply(r,n)!==!1)){if(!i&&!l.noBubble&&!ie.isWindow(r)){for(u=l.delegateType||p,Me.test(u+p)||(s=s.parentNode);s;s=s.parentNode)d.push(s),c=s;c===(r.ownerDocument||he)&&d.push(c.defaultView||c.parentWindow||e)}for(f=0;(s=d[f++])&&!t.isPropagationStopped();)t.type=f>1?u:l.bindType||p,o=(ie._data(s,"events")||{})[t.type]&&ie._data(s,"handle"),o&&o.apply(s,n),o=a&&s[a],o&&o.apply&&ie.acceptData(s)&&(t.result=o.apply(s,n),t.result===!1&&t.preventDefault());if(t.type=p,!i&&!t.isDefaultPrevented()&&(!l._default||l._default.apply(d.pop(),n)===!1)&&ie.acceptData(r)&&a&&r[p]&&!ie.isWindow(r)){c=r[a],c&&(r[a]=null),ie.event.triggered=p;try{r[p]()}catch(m){}ie.event.triggered=void 0,c&&(r[a]=c)}return t.result}},dispatch:function(e){e=ie.event.fix(e);var t,n,r,i,o,a=[],s=X.call(arguments),u=(ie._data(this,"events")||{})[e.type]||[],l=ie.event.special[e.type]||{};if(s[0]=e,e.delegateTarget=this,!l.preDispatch||l.preDispatch.call(this,e)!==!1){for(a=ie.event.handlers.call(this,e,u),t=0;(i=a[t++])&&!e.isPropagationStopped();)for(e.currentTarget=i.elem,o=0;(r=i.handlers[o++])&&!e.isImmediatePropagationStopped();)e.namespace_re&&!e.namespace_re.test(r.namespace)||(e.handleObj=r,e.data=r.data,n=((ie.event.special[r.origType]||{}).handle||r.handler).apply(i.elem,s),void 0!==n&&(e.result=n)===!1&&(e.preventDefault(),e.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,o,a=[],s=t.delegateCount,u=e.target;if(s&&u.nodeType&&(!e.button||"click"!==e.type))for(;u!=this;u=u.parentNode||this)if(1===u.nodeType&&(u.disabled!==!0||"click"!==e.type)){for(i=[],o=0;o<s;o++)r=t[o],n=r.selector+" ",void 0===i[n]&&(i[n]=r.needsContext?ie(n,this).index(u)>=0:ie.find(n,this,null,[u]).length),i[n]&&i.push(r);i.length&&a.push({elem:u,handlers:i})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[ie.expando])return e;var t,n,r,i=e.type,o=e,a=this.fixHooks[i];for(a||(this.fixHooks[i]=a=Ie.test(i)?this.mouseHooks:De.test(i)?this.keyHooks:{}),r=a.props?this.props.concat(a.props):this.props,e=new ie.Event(o),t=r.length;t--;)n=r[t],e[n]=o[n];return e.target||(e.target=o.srcElement||he),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,o=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(r=e.target.ownerDocument||he,i=r.documentElement,n=r.body,e.pageX=t.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==h()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===h()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(ie.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(e){return ie.nodeName(e.target,"a")}},beforeunload:{
postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){var i=ie.extend(new ie.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?ie.event.trigger(i,null,t):ie.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},ie.removeEvent=he.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(typeof e[r]===Ee&&(e[r]=null),e.detachEvent(r,n))},ie.Event=function(e,t){return this instanceof ie.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?d:p):this.type=e,t&&ie.extend(this,t),this.timeStamp=e&&e.timeStamp||ie.now(),void(this[ie.expando]=!0)):new ie.Event(e,t)},ie.Event.prototype={isDefaultPrevented:p,isPropagationStopped:p,isImmediatePropagationStopped:p,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=d,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=d,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=d,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},ie.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){ie.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||ie.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),ne.submitBubbles||(ie.event.special.submit={setup:function(){return!ie.nodeName(this,"form")&&void ie.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=ie.nodeName(t,"input")||ie.nodeName(t,"button")?t.form:void 0;n&&!ie._data(n,"submitBubbles")&&(ie.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),ie._data(n,"submitBubbles",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&ie.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){return!ie.nodeName(this,"form")&&void ie.event.remove(this,"._submit")}}),ne.changeBubbles||(ie.event.special.change={setup:function(){return Oe.test(this.nodeName)?("checkbox"!==this.type&&"radio"!==this.type||(ie.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),ie.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),ie.event.simulate("change",this,e,!0)})),!1):void ie.event.add(this,"beforeactivate._change",function(e){var t=e.target;Oe.test(t.nodeName)&&!ie._data(t,"changeBubbles")&&(ie.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||ie.event.simulate("change",this.parentNode,e,!0)}),ie._data(t,"changeBubbles",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return ie.event.remove(this,"._change"),!Oe.test(this.nodeName)}}),ne.focusinBubbles||ie.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){ie.event.simulate(t,e.target,ie.event.fix(e),!0)};ie.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=ie._data(r,t);i||r.addEventListener(e,n,!0),ie._data(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=ie._data(r,t)-1;i?ie._data(r,t,i):(r.removeEventListener(e,n,!0),ie._removeData(r,t))}}}),ie.fn.extend({on:function(e,t,n,r,i){var o,a;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=void 0);for(o in e)this.on(o,t,n,e[o],i);return this}if(null==n&&null==r?(r=t,n=t=void 0):null==r&&("string"==typeof t?(r=n,n=void 0):(r=n,n=t,t=void 0)),r===!1)r=p;else if(!r)return this;return 1===i&&(a=r,r=function(e){return ie().off(e),a.apply(this,arguments)},r.guid=a.guid||(a.guid=ie.guid++)),this.each(function(){ie.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,ie(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return t!==!1&&"function"!=typeof t||(n=t,t=void 0),n===!1&&(n=p),this.each(function(){ie.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){ie.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return ie.event.trigger(e,t,n,!0)}});var Pe="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",Le=/ jQuery\d+="(?:null|\d+)"/g,Ve=new RegExp("<(?:"+Pe+")[\\s/>]","i"),Re=/^\s+/,Fe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,He=/<([\w:]+)/,Be=/<tbody/i,qe=/<|&#?\w+;/,Ue=/<(?:script|style|link)/i,We=/checked\s*(?:[^=]|=\s*.checked.)/i,ze=/^$|\/(?:java|ecma)script/i,Ge=/^true\/(.*)/,Ye=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Xe={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:ne.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Je=m(he),Qe=Je.appendChild(he.createElement("div"));Xe.optgroup=Xe.option,Xe.tbody=Xe.tfoot=Xe.colgroup=Xe.caption=Xe.thead,Xe.th=Xe.td,ie.extend({clone:function(e,t,n){var r,i,o,a,s,u=ie.contains(e.ownerDocument,e);if(ne.html5Clone||ie.isXMLDoc(e)||!Ve.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(Qe.innerHTML=e.outerHTML,Qe.removeChild(o=Qe.firstChild)),!(ne.noCloneEvent&&ne.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||ie.isXMLDoc(e)))for(r=g(o),s=g(e),a=0;null!=(i=s[a]);++a)r[a]&&E(i,r[a]);if(t)if(n)for(s=s||g(e),r=r||g(o),a=0;null!=(i=s[a]);a++)x(i,r[a]);else x(e,o);return r=g(o,"script"),r.length>0&&w(r,!u&&g(e,"script")),r=s=i=null,o},buildFragment:function(e,t,n,r){for(var i,o,a,s,u,l,c,f=e.length,d=m(t),p=[],h=0;h<f;h++)if(o=e[h],o||0===o)if("object"===ie.type(o))ie.merge(p,o.nodeType?[o]:o);else if(qe.test(o)){for(s=s||d.appendChild(t.createElement("div")),u=(He.exec(o)||["",""])[1].toLowerCase(),c=Xe[u]||Xe._default,s.innerHTML=c[1]+o.replace(Fe,"<$1></$2>")+c[2],i=c[0];i--;)s=s.lastChild;if(!ne.leadingWhitespace&&Re.test(o)&&p.push(t.createTextNode(Re.exec(o)[0])),!ne.tbody)for(o="table"!==u||Be.test(o)?"<table>"!==c[1]||Be.test(o)?0:s:s.firstChild,i=o&&o.childNodes.length;i--;)ie.nodeName(l=o.childNodes[i],"tbody")&&!l.childNodes.length&&o.removeChild(l);for(ie.merge(p,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=d.lastChild}else p.push(t.createTextNode(o));for(s&&d.removeChild(s),ne.appendChecked||ie.grep(g(p,"input"),v),h=0;o=p[h++];)if((!r||ie.inArray(o,r)===-1)&&(a=ie.contains(o.ownerDocument,o),s=g(d.appendChild(o),"script"),a&&w(s),n))for(i=0;o=s[i++];)ze.test(o.type||"")&&n.push(o);return s=null,d},cleanData:function(e,t){for(var n,r,i,o,a=0,s=ie.expando,u=ie.cache,l=ne.deleteExpando,c=ie.event.special;null!=(n=e[a]);a++)if((t||ie.acceptData(n))&&(i=n[s],o=i&&u[i])){if(o.events)for(r in o.events)c[r]?ie.event.remove(n,r):ie.removeEvent(n,r,o.handle);u[i]&&(delete u[i],l?delete n[s]:typeof n.removeAttribute!==Ee?n.removeAttribute(s):n[s]=null,Y.push(i))}}}),ie.fn.extend({text:function(e){return Ae(this,function(e){return void 0===e?ie.text(this):this.empty().append((this[0]&&this[0].ownerDocument||he).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=$(this,e);t.appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=$(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var n,r=e?ie.filter(e,this):this,i=0;null!=(n=r[i]);i++)t||1!==n.nodeType||ie.cleanData(g(n)),n.parentNode&&(t&&ie.contains(n.ownerDocument,n)&&w(g(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&ie.cleanData(g(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&ie.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return ie.clone(this,e,t)})},html:function(e){return Ae(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(Le,""):void 0;if("string"==typeof e&&!Ue.test(e)&&(ne.htmlSerialize||!Ve.test(e))&&(ne.leadingWhitespace||!Re.test(e))&&!Xe[(He.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(Fe,"<$1></$2>");try{for(;n<r;n++)t=this[n]||{},1===t.nodeType&&(ie.cleanData(g(t,!1)),t.innerHTML=e);t=0}catch(i){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=arguments[0];return this.domManip(arguments,function(t){e=this.parentNode,ie.cleanData(g(this)),e&&e.replaceChild(t,this)}),e&&(e.length||e.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t){e=J.apply([],e);var n,r,i,o,a,s,u=0,l=this.length,c=this,f=l-1,d=e[0],p=ie.isFunction(d);if(p||l>1&&"string"==typeof d&&!ne.checkClone&&We.test(d))return this.each(function(n){var r=c.eq(n);p&&(e[0]=d.call(this,n,r.html())),r.domManip(e,t)});if(l&&(s=ie.buildFragment(e,this[0].ownerDocument,!1,this),n=s.firstChild,1===s.childNodes.length&&(s=n),n)){for(o=ie.map(g(s,"script"),y),i=o.length;u<l;u++)r=s,u!==f&&(r=ie.clone(r,!0,!0),i&&ie.merge(o,g(r,"script"))),t.call(this[u],r,u);if(i)for(a=o[o.length-1].ownerDocument,ie.map(o,b),u=0;u<i;u++)r=o[u],ze.test(r.type||"")&&!ie._data(r,"globalEval")&&ie.contains(a,r)&&(r.src?ie._evalUrl&&ie._evalUrl(r.src):ie.globalEval((r.text||r.textContent||r.innerHTML||"").replace(Ye,"")));s=n=null}return this}}),ie.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){ie.fn[e]=function(e){for(var n,r=0,i=[],o=ie(e),a=o.length-1;r<=a;r++)n=r===a?this:this.clone(!0),ie(o[r])[t](n),Q.apply(i,n.get());return this.pushStack(i)}});var Ke,Ze={};!function(){var e;ne.shrinkWrapBlocks=function(){if(null!=e)return e;e=!1;var t,n,r;return n=he.getElementsByTagName("body")[0],n&&n.style?(t=he.createElement("div"),r=he.createElement("div"),r.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(t),typeof t.style.zoom!==Ee&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(he.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(r),e):void 0}}();var et,tt,nt=/^margin/,rt=new RegExp("^("+Se+")(?!px)[a-z%]+$","i"),it=/^(top|right|bottom|left)$/;e.getComputedStyle?(et=function(t){return t.ownerDocument.defaultView.opener?t.ownerDocument.defaultView.getComputedStyle(t,null):e.getComputedStyle(t,null)},tt=function(e,t,n){var r,i,o,a,s=e.style;return n=n||et(e),a=n?n.getPropertyValue(t)||n[t]:void 0,n&&(""!==a||ie.contains(e.ownerDocument,e)||(a=ie.style(e,t)),rt.test(a)&&nt.test(t)&&(r=s.width,i=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=i,s.maxWidth=o)),void 0===a?a:a+""}):he.documentElement.currentStyle&&(et=function(e){return e.currentStyle},tt=function(e,t,n){var r,i,o,a,s=e.style;return n=n||et(e),a=n?n[t]:void 0,null==a&&s&&s[t]&&(a=s[t]),rt.test(a)&&!it.test(t)&&(r=s.left,i=e.runtimeStyle,o=i&&i.left,o&&(i.left=e.currentStyle.left),s.left="fontSize"===t?"1em":a,a=s.pixelLeft+"px",s.left=r,o&&(i.left=o)),void 0===a?a:a+""||"auto"}),function(){function t(){var t,n,r,i;n=he.getElementsByTagName("body")[0],n&&n.style&&(t=he.createElement("div"),r=he.createElement("div"),r.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(t),t.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",o=a=!1,u=!0,e.getComputedStyle&&(o="1%"!==(e.getComputedStyle(t,null)||{}).top,a="4px"===(e.getComputedStyle(t,null)||{width:"4px"}).width,i=t.appendChild(he.createElement("div")),i.style.cssText=t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",i.style.marginRight=i.style.width="0",t.style.width="1px",u=!parseFloat((e.getComputedStyle(i,null)||{}).marginRight),t.removeChild(i)),t.innerHTML="<table><tr><td></td><td>t</td></tr></table>",i=t.getElementsByTagName("td"),i[0].style.cssText="margin:0;border:0;padding:0;display:none",s=0===i[0].offsetHeight,s&&(i[0].style.display="",i[1].style.display="none",s=0===i[0].offsetHeight),n.removeChild(r))}var n,r,i,o,a,s,u;n=he.createElement("div"),n.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",i=n.getElementsByTagName("a")[0],r=i&&i.style,r&&(r.cssText="float:left;opacity:.5",ne.opacity="0.5"===r.opacity,ne.cssFloat=!!r.cssFloat,n.style.backgroundClip="content-box",n.cloneNode(!0).style.backgroundClip="",ne.clearCloneStyle="content-box"===n.style.backgroundClip,ne.boxSizing=""===r.boxSizing||""===r.MozBoxSizing||""===r.WebkitBoxSizing,ie.extend(ne,{reliableHiddenOffsets:function(){return null==s&&t(),s},boxSizingReliable:function(){return null==a&&t(),a},pixelPosition:function(){return null==o&&t(),o},reliableMarginRight:function(){return null==u&&t(),u}}))}(),ie.swap=function(e,t,n,r){var i,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];i=n.apply(e,r||[]);for(o in t)e.style[o]=a[o];return i};var ot=/alpha\([^)]*\)/i,at=/opacity\s*=\s*([^)]*)/,st=/^(none|table(?!-c[ea]).+)/,ut=new RegExp("^("+Se+")(.*)$","i"),lt=new RegExp("^([+-])=("+Se+")","i"),ct={position:"absolute",visibility:"hidden",display:"block"},ft={letterSpacing:"0",fontWeight:"400"},dt=["Webkit","O","Moz","ms"];ie.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=tt(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":ne.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=ie.camelCase(t),u=e.style;if(t=ie.cssProps[s]||(ie.cssProps[s]=k(u,s)),a=ie.cssHooks[t]||ie.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:u[t];if(o=typeof n,"string"===o&&(i=lt.exec(n))&&(n=(i[1]+1)*i[2]+parseFloat(ie.css(e,t)),o="number"),null!=n&&n===n&&("number"!==o||ie.cssNumber[s]||(n+="px"),ne.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),!(a&&"set"in a&&void 0===(n=a.set(e,n,r)))))try{u[t]=n}catch(l){}}},css:function(e,t,n,r){var i,o,a,s=ie.camelCase(t);return t=ie.cssProps[s]||(ie.cssProps[s]=k(e.style,s)),a=ie.cssHooks[t]||ie.cssHooks[s],a&&"get"in a&&(o=a.get(e,!0,n)),void 0===o&&(o=tt(e,t,r)),"normal"===o&&t in ft&&(o=ft[t]),""===n||n?(i=parseFloat(o),n===!0||ie.isNumeric(i)?i||0:o):o}}),ie.each(["height","width"],function(e,t){ie.cssHooks[t]={get:function(e,n,r){if(n)return st.test(ie.css(e,"display"))&&0===e.offsetWidth?ie.swap(e,ct,function(){return O(e,t,r)}):O(e,t,r)},set:function(e,n,r){var i=r&&et(e);return A(e,n,r?N(e,t,r,ne.boxSizing&&"border-box"===ie.css(e,"boxSizing",!1,i),i):0)}}}),ne.opacity||(ie.cssHooks.opacity={get:function(e,t){return at.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,r=e.currentStyle,i=ie.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=r&&r.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===ie.trim(o.replace(ot,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||r&&!r.filter)||(n.filter=ot.test(o)?o.replace(ot,i):o+" "+i)}}),ie.cssHooks.marginRight=S(ne.reliableMarginRight,function(e,t){if(t)return ie.swap(e,{display:"inline-block"},tt,[e,"marginRight"])}),ie.each({margin:"",padding:"",border:"Width"},function(e,t){ie.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+ke[r]+t]=o[r]||o[r-2]||o[0];return i}},nt.test(e)||(ie.cssHooks[e+t].set=A)}),ie.fn.extend({css:function(e,t){return Ae(this,function(e,t,n){var r,i,o={},a=0;if(ie.isArray(t)){for(r=et(e),i=t.length;a<i;a++)o[t[a]]=ie.css(e,t[a],!1,r);return o}return void 0!==n?ie.style(e,t,n):ie.css(e,t)},e,t,arguments.length>1)},show:function(){return _(this,!0)},hide:function(){return _(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){_e(this)?ie(this).show():ie(this).hide()})}}),ie.Tween=D,D.prototype={constructor:D,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(ie.cssNumber[n]?"":"px")},cur:function(){var e=D.propHooks[this.prop];return e&&e.get?e.get(this):D.propHooks._default.get(this)},run:function(e){var t,n=D.propHooks[this.prop];return this.options.duration?this.pos=t=ie.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):D.propHooks._default.set(this),this}},D.prototype.init.prototype=D.prototype,D.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=ie.css(e.elem,e.prop,""),t&&"auto"!==t?t:0):e.elem[e.prop]},set:function(e){ie.fx.step[e.prop]?ie.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[ie.cssProps[e.prop]]||ie.cssHooks[e.prop])?ie.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},D.propHooks.scrollTop=D.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},ie.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},ie.fx=D.prototype.init,ie.fx.step={};var pt,ht,mt=/^(?:toggle|show|hide)$/,gt=new RegExp("^(?:([+-])=|)("+Se+")([a-z%]*)$","i"),vt=/queueHooks$/,$t=[P],yt={"*":[function(e,t){var n=this.createTween(e,t),r=n.cur(),i=gt.exec(t),o=i&&i[3]||(ie.cssNumber[e]?"":"px"),a=(ie.cssNumber[e]||"px"!==o&&+r)&&gt.exec(ie.css(n.elem,e)),s=1,u=20;if(a&&a[3]!==o){o=o||a[3],i=i||[],a=+r||1;do s=s||".5",a/=s,ie.style(n.elem,e,a+o);while(s!==(s=n.cur()/r)&&1!==s&&--u)}return i&&(a=n.start=+a||+r||0,n.unit=o,n.end=i[1]?a+(i[1]+1)*i[2]:+i[2]),n}]};ie.Animation=ie.extend(V,{tweener:function(e,t){ie.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,r=0,i=e.length;r<i;r++)n=e[r],yt[n]=yt[n]||[],yt[n].unshift(t)},prefilter:function(e,t){t?$t.unshift(e):$t.push(e)}}),ie.speed=function(e,t,n){var r=e&&"object"==typeof e?ie.extend({},e):{complete:n||!n&&t||ie.isFunction(e)&&e,duration:e,easing:n&&t||t&&!ie.isFunction(t)&&t};return r.duration=ie.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in ie.fx.speeds?ie.fx.speeds[r.duration]:ie.fx.speeds._default,null!=r.queue&&r.queue!==!0||(r.queue="fx"),r.old=r.complete,r.complete=function(){ie.isFunction(r.old)&&r.old.call(this),r.queue&&ie.dequeue(this,r.queue)},r},ie.fn.extend({fadeTo:function(e,t,n,r){return this.filter(_e).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=ie.isEmptyObject(e),o=ie.speed(t,n,r),a=function(){var t=V(this,ie.extend({},e),o);(i||ie._data(this,"finish"))&&t.stop(!0)};return a.finish=a,i||o.queue===!1?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=ie.timers,a=ie._data(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&vt.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||ie.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=ie._data(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=ie.timers,a=r?r.length:0;for(n.finish=!0,ie.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),ie.each(["toggle","show","hide"],function(e,t){var n=ie.fn[t];ie.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(M(t,!0),e,r,i)}}),ie.each({slideDown:M("show"),slideUp:M("hide"),slideToggle:M("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){ie.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),ie.timers=[],ie.fx.tick=function(){var e,t=ie.timers,n=0;for(pt=ie.now();n<t.length;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||ie.fx.stop(),pt=void 0},ie.fx.timer=function(e){ie.timers.push(e),e()?ie.fx.start():ie.timers.pop()},ie.fx.interval=13,ie.fx.start=function(){ht||(ht=setInterval(ie.fx.tick,ie.fx.interval))},ie.fx.stop=function(){clearInterval(ht),ht=null},ie.fx.speeds={slow:600,fast:200,_default:400},ie.fn.delay=function(e,t){return e=ie.fx?ie.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})},function(){var e,t,n,r,i;t=he.createElement("div"),t.setAttribute("className","t"),t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",r=t.getElementsByTagName("a")[0],n=he.createElement("select"),i=n.appendChild(he.createElement("option")),e=t.getElementsByTagName("input")[0],r.style.cssText="top:1px",ne.getSetAttribute="t"!==t.className,ne.style=/top/.test(r.getAttribute("style")),ne.hrefNormalized="/a"===r.getAttribute("href"),ne.checkOn=!!e.value,ne.optSelected=i.selected,ne.enctype=!!he.createElement("form").enctype,n.disabled=!0,ne.optDisabled=!i.disabled,e=he.createElement("input"),e.setAttribute("value",""),ne.input=""===e.getAttribute("value"),e.value="t",e.setAttribute("type","radio"),ne.radioValue="t"===e.value}();var bt=/\r/g;ie.fn.extend({val:function(e){var t,n,r,i=this[0];{if(arguments.length)return r=ie.isFunction(e),this.each(function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,ie(this).val()):e,null==i?i="":"number"==typeof i?i+="":ie.isArray(i)&&(i=ie.map(i,function(e){return null==e?"":e+""})),t=ie.valHooks[this.type]||ie.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))});if(i)return t=ie.valHooks[i.type]||ie.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"==typeof n?n.replace(bt,""):null==n?"":n)}}}),ie.extend({valHooks:{option:{get:function(e){var t=ie.find.attr(e,"value");return null!=t?t:ie.trim(ie.text(e))}},select:{get:function(e){for(var t,n,r=e.options,i=e.selectedIndex,o="select-one"===e.type||i<0,a=o?null:[],s=o?i+1:r.length,u=i<0?s:o?i:0;u<s;u++)if(n=r[u],(n.selected||u===i)&&(ne.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!ie.nodeName(n.parentNode,"optgroup"))){if(t=ie(n).val(),o)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=ie.makeArray(t),a=i.length;a--;)if(r=i[a],ie.inArray(ie.valHooks.option.get(r),o)>=0)try{r.selected=n=!0}catch(s){r.scrollHeight}else r.selected=!1;return n||(e.selectedIndex=-1),i}}}}),ie.each(["radio","checkbox"],function(){ie.valHooks[this]={set:function(e,t){if(ie.isArray(t))return e.checked=ie.inArray(ie(e).val(),t)>=0}},ne.checkOn||(ie.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var wt,xt,Et=ie.expr.attrHandle,Tt=/^(?:checked|selected)$/i,Ct=ne.getSetAttribute,St=ne.input;ie.fn.extend({attr:function(e,t){return Ae(this,ie.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){ie.removeAttr(this,e)})}}),ie.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===Ee?ie.prop(e,t,n):(1===o&&ie.isXMLDoc(e)||(t=t.toLowerCase(),r=ie.attrHooks[t]||(ie.expr.match.bool.test(t)?xt:wt)),void 0===n?r&&"get"in r&&null!==(i=r.get(e,t))?i:(i=ie.find.attr(e,t),null==i?void 0:i):null!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):void ie.removeAttr(e,t))},removeAttr:function(e,t){var n,r,i=0,o=t&&t.match(ye);if(o&&1===e.nodeType)for(;n=o[i++];)r=ie.propFix[n]||n,ie.expr.match.bool.test(n)?St&&Ct||!Tt.test(n)?e[r]=!1:e[ie.camelCase("default-"+n)]=e[r]=!1:ie.attr(e,n,""),e.removeAttribute(Ct?n:r)},attrHooks:{type:{set:function(e,t){if(!ne.radioValue&&"radio"===t&&ie.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}}}),xt={set:function(e,t,n){return t===!1?ie.removeAttr(e,n):St&&Ct||!Tt.test(n)?e.setAttribute(!Ct&&ie.propFix[n]||n,n):e[ie.camelCase("default-"+n)]=e[n]=!0,n}},ie.each(ie.expr.match.bool.source.match(/\w+/g),function(e,t){var n=Et[t]||ie.find.attr;Et[t]=St&&Ct||!Tt.test(t)?function(e,t,r){var i,o;return r||(o=Et[t],Et[t]=i,i=null!=n(e,t,r)?t.toLowerCase():null,Et[t]=o),i}:function(e,t,n){if(!n)return e[ie.camelCase("default-"+t)]?t.toLowerCase():null}}),St&&Ct||(ie.attrHooks.value={set:function(e,t,n){return ie.nodeName(e,"input")?void(e.defaultValue=t):wt&&wt.set(e,t,n)}}),Ct||(wt={set:function(e,t,n){var r=e.getAttributeNode(n);if(r||e.setAttributeNode(r=e.ownerDocument.createAttribute(n)),r.value=t+="","value"===n||t===e.getAttribute(n))return t}},Et.id=Et.name=Et.coords=function(e,t,n){var r;if(!n)return(r=e.getAttributeNode(t))&&""!==r.value?r.value:null},ie.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);if(n&&n.specified)return n.value},set:wt.set},ie.attrHooks.contenteditable={set:function(e,t,n){wt.set(e,""!==t&&t,n)}},ie.each(["width","height"],function(e,t){ie.attrHooks[t]={set:function(e,n){if(""===n)return e.setAttribute(t,"auto"),n}}})),ne.style||(ie.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var kt=/^(?:input|select|textarea|button|object)$/i,_t=/^(?:a|area)$/i;ie.fn.extend({prop:function(e,t){return Ae(this,ie.prop,e,t,arguments.length>1)},removeProp:function(e){return e=ie.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(t){}})}}),ie.extend({propFix:{"for":"htmlFor","class":"className"},prop:function(e,t,n){var r,i,o,a=e.nodeType;if(e&&3!==a&&8!==a&&2!==a)return o=1!==a||!ie.isXMLDoc(e),o&&(t=ie.propFix[t]||t,i=ie.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=ie.find.attr(e,"tabindex");return t?parseInt(t,10):kt.test(e.nodeName)||_t.test(e.nodeName)&&e.href?0:-1}}}}),ne.hrefNormalized||ie.each(["href","src"],function(e,t){ie.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),ne.optSelected||(ie.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}}),ie.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ie.propFix[this.toLowerCase()]=this}),ne.enctype||(ie.propFix.enctype="encoding");var At=/[\t\r\n\f]/g;ie.fn.extend({addClass:function(e){var t,n,r,i,o,a,s=0,u=this.length,l="string"==typeof e&&e;if(ie.isFunction(e))return this.each(function(t){ie(this).addClass(e.call(this,t,this.className))});if(l)for(t=(e||"").match(ye)||[];s<u;s++)if(n=this[s],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(At," "):" ")){for(o=0;i=t[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");a=ie.trim(r),n.className!==a&&(n.className=a)}return this},removeClass:function(e){var t,n,r,i,o,a,s=0,u=this.length,l=0===arguments.length||"string"==typeof e&&e;if(ie.isFunction(e))return this.each(function(t){ie(this).removeClass(e.call(this,t,this.className))});if(l)for(t=(e||"").match(ye)||[];s<u;s++)if(n=this[s],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(At," "):"")){for(o=0;i=t[o++];)for(;r.indexOf(" "+i+" ")>=0;)r=r.replace(" "+i+" "," ");a=e?ie.trim(r):"",n.className!==a&&(n.className=a)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):ie.isFunction(e)?this.each(function(n){ie(this).toggleClass(e.call(this,n,this.className,t),t)}):this.each(function(){if("string"===n)for(var t,r=0,i=ie(this),o=e.match(ye)||[];t=o[r++];)i.hasClass(t)?i.removeClass(t):i.addClass(t);else n!==Ee&&"boolean"!==n||(this.className&&ie._data(this,"__className__",this.className),this.className=this.className||e===!1?"":ie._data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;n<r;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(At," ").indexOf(t)>=0)return!0;return!1}}),ie.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){ie.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),ie.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var Nt=ie.now(),Ot=/\?/,Dt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;ie.parseJSON=function(t){if(e.JSON&&e.JSON.parse)return e.JSON.parse(t+"");var n,r=null,i=ie.trim(t+"");return i&&!ie.trim(i.replace(Dt,function(e,t,i,o){return n&&t&&(r=0),0===r?e:(n=i||t,r+=!o-!i,"")}))?Function("return "+i)():ie.error("Invalid JSON: "+t)},ie.parseXML=function(t){var n,r;if(!t||"string"!=typeof t)return null;try{e.DOMParser?(r=new DOMParser,n=r.parseFromString(t,"text/xml")):(n=new ActiveXObject("Microsoft.XMLDOM"),n.async="false",n.loadXML(t))}catch(i){n=void 0}return n&&n.documentElement&&!n.getElementsByTagName("parsererror").length||ie.error("Invalid XML: "+t),n};var It,Mt,jt=/#.*$/,Pt=/([?&])_=[^&]*/,Lt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Vt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Rt=/^(?:GET|HEAD)$/,Ft=/^\/\//,Ht=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Bt={},qt={},Ut="*/".concat("*");try{Mt=location.href}catch(Wt){Mt=he.createElement("a"),Mt.href="",Mt=Mt.href}It=Ht.exec(Mt.toLowerCase())||[],ie.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Mt,type:"GET",isLocal:Vt.test(It[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ut,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":ie.parseJSON,"text xml":ie.parseXML},flatOptions:{
url:!0,context:!0}},ajaxSetup:function(e,t){return t?H(H(e,ie.ajaxSettings),t):H(ie.ajaxSettings,e)},ajaxPrefilter:R(Bt),ajaxTransport:R(qt),ajax:function(e,t){function n(e,t,n,r){var i,c,v,$,b,x=t;2!==y&&(y=2,s&&clearTimeout(s),l=void 0,a=r||"",w.readyState=e>0?4:0,i=e>=200&&e<300||304===e,n&&($=B(f,w,n)),$=q(f,$,w,i),i?(f.ifModified&&(b=w.getResponseHeader("Last-Modified"),b&&(ie.lastModified[o]=b),b=w.getResponseHeader("etag"),b&&(ie.etag[o]=b)),204===e||"HEAD"===f.type?x="nocontent":304===e?x="notmodified":(x=$.state,c=$.data,v=$.error,i=!v)):(v=x,!e&&x||(x="error",e<0&&(e=0))),w.status=e,w.statusText=(t||x)+"",i?h.resolveWith(d,[c,x,w]):h.rejectWith(d,[w,x,v]),w.statusCode(g),g=void 0,u&&p.trigger(i?"ajaxSuccess":"ajaxError",[w,f,i?c:v]),m.fireWith(d,[w,x]),u&&(p.trigger("ajaxComplete",[w,f]),--ie.active||ie.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var r,i,o,a,s,u,l,c,f=ie.ajaxSetup({},t),d=f.context||f,p=f.context&&(d.nodeType||d.jquery)?ie(d):ie.event,h=ie.Deferred(),m=ie.Callbacks("once memory"),g=f.statusCode||{},v={},$={},y=0,b="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===y){if(!c)for(c={};t=Lt.exec(a);)c[t[1].toLowerCase()]=t[2];t=c[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===y?a:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return y||(e=$[n]=$[n]||e,v[e]=t),this},overrideMimeType:function(e){return y||(f.mimeType=e),this},statusCode:function(e){var t;if(e)if(y<2)for(t in e)g[t]=[g[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||b;return l&&l.abort(t),n(0,t),this}};if(h.promise(w).complete=m.add,w.success=w.done,w.error=w.fail,f.url=((e||f.url||Mt)+"").replace(jt,"").replace(Ft,It[1]+"//"),f.type=t.method||t.type||f.method||f.type,f.dataTypes=ie.trim(f.dataType||"*").toLowerCase().match(ye)||[""],null==f.crossDomain&&(r=Ht.exec(f.url.toLowerCase()),f.crossDomain=!(!r||r[1]===It[1]&&r[2]===It[2]&&(r[3]||("http:"===r[1]?"80":"443"))===(It[3]||("http:"===It[1]?"80":"443")))),f.data&&f.processData&&"string"!=typeof f.data&&(f.data=ie.param(f.data,f.traditional)),F(Bt,f,t,w),2===y)return w;u=ie.event&&f.global,u&&0===ie.active++&&ie.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Rt.test(f.type),o=f.url,f.hasContent||(f.data&&(o=f.url+=(Ot.test(o)?"&":"?")+f.data,delete f.data),f.cache===!1&&(f.url=Pt.test(o)?o.replace(Pt,"$1_="+Nt++):o+(Ot.test(o)?"&":"?")+"_="+Nt++)),f.ifModified&&(ie.lastModified[o]&&w.setRequestHeader("If-Modified-Since",ie.lastModified[o]),ie.etag[o]&&w.setRequestHeader("If-None-Match",ie.etag[o])),(f.data&&f.hasContent&&f.contentType!==!1||t.contentType)&&w.setRequestHeader("Content-Type",f.contentType),w.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Ut+"; q=0.01":""):f.accepts["*"]);for(i in f.headers)w.setRequestHeader(i,f.headers[i]);if(f.beforeSend&&(f.beforeSend.call(d,w,f)===!1||2===y))return w.abort();b="abort";for(i in{success:1,error:1,complete:1})w[i](f[i]);if(l=F(qt,f,t,w)){w.readyState=1,u&&p.trigger("ajaxSend",[w,f]),f.async&&f.timeout>0&&(s=setTimeout(function(){w.abort("timeout")},f.timeout));try{y=1,l.send(v,n)}catch(x){if(!(y<2))throw x;n(-1,x)}}else n(-1,"No Transport");return w},getJSON:function(e,t,n){return ie.get(e,t,n,"json")},getScript:function(e,t){return ie.get(e,void 0,t,"script")}}),ie.each(["get","post"],function(e,t){ie[t]=function(e,n,r,i){return ie.isFunction(n)&&(i=i||r,r=n,n=void 0),ie.ajax({url:e,type:t,dataType:i,data:n,success:r})}}),ie._evalUrl=function(e){return ie.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})},ie.fn.extend({wrapAll:function(e){if(ie.isFunction(e))return this.each(function(t){ie(this).wrapAll(e.call(this,t))});if(this[0]){var t=ie(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return ie.isFunction(e)?this.each(function(t){ie(this).wrapInner(e.call(this,t))}):this.each(function(){var t=ie(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=ie.isFunction(e);return this.each(function(n){ie(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){ie.nodeName(this,"body")||ie(this).replaceWith(this.childNodes)}).end()}}),ie.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0||!ne.reliableHiddenOffsets()&&"none"===(e.style&&e.style.display||ie.css(e,"display"))},ie.expr.filters.visible=function(e){return!ie.expr.filters.hidden(e)};var zt=/%20/g,Gt=/\[\]$/,Yt=/\r?\n/g,Xt=/^(?:submit|button|image|reset|file)$/i,Jt=/^(?:input|select|textarea|keygen)/i;ie.param=function(e,t){var n,r=[],i=function(e,t){t=ie.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=ie.ajaxSettings&&ie.ajaxSettings.traditional),ie.isArray(e)||e.jquery&&!ie.isPlainObject(e))ie.each(e,function(){i(this.name,this.value)});else for(n in e)U(n,e[n],t,i);return r.join("&").replace(zt,"+")},ie.fn.extend({serialize:function(){return ie.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=ie.prop(this,"elements");return e?ie.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!ie(this).is(":disabled")&&Jt.test(this.nodeName)&&!Xt.test(e)&&(this.checked||!Ne.test(e))}).map(function(e,t){var n=ie(this).val();return null==n?null:ie.isArray(n)?ie.map(n,function(e){return{name:t.name,value:e.replace(Yt,"\r\n")}}):{name:t.name,value:n.replace(Yt,"\r\n")}}).get()}}),ie.ajaxSettings.xhr=void 0!==e.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&W()||z()}:W;var Qt=0,Kt={},Zt=ie.ajaxSettings.xhr();e.attachEvent&&e.attachEvent("onunload",function(){for(var e in Kt)Kt[e](void 0,!0)}),ne.cors=!!Zt&&"withCredentials"in Zt,Zt=ne.ajax=!!Zt,Zt&&ie.ajaxTransport(function(e){if(!e.crossDomain||ne.cors){var t;return{send:function(n,r){var i,o=e.xhr(),a=++Qt;if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(i in e.xhrFields)o[i]=e.xhrFields[i];e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(i in n)void 0!==n[i]&&o.setRequestHeader(i,n[i]+"");o.send(e.hasContent&&e.data||null),t=function(n,i){var s,u,l;if(t&&(i||4===o.readyState))if(delete Kt[a],t=void 0,o.onreadystatechange=ie.noop,i)4!==o.readyState&&o.abort();else{l={},s=o.status,"string"==typeof o.responseText&&(l.text=o.responseText);try{u=o.statusText}catch(c){u=""}s||!e.isLocal||e.crossDomain?1223===s&&(s=204):s=l.text?200:404}l&&r(s,u,l,o.getAllResponseHeaders())},e.async?4===o.readyState?setTimeout(t):o.onreadystatechange=Kt[a]=t:t()},abort:function(){t&&t(void 0,!0)}}}}),ie.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return ie.globalEval(e),e}}}),ie.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),ie.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=he.head||ie("head")[0]||he.documentElement;return{send:function(r,i){t=he.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||i(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var en=[],tn=/(=)\?(?=&|$)|\?\?/;ie.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=en.pop()||ie.expando+"_"+Nt++;return this[e]=!0,e}}),ie.ajaxPrefilter("json jsonp",function(t,n,r){var i,o,a,s=t.jsonp!==!1&&(tn.test(t.url)?"url":"string"==typeof t.data&&!(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&tn.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return i=t.jsonpCallback=ie.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(tn,"$1"+i):t.jsonp!==!1&&(t.url+=(Ot.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return a||ie.error(i+" was not called"),a[0]},t.dataTypes[0]="json",o=e[i],e[i]=function(){a=arguments},r.always(function(){e[i]=o,t[i]&&(t.jsonpCallback=n.jsonpCallback,en.push(i)),a&&ie.isFunction(o)&&o(a[0]),a=o=void 0}),"script"}),ie.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||he;var r=fe.exec(e),i=!n&&[];return r?[t.createElement(r[1])]:(r=ie.buildFragment([e],t,i),i&&i.length&&ie(i).remove(),ie.merge([],r.childNodes))};var nn=ie.fn.load;ie.fn.load=function(e,t,n){if("string"!=typeof e&&nn)return nn.apply(this,arguments);var r,i,o,a=this,s=e.indexOf(" ");return s>=0&&(r=ie.trim(e.slice(s,e.length)),e=e.slice(0,s)),ie.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&ie.ajax({url:e,type:o,dataType:"html",data:t}).done(function(e){i=arguments,a.html(r?ie("<div>").append(ie.parseHTML(e)).find(r):e)}).complete(n&&function(e,t){a.each(n,i||[e.responseText,t,e])}),this},ie.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){ie.fn[t]=function(e){return this.on(t,e)}}),ie.expr.filters.animated=function(e){return ie.grep(ie.timers,function(t){return e===t.elem}).length};var rn=e.document.documentElement;ie.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l,c=ie.css(e,"position"),f=ie(e),d={};"static"===c&&(e.style.position="relative"),s=f.offset(),o=ie.css(e,"top"),u=ie.css(e,"left"),l=("absolute"===c||"fixed"===c)&&ie.inArray("auto",[o,u])>-1,l?(r=f.position(),a=r.top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),ie.isFunction(t)&&(t=t.call(e,n,s)),null!=t.top&&(d.top=t.top-s.top+a),null!=t.left&&(d.left=t.left-s.left+i),"using"in t?t.using.call(e,d):f.css(d)}},ie.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){ie.offset.setOffset(this,e,t)});var t,n,r={top:0,left:0},i=this[0],o=i&&i.ownerDocument;if(o)return t=o.documentElement,ie.contains(t,i)?(typeof i.getBoundingClientRect!==Ee&&(r=i.getBoundingClientRect()),n=G(o),{top:r.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:r.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):r},position:function(){if(this[0]){var e,t,n={top:0,left:0},r=this[0];return"fixed"===ie.css(r,"position")?t=r.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),ie.nodeName(e[0],"html")||(n=e.offset()),n.top+=ie.css(e[0],"borderTopWidth",!0),n.left+=ie.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-ie.css(r,"marginTop",!0),left:t.left-n.left-ie.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||rn;e&&!ie.nodeName(e,"html")&&"static"===ie.css(e,"position");)e=e.offsetParent;return e||rn})}}),ie.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n=/Y/.test(t);ie.fn[e]=function(r){return Ae(this,function(e,r,i){var o=G(e);return void 0===i?o?t in o?o[t]:o.document.documentElement[r]:e[r]:void(o?o.scrollTo(n?ie(o).scrollLeft():i,n?i:ie(o).scrollTop()):e[r]=i)},e,r,arguments.length,null)}}),ie.each(["top","left"],function(e,t){ie.cssHooks[t]=S(ne.pixelPosition,function(e,n){if(n)return n=tt(e,t),rt.test(n)?ie(e).position()[t]+"px":n})}),ie.each({Height:"height",Width:"width"},function(e,t){ie.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){ie.fn[r]=function(r,i){var o=arguments.length&&(n||"boolean"!=typeof r),a=n||(r===!0||i===!0?"margin":"border");return Ae(this,function(t,n,r){var i;return ie.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(i=t.documentElement,Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])):void 0===r?ie.css(t,n,a):ie.style(t,n,r,a)},t,o?r:void 0,o,null)}})}),ie.fn.size=function(){return this.length},ie.fn.andSelf=ie.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return ie});var on=e.jQuery,an=e.$;return ie.noConflict=function(t){return e.$===ie&&(e.$=an),t&&e.jQuery===ie&&(e.jQuery=on),ie},typeof t===Ee&&(e.jQuery=e.$=ie),ie}),function(){!function(e,t,n){var r,i,o;return o="slidesjs",i={width:940,height:528,start:1,navigation:{active:!0,effect:"slide"},pagination:{active:!0,effect:"slide"},play:{active:!1,effect:"slide",interval:5e3,auto:!1,swap:!0,pauseOnHover:!1,restartDelay:2500},effect:{slide:{speed:500},fade:{speed:300,crossfade:!0}},callback:{loaded:function(){},start:function(){},complete:function(){}}},r=function(){function t(t,n){this.element=t,this.options=e.extend(!0,{},i,n),this._defaults=i,this._name=o,this.init()}return t}(),r.prototype.init=function(){var n,r,i,o,a,s,u=this;return n=e(this.element),this.data=e.data(this),e.data(this,"animating",!1),e.data(this,"total",n.children().not(".slidesjs-navigation",n).length),e.data(this,"current",this.options.start-1),e.data(this,"vendorPrefix",this._getVendorPrefix()),"undefined"!=typeof TouchEvent&&(e.data(this,"touch",!0),this.options.effect.slide.speed=this.options.effect.slide.speed/2),n.css({overflow:"hidden"}),n.slidesContainer=n.children().not(".slidesjs-navigation",n).wrapAll("<div class='slidesjs-container'>",n).parent().css({overflow:"hidden",position:"relative"}),e(".slidesjs-container",n).wrapInner("<div class='slidesjs-control'>",n).children(),e(".slidesjs-control",n).css({position:"relative",left:0}),e(".slidesjs-control",n).children().addClass("slidesjs-slide").css({position:"absolute",top:0,left:0,width:"100%",zIndex:0,display:"none",webkitBackfaceVisibility:"hidden"}),e.each(e(".slidesjs-control",n).children(),function(t){var n;return n=e(this),n.attr("slidesjs-index",t)}),this.data.touch&&(e(".slidesjs-control",n).on("touchstart",function(e){return u._touchstart(e)}),e(".slidesjs-control",n).on("touchmove",function(e){return u._touchmove(e)}),e(".slidesjs-control",n).on("touchend",function(e){return u._touchend(e)})),n.fadeIn(0),this.update(),this.data.touch&&this._setuptouch(),e(".slidesjs-control",n).children(":eq("+this.data.current+")").eq(0).fadeIn(0,function(){return e(this).css({zIndex:10})}),this.options.navigation.active&&(a=e("<a>",{"class":"slidesjs-previous slidesjs-navigation",href:"#",title:"Previous",text:"Previous"}).appendTo(n),r=e("<a>",{"class":"slidesjs-next slidesjs-navigation",href:"#",title:"Next",text:"Next"}).appendTo(n)),e(".slidesjs-next",n).click(function(e){return e.preventDefault(),u.stop(!0),u.next(u.options.navigation.effect)}),e(".slidesjs-previous",n).click(function(e){return e.preventDefault(),u.stop(!0),u.previous(u.options.navigation.effect)}),this.options.play.active&&(o=e("<a>",{"class":"slidesjs-play slidesjs-navigation",href:"#",title:"Play",text:"Play"}).appendTo(n),s=e("<a>",{"class":"slidesjs-stop slidesjs-navigation",href:"#",title:"Stop",text:"Stop"}).appendTo(n),o.click(function(e){return e.preventDefault(),u.play(!0)}),s.click(function(e){return e.preventDefault(),u.stop(!0)}),this.options.play.swap&&s.css({display:"none"})),this.options.pagination.active&&(i=e("<ul>",{"class":"slidesjs-pagination"}).appendTo(n),e.each(new Array(this.data.total),function(t){var n,r;return n=e("<li>",{"class":"slidesjs-pagination-item"}).appendTo(i),r=e("<a>",{href:"#","data-slidesjs-item":t,html:t+1}).appendTo(n),r.click(function(t){return t.preventDefault(),u.stop(!0),u["goto"](1*e(t.currentTarget).attr("data-slidesjs-item")+1)})})),e(t).bind("resize",function(){return u.update()}),this._setActive(),this.options.play.auto&&this.play(),this.options.callback.loaded(this.options.start)},r.prototype._setActive=function(t){var n,r;return n=e(this.element),this.data=e.data(this),r=t>-1?t:this.data.current,e(".active",n).removeClass("active"),e(".slidesjs-pagination li:eq("+r+") a",n).addClass("active")},r.prototype.update=function(){var t,n,r;return t=e(this.element),this.data=e.data(this),e(".slidesjs-control",t).children(":not(:eq("+this.data.current+"))").css({display:"none",left:0,zIndex:0}),r=t.width(),n=this.options.height/this.options.width*r,this.options.width=r,this.options.height=n,e(".slidesjs-control, .slidesjs-container",t).css({width:r,height:n})},r.prototype.next=function(t){var n;return n=e(this.element),this.data=e.data(this),e.data(this,"direction","next"),void 0===t&&(t=this.options.navigation.effect),"fade"===t?this._fade():this._slide()},r.prototype.previous=function(t){var n;return n=e(this.element),this.data=e.data(this),e.data(this,"direction","previous"),void 0===t&&(t=this.options.navigation.effect),"fade"===t?this._fade():this._slide()},r.prototype["goto"]=function(t){var n,r;if(n=e(this.element),this.data=e.data(this),void 0===r&&(r=this.options.pagination.effect),t>this.data.total?t=this.data.total:t<1&&(t=1),"number"==typeof t)return"fade"===r?this._fade(t):this._slide(t);if("string"==typeof t){if("first"===t)return"fade"===r?this._fade(0):this._slide(0);if("last"===t)return"fade"===r?this._fade(this.data.total):this._slide(this.data.total)}},r.prototype._setuptouch=function(){var t,n,r,i;return t=e(this.element),this.data=e.data(this),i=e(".slidesjs-control",t),n=this.data.current+1,r=this.data.current-1,r<0&&(r=this.data.total-1),n>this.data.total-1&&(n=0),i.children(":eq("+n+")").css({display:"block",left:this.options.width}),i.children(":eq("+r+")").css({display:"block",left:-this.options.width})},r.prototype._touchstart=function(t){var n,r;return n=e(this.element),this.data=e.data(this),r=t.originalEvent.touches[0],this._setuptouch(),e.data(this,"touchtimer",Number(new Date)),e.data(this,"touchstartx",r.pageX),e.data(this,"touchstarty",r.pageY),t.stopPropagation()},r.prototype._touchend=function(t){var n,r,i,o,a,s,u,l=this;return n=e(this.element),this.data=e.data(this),s=t.originalEvent.touches[0],o=e(".slidesjs-control",n),o.position().left>.5*this.options.width||o.position().left>.1*this.options.width&&Number(new Date)-this.data.touchtimer<250?(e.data(this,"direction","previous"),this._slide()):o.position().left<-(.5*this.options.width)||o.position().left<-(.1*this.options.width)&&Number(new Date)-this.data.touchtimer<250?(e.data(this,"direction","next"),this._slide()):(i=this.data.vendorPrefix,u=i+"Transform",r=i+"TransitionDuration",a=i+"TransitionTimingFunction",o[0].style[u]="translateX(0px)",o[0].style[r]=.85*this.options.effect.slide.speed+"ms"),o.on("transitionend webkitTransitionEnd oTransitionEnd otransitionend MSTransitionEnd",function(){return i=l.data.vendorPrefix,u=i+"Transform",r=i+"TransitionDuration",a=i+"TransitionTimingFunction",o[0].style[u]="",o[0].style[r]="",o[0].style[a]=""}),t.stopPropagation()},r.prototype._touchmove=function(t){var n,r,i,o,a;return n=e(this.element),this.data=e.data(this),o=t.originalEvent.touches[0],r=this.data.vendorPrefix,i=e(".slidesjs-control",n),a=r+"Transform",e.data(this,"scrolling",Math.abs(o.pageX-this.data.touchstartx)<Math.abs(o.pageY-this.data.touchstarty)),this.data.animating||this.data.scrolling||(t.preventDefault(),this._setuptouch(),i[0].style[a]="translateX("+(o.pageX-this.data.touchstartx)+"px)"),t.stopPropagation()},r.prototype.play=function(t){var n,r,i,o=this;if(n=e(this.element),this.data=e.data(this),!this.data.playInterval&&(t&&(r=this.data.current,this.data.direction="next","fade"===this.options.play.effect?this._fade():this._slide()),e.data(this,"playInterval",setInterval(function(){return r=o.data.current,o.data.direction="next","fade"===o.options.play.effect?o._fade():o._slide()},this.options.play.interval)),i=e(".slidesjs-container",n),this.options.play.pauseOnHover&&(i.unbind(),i.bind("mouseenter",function(){return o.stop()}),i.bind("mouseleave",function(){return o.options.play.restartDelay?e.data(o,"restartDelay",setTimeout(function(){return o.play(!0)},o.options.play.restartDelay)):o.play()})),e.data(this,"playing",!0),e(".slidesjs-play",n).addClass("slidesjs-playing"),this.options.play.swap))return e(".slidesjs-play",n).hide(),e(".slidesjs-stop",n).show()},r.prototype.stop=function(t){var n;if(n=e(this.element),this.data=e.data(this),clearInterval(this.data.playInterval),this.options.play.pauseOnHover&&t&&e(".slidesjs-container",n).unbind(),e.data(this,"playInterval",null),e.data(this,"playing",!1),e(".slidesjs-play",n).removeClass("slidesjs-playing"),this.options.play.swap)return e(".slidesjs-stop",n).hide(),e(".slidesjs-play",n).show()},r.prototype._slide=function(t){var n,r,i,o,a,s,u,l,c,f,d=this;if(n=e(this.element),this.data=e.data(this),!this.data.animating&&t!==this.data.current+1)return e.data(this,"animating",!0),r=this.data.current,t>-1?(t-=1,f=t>r?1:-1,i=t>r?-this.options.width:this.options.width,a=t):(f="next"===this.data.direction?1:-1,i="next"===this.data.direction?-this.options.width:this.options.width,a=r+f),a===-1&&(a=this.data.total-1),a===this.data.total&&(a=0),this._setActive(a),u=e(".slidesjs-control",n),t>-1&&u.children(":not(:eq("+r+"))").css({display:"none",left:0,zIndex:0}),u.children(":eq("+a+")").css({display:"block",left:f*this.options.width,zIndex:10}),this.options.callback.start(r+1),this.data.vendorPrefix?(s=this.data.vendorPrefix,c=s+"Transform",o=s+"TransitionDuration",l=s+"TransitionTimingFunction",u[0].style[c]="translateX("+i+"px)",u[0].style[o]=this.options.effect.slide.speed+"ms",u.on("transitionend webkitTransitionEnd oTransitionEnd otransitionend MSTransitionEnd",function(){return u[0].style[c]="",u[0].style[o]="",u.children(":eq("+a+")").css({left:0}),u.children(":eq("+r+")").css({display:"none",left:0,zIndex:0}),e.data(d,"current",a),e.data(d,"animating",!1),u.unbind("transitionend webkitTransitionEnd oTransitionEnd otransitionend MSTransitionEnd"),u.children(":not(:eq("+a+"))").css({display:"none",left:0,zIndex:0}),d.data.touch&&d._setuptouch(),d.options.callback.complete(a+1)})):u.stop().animate({left:i},this.options.effect.slide.speed,function(){return u.css({left:0}),u.children(":eq("+a+")").css({left:0}),u.children(":eq("+r+")").css({display:"none",left:0,zIndex:0},e.data(d,"current",a),e.data(d,"animating",!1),d.options.callback.complete(a+1))})},r.prototype._fade=function(t){var n,r,i,o,a,s=this;if(n=e(this.element),this.data=e.data(this),!this.data.animating&&t!==this.data.current+1)return e.data(this,"animating",!0),r=this.data.current,t?(t-=1,a=t>r?1:-1,i=t):(a="next"===this.data.direction?1:-1,i=r+a),i===-1&&(i=this.data.total-1),i===this.data.total&&(i=0),this._setActive(i),o=e(".slidesjs-control",n),o.children(":eq("+i+")").css({display:"none",left:0,zIndex:10}),this.options.callback.start(r+1),this.options.effect.fade.crossfade?(o.children(":eq("+this.data.current+")").stop().fadeOut(this.options.effect.fade.speed),o.children(":eq("+i+")").stop().fadeIn(this.options.effect.fade.speed,function(){return o.children(":eq("+i+")").css({zIndex:0}),e.data(s,"animating",!1),e.data(s,"current",i),s.options.callback.complete(i+1)})):o.children(":eq("+r+")").stop().fadeOut(this.options.effect.fade.speed,function(){return o.children(":eq("+i+")").stop().fadeIn(s.options.effect.fade.speed,function(){return o.children(":eq("+i+")").css({zIndex:10})}),e.data(s,"animating",!1),e.data(s,"current",i),s.options.callback.complete(i+1)})},r.prototype._getVendorPrefix=function(){var e,t,r,i,o;for(e=n.body||n.documentElement,r=e.style,i="transition",o=["Moz","Webkit","Khtml","O","ms"],i=i.charAt(0).toUpperCase()+i.substr(1),t=0;t<o.length;){if("string"==typeof r[o[t]+i])return o[t];t++}return!1},e.fn[o]=function(t){return this.each(function(){if(!e.data(this,"plugin_"+o))return e.data(this,"plugin_"+o,new r(this,t))})}}(jQuery,window,document)}.call(this),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");+function(e){"use strict";var t=e.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1==t[0]&&9==t[1]&&t[2]<1)throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher")}(jQuery),+function(e){"use strict";function t(){var e=document.createElement("bootstrap"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var n in t)if(void 0!==e.style[n])return{end:t[n]};return!1}e.fn.emulateTransitionEnd=function(t){var n=!1,r=this;e(this).one("bsTransitionEnd",function(){n=!0});var i=function(){n||e(r).trigger(e.support.transition.end)};return setTimeout(i,t),this},e(function(){e.support.transition=t(),e.support.transition&&(e.event.special.bsTransitionEnd={bindType:e.support.transition.end,delegateType:e.support.transition.end,handle:function(t){if(e(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}})})}(jQuery),+function(e){"use strict";function t(t){return this.each(function(){var n=e(this),i=n.data("bs.alert");i||n.data("bs.alert",i=new r(this)),"string"==typeof t&&i[t].call(n)})}var n='[data-dismiss="alert"]',r=function(t){e(t).on("click",n,this.close)};r.VERSION="3.3.2",r.TRANSITION_DURATION=150,r.prototype.close=function(t){function n(){a.detach().trigger("closed.bs.alert").remove()}var i=e(this),o=i.attr("data-target");o||(o=i.attr("href"),o=o&&o.replace(/.*(?=#[^\s]*$)/,""));var a=e(o);t&&t.preventDefault(),a.length||(a=i.closest(".alert")),a.trigger(t=e.Event("close.bs.alert")),t.isDefaultPrevented()||(a.removeClass("in"),e.support.transition&&a.hasClass("fade")?a.one("bsTransitionEnd",n).emulateTransitionEnd(r.TRANSITION_DURATION):n())};var i=e.fn.alert;e.fn.alert=t,e.fn.alert.Constructor=r,e.fn.alert.noConflict=function(){return e.fn.alert=i,this},e(document).on("click.bs.alert.data-api",n,r.prototype.close)}(jQuery),+function(e){"use strict";function t(t){return this.each(function(){var r=e(this),i=r.data("bs.button"),o="object"==typeof t&&t;i||r.data("bs.button",i=new n(this,o)),"toggle"==t?i.toggle():t&&i.setState(t)})}var n=function(t,r){this.$element=e(t),this.options=e.extend({},n.DEFAULTS,r),this.isLoading=!1};n.VERSION="3.3.2",n.DEFAULTS={loadingText:"loading..."},n.prototype.setState=function(t){var n="disabled",r=this.$element,i=r.is("input")?"val":"html",o=r.data();t+="Text",null==o.resetText&&r.data("resetText",r[i]()),setTimeout(e.proxy(function(){r[i](null==o[t]?this.options[t]:o[t]),"loadingText"==t?(this.isLoading=!0,r.addClass(n).attr(n,n)):this.isLoading&&(this.isLoading=!1,r.removeClass(n).removeAttr(n))},this),0)},n.prototype.toggle=function(){var e=!0,t=this.$element.closest('[data-toggle="buttons"]');if(t.length){var n=this.$element.find("input");"radio"==n.prop("type")&&(n.prop("checked")&&this.$element.hasClass("active")?e=!1:t.find(".active").removeClass("active")),e&&n.prop("checked",!this.$element.hasClass("active")).trigger("change")}else this.$element.attr("aria-pressed",!this.$element.hasClass("active"));e&&this.$element.toggleClass("active")};var r=e.fn.button;e.fn.button=t,e.fn.button.Constructor=n,e.fn.button.noConflict=function(){return e.fn.button=r,this},e(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(n){var r=e(n.target);r.hasClass("btn")||(r=r.closest(".btn")),t.call(r,"toggle"),n.preventDefault()}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(t){e(t.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(t.type))})}(jQuery),+function(e){"use strict";function t(t){return this.each(function(){var r=e(this),i=r.data("bs.carousel"),o=e.extend({},n.DEFAULTS,r.data(),"object"==typeof t&&t),a="string"==typeof t?t:o.slide;i||r.data("bs.carousel",i=new n(this,o)),"number"==typeof t?i.to(t):a?i[a]():o.interval&&i.pause().cycle()})}var n=function(t,n){this.$element=e(t),this.$indicators=this.$element.find(".carousel-indicators"),this.options=n,this.paused=this.sliding=this.interval=this.$active=this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",e.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",e.proxy(this.pause,this)).on("mouseleave.bs.carousel",e.proxy(this.cycle,this))};n.VERSION="3.3.2",n.TRANSITION_DURATION=600,n.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0},n.prototype.keydown=function(e){if(!/input|textarea/i.test(e.target.tagName)){switch(e.which){case 37:this.prev();break;case 39:this.next();break;default:return}e.preventDefault()}},n.prototype.cycle=function(t){return t||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(e.proxy(this.next,this),this.options.interval)),this},n.prototype.getItemIndex=function(e){return this.$items=e.parent().children(".item"),this.$items.index(e||this.$active)},n.prototype.getItemForDirection=function(e,t){var n=this.getItemIndex(t),r="prev"==e&&0===n||"next"==e&&n==this.$items.length-1;if(r&&!this.options.wrap)return t;var i="prev"==e?-1:1,o=(n+i)%this.$items.length;return this.$items.eq(o)},n.prototype.to=function(e){var t=this,n=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(e>this.$items.length-1||e<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){t.to(e)}):n==e?this.pause().cycle():this.slide(e>n?"next":"prev",this.$items.eq(e))},n.prototype.pause=function(t){return t||(this.paused=!0),this.$element.find(".next, .prev").length&&e.support.transition&&(this.$element.trigger(e.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},n.prototype.next=function(){if(!this.sliding)return this.slide("next")},n.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},n.prototype.slide=function(t,r){var i=this.$element.find(".item.active"),o=r||this.getItemForDirection(t,i),a=this.interval,s="next"==t?"left":"right",u=this;if(o.hasClass("active"))return this.sliding=!1;var l=o[0],c=e.Event("slide.bs.carousel",{relatedTarget:l,direction:s});if(this.$element.trigger(c),!c.isDefaultPrevented()){if(this.sliding=!0,a&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var f=e(this.$indicators.children()[this.getItemIndex(o)]);f&&f.addClass("active")}var d=e.Event("slid.bs.carousel",{relatedTarget:l,direction:s});return e.support.transition&&this.$element.hasClass("slide")?(o.addClass(t),o[0].offsetWidth,i.addClass(s),o.addClass(s),i.one("bsTransitionEnd",function(){o.removeClass([t,s].join(" ")).addClass("active"),i.removeClass(["active",s].join(" ")),u.sliding=!1,setTimeout(function(){u.$element.trigger(d)},0)}).emulateTransitionEnd(n.TRANSITION_DURATION)):(i.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger(d)),a&&this.cycle(),this}};var r=e.fn.carousel;e.fn.carousel=t,e.fn.carousel.Constructor=n,e.fn.carousel.noConflict=function(){return e.fn.carousel=r,this};var i=function(n){var r,i=e(this),o=e(i.attr("data-target")||(r=i.attr("href"))&&r.replace(/.*(?=#[^\s]+$)/,""));if(o.hasClass("carousel")){var a=e.extend({},o.data(),i.data()),s=i.attr("data-slide-to");s&&(a.interval=!1),t.call(o,a),s&&o.data("bs.carousel").to(s),n.preventDefault()}};e(document).on("click.bs.carousel.data-api","[data-slide]",i).on("click.bs.carousel.data-api","[data-slide-to]",i),e(window).on("load",function(){e('[data-ride="carousel"]').each(function(){var n=e(this);t.call(n,n.data())})})}(jQuery),+function(e){"use strict";function t(t){var n,r=t.attr("data-target")||(n=t.attr("href"))&&n.replace(/.*(?=#[^\s]+$)/,"");return e(r)}function n(t){return this.each(function(){var n=e(this),i=n.data("bs.collapse"),o=e.extend({},r.DEFAULTS,n.data(),"object"==typeof t&&t);!i&&o.toggle&&"show"==t&&(o.toggle=!1),i||n.data("bs.collapse",i=new r(this,o)),"string"==typeof t&&i[t]()})}var r=function(t,n){this.$element=e(t),this.options=e.extend({},r.DEFAULTS,n),this.$trigger=e(this.options.trigger).filter('[href="#'+t.id+'"], [data-target="#'+t.id+'"]'),
this.transitioning=null,this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};r.VERSION="3.3.2",r.TRANSITION_DURATION=350,r.DEFAULTS={toggle:!0,trigger:'[data-toggle="collapse"]'},r.prototype.dimension=function(){var e=this.$element.hasClass("width");return e?"width":"height"},r.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var t,i=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(i&&i.length&&(t=i.data("bs.collapse"),t&&t.transitioning))){var o=e.Event("show.bs.collapse");if(this.$element.trigger(o),!o.isDefaultPrevented()){i&&i.length&&(n.call(i,"hide"),t||i.data("bs.collapse",null));var a=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[a](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var s=function(){this.$element.removeClass("collapsing").addClass("collapse in")[a](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!e.support.transition)return s.call(this);var u=e.camelCase(["scroll",a].join("-"));this.$element.one("bsTransitionEnd",e.proxy(s,this)).emulateTransitionEnd(r.TRANSITION_DURATION)[a](this.$element[0][u])}}}},r.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var t=e.Event("hide.bs.collapse");if(this.$element.trigger(t),!t.isDefaultPrevented()){var n=this.dimension();this.$element[n](this.$element[n]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var i=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};return e.support.transition?void this.$element[n](0).one("bsTransitionEnd",e.proxy(i,this)).emulateTransitionEnd(r.TRANSITION_DURATION):i.call(this)}}},r.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},r.prototype.getParent=function(){return e(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(e.proxy(function(n,r){var i=e(r);this.addAriaAndCollapsedClass(t(i),i)},this)).end()},r.prototype.addAriaAndCollapsedClass=function(e,t){var n=e.hasClass("in");e.attr("aria-expanded",n),t.toggleClass("collapsed",!n).attr("aria-expanded",n)};var i=e.fn.collapse;e.fn.collapse=n,e.fn.collapse.Constructor=r,e.fn.collapse.noConflict=function(){return e.fn.collapse=i,this},e(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(r){var i=e(this);i.attr("data-target")||r.preventDefault();var o=t(i),a=o.data("bs.collapse"),s=a?"toggle":e.extend({},i.data(),{trigger:this});n.call(o,s)})}(jQuery),+function(e){"use strict";function t(t){t&&3===t.which||(e(i).remove(),e(o).each(function(){var r=e(this),i=n(r),o={relatedTarget:this};i.hasClass("open")&&(i.trigger(t=e.Event("hide.bs.dropdown",o)),t.isDefaultPrevented()||(r.attr("aria-expanded","false"),i.removeClass("open").trigger("hidden.bs.dropdown",o)))}))}function n(t){var n=t.attr("data-target");n||(n=t.attr("href"),n=n&&/#[A-Za-z]/.test(n)&&n.replace(/.*(?=#[^\s]*$)/,""));var r=n&&e(n);return r&&r.length?r:t.parent()}function r(t){return this.each(function(){var n=e(this),r=n.data("bs.dropdown");r||n.data("bs.dropdown",r=new a(this)),"string"==typeof t&&r[t].call(n)})}var i=".dropdown-backdrop",o='[data-toggle="dropdown"]',a=function(t){e(t).on("click.bs.dropdown",this.toggle)};a.VERSION="3.3.2",a.prototype.toggle=function(r){var i=e(this);if(!i.is(".disabled, :disabled")){var o=n(i),a=o.hasClass("open");if(t(),!a){"ontouchstart"in document.documentElement&&!o.closest(".navbar-nav").length&&e('<div class="dropdown-backdrop"/>').insertAfter(e(this)).on("click",t);var s={relatedTarget:this};if(o.trigger(r=e.Event("show.bs.dropdown",s)),r.isDefaultPrevented())return;i.trigger("focus").attr("aria-expanded","true"),o.toggleClass("open").trigger("shown.bs.dropdown",s)}return!1}},a.prototype.keydown=function(t){if(/(38|40|27|32)/.test(t.which)&&!/input|textarea/i.test(t.target.tagName)){var r=e(this);if(t.preventDefault(),t.stopPropagation(),!r.is(".disabled, :disabled")){var i=n(r),a=i.hasClass("open");if(!a&&27!=t.which||a&&27==t.which)return 27==t.which&&i.find(o).trigger("focus"),r.trigger("click");var s=" li:not(.divider):visible a",u=i.find('[role="menu"]'+s+', [role="listbox"]'+s);if(u.length){var l=u.index(t.target);38==t.which&&l>0&&l--,40==t.which&&l<u.length-1&&l++,~l||(l=0),u.eq(l).trigger("focus")}}}};var s=e.fn.dropdown;e.fn.dropdown=r,e.fn.dropdown.Constructor=a,e.fn.dropdown.noConflict=function(){return e.fn.dropdown=s,this},e(document).on("click.bs.dropdown.data-api",t).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}).on("click.bs.dropdown.data-api",o,a.prototype.toggle).on("keydown.bs.dropdown.data-api",o,a.prototype.keydown).on("keydown.bs.dropdown.data-api",'[role="menu"]',a.prototype.keydown).on("keydown.bs.dropdown.data-api",'[role="listbox"]',a.prototype.keydown)}(jQuery),+function(e){"use strict";function t(t,r){return this.each(function(){var i=e(this),o=i.data("bs.modal"),a=e.extend({},n.DEFAULTS,i.data(),"object"==typeof t&&t);o||i.data("bs.modal",o=new n(this,a)),"string"==typeof t?o[t](r):a.show&&o.show(r)})}var n=function(t,n){this.options=n,this.$body=e(document.body),this.$element=e(t),this.$backdrop=this.isShown=null,this.scrollbarWidth=0,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,e.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};n.VERSION="3.3.2",n.TRANSITION_DURATION=300,n.BACKDROP_TRANSITION_DURATION=150,n.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},n.prototype.toggle=function(e){return this.isShown?this.hide():this.show(e)},n.prototype.show=function(t){var r=this,i=e.Event("show.bs.modal",{relatedTarget:t});this.$element.trigger(i),this.isShown||i.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',e.proxy(this.hide,this)),this.backdrop(function(){var i=e.support.transition&&r.$element.hasClass("fade");r.$element.parent().length||r.$element.appendTo(r.$body),r.$element.show().scrollTop(0),r.options.backdrop&&r.adjustBackdrop(),r.adjustDialog(),i&&r.$element[0].offsetWidth,r.$element.addClass("in").attr("aria-hidden",!1),r.enforceFocus();var o=e.Event("shown.bs.modal",{relatedTarget:t});i?r.$element.find(".modal-dialog").one("bsTransitionEnd",function(){r.$element.trigger("focus").trigger(o)}).emulateTransitionEnd(n.TRANSITION_DURATION):r.$element.trigger("focus").trigger(o)}))},n.prototype.hide=function(t){t&&t.preventDefault(),t=e.Event("hide.bs.modal"),this.$element.trigger(t),this.isShown&&!t.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),e(document).off("focusin.bs.modal"),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss.bs.modal"),e.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",e.proxy(this.hideModal,this)).emulateTransitionEnd(n.TRANSITION_DURATION):this.hideModal())},n.prototype.enforceFocus=function(){e(document).off("focusin.bs.modal").on("focusin.bs.modal",e.proxy(function(e){this.$element[0]===e.target||this.$element.has(e.target).length||this.$element.trigger("focus")},this))},n.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",e.proxy(function(e){27==e.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},n.prototype.resize=function(){this.isShown?e(window).on("resize.bs.modal",e.proxy(this.handleUpdate,this)):e(window).off("resize.bs.modal")},n.prototype.hideModal=function(){var e=this;this.$element.hide(),this.backdrop(function(){e.$body.removeClass("modal-open"),e.resetAdjustments(),e.resetScrollbar(),e.$element.trigger("hidden.bs.modal")})},n.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},n.prototype.backdrop=function(t){var r=this,i=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var o=e.support.transition&&i;if(this.$backdrop=e('<div class="modal-backdrop '+i+'" />').prependTo(this.$element).on("click.dismiss.bs.modal",e.proxy(function(e){e.target===e.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))},this)),o&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!t)return;o?this.$backdrop.one("bsTransitionEnd",t).emulateTransitionEnd(n.BACKDROP_TRANSITION_DURATION):t()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var a=function(){r.removeBackdrop(),t&&t()};e.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",a).emulateTransitionEnd(n.BACKDROP_TRANSITION_DURATION):a()}else t&&t()},n.prototype.handleUpdate=function(){this.options.backdrop&&this.adjustBackdrop(),this.adjustDialog()},n.prototype.adjustBackdrop=function(){this.$backdrop.css("height",0).css("height",this.$element[0].scrollHeight)},n.prototype.adjustDialog=function(){var e=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&e?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!e?this.scrollbarWidth:""})},n.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},n.prototype.checkScrollbar=function(){this.bodyIsOverflowing=document.body.scrollHeight>document.documentElement.clientHeight,this.scrollbarWidth=this.measureScrollbar()},n.prototype.setScrollbar=function(){var e=parseInt(this.$body.css("padding-right")||0,10);this.bodyIsOverflowing&&this.$body.css("padding-right",e+this.scrollbarWidth)},n.prototype.resetScrollbar=function(){this.$body.css("padding-right","")},n.prototype.measureScrollbar=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",this.$body.append(e);var t=e.offsetWidth-e.clientWidth;return this.$body[0].removeChild(e),t};var r=e.fn.modal;e.fn.modal=t,e.fn.modal.Constructor=n,e.fn.modal.noConflict=function(){return e.fn.modal=r,this},e(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(n){var r=e(this),i=r.attr("href"),o=e(r.attr("data-target")||i&&i.replace(/.*(?=#[^\s]+$)/,"")),a=o.data("bs.modal")?"toggle":e.extend({remote:!/#/.test(i)&&i},o.data(),r.data());r.is("a")&&n.preventDefault(),o.one("show.bs.modal",function(e){e.isDefaultPrevented()||o.one("hidden.bs.modal",function(){r.is(":visible")&&r.trigger("focus")})}),t.call(o,a,this)})}(jQuery),+function(e){"use strict";function t(t){return this.each(function(){var r=e(this),i=r.data("bs.tooltip"),o="object"==typeof t&&t;(i||"destroy"!=t)&&(i||r.data("bs.tooltip",i=new n(this,o)),"string"==typeof t&&i[t]())})}var n=function(e,t){this.type=this.options=this.enabled=this.timeout=this.hoverState=this.$element=null,this.init("tooltip",e,t)};n.VERSION="3.3.2",n.TRANSITION_DURATION=150,n.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},n.prototype.init=function(t,n,r){this.enabled=!0,this.type=t,this.$element=e(n),this.options=this.getOptions(r),this.$viewport=this.options.viewport&&e(this.options.viewport.selector||this.options.viewport);for(var i=this.options.trigger.split(" "),o=i.length;o--;){var a=i[o];if("click"==a)this.$element.on("click."+this.type,this.options.selector,e.proxy(this.toggle,this));else if("manual"!=a){var s="hover"==a?"mouseenter":"focusin",u="hover"==a?"mouseleave":"focusout";this.$element.on(s+"."+this.type,this.options.selector,e.proxy(this.enter,this)),this.$element.on(u+"."+this.type,this.options.selector,e.proxy(this.leave,this))}}this.options.selector?this._options=e.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},n.prototype.getDefaults=function(){return n.DEFAULTS},n.prototype.getOptions=function(t){return t=e.extend({},this.getDefaults(),this.$element.data(),t),t.delay&&"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),t},n.prototype.getDelegateOptions=function(){var t={},n=this.getDefaults();return this._options&&e.each(this._options,function(e,r){n[e]!=r&&(t[e]=r)}),t},n.prototype.enter=function(t){var n=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);return n&&n.$tip&&n.$tip.is(":visible")?void(n.hoverState="in"):(n||(n=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,n)),clearTimeout(n.timeout),n.hoverState="in",n.options.delay&&n.options.delay.show?void(n.timeout=setTimeout(function(){"in"==n.hoverState&&n.show()},n.options.delay.show)):n.show())},n.prototype.leave=function(t){var n=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);return n||(n=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,n)),clearTimeout(n.timeout),n.hoverState="out",n.options.delay&&n.options.delay.hide?void(n.timeout=setTimeout(function(){"out"==n.hoverState&&n.hide()},n.options.delay.hide)):n.hide()},n.prototype.show=function(){var t=e.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(t);var r=e.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(t.isDefaultPrevented()||!r)return;var i=this,o=this.tip(),a=this.getUID(this.type);this.setContent(),o.attr("id",a),this.$element.attr("aria-describedby",a),this.options.animation&&o.addClass("fade");var s="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,u=/\s?auto?\s?/i,l=u.test(s);l&&(s=s.replace(u,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(s).data("bs."+this.type,this),this.options.container?o.appendTo(this.options.container):o.insertAfter(this.$element);var c=this.getPosition(),f=o[0].offsetWidth,d=o[0].offsetHeight;if(l){var p=s,h=this.options.container?e(this.options.container):this.$element.parent(),m=this.getPosition(h);s="bottom"==s&&c.bottom+d>m.bottom?"top":"top"==s&&c.top-d<m.top?"bottom":"right"==s&&c.right+f>m.width?"left":"left"==s&&c.left-f<m.left?"right":s,o.removeClass(p).addClass(s)}var g=this.getCalculatedOffset(s,c,f,d);this.applyPlacement(g,s);var v=function(){var e=i.hoverState;i.$element.trigger("shown.bs."+i.type),i.hoverState=null,"out"==e&&i.leave(i)};e.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",v).emulateTransitionEnd(n.TRANSITION_DURATION):v()}},n.prototype.applyPlacement=function(t,n){var r=this.tip(),i=r[0].offsetWidth,o=r[0].offsetHeight,a=parseInt(r.css("margin-top"),10),s=parseInt(r.css("margin-left"),10);isNaN(a)&&(a=0),isNaN(s)&&(s=0),t.top=t.top+a,t.left=t.left+s,e.offset.setOffset(r[0],e.extend({using:function(e){r.css({top:Math.round(e.top),left:Math.round(e.left)})}},t),0),r.addClass("in");var u=r[0].offsetWidth,l=r[0].offsetHeight;"top"==n&&l!=o&&(t.top=t.top+o-l);var c=this.getViewportAdjustedDelta(n,t,u,l);c.left?t.left+=c.left:t.top+=c.top;var f=/top|bottom/.test(n),d=f?2*c.left-i+u:2*c.top-o+l,p=f?"offsetWidth":"offsetHeight";r.offset(t),this.replaceArrow(d,r[0][p],f)},n.prototype.replaceArrow=function(e,t,n){this.arrow().css(n?"left":"top",50*(1-e/t)+"%").css(n?"top":"left","")},n.prototype.setContent=function(){var e=this.tip(),t=this.getTitle();e.find(".tooltip-inner")[this.options.html?"html":"text"](t),e.removeClass("fade in top bottom left right")},n.prototype.hide=function(t){function r(){"in"!=i.hoverState&&o.detach(),i.$element.removeAttr("aria-describedby").trigger("hidden.bs."+i.type),t&&t()}var i=this,o=this.tip(),a=e.Event("hide.bs."+this.type);if(this.$element.trigger(a),!a.isDefaultPrevented())return o.removeClass("in"),e.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",r).emulateTransitionEnd(n.TRANSITION_DURATION):r(),this.hoverState=null,this},n.prototype.fixTitle=function(){var e=this.$element;(e.attr("title")||"string"!=typeof e.attr("data-original-title"))&&e.attr("data-original-title",e.attr("title")||"").attr("title","")},n.prototype.hasContent=function(){return this.getTitle()},n.prototype.getPosition=function(t){t=t||this.$element;var n=t[0],r="BODY"==n.tagName,i=n.getBoundingClientRect();null==i.width&&(i=e.extend({},i,{width:i.right-i.left,height:i.bottom-i.top}));var o=r?{top:0,left:0}:t.offset(),a={scroll:r?document.documentElement.scrollTop||document.body.scrollTop:t.scrollTop()},s=r?{width:e(window).width(),height:e(window).height()}:null;return e.extend({},i,a,s,o)},n.prototype.getCalculatedOffset=function(e,t,n,r){return"bottom"==e?{top:t.top+t.height,left:t.left+t.width/2-n/2}:"top"==e?{top:t.top-r,left:t.left+t.width/2-n/2}:"left"==e?{top:t.top+t.height/2-r/2,left:t.left-n}:{top:t.top+t.height/2-r/2,left:t.left+t.width}},n.prototype.getViewportAdjustedDelta=function(e,t,n,r){var i={top:0,left:0};if(!this.$viewport)return i;var o=this.options.viewport&&this.options.viewport.padding||0,a=this.getPosition(this.$viewport);if(/right|left/.test(e)){var s=t.top-o-a.scroll,u=t.top+o-a.scroll+r;s<a.top?i.top=a.top-s:u>a.top+a.height&&(i.top=a.top+a.height-u)}else{var l=t.left-o,c=t.left+o+n;l<a.left?i.left=a.left-l:c>a.width&&(i.left=a.left+a.width-c)}return i},n.prototype.getTitle=function(){var e,t=this.$element,n=this.options;return e=t.attr("data-original-title")||("function"==typeof n.title?n.title.call(t[0]):n.title)},n.prototype.getUID=function(e){do e+=~~(1e6*Math.random());while(document.getElementById(e));return e},n.prototype.tip=function(){return this.$tip=this.$tip||e(this.options.template)},n.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},n.prototype.enable=function(){this.enabled=!0},n.prototype.disable=function(){this.enabled=!1},n.prototype.toggleEnabled=function(){this.enabled=!this.enabled},n.prototype.toggle=function(t){var n=this;t&&(n=e(t.currentTarget).data("bs."+this.type),n||(n=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,n))),n.tip().hasClass("in")?n.leave(n):n.enter(n)},n.prototype.destroy=function(){var e=this;clearTimeout(this.timeout),this.hide(function(){e.$element.off("."+e.type).removeData("bs."+e.type)})};var r=e.fn.tooltip;e.fn.tooltip=t,e.fn.tooltip.Constructor=n,e.fn.tooltip.noConflict=function(){return e.fn.tooltip=r,this}}(jQuery),+function(e){"use strict";function t(t){return this.each(function(){var r=e(this),i=r.data("bs.popover"),o="object"==typeof t&&t;(i||"destroy"!=t)&&(i||r.data("bs.popover",i=new n(this,o)),"string"==typeof t&&i[t]())})}var n=function(e,t){this.init("popover",e,t)};if(!e.fn.tooltip)throw new Error("Popover requires tooltip.js");n.VERSION="3.3.2",n.DEFAULTS=e.extend({},e.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),n.prototype=e.extend({},e.fn.tooltip.Constructor.prototype),n.prototype.constructor=n,n.prototype.getDefaults=function(){return n.DEFAULTS},n.prototype.setContent=function(){var e=this.tip(),t=this.getTitle(),n=this.getContent();e.find(".popover-title")[this.options.html?"html":"text"](t),e.find(".popover-content").children().detach().end()[this.options.html?"string"==typeof n?"html":"append":"text"](n),e.removeClass("fade top bottom left right in"),e.find(".popover-title").html()||e.find(".popover-title").hide()},n.prototype.hasContent=function(){return this.getTitle()||this.getContent()},n.prototype.getContent=function(){var e=this.$element,t=this.options;return e.attr("data-content")||("function"==typeof t.content?t.content.call(e[0]):t.content)},n.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")},n.prototype.tip=function(){return this.$tip||(this.$tip=e(this.options.template)),this.$tip};var r=e.fn.popover;e.fn.popover=t,e.fn.popover.Constructor=n,e.fn.popover.noConflict=function(){return e.fn.popover=r,this}}(jQuery),+function(e){"use strict";function t(n,r){var i=e.proxy(this.process,this);this.$body=e("body"),this.$scrollElement=e(e(n).is("body")?window:n),this.options=e.extend({},t.DEFAULTS,r),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",i),this.refresh(),this.process()}function n(n){return this.each(function(){var r=e(this),i=r.data("bs.scrollspy"),o="object"==typeof n&&n;i||r.data("bs.scrollspy",i=new t(this,o)),"string"==typeof n&&i[n]()})}t.VERSION="3.3.2",t.DEFAULTS={offset:10},t.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},t.prototype.refresh=function(){var t="offset",n=0;e.isWindow(this.$scrollElement[0])||(t="position",n=this.$scrollElement.scrollTop()),this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight();var r=this;this.$body.find(this.selector).map(function(){var r=e(this),i=r.data("target")||r.attr("href"),o=/^#./.test(i)&&e(i);return o&&o.length&&o.is(":visible")&&[[o[t]().top+n,i]]||null}).sort(function(e,t){return e[0]-t[0]}).each(function(){r.offsets.push(this[0]),r.targets.push(this[1])})},t.prototype.process=function(){var e,t=this.$scrollElement.scrollTop()+this.options.offset,n=this.getScrollHeight(),r=this.options.offset+n-this.$scrollElement.height(),i=this.offsets,o=this.targets,a=this.activeTarget;if(this.scrollHeight!=n&&this.refresh(),t>=r)return a!=(e=o[o.length-1])&&this.activate(e);if(a&&t<i[0])return this.activeTarget=null,this.clear();for(e=i.length;e--;)a!=o[e]&&t>=i[e]&&(!i[e+1]||t<=i[e+1])&&this.activate(o[e])},t.prototype.activate=function(t){this.activeTarget=t,this.clear();var n=this.selector+'[data-target="'+t+'"],'+this.selector+'[href="'+t+'"]',r=e(n).parents("li").addClass("active");r.parent(".dropdown-menu").length&&(r=r.closest("li.dropdown").addClass("active")),r.trigger("activate.bs.scrollspy")},t.prototype.clear=function(){e(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var r=e.fn.scrollspy;e.fn.scrollspy=n,e.fn.scrollspy.Constructor=t,e.fn.scrollspy.noConflict=function(){return e.fn.scrollspy=r,this},e(window).on("load.bs.scrollspy.data-api",function(){e('[data-spy="scroll"]').each(function(){var t=e(this);n.call(t,t.data())})})}(jQuery),+function(e){"use strict";function t(t){return this.each(function(){var r=e(this),i=r.data("bs.tab");i||r.data("bs.tab",i=new n(this)),"string"==typeof t&&i[t]()})}var n=function(t){this.element=e(t)};n.VERSION="3.3.2",n.TRANSITION_DURATION=150,n.prototype.show=function(){var t=this.element,n=t.closest("ul:not(.dropdown-menu)"),r=t.data("target");if(r||(r=t.attr("href"),r=r&&r.replace(/.*(?=#[^\s]*$)/,"")),!t.parent("li").hasClass("active")){var i=n.find(".active:last a"),o=e.Event("hide.bs.tab",{relatedTarget:t[0]}),a=e.Event("show.bs.tab",{relatedTarget:i[0]});if(i.trigger(o),t.trigger(a),!a.isDefaultPrevented()&&!o.isDefaultPrevented()){var s=e(r);this.activate(t.closest("li"),n),this.activate(s,s.parent(),function(){i.trigger({type:"hidden.bs.tab",relatedTarget:t[0]}),t.trigger({type:"shown.bs.tab",relatedTarget:i[0]})})}}},n.prototype.activate=function(t,r,i){function o(){a.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),t.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),s?(t[0].offsetWidth,t.addClass("in")):t.removeClass("fade"),t.parent(".dropdown-menu")&&t.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),i&&i()}var a=r.find("> .active"),s=i&&e.support.transition&&(a.length&&a.hasClass("fade")||!!r.find("> .fade").length);a.length&&s?a.one("bsTransitionEnd",o).emulateTransitionEnd(n.TRANSITION_DURATION):o(),a.removeClass("in")};var r=e.fn.tab;e.fn.tab=t,e.fn.tab.Constructor=n,e.fn.tab.noConflict=function(){return e.fn.tab=r,this};var i=function(n){n.preventDefault(),t.call(e(this),"show")};e(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',i).on("click.bs.tab.data-api",'[data-toggle="pill"]',i)}(jQuery),+function(e){"use strict";function t(t){return this.each(function(){var r=e(this),i=r.data("bs.affix"),o="object"==typeof t&&t;i||r.data("bs.affix",i=new n(this,o)),"string"==typeof t&&i[t]()})}var n=function(t,r){this.options=e.extend({},n.DEFAULTS,r),this.$target=e(this.options.target).on("scroll.bs.affix.data-api",e.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",e.proxy(this.checkPositionWithEventLoop,this)),this.$element=e(t),this.affixed=this.unpin=this.pinnedOffset=null,this.checkPosition()};n.VERSION="3.3.2",n.RESET="affix affix-top affix-bottom",n.DEFAULTS={offset:0,target:window},n.prototype.getState=function(e,t,n,r){var i=this.$target.scrollTop(),o=this.$element.offset(),a=this.$target.height();if(null!=n&&"top"==this.affixed)return i<n&&"top";if("bottom"==this.affixed)return null!=n?!(i+this.unpin<=o.top)&&"bottom":!(i+a<=e-r)&&"bottom";var s=null==this.affixed,u=s?i:o.top,l=s?a:t;return null!=n&&i<=n?"top":null!=r&&u+l>=e-r&&"bottom"},n.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(n.RESET).addClass("affix");var e=this.$target.scrollTop(),t=this.$element.offset();return this.pinnedOffset=t.top-e},n.prototype.checkPositionWithEventLoop=function(){setTimeout(e.proxy(this.checkPosition,this),1)},n.prototype.checkPosition=function(){if(this.$element.is(":visible")){var t=this.$element.height(),r=this.options.offset,i=r.top,o=r.bottom,a=e("body").height();"object"!=typeof r&&(o=i=r),"function"==typeof i&&(i=r.top(this.$element)),"function"==typeof o&&(o=r.bottom(this.$element));var s=this.getState(a,t,i,o);if(this.affixed!=s){null!=this.unpin&&this.$element.css("top","");var u="affix"+(s?"-"+s:""),l=e.Event(u+".bs.affix");if(this.$element.trigger(l),l.isDefaultPrevented())return;this.affixed=s,this.unpin="bottom"==s?this.getPinnedOffset():null,this.$element.removeClass(n.RESET).addClass(u).trigger(u.replace("affix","affixed")+".bs.affix")}"bottom"==s&&this.$element.offset({top:a-t-o})}};var r=e.fn.affix;e.fn.affix=t,e.fn.affix.Constructor=n,e.fn.affix.noConflict=function(){return e.fn.affix=r,this},e(window).on("load",function(){e('[data-spy="affix"]').each(function(){var n=e(this),r=n.data();r.offset=r.offset||{},null!=r.offsetBottom&&(r.offset.bottom=r.offsetBottom),null!=r.offsetTop&&(r.offset.top=r.offsetTop),t.call(n,r)})})}(jQuery),function(e,t){"use strict";function n(e){return null!=e&&""!==e&&"hasOwnProperty"!==e&&a.test("."+e)}function r(e,r){if(!n(r))throw o("badmember",'Dotted member path "@{0}" is invalid.',r);for(var i=r.split("."),a=0,s=i.length;a<s&&t.isDefined(e);a++){var u=i[a];e=null!==e?e[u]:void 0}return e}function i(e,n){n=n||{},t.forEach(n,function(e,t){delete n[t]});for(var r in e)!e.hasOwnProperty(r)||"$"===r.charAt(0)&&"$"===r.charAt(1)||(n[r]=e[r]);return n}var o=t.$$minErr("$resource"),a=/^(\.[a-zA-Z_$@][0-9a-zA-Z_$@]*)+$/;t.module("ngResource",["ng"]).provider("$resource",function(){var e=/^https?:\/\/[^\/]*/,n=this;this.defaults={stripTrailingSlashes:!0,cancellable:!1,actions:{get:{method:"GET"},save:{method:"POST"},query:{method:"GET",isArray:!0},remove:{method:"DELETE"},"delete":{method:"DELETE"}}},this.$get=["$http","$log","$q","$timeout",function(a,s,u,l){function c(e){return f(e,!0).replace(/%26/gi,"&").replace(/%3D/gi,"=").replace(/%2B/gi,"+")}function f(e,t){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,t?"%20":"+")}function d(e,t){this.template=e,this.defaults=g({},n.defaults,t),this.urlParams={}}function p(e,c,f,y){function b(e,t){var n={};return t=g({},c,t),m(t,function(t,i){$(t)&&(t=t(e)),n[i]=t&&t.charAt&&"@"==t.charAt(0)?r(e,t.substr(1)):t}),n}function w(e){return e.resource}function x(e){i(e||{},this)}var E=new d(e,y);return f=g({},n.defaults.actions,f),x.prototype.toJSON=function(){var e=g({},this);return delete e.$promise,delete e.$resolved,e},m(f,function(e,r){var c=/^(POST|PUT|PATCH)$/i.test(e.method),f=e.timeout,d=t.isDefined(e.cancellable)?e.cancellable:y&&t.isDefined(y.cancellable)?y.cancellable:n.defaults.cancellable;f&&!t.isNumber(f)&&(s.debug("ngResource:\n  Only numeric values are allowed as `timeout`.\n  Promises are not supported in $resource, because the same value would be used for multiple requests. If you are looking for a way to cancel requests, you should use the `cancellable` option."),delete e.timeout,f=null),x[r]=function(n,s,p,y){var T,C,S,k={};switch(arguments.length){case 4:S=y,C=p;case 3:case 2:if(!$(s)){k=n,T=s,C=p;break}if($(n)){C=n,S=s;break}C=s,S=p;case 1:$(n)?C=n:c?T=n:k=n;break;case 0:break;default:throw o("badargs","Expected up to 4 arguments [params, data, success, error], got {0} arguments",arguments.length)}var _,A,N=this instanceof x,O=N?T:e.isArray?[]:new x(T),D={},I=e.interceptor&&e.interceptor.response||w,M=e.interceptor&&e.interceptor.responseError||void 0;m(e,function(e,t){switch(t){default:D[t]=v(e);break;case"params":case"isArray":case"interceptor":case"cancellable":}}),!N&&d&&(_=u.defer(),D.timeout=_.promise,f&&(A=l(_.resolve,f))),c&&(D.data=T),E.setUrlParams(D,g({},b(T,e.params||{}),k),e.url);var j=a(D).then(function(n){var a=n.data;if(a){if(t.isArray(a)!==!!e.isArray)throw o("badcfg","Error in resource configuration for action `{0}`. Expected response to contain an {1} but got an {2} (Request: {3} {4})",r,e.isArray?"array":"object",t.isArray(a)?"array":"object",D.method,D.url);if(e.isArray)O.length=0,m(a,function(e){"object"==typeof e?O.push(new x(e)):O.push(e)});else{var s=O.$promise;i(a,O),O.$promise=s}}return n.resource=O,n},function(e){return(S||h)(e),u.reject(e)});return j["finally"](function(){O.$resolved=!0,!N&&d&&(O.$cancelRequest=t.noop,l.cancel(A),_=A=D.timeout=null)}),j=j.then(function(e){var t=I(e);return(C||h)(t,e.headers),t},M),N?j:(O.$promise=j,O.$resolved=!1,d&&(O.$cancelRequest=_.resolve),O)},x.prototype["$"+r]=function(e,t,n){$(e)&&(n=t,t=e,e={});var i=x[r].call(this,e,this,t,n);return i.$promise||i}}),x.bind=function(t){return p(e,g({},c,t),f)},x}var h=t.noop,m=t.forEach,g=t.extend,v=t.copy,$=t.isFunction;return d.prototype={setUrlParams:function(n,r,i){var a,s,u=this,l=i||u.template,d="",p=u.urlParams={};m(l.split(/\W/),function(e){if("hasOwnProperty"===e)throw o("badname","hasOwnProperty is not a valid parameter name.");!new RegExp("^\\d+$").test(e)&&e&&new RegExp("(^|[^\\\\]):"+e+"(\\W|$)").test(l)&&(p[e]={isQueryParamValue:new RegExp("\\?.*=:"+e+"(?:\\W|$)").test(l)})}),l=l.replace(/\\:/g,":"),l=l.replace(e,function(e){return d=e,""}),r=r||{},m(u.urlParams,function(e,n){a=r.hasOwnProperty(n)?r[n]:u.defaults[n],t.isDefined(a)&&null!==a?(s=e.isQueryParamValue?f(a,!0):c(a),l=l.replace(new RegExp(":"+n+"(\\W|$)","g"),function(e,t){return s+t})):l=l.replace(new RegExp("(/?):"+n+"(\\W|$)","g"),function(e,t,n){return"/"==n.charAt(0)?n:t+n})}),u.defaults.stripTrailingSlashes&&(l=l.replace(/\/+$/,"")||"/"),l=l.replace(/\/\.(?=\w+($|\?))/,"."),n.url=d+l.replace(/\/\\\./,"/."),m(r,function(e,t){u.urlParams[t]||(n.params=n.params||{},n.params[t]=e)})}},p}]})}(window,window.angular),function(e,t){"use strict";function n(e,t){if(s(e)){t=t||[];for(var n=0,r=e.length;n<r;n++)t[n]=e[n]}else if(u(e)){t=t||{};for(var i in e)"$"===i.charAt(0)&&"$"===i.charAt(1)||(t[i]=e[i])}return t||e}function r(){function e(e,n){
return t.extend(Object.create(e),n)}function r(e,t){var n=t.caseInsensitiveMatch,r={originalPath:e,regexp:e},i=r.keys=[];return e=e.replace(/([().])/g,"\\$1").replace(/(\/)?:(\w+)(\*\?|[\?\*])?/g,function(e,t,n,r){var o="?"===r||"*?"===r?"?":null,a="*"===r||"*?"===r?"*":null;return i.push({name:n,optional:!!o}),t=t||"",""+(o?"":t)+"(?:"+(o?t:"")+(a&&"(.+?)"||"([^/]+)")+(o||"")+")"+(o||"")}).replace(/([\/$\*])/g,"\\$1"),r.regexp=new RegExp("^"+e+"$",n?"i":""),r}s=t.isArray,u=t.isObject;var i={};this.when=function(e,o){var a=n(o);if(t.isUndefined(a.reloadOnSearch)&&(a.reloadOnSearch=!0),t.isUndefined(a.caseInsensitiveMatch)&&(a.caseInsensitiveMatch=this.caseInsensitiveMatch),i[e]=t.extend(a,e&&r(e,a)),e){var s="/"==e[e.length-1]?e.substr(0,e.length-1):e+"/";i[s]=t.extend({redirectTo:e},r(s,a))}return this},this.caseInsensitiveMatch=!1,this.otherwise=function(e){return"string"==typeof e&&(e={redirectTo:e}),this.when(null,e),this},this.$get=["$rootScope","$location","$routeParams","$q","$injector","$templateRequest","$sce",function(n,r,o,a,s,u,l){function f(e,t){var n=t.keys,r={};if(!t.regexp)return null;var i=t.regexp.exec(e);if(!i)return null;for(var o=1,a=i.length;o<a;++o){var s=n[o-1],u=i[o];s&&u&&(r[s.name]=u)}return r}function d(e){var r=w.current;$=g(),y=$&&r&&$.$$route===r.$$route&&t.equals($.pathParams,r.pathParams)&&!$.reloadOnSearch&&!b,y||!r&&!$||n.$broadcast("$routeChangeStart",$,r).defaultPrevented&&e&&e.preventDefault()}function p(){var e=w.current,i=$;y?(e.params=i.params,t.copy(e.params,o),n.$broadcast("$routeUpdate",e)):(i||e)&&(b=!1,w.current=i,i&&i.redirectTo&&(t.isString(i.redirectTo)?r.path(v(i.redirectTo,i.params)).search(i.params).replace():r.url(i.redirectTo(i.pathParams,r.path(),r.search())).replace()),a.when(i).then(h).then(function(r){i==w.current&&(i&&(i.locals=r,t.copy(i.params,o)),n.$broadcast("$routeChangeSuccess",i,e))},function(t){i==w.current&&n.$broadcast("$routeChangeError",i,e,t)}))}function h(e){if(e){var n=t.extend({},e.resolve);t.forEach(n,function(e,r){n[r]=t.isString(e)?s.get(e):s.invoke(e,null,null,r)});var r=m(e);return t.isDefined(r)&&(n.$template=r),a.all(n)}}function m(e){var n,r;return t.isDefined(n=e.template)?t.isFunction(n)&&(n=n(e.params)):t.isDefined(r=e.templateUrl)&&(t.isFunction(r)&&(r=r(e.params)),t.isDefined(r)&&(e.loadedTemplateUrl=l.valueOf(r),n=u(r))),n}function g(){var n,o;return t.forEach(i,function(i,a){!o&&(n=f(r.path(),i))&&(o=e(i,{params:t.extend({},r.search(),n),pathParams:n}),o.$$route=i)}),o||i[null]&&e(i[null],{params:{},pathParams:{}})}function v(e,n){var r=[];return t.forEach((e||"").split(":"),function(e,t){if(0===t)r.push(e);else{var i=e.match(/(\w+)(?:[?*])?(.*)/),o=i[1];r.push(n[o]),r.push(i[2]||""),delete n[o]}}),r.join("")}var $,y,b=!1,w={routes:i,reload:function(){b=!0;var e={defaultPrevented:!1,preventDefault:function(){this.defaultPrevented=!0,b=!1}};n.$evalAsync(function(){d(e),e.defaultPrevented||p()})},updateParams:function(e){if(!this.current||!this.current.$$route)throw c("norout","Tried updating route when with no current route");e=t.extend({},this.current.params,e),r.path(v(this.current.$$route.originalPath,e)),r.search(e)}};return n.$on("$locationChangeStart",d),n.$on("$locationChangeSuccess",p),w}]}function i(){this.$get=function(){return{}}}function o(e,n,r){return{restrict:"ECA",terminal:!0,priority:400,transclude:"element",link:function(i,o,a,s,u){function l(){p&&(r.cancel(p),p=null),f&&(f.$destroy(),f=null),d&&(p=r.leave(d),p.then(function(){p=null}),d=null)}function c(){var a=e.current&&e.current.locals,s=a&&a.$template;if(t.isDefined(s)){var c=i.$new(),p=e.current,g=u(c,function(e){r.enter(e,null,d||o).then(function(){!t.isDefined(h)||h&&!i.$eval(h)||n()}),l()});d=g,f=p.scope=c,f.$emit("$viewContentLoaded"),f.$eval(m)}else l()}var f,d,p,h=a.autoscroll,m=a.onload||"";i.$on("$routeChangeSuccess",c),c()}}}function a(e,t,n){return{restrict:"ECA",priority:-400,link:function(r,i){var o=n.current,a=o.locals;i.html(a.$template);var s=e(i.contents());if(o.controller){a.$scope=r;var u=t(o.controller,a);o.controllerAs&&(r[o.controllerAs]=u),i.data("$ngControllerController",u),i.children().data("$ngControllerController",u)}r[o.resolveAs||"$resolve"]=a,s(r)}}}var s,u,l=t.module("ngRoute",["ng"]).provider("$route",r),c=t.$$minErr("ngRoute");l.provider("$routeParams",i),l.directive("ngView",o),l.directive("ngView",a),o.$inject=["$route","$anchorScroll","$animate"],a.$inject=["$compile","$controller","$route"]}(window,window.angular),function(e,t){"use strict";function n(){function n(e,t){var n,r={},i=e.split(",");for(n=0;n<i.length;n++)r[t?u(i[n]):i[n]]=!0;return r}function r(t,n){null===t||void 0===t?t="":"string"!=typeof t&&(t=""+t),$.innerHTML=t;var r=5;do{if(0===r)throw d("uinput","Failed to sanitize html because the input is unstable");r--,e.document.documentMode&&g($),t=$.innerHTML,$.innerHTML=t}while(t!==$.innerHTML);for(var i=$.firstChild;i;){switch(i.nodeType){case 1:n.start(i.nodeName.toLowerCase(),p(i.attributes));break;case 3:n.chars(i.textContent)}var o;if(!(o=i.firstChild)&&(1==i.nodeType&&n.end(i.nodeName.toLowerCase()),o=i.nextSibling,!o))for(;null==o&&(i=i.parentNode,i!==$);)o=i.nextSibling,1==i.nodeType&&n.end(i.nodeName.toLowerCase());i=o}for(;i=$.firstChild;)$.removeChild(i)}function p(e){for(var t={},n=0,r=e.length;n<r;n++){var i=e[n];t[i.name]=i.value}return t}function h(e){return e.replace(/&/g,"&amp;").replace(y,function(e){var t=e.charCodeAt(0),n=e.charCodeAt(1);return"&#"+(1024*(t-55296)+(n-56320)+65536)+";"}).replace(b,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}function m(e,t){var n=!1,r=i(e,e.push);return{start:function(e,i){e=u(e),!n&&_[e]&&(n=e),n||A[e]!==!0||(r("<"),r(e),a(i,function(n,i){var o=u(i),a="img"===e&&"src"===o||"background"===o;I[o]!==!0||N[o]===!0&&!t(n,a)||(r(" "),r(i),r('="'),r(h(n)),r('"'))}),r(">"))},end:function(e){e=u(e),n||A[e]!==!0||w[e]===!0||(r("</"),r(e),r(">")),e==n&&(n=!1)},chars:function(e){n||r(h(e))}}}function g(t){if(t.nodeType===e.Node.ELEMENT_NODE)for(var n=t.attributes,r=0,i=n.length;r<i;r++){var o=n[r],a=o.name.toLowerCase();"xmlns:ns1"!==a&&0!==a.lastIndexOf("ns1:",0)||(t.removeAttributeNode(o),r--,i--)}var s=t.firstChild;s&&g(s),s=t.nextSibling,s&&g(s)}var v=!1;this.$get=["$$sanitizeUri",function(e){return v&&o(A,k),function(t){var n=[];return c(t,f(n,function(t,n){return!/^unsafe:/.test(e(t,n))})),n.join("")}}],this.enableSvg=function(e){return s(e)?(v=e,this):v},i=t.bind,o=t.extend,a=t.forEach,s=t.isDefined,u=t.lowercase,l=t.noop,c=r,f=m;var $,y=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,b=/([^\#-~ |!])/g,w=n("area,br,col,hr,img,wbr"),x=n("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),E=n("rp,rt"),T=o({},E,x),C=o({},x,n("address,article,aside,blockquote,caption,center,del,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,map,menu,nav,ol,pre,section,table,ul")),S=o({},E,n("a,abbr,acronym,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,q,ruby,rp,rt,s,samp,small,span,strike,strong,sub,sup,time,tt,u,var")),k=n("circle,defs,desc,ellipse,font-face,font-face-name,font-face-src,g,glyph,hkern,image,linearGradient,line,marker,metadata,missing-glyph,mpath,path,polygon,polyline,radialGradient,rect,stop,svg,switch,text,title,tspan"),_=n("script,style"),A=o({},w,C,S,T),N=n("background,cite,href,longdesc,src,xlink:href"),O=n("abbr,align,alt,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,coords,dir,face,headers,height,hreflang,hspace,ismap,lang,language,nohref,nowrap,rel,rev,rows,rowspan,rules,scope,scrolling,shape,size,span,start,summary,tabindex,target,title,type,valign,value,vspace,width"),D=n("accent-height,accumulate,additive,alphabetic,arabic-form,ascent,baseProfile,bbox,begin,by,calcMode,cap-height,class,color,color-rendering,content,cx,cy,d,dx,dy,descent,display,dur,end,fill,fill-rule,font-family,font-size,font-stretch,font-style,font-variant,font-weight,from,fx,fy,g1,g2,glyph-name,gradientUnits,hanging,height,horiz-adv-x,horiz-origin-x,ideographic,k,keyPoints,keySplines,keyTimes,lang,marker-end,marker-mid,marker-start,markerHeight,markerUnits,markerWidth,mathematical,max,min,offset,opacity,orient,origin,overline-position,overline-thickness,panose-1,path,pathLength,points,preserveAspectRatio,r,refX,refY,repeatCount,repeatDur,requiredExtensions,requiredFeatures,restart,rotate,rx,ry,slope,stemh,stemv,stop-color,stop-opacity,strikethrough-position,strikethrough-thickness,stroke,stroke-dasharray,stroke-dashoffset,stroke-linecap,stroke-linejoin,stroke-miterlimit,stroke-opacity,stroke-width,systemLanguage,target,text-anchor,to,transform,type,u1,u2,underline-position,underline-thickness,unicode,unicode-range,units-per-em,values,version,viewBox,visibility,width,widths,x,x-height,x1,x2,xlink:actuate,xlink:arcrole,xlink:role,xlink:show,xlink:title,xlink:type,xml:base,xml:lang,xml:space,xmlns,xmlns:xlink,y,y1,y2,zoomAndPan",!0),I=o({},N,D,O);!function(e){var t;if(!e.document||!e.document.implementation)throw d("noinert","Can't create an inert html document");t=e.document.implementation.createHTMLDocument("inert");var n=t.documentElement||t.getDocumentElement(),r=n.getElementsByTagName("body");if(1===r.length)$=r[0];else{var i=t.createElement("html");$=t.createElement("body"),i.appendChild($),t.appendChild(i)}}(e)}function r(e){var t=[],n=f(t,l);return n.chars(e),t.join("")}var i,o,a,s,u,l,c,f,d=t.$$minErr("$sanitize");t.module("ngSanitize",[]).provider("$sanitize",n),t.module("ngSanitize").filter("linky",["$sanitize",function(e){var n=/((ftp|https?):\/\/|(www\.)|(mailto:)?[A-Za-z0-9._%+-]+@)\S*[^\s.;,(){}<>"\u201d\u2019]/i,i=/^mailto:/i,o=t.$$minErr("linky"),a=t.isDefined,s=t.isFunction,u=t.isObject,l=t.isString;return function(t,c,f){function d(e){e&&y.push(r(e))}function p(e,t){var n,r=v(e);y.push("<a ");for(n in r)y.push(n+'="'+r[n]+'" ');!a(c)||"target"in r||y.push('target="',c,'" '),y.push('href="',e.replace(/"/g,"&quot;"),'">'),d(t),y.push("</a>")}if(null==t||""===t)return t;if(!l(t))throw o("notstring","Expected string but received: {0}",t);for(var h,m,g,v=s(f)?f:u(f)?function(){return f}:function(){return{}},$=t,y=[];h=$.match(n);)m=h[0],h[2]||h[4]||(m=(h[3]?"http://":"mailto:")+m),g=h.index,d($.substr(0,g)),p(m,h[0].replace(i,"")),$=$.substring(g+h[0].length);return d($),e(y.join(""))}}])}(window,window.angular),function(){function e(e){return["$rootScope","$window","$log",function(t,n,r){for(var i,o,a,s=n[e]||(r.warn("This browser does not support Web Storage!"),{}),u={$default:function(e){for(var t in e)angular.isDefined(u[t])||(u[t]=e[t]);return u},$reset:function(e){for(var t in u)"$"===t[0]||delete u[t];return u.$default(e)}},l=0;l<s.length;l++)(a=s.key(l))&&"ngStorage-"===a.slice(0,10)&&(u[a.slice(10)]=angular.fromJson(s.getItem(a)));return i=angular.copy(u),t.$watch(function(){o||(o=setTimeout(function(){if(o=null,!angular.equals(u,i)){angular.forEach(u,function(e,t){angular.isDefined(e)&&"$"!==t[0]&&s.setItem("ngStorage-"+t,angular.toJson(e)),delete i[t]});for(var e in i)s.removeItem("ngStorage-"+e);i=angular.copy(u)}},100))}),"localStorage"===e&&n.addEventListener&&n.addEventListener("storage",function(e){"ngStorage-"===e.key.slice(0,10)&&(e.newValue?u[e.key.slice(10)]=angular.fromJson(e.newValue):delete u[e.key.slice(10)],i=angular.copy(u),t.$apply())}),u}]}angular.module("ngStorage",[]).factory("$localStorage",e("localStorage")).factory("$sessionStorage",e("sessionStorage"))}(),angular.module("todoApp",["ngRoute","ngResource","ngStorage","ngSanitize","templates"],["$interpolateProvider",function(e){e.startSymbol("<%"),e.endSymbol("%>")}]).config(["$httpProvider",function(e){e.interceptors.push("RequestsErrorHandler")}]),angular.module("todoApp").config(["$routeProvider",function(e){e.when("/",{name:"home",controller:"HomeV2Controller",templateUrl:"/underodds",params:{TYPE:"single",ODD_TYPE:"malay",GAME_TYPE:"socer"}}).when("/correct_score",{name:"correct_score",controller:"HomeV2Controller",templateUrl:"/correct_score",params:{TYPE:"single",ODD_TYPE:"malay",GAME_TYPE:"socer"}}).when("/saba_game",{name:"saba_game",controller:"HomeV2Controller",templateUrl:"/saba_game",params:{TYPE:"single",ODD_TYPE:"saba",GAME_TYPE:"saba"}}).when("/parlay",{name:"parlay",controller:"HomeV2Controller",templateUrl:"/underodds",params:{TYPE:"multiple",ODD_TYPE:"decimal",GAME_TYPE:"socer"}}).when("/bingo",{name:"bingo",controller:"HomeV2Controller",templateUrl:"/bingo",params:{TYPE:"multiple",ODD_TYPE:"decimal"}}).otherwise({redirectTo:"/"})}]),angular.module("todoApp").controller("BetsController",["$scope","$location","$localStorage","leaguesAPI","$templateCache",function(e,t,n,r,i){}]),angular.module("todoApp").controller("HomeController",["$scope","$location","$localStorage","leaguesAPI","$templateCache",function(e,t,n,r,i){function o(){e.menu.showLeague=!e.menu.showLeague}function a(){SwitchVersion(),offNewVersionPopup(),r.changeVersion().then(function(){setTimeout(function(){top.window.location.href="/"},1500)})}function s(){e.loading.leagues=!0,r.getInPlayLeagues(top.TYPE).then(function(t){l("leagues",t),setTimeout(function(){d(e,function(){e.loading.leagues=!1,e.leagues.sort(function(e,t){return e.order-t.order})})},3e3)})}function u(){e.loading.upleagues=!0,r.getUpcomingPlayLeagues(top.TYPE).then(function(t){l("upleagues",t),setTimeout(function(){d(e,function(){e.loading.upleagues=!1,e.upleagues.sort(function(e,t){return e.order_upcoming-t.order_upcoming})})},3e3)})}function l(t,n){if(!e[t])return e[t]=n;e[t]=e[t].filter(function(e,t){return n.findIndex(function(t){return t.id==e.id})>-1});for(var r=0;r<n.length;r++){var i=n[r],o=e[t].findIndex(function(e){return e.id==i.id});o==-1?e[t].push(i):c(t,o,i)}}function c(t,n,r){angular.forEach(e[t][n],function(i,o){"events"!==o&&(e[t][n][o]=r[o])}),e[t][n].events=e[t][n].events.filter(function(e,t){return r.events.findIndex(function(t){return t.odd_id==e.odd_id})>-1});for(var i=0;i<r.events.length;i++){var o=r.events[i],a=e[t][n].events.findIndex(function(e){return e.odd_id==o.odd_id});a==-1?e[t][n].events.push(o):f(t,n,a,o)}}function f(t,n,r,i){var o=e[t][n].events[r];d(e,function(){angular.forEach(o,function(o,a){e[t][n].events[r][a]=i[a]}),e[t][n].events.sort(function(e,t){return e.order-t.order})})}function d(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.loading={leagues:!1,upleagues:!1},e.menu={showLeague:!1,sports:!1},e.leagueNumber=0,e.leagueSelectNumber=0,s(),u(),e.onrefreshUpcoming=function(){console.log("reload"),u()},e.onrefreshInplay=function(){console.log("reload onrefreshInplay"),s()},e.openLeague=o,e.changeVersion=a}]),angular.module("todoApp").controller("LeftController",["$scope","$location","$localStorage","leaguesAPI","$templateCache",function(e,t,n,r,i){function o(){SwitchVersion(),r.changeVersion().then(function(){setTimeout(function(){top.window.location.reload()},1500)})}function a(t){e.menu.account=t}function s(){e.menu.sports=!e.menu.sports}function u(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.menu={account:"full",sports:!0},e.onMenuAccount=a,e.changeVersion=o,e.onMenuSports=s,e.$on("EVENT_MENU_SHOW",function(t,n){angular.forEach(n,function(t,n){e.menu[n]=t})}),bindEvent(window,"message",function(t){var n=JSON.parse(t.data);"EVENT_MENU_SHOW"==n.name&&u(e,function(t){angular.forEach(n.data,function(t,n){e.menu[n]=t})})})}]),angular.module("todoApp").controller("NumberGameController",["$scope","$location","$localStorage","leaguesAPI","$templateCache",function(e,t,n,r,i){function o(){e.loading=!0,r.getNumberGames().then(function(t){t.number||(e.number_games[1]={name:"Number Game"}),t.turbo||(e.number_games[0]={name:"Turbo Number Game"}),angular.forEach(t.turbo,function(t,n){e.number_games[0][n]=t}),angular.forEach(t.number,function(t,n){e.number_games[1][n]=t}),setTimeout(function(){a(e,function(){e.loading=!1})},3e3)})}function a(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.loading={leagues:!1,upleagues:!1},e.number_games=[{data:[]},{data:[]}],o(),e.loadNumberGame=o,e.checkOn=function(e,t){return e.ball_numbers&&e.ball_numbers.indexOf(t)>=0},e.betBingo=function(e,t,n,r,i,o,a){var s={TYPE:top.TYPE,event:e,step:e.step,odd_type:o,bet_type:t,game_type:"number_game",bet_value:r,bet_position:n,ss:a};window.parent.postMessage(JSON.stringify({data:s,name:"EVENT_BET",to:"leftFrame"}),"*")}}]),angular.module("todoApp").controller("RightController",["$scope","$location","$localStorage","leaguesAPI","$templateCache",function(e,t,n,r,i){function o(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.rightOpen=!1,setTimeout(function(t){o(e,function(){e.openSlide()})},2e3),e.openSlide=function(t){e.rightOpen=!e.rightOpen},$("#slides").slidesjs({width:305,height:165,navigation:!1,play:{active:!1,effect:"slide",interval:3e3,auto:!0,swap:!0,pauseOnHover:!0,restartDelay:2500}});new Swiper(".swiper-container",{width:275,height:105,autoplay:{delay:4e3,disableOnInteraction:!1},navigation:{nextEl:".arrow-left",prevEl:".arrow-right"}})}]),angular.module("todoApp").controller("TopMenuController",["$scope","$location","$localStorage","leaguesAPI","$rootScope",function(e,t,n,r,i){function o(e){window.parent.postMessage(JSON.stringify({data:{},name:"EVENT_GO_LOGOUT",to:"main"}),"*")}function a(){window.parent.postMessage(JSON.stringify({data:{},name:"EVENT_BET_SUCCESS",to:"leftFrame"}),"*"),i.$broadcast("EVENT_OPEN_BETLIST")}function s(){window.parent.postMessage(JSON.stringify({data:{account:"full",sports:!0},name:"EVENT_MENU_SHOW",to:"leftFrame"}),"*")}e.logout=o,e.goBetList=a,e.goAccount=s,e.showHotNews=!0}]),angular.module("todoApp").controller("AccountV2Controller",["$scope","$location","$localStorage","leaguesAPI","$templateCache","$rootScope",function(e,t,n,r,i,o){function a(){e.accountSetting.loading||(e.accountSetting.loading=!0,r.getAccountInfo().then(function(t){e.account=t,o.$broadcast("EVENT_UPDATE_MIN_MAX",t),u(e,function(){setTimeout(function(){e.accountSetting.loading=!1},2e3)})}))}function s(t){e.show=t}function u(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.loadInfo=a,e.onMenuAccount=s,e.accountSetting={loading:!1},a(),e.$on("EVENT_ACCOUNT_REFRESH",a)}]),angular.module("todoApp").controller("HomeV2Controller",["$route","$scope","$location","$localStorage","leaguesAPI","$templateCache",function(e,t,n,r,i,o){function a(){t.menu.showLeague=!t.menu.showLeague}function s(){t.loading.leagues=!0,i.getV2InPlayLeagues(top.TYPE,top.GAME_TYPE).then(function(e){l("leagues",e),setTimeout(function(){p(t,function(){t.loading.leagues=!1,t.leagues.sort(function(e,t){return e.order-t.order})})},3e3)})}function u(){t.loading.upleagues=!0,i.getV2UpcomingPlayLeagues(top.TYPE,top.GAME_TYPE).then(function(e){l("upleagues",e),setTimeout(function(){p(t,function(){t.loading.upleagues=!1,t.upleagues.sort(function(e,t){return e.order_upcoming-t.order_upcoming})})},3e3)})}function l(e,n){if(!t[e])return t[e]=n;t[e]=t[e].filter(function(e,t){return n.findIndex(function(t){return t.id==e.id})>-1});for(var r=0;r<n.length;r++){var i=n[r],o=t[e].findIndex(function(e){return e.id==i.id});o==-1?t[e].push(i):c(e,o,i)}}function c(e,n,r){angular.forEach(r,function(i,o){"events"!==o&&(t[e][n][o]=r[o])}),t[e][n].events=t[e][n].events.filter(function(e,t){return r.events.findIndex(function(t){return t.event_id==e.event_id})>-1});for(var i=0;i<r.events.length;i++){var o=r.events[i],a=t[e][n].events.findIndex(function(e){return e.event_id==o.event_id});a==-1?t[e][n].events.push(o):f(e,n,a,o)}}function f(e,n,r,i){var o=t[e][n].events[r];angular.forEach(o,function(o,a){"odds"!==a&&(t[e][n].events[r][a]=i[a])}),t[e][n].events[r].odds=t[e][n].events[r].odds.filter(function(e,t){return i.odds.findIndex(function(t){return t.odd_id==e.odd_id})>-1});for(var a=0;a<i.odds.length;a++){var s=i.odds[a],u=t[e][n].events[r].odds.findIndex(function(e){return e.odd_id==s.odd_id});u==-1?t[e][n].events[r].odds.push(s):d(e,n,r,u,s)}}function d(e,n,r,i,o){var a=t[e][n].events[r].odds[i];p(t,function(){angular.forEach(a,function(a,s){t[e][n].events[r].odds[i][s]=o[s]})})}function p(e,t){e.$$phase||!e.$root||e.$root.$$phase?t():e.$apply(t)}function h(){function e(){var e=["webkit","moz","ms","o"];if("hidden"in document)return"hidden";for(var t=0;t<e.length;t++)if(e[t]+"Hidden"in document)return e[t]+"Hidden";return null}var t=new Date("Tue Jun 14 2018 23:00:00 GMT+0800 (台北標準時間)");if(new Date>=t)$("#WCBanner").attr("class","worldcup-open"),$("#countdown-3").remove();else{$("#countdown-3").timeTo({timeTo:new Date(t),displayDays:2,theme:"black",displayCaptions:!0,fontFamily:"Arail, sans-serif",fontSize:15,captionSize:12,callback:function(){$("#WCBanner").attr("class","worldcup-open"),$("#countdown-3").remove()}});var n=e();if(n){var r=n.replace(/[H|h]idden/,"")+"visibilitychange";document.addEventListener(r,function(){$("#countdown-3").timeTo(t)},!1)}}}var m=e.current.$$route.params;top.TYPE=m.TYPE||top.TYPE,top.ODD_TYPE=m.ODD_TYPE||top.ODD_TYPE,top.GAME_TYPE=m.GAME_TYPE||top.GAME_TYPE,t.TYPE=top.TYPE,t.loading={leagues:!1,upleagues:!1},t.menu={showLeague:!1,sports:!1,showPromotions:!0},t.leagueNumber=0,t.leagueSelectNumber=0,s(),u(),t.onrefreshUpcoming=function(){console.log("reload"),u()},t.onrefreshInplay=function(){console.log("reload onrefreshInplay"),s()},t.openLeague=a,t.goToToday=function(){n.path("home")},t.goToParlay=function(){n.path("parlay")},h()}]),angular.module("todoApp").controller("LeftV2Controller",["$scope","$location","$localStorage","leaguesAPI","$templateCache",function(e,t,n,r,i){e.menu={account:!1,sports:!0},e.onChangeVersion=function(){$("#change_version").show(),r.changeVersion().then(function(){setTimeout(function(){window.location.reload()},1500)})}}]),angular.module("todoApp").factory("RequestsErrorHandler",["$q",function(e){var t=!1;return{specificallyHandled:function(e){t=!0;try{return e()}finally{t=!1}},responseError:function(t){return t&&401==t.status&&(alert("Bạn đã đăng nhập tại phiên bản khác buộc phải thoát?"),window.parent.postMessage(JSON.stringify({data:{},name:"EVENT_GO_LOGIN",to:"main"}),"*"),window.location.href="/login"),e.reject(t)}}}]),angular.module("todoApp").factory("leaguesAPI",["$http",function(e){return{getInPlayLeagues:function(t){return e.get("/api/leagues/inplay?type="+t).then(function(e){return e.data.data})},getNumberGames:function(t){return e.get("/api/number_games").then(function(e){return e.data.data})},getV2InPlayLeagues:function(t,n){return e.get("/api/v2/leagues/inplay?type="+t+"&game_type="+n).then(function(e){return e.data.data})},getUpcomingPlayLeagues:function(t){return e.get("/api/leagues/upcoming?type="+t).then(function(e){return e.data.data})},getV2UpcomingPlayLeagues:function(t,n){return e.get("/api/v2/leagues/upcoming?type="+t+"&game_type="+n).then(function(e){return e.data.data})},getInPlayLeaguesById:function(t){return e.get("/api/leagues/inplay/"+t).then(function(e){return e.data.data})},getEventById:function(t,n){return e.get("/api/events/"+t+"/"+n).then(function(e){return e.data.data})},createBet:function(t){var n="/api/bets";return t.is_game&&(n="/api/games"),e.post(n,t).then(function(e){return e.data.data})},createGroup:function(t){return e.post("/api/bets/group",t).then(function(e){return e.data.data})},getInplayBets:function(){return e.get("api/bets/inplay").then(function(e){return e.data.data})},getHistoriesBets:function(){return e.get("api/bets/histories").then(function(e){return e.data.data})},getLeagues:function(){return e.get("api/leagues").then(function(e){return e.data.data})},getAccountInfo:function(){return e.get("api/account").then(function(e){return e.data.data})},changeVersion:function(){return e.get("api/version/change").then(function(e){return e.data.data})},getInplayCorrectScore:function(){return e.get("api/v2/correct_score").then(function(e){return e.data.data})}}}]),angular.module("todoApp").factory("User",["$resource",function(e){return e("/api/user/:userId",{userId:"@id"},{update:{method:"PUT"},login:{method:"POST",url:"/api/user/login"},getByToken:{method:"GET",url:"/api/user/getByToken"}})}]),angular.module("todoApp").filter("range",function(){return function(e,t){if(!t)return e;t=parseInt(t);for(var n=0;n<t;n++)e.push(n);return e}}).filter("to_trusted",["$sce",function(e){return function(t){return e.trustAsHtml(t)}}]).filter("oddName",function(){return function(e){return["ft_ou","hf_ou"].indexOf(e.bet_type)>-1?0==e.bet_position?"Tài":"Xỉu":e.event[0==e.bet_position?"home":"away"]}}).filter("betType",function(){return function(e){switch(e.bet_type){case"ft_ou":return"Tài/Xỉu";case"hf_ou":return"1H Tài/Xỉu";case"ft_hdp":return"Handicap";case"hf_hdp":return"1H Handicap";default:return"Handicap"}}}).filter("handicapValue",function(){return function(e){if("game_next_ou"==e.bet_type)return 37.5;if(e.odd)return["ft_ou","hf_ou"].indexOf(e.bet_type)>-1?e.odd.handicap_value:e.event.saba?(0==e.bet_position?-1:1)*parseFloat(e.odd.handicap_value):("home"==e.odd.handicap_team?-1:1)*(0==e.bet_position?1:-1)*parseFloat(e.odd.handicap_value)}}).filter("betsValue",function(){return function(e){if(!e||!e.length)return 0;var t=1;return e.map(function(e){t*=convertMalayToDecimal(parseFloat(e.bet_value))}),Math.abs(Math.round(100*t)/100)}}).filter("safeHtml",["$sce",function(e){return function(t){return e.trustAsHtml(t)}}]).filter("payAmount",function(){return function(e){if(""==e.bet_value||!e.bet_amount)return"";var t=Math.abs(e.bet_value);return(t+1)*e.bet_amount}}).filter("statusVn",function(){return function(e,t){if("cancel"==t)return"Hoàn Phí";if("runing"==t)return"Đang chạy";if("pending"==t)return"Đang chờ";var n={won:"Thắng",lose:"Thua",draw:"Hòa"};return n[e]}}).filter("timeStatus",function(){return function(e,t){var n={0:"Đang chạy",1:"Đang chạy",2:"Đang cập nhật",3:"Hoàn thành"};return n[e]}}),angular.module("todoApp").filter("numberOdd",["$filter",function(e){return function(t,n){return t&&""!=t?("decimal"==top.ODD_TYPE&&(t=convertMalayToDecimal(t)),e("number")(t,n)):""}}]).filter("numberPretty",["$filter",function(e){return function(t,n){if(!t||""==t)return 0;for(var r=e("number")(t,n);("0"==r.charAt(r.length-1)||"."==r.charAt(r.length-1))&&r.indexOf(".")>0;)r=r.substr(0,r.length-1);return r}}]),angular.module("todoApp").filter("timer",function(){return function(e){if(!e||0==e.tm&&"0"==String(e.tt))return"!Live";var t=1;return t=e.tm<45||e.ta>0&&e.tm<55?1:e.tm>=45&&e.tm<55&&0==String(e.tt)?0:2,0==t?"H.Time":t+"H "+(e.tm-(2==t?45:0))+"'"}}),angular.module("todoApp").directive("defineText",["$templateCache",function(e){return{restrict:"A",link:function(e,t,n){e.$watch(n.defineText,function(e,r){var i=null;e!==r&&(t.parent().addClass("Odds_Upd statusChanged"),i&&clearTimeout(i),i=setTimeout(function(){t.parent().removeClass("Odds_Upd statusChanged")},2e4)),"decimal"==n.oddKind&&(e=convertMalayToDecimal(e)),e<0?t.addClass("FavOddsClass"):t.removeClass("FavOddsClass")})}}}]),angular.module("todoApp").directive("defineTextV2",["$templateCache",function(e){return{restrict:"A",link:function(e,t,n){e.$watch(n.defineTextV2,function(e,r){t.removeClass("indicatorUp"),t.removeClass("indicatorDown"),e!==r&&(e>r?t.addClass("indicatorUp"):t.addClass("indicatorDown")),"decimal"==n.oddKind&&(e=convertMalayToDecimal(e)),e<0?t.addClass("underdog"):t.removeClass("underdog")})}}}]),angular.module("todoApp").directive("enterStroke",function(){return function(e,t,n){t.bind("keydown keypress",function(t){13===t.which&&(e.$apply(function(){e.$eval(n.enterStroke)}),t.preventDefault())})}}),angular.module("todoApp").directive("textRed",["$templateCache",function(e){return{restrict:"A",link:function(e,t,n){parseFloat(t[0].innerText)<0?t.addClass("FavOddsClass"):t.addClass("UdrDogOddsClass")}}}]),angular.module("todoApp").directive("visible",function(){return{restrict:"A",link:function(e,t,n){e.$watch(n.visible,function(e){t.css("visibility",e?"visible":"hidden")})}}}),angular.module("todoApp").directive("leftBetV2",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",template:e.get("elements/v2/left-bet/left-bet.html"),controller:["$scope","$rootScope",function(e,n){function r(){c("betList")}function i(t,r){e.settings.show=!0,e.settings.type=r.TYPE,e.settings.menu="bet",n.$broadcast("EVENT_MENU_SHOW",{account:"mini",sports:!1}),n.$broadcast("EVENT_ACCOUNT_REFRESH");var i=u(r);"single"==r.TYPE?(e.oldValue=null,e.model=i):(e.model&&e.model.odd_id||(e.oldValue=null,e.model=i),o(i)),f(r.TYPE)}function o(t){var n=e.mulBets.findIndex(function(e){return e.event_id==t.event_id});n>-1?e.mulBets[n]=t:e.mulBets.push(t)}function a(t){e.mulBets=e.mulBets.filter(function(e){return e.odd_id!==t})}function s(){e.settings.auto&&(e.number=T,E&&clearInterval(E),E=setInterval(function(){w(e,function(){e.number--,0==e.number&&(console.log("DOne"),e.number=T,e.onRefresh&&e.onRefresh())})},1e3))}function u(e){return{odd_name:y(e),odd_type:e.odd_type,odd:e.event[e.bet_type],event_id:e.event.event_id,event:e.event,odd_id:e.event.odd_id,bet_value:e.bet_value,bet_type:e.bet_type,bet_position:"home"==e.type?0:"away"==e.type?1:2,type:e.type}}function l(t,n){e.mulBets.findIndex(function(e){return e.odd_id==t&&(e.event=n,e.bet_value=g(n,e),!0)})}function c(t){e.settings.show?e.settings.menu==t&&(e.settings.show=!e.settings.show):e.settings.show=!0,e.settings.menu=t}function f(t){e.settings.type=t,"single"==t?(setTimeout(function(e){$("#BPstake").focus()},800),s()):setTimeout(function(e){$("#mulStake").focus()},800)}function d(t,n){e.data.bongdamin=n.bongdamin,e.data.bongdamax=n.bongdamax}function p(){"correct_score"===e.model.bet_type&&t.getEventById(e.model.event_id,e.model.bet_type).then(function(t){e.model.event=t,e.model.odd=e.model.event[e.model.bet_type],e.oldValue=e.model.bet_value,e.model.bet_value=g(e.model.event,e.model),m()}),e.model&&e.model.odd_id&&t.getEventById(e.model.odd_id,e.model.bet_type).then(function(t){e.model.event=t,e.model.odd=e.model.event[e.model.bet_type],e.oldValue=e.model.bet_value,e.model.bet_value=g(e.model.event,e.model),m()})}function h(){e.mulBets.map(function(n){t.getEventById(n.odd_id).then(function(t){w(e,function(){l(t.odd_id,t)})})})}function m(){e.model.bet_value&&""!=e.model.bet_value?e.oldValue&&e.oldValue!=e.model.bet_value&&(e.error_message="Tỷ lệ cược đã thay đổi từ <em>"+e.oldValue+"</em> Thành <em>"+e.model.bet_value+"</em>"):e.error_message="Tỷ lệ cược đang được cập nhật!"}function g(e,t){var n=e[t.bet_type];return"correct_score"===t.bet_type?e.correct_score[t.type]:["ft_hdp","hf_hdp","hf_1x2","ft_1x2"].indexOf(t.bet_type)>-1?0==t.bet_position?n.home_od:1==t.bet_position?n.away_od:n.draw_od:0==t.bet_position?n.over_od:n.under_od}function v(){e.settings.auto?run():E&&clearInterval(E)}function y(e){return["ft_ou","hf_ou"].indexOf(e.bet_type)>-1?"home"==e.type?"Tài":"Xỉu":e.event[e.type]}function b(t){e.settings.show=!1,E&&clearInterval(E)}function w(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.settings={show:!1,auto:!0,type:"single",menu:"bet"},e.data={},e.model={},e.mulModel={},e.mulBets=[];var x=!1;e.number=10;var E,T=10;e.$on("EVENT_BET",i),e.$on("EVENT_OPEN_BETLIST",r),e.$on("EVENT_UPDATE_MIN_MAX",d),e.$on("CANCEL_BOX_BET",b),e.onChange=v,e.onRefresh=p,e.onDelete=a,e.backToHome=b,e.switchBettingMode=f,e.onSwithMenu=c,e.betMulSubmit=function(){if(e.mul_error_message="",!e.mulModel.bet_amount||e.mulModel.bet_amount<=0)return e.mul_error_message="Bạn chưa nhập tiền cược!";if(e.mulModel.bet_amount<e.data.bongdamin)return e.mulModel.bet_amount=e.data.bongdamin,void(e.mul_error_message="Tiền cược của bạn thấp hơn mức cược tối thiểu!");if(e.mulModel.bet_amount>e.data.bongdamax)return e.mulModel.bet_amount=e.data.bongdamax,void(e.mul_error_message="Tiền cược của bạn cao hơn mức cược tối đa!");var r=confirm("Bạn có chắc chắn muốn xử lý đặt cược không?");if(r&&!x){x=!0;var i={bet_amount:e.mulModel.bet_amount,bets:e.mulBets.map(function(e){return{odd_id:e.odd_id,bet_value:e.bet_value,bet_position:e.bet_position,bet_type:e.bet_type}})};t.createGroup(i).then(function(t){e.mulModel={},e.mulBets=[],"runing"==t.status&&alert("Đặt cược thành công. \nMã cược: "+t.id),
n.$broadcast("EVENT_BET_SUCCESS",t),n.$broadcast("EVENT_ACCOUNT_REFRESH"),c("betList"),x=!1})["catch"](function(e){x=!1,h(),alert(e.data&&e.data.msg||"Có lỗi xảy ra, làm ơn reload page")})}},e.betSubmit=function(){if(!e.model.bet_amount||e.model.bet_amount<=0)return e.error_message="Bạn chưa nhập tiền cược!";if(e.model.bet_amount<e.data.bongdamin)return e.model.bet_amount=e.data.bongdamin,void(e.error_message="Tiền cược của bạn thấp hơn mức cược tối thiểu!");if(e.model.bet_amount>e.data.bongdamax)return e.model.bet_amount=e.data.bongdamax,void(e.error_message="Tiền cược của bạn cao hơn mức cược tối đa!");e.model.bet_value&&""!=e.model.bet_value||(e.error_message="Tỷ lệ cược đang được cập nhật!");var r=confirm("Bạn có chắc chắn muốn xử lý đặt cược không?");r&&(x||(x=!0,t.createBet(e.model).then(function(t){e.model={},"runing"==t.status&&alert("Đặt cược thành công. \nMã cược: "+t.id),n.$broadcast("EVENT_BET_SUCCESS",t),n.$broadcast("EVENT_ACCOUNT_REFRESH"),c("betList"),x=!1})["catch"](function(t){x=!1,t&&t.data&&"ODD_CHANGED"==t.data.code?(w(e,function(){e.model.bet_value=t.data.data.newVal}),t.data.data.newVal?alert("Tỉ lệ thay đổi từ "+t.data.data.oldVal+" đến "+t.data.data.newVal):alert("Tỷ lệ cược đang được cập nhật!")):alert(t.data&&t.data.msg||"Có lỗi xảy ra, làm ơn reload page")})))}}]}}]),angular.module("todoApp").directive("mulBetItemV2",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",scope:{item:"<",onDelete:"<"},template:e.get("elements/v2/left-bet/mul-bet-item.html"),controller:["$scope","$rootScope",function(e,t){e.onDeleteItem=function(){e.onDelete&&e.onDelete(e.item.odd_id)}}]}}]),angular.module("todoApp").directive("leftBetItemV2",["$templateCache",function(e){return{restrict:"EA",scope:{value:"<"},template:e.get("elements/v2/left-bet-wait/left-bet-item.html"),controller:["$scope","$rootScope",function(e,t){}]}}]),angular.module("todoApp").directive("leftBetWaitV2",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",template:e.get("elements/v2/left-bet-wait/left-bet-wait.html"),controller:["$scope","$rootScope",function(e,n){function r(t,n){n&&"pending"==n.status?e.waitSetting.type="waitingBets":e.waitSetting.type="betList",e.loadBets()}function i(){e.waitSetting.loading||(e.waitSetting.bet_number=0,l&&clearInterval(l),c&&clearInterval(c),e.waitSetting.loading=!0,t.getInplayBets().then(function(t){var r=[],i=[],u=[],l=[];t.map(function(e){"pending"==e.status?(f.indexOf(e.id)==-1&&f.push(e.id),r.push(e)):"runing"==e.status?(i.push(e),f.indexOf(e.id)>-1&&s(e.id)):(f.indexOf(e.id)>-1&&(n.$broadcast("EVENT_ACCOUNT_REFRESH"),l.push(e)),u.push(e))}),e.waitData.waitingBets=r,e.waitData.betList=i,e.waitData.voidTicket=u,e.waitData.voidTicketCaches=l,e.waitSetting.loading=!1,r.length>0?a(5):i.length&&o()}))}function o(){console.log("Reload"),c&&clearTimeout(c),c=setTimeout(function(){console.log("Reload 1"),e.$apply(function(){e.loadBets()})},18e4)}function a(t){e.waitSetting.type="waitingBets",e.waitSetting.bet_number=t,l&&clearInterval(l),l=setInterval(function(){e.$apply(function(){e.waitSetting.bet_number--,0===e.waitSetting.bet_number&&(clearInterval(l),e.loadBets())})},800)}function s(e){f=f.filter(function(t,n){return e!==t}),n.$broadcast("EVENT_ACCOUNT_REFRESH")}function u(t){f=f.filter(function(t,n){return e.waitData.voidTicketCaches.findIndex(function(e){return e.id==t})==-1}),e.waitData.voidTicketCaches=[],e.waitSetting.type=t}var l,c;e.loadBets=i,e.openBetList=u,e.waitSetting={type:"betList",bet_number:5,loading:!1},e.waitData={betList:[],waitingBets:[],voidTicket:[],voidTicketCaches:[]};var f=[];e.loadBets(),e.$on("EVENT_BET_SUCCESS",r)}]}}]),angular.module("todoApp").directive("accountInfo",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",scope:{show:"="},template:e.get("elements/account-info/account-info.html"),controller:["$scope","$rootScope",function(e,n){function r(){e.accountSetting.loading||(e.accountSetting.loading=!0,t.getAccountInfo().then(function(t){e.account=t,n.$broadcast("EVENT_UPDATE_MIN_MAX",t),o(e,function(){e.accountSetting.loading=!1})}))}function i(t){e.show=t}function o(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.loadInfo=r,e.onMenuAccount=i,e.accountSetting={loading:!1},r(),e.$on("EVENT_ACCOUNT_REFRESH",r)}]}}]),angular.module("todoApp").directive("leftBet",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",template:e.get("elements/left-bet/left-bet.html"),controller:["$scope","$rootScope",function(e,n){function r(t,r){console.log(r),e.settings.show=!0,e.settings.type=r.TYPE,n.$broadcast("EVENT_MENU_SHOW",{account:"mini",sports:!1}),n.$broadcast("EVENT_ACCOUNT_REFRESH"),e.error_message="";var o=s(r);"single"==r.TYPE?(e.oldValue=null,e.model=o):(e.model&&e.model.odd_id||(e.oldValue=null,e.model=o),i(o)),c(r.TYPE)}function i(t){var n=e.mulBets.findIndex(function(e){return e.event_id==t.event_id});n>-1?e.mulBets[n]=t:e.mulBets.push(t)}function o(t){e.mulBets=e.mulBets.filter(function(e){return e.odd_id!==t})}function a(){e.settings.auto&&(e.number=E,x&&clearInterval(x),x=setInterval(function(){b(e,function(){e.number--,0==e.number&&(console.log("DOne"),e.number=E,e.onRefresh&&e.onRefresh())})},1e3))}function s(t){return"number_game"==t.game_type?(e.game_name="Number Game",{is_game:!0,odd_name:u(t),odd_type:t.odd_type,step:t.step,odd:t.event[t.bet_type],event_id:t.event.event_id,event:t.event,ss:t.ss,odd_id:t.event.event_id,bet_value:t.bet_value,bet_type:t.bet_type,bet_position:t.bet_position}):(e.game_name="Bóng đá",{odd_name:v(t),odd_type:t.odd_type,odd:t.event[t.bet_type],event_id:t.event.event_id,event:t.event,ss:t.event.ss,odd_id:t.event.odd_id,bet_value:t.bet_value,bet_type:t.bet_type,bet_position:"home"==t.type?0:1})}function u(e){if(e.bet_position>=80&&e.bet_position<=154)return e.bet_position-79;switch(e.bet_position){case 30:return"Tài";case 31:return"Xỉu";case 33:return"Lẻ";case 34:return"Chẳn";case 43:return"Tài 37.5/Lẻ";case 45:return"Tài 37.5/Chẳn";case 44:return"Xỉu 37.5/Lẻ";case 46:return"Xỉu 37.5/Chẳn";case 74:return"1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71";case 75:return"2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 67, 72";case 76:return"3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 63, 68, 73";case 77:return"4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64, 69, 74";case 78:return"5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75";case 48:return"1~5";case 49:return"6~10";case 50:return"11~15";case 51:return"16~20";case 52:return"21~25";case 53:return"26~30";case 54:return"31~35";case 55:return"36~40";case 56:return"41~45";case 57:return"46~50";case 58:return"51~55";case 59:return"56~60";case 60:return"61~65";case 61:return"66~70";case 62:return"71~75";case 64:return"1~15";case 65:return"16~30";case 66:return"31~45";case 67:return"45~60";case 68:return"61~75";case 70:return"1~25";case 71:return"26~50";case 72:return"51~75"}}function l(t,n){e.mulBets.findIndex(function(e){return e.odd_id==t&&(e.event=n,e.bet_value=m(n,e),!0)})}function c(t){e.settings.type=t,"single"==t?(setTimeout(function(e){$("#BPstake").focus()},800),a()):setTimeout(function(e){$("#mulStake").focus()},800)}function f(t,n){e.data.bongdamin=n.bongdamin,e.data.bongdamax=n.bongdamax}function d(){t.getEventById(e.model.odd_id).then(function(t){e.model.event=t,e.model.odd=e.model.event[e.model.bet_type],e.oldValue=e.model.bet_value,e.model.bet_value=m(e.model.event,e.model),h()})}function p(){e.mulBets.map(function(n){t.getEventById(n.odd_id).then(function(t){b(e,function(){l(t.odd_id,t)})})})}function h(){e.model.bet_value&&""!=e.model.bet_value?e.oldValue&&e.oldValue!=e.model.bet_value&&(e.error_message="Tỷ lệ cược đã thay đổi từ <em>"+e.oldValue+"</em> Thành <em>"+e.model.bet_value+"</em>"):e.error_message="Tỷ lệ cược đang được cập nhật!"}function m(e,t){var n=e[t.bet_type];return["ft_hdp","hf_hdp","hf_1x2","ft_1x2"].indexOf(t.bet_type)>-1?0==t.bet_position?n.home_od:1==t.bet_position?n.away_od:n.draw_od:0==t.bet_position?n.over_od:n.under_od}function g(){e.settings.auto?run():x&&clearInterval(x)}function v(e){return["ft_ou","hf_ou"].indexOf(e.bet_type)>-1?"home"==e.type?"Tài":"Xỉu":e.event[e.type]}function y(t){e.settings.show=!1,x&&clearInterval(x)}function b(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}console.log("render"),e.settings={show:!1,auto:!0,type:"single"};var w=!1;e.data={},e.model={},e.mulModel={},e.mulBets=[],e.number=10;var x,E=10;e.$on("EVENT_BET",r),e.$on("EVENT_UPDATE_MIN_MAX",f),e.$on("CANCEL_BOX_BET",y),e.onChange=g,e.onRefresh=d,e.onDelete=o,e.backToHome=y,e.switchBettingMode=c,bindEvent(window,"message",function(t){var n=JSON.parse(t.data);"EVENT_BET"==n.name&&b(e,function(e){r("EVENT_BET",n.data)})}),e.betMulSubmit=function(){if(e.mul_error_message="",!e.mulModel.bet_amount||e.mulModel.bet_amount<=0)return e.mul_error_message="Bạn chưa nhập tiền cược!";if(e.mulModel.bet_amount<e.data.bongdamin)return e.mulModel.bet_amount=e.data.bongdamin,void(e.mul_error_message="Tiền cược của bạn thấp hơn mức cược tối thiểu!");if(e.mulModel.bet_amount>e.data.bongdamax)return e.mulModel.bet_amount=e.data.bongdamax,void(e.mul_error_message="Tiền cược của bạn cao hơn mức cược tối đa!");var r=confirm("Bạn có chắc chắn muốn xử lý đặt cược không?");if(r&&!w){w=!0;var i={bet_amount:e.mulModel.bet_amount,bets:e.mulBets.map(function(e){return{odd_id:e.odd_id,bet_value:e.bet_value,bet_position:e.bet_position,bet_type:e.bet_type}})};t.createGroup(i).then(function(t){y(),e.mulModel={},e.mulBets=[],"runing"==t.status&&alert("Đặt cược thành công. \nMã cược: "+t.id),n.$broadcast("EVENT_BET_SUCCESS",t),n.$broadcast("EVENT_ACCOUNT_REFRESH"),w=!1})["catch"](function(e){w=!1,p(),alert(e.data&&e.data.msg||"Có lỗi xảy ra, làm ơn reload page")})}},e.betSubmit=function(){if(!e.model.bet_amount||e.model.bet_amount<=0)return e.error_message="Bạn chưa nhập tiền cược!";if(e.model.bet_amount<e.data.bongdamin)return e.model.bet_amount=e.data.bongdamin,void(e.error_message="Tiền cược của bạn thấp hơn mức cược tối thiểu!");if(e.model.bet_amount>e.data.bongdamax)return e.model.bet_amount=e.data.bongdamax,void(e.error_message="Tiền cược của bạn cao hơn mức cược tối đa!");e.model.bet_value&&""!=e.model.bet_value||(e.error_message="Tỷ lệ cược đang được cập nhật!");var r=confirm("Bạn có chắc chắn muốn xử lý đặt cược không?");r&&(w||(w=!0,t.createBet(e.model).then(function(t){y(),e.model={},"runing"==t.status&&alert("Đặt cược thành công. \nMã cược: "+t.id),n.$broadcast("EVENT_BET_SUCCESS",t),n.$broadcast("EVENT_ACCOUNT_REFRESH"),w=!1})["catch"](function(t){w=!1,t&&t.data&&"ODD_CHANGED"==t.data.code?(b(e,function(){e.model.bet_value=t.data.data.newVal}),t.data.data.newVal?alert("Tỉ lệ thay đổi từ "+t.data.data.oldVal+" đến "+t.data.data.newVal):alert("Tỷ lệ cược đang được cập nhật!")):alert(t.data&&t.data.msg||"Có lỗi xảy ra, làm ơn reload page")})))}}]}}]),angular.module("todoApp").directive("mulBetItem",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",scope:{item:"<",onDelete:"<"},template:e.get("elements/left-bet/mul-bet-item.html"),controller:["$scope","$rootScope",function(e,t){e.onDeleteItem=function(){e.onDelete&&e.onDelete(e.item.odd_id)}}]}}]),angular.module("todoApp").directive("eventItem",["$templateCache",function(e){return{restrict:"EA",scope:{type:"@",event:"<",leagueName:"<"},template:e.get("elements/event-item/event-item.html"),controller:["$scope","$rootScope",function(e,t){e.odd_kind=top.ODD_TYPE,e.onBet=function(t,n,r,i){var o={TYPE:top.TYPE,event:e.event,league_name:e.leagueName,bet_type:n,type:r,bet_value:i,odd_type:t};window.parent.postMessage(JSON.stringify({data:o,name:"EVENT_BET",to:"leftFrame"}),"*")}}],link:function(e,t,n){}}}]),angular.module("todoApp").directive("leftBetItem",["$templateCache",function(e){return{restrict:"EA",scope:{value:"<"},template:e.get("elements/left-bet-wait/left-bet-item.html"),controller:["$scope","$rootScope",function(e,t){}]}}]),angular.module("todoApp").directive("leftBetWait",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",template:e.get("elements/left-bet-wait/left-bet-wait.html"),controller:["$scope","$rootScope",function(e,n){function r(t,n){n&&"pending"==n.status?e.waitSetting.type="waitingBets":e.waitSetting.type="betList",e.loadBets()}function i(){e.waitSetting.loading||(e.waitSetting.bet_number=0,l&&clearInterval(l),e.waitSetting.loading=!0,t.getInplayBets().then(function(t){var r=[],i=[],s=[],u=[];t.map(function(e){"pending"==e.status?(c.indexOf(e.id)==-1&&c.push(e.id),r.push(e)):"runing"==e.status?(i.push(e),c.indexOf(e.id)>-1&&a(e.id)):(c.indexOf(e.id)>-1&&(n.$broadcast("EVENT_ACCOUNT_REFRESH"),u.push(e)),s.push(e))}),e.waitData.waitingBets=r,e.waitData.betList=i,e.waitData.voidTicket=s,e.waitData.voidTicketCaches=u,e.waitSetting.loading=!1,r.length>0&&o()}))}function o(){e.waitSetting.type="waitingBets",e.waitSetting.bet_number=5,l&&clearInterval(l),l=setInterval(function(){e.$apply(function(){e.waitSetting.bet_number--,0==e.waitSetting.bet_number&&(clearInterval(l),e.loadBets())})},800)}function a(e){c=c.filter(function(t,n){return e!==t}),n.$broadcast("EVENT_ACCOUNT_REFRESH")}function s(t){c=c.filter(function(t,n){return e.waitData.voidTicketCaches.findIndex(function(e){return e.id==t})==-1}),e.waitData.voidTicketCaches=[],e.waitSetting.type=t}function u(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}var l;e.loadBets=i,e.openBetList=s,e.waitSetting={type:"betList",bet_number:5,loading:!1},e.waitData={betList:[],waitingBets:[],voidTicket:[],voidTicketCaches:[]};var c=[];e.loadBets(),e.$on("EVENT_BET_SUCCESS",r),bindEvent(window,"message",function(t){var i=JSON.parse(t.data);"EVENT_BET_SUCCESS"==i.name&&u(e,function(e){n.$broadcast("EVENT_MENU_SHOW",{account:"mini",sports:!1}),n.$broadcast("CANCEL_BOX_BET"),r()})})}]}}]),angular.module("todoApp").directive("leftSports",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",scope:{show:"="},template:e.get("elements/left-sports/left-sports.html"),controller:["$scope","$rootScope",function(e,t){e.onMenuSports=function(){e.show=!e.show}}]}}]),angular.module("todoApp").directive("numbeGames",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",scope:{list:"="},template:e.get("elements/numbe-games/index.html"),controller:["$scope","$rootScope",function(e,t){e.checkOn=function(e,t){return e.ball_numbers&&e.ball_numbers.indexOf(t)>=0},e.betBingo=function(e,t,n,r,i,o,a){var s={TYPE:top.TYPE,event:e,step:e.step,odd_type:o,bet_type:t,game_type:"number_game",bet_value:r,bet_position:n,ss:a};window.parent.postMessage(JSON.stringify({data:s,name:"EVENT_BET",to:"leftFrame"}),"*")}}]}}]),angular.module("todoApp").directive("oddItem",["$templateCache",function(e){return{restrict:"EA",scope:{type:"@",odd:"<",key:"<",event:"<",leagueName:"<"},template:e.get("elements/odd-item/odd-item.html"),controller:["$scope","$rootScope",function(e,t){e.odd_kind=top.ODD_TYPE,e.onBet=function(n,r,i,o){if("ft_1x2"!=r&&"hf_1x2"!=r||"multiple"!=top.TYPE){var a={TYPE:top.TYPE,event:$.extend({},e.odd,e.event,{odds:null,$$hashKey:null}),league_name:e.leagueName,bet_type:r,type:i,bet_value:o,odd_type:n};t.$broadcast("EVENT_BET",a)}},e.openMatch=function(t){window.open("http://ls.1266366.com/index.aspx?clientid=846&flag=lcw&language=en&clientmatchid="+e.odd.event_id+"&t=S")},e.openLive=function(t){function n(e,t,n,r){var i=screen.width/2-n/2,o=screen.height/2-r/2;return window.open(e,t,"toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width="+n+", height="+r+", top="+o+", left="+i)}n("/live/"+e.event.event_id+"/"+e.event.live_id,e.event.away+" - "+e.event.home,800,500)},e.openStatic=function(t){window.open("http://ls.1266366.com/index.aspx?clientid=846&flag=ls&language=vi&clientmatchid="+e.odd.event_id)}}],link:function(e,t,n){}}}]),angular.module("todoApp").directive("popupLeague",["$templateCache","leaguesAPI",function(e,t){return{restrict:"EA",scope:{show:"=",leagueNumber:"=",leagueSelectNumber:"="},template:e.get("elements/popup-league/popup-league.html"),controller:["$scope","$rootScope",function(e,n){function r(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}t.getLeagues().then(function(t){r(e,function(){e.leagueNumber=t.length;var n=[],r={};t.forEach(function(e,t){t%2==0?(r={},r.left=e):(r.right=e,n.push(r))}),e.leagues=n})}),e.closeLeague=function(t){e.show=!1}}]}}]),angular.module("todoApp").directive("refreshItem",["$templateCache",function(e){return{restrict:"EA",scope:{number:"@",type:"@",onRefresh:"<",loading:"<"},template:e.get("elements/refresh-item/refresh-item.html"),controller:["$scope","$rootScope",function(e,t){function n(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}function r(){"inplay"!=e.type&&(e.number=i,o=setInterval(function(){n(e,function(){e.number--,0==e.number&&(console.log("DOne"),e.number=i,e.onRefresh&&e.onRefresh())})},1e3))}console.log("init");var i=e.number,o=null;r(),e.$watch("loading",function(t,n){t?(e.number="",o&&clearInterval(o)):r()}),e.$on("$destroy",function(){o&&clearInterval(o)}),e.reload=function(t){e.loading||e.onRefresh&&e.onRefresh()}}],link:function(e,t,n){}}}]),angular.module("todoApp").directive("timeItem",["$templateCache",function(e){return{restrict:"EA",scope:{time:"="},template:e.get("elements/time-item/index.html"),controller:["$scope","$rootScope",function(e,t){function n(){i&&clearInterval(i),i=setInterval(function(){return e.time<=0?(e.time=0,void clearInterval(i)):void r(e,function(){e.time--})},1e3)}function r(e,t){e.$$phase||e.$root.$$phase?t():e.$apply(t)}e.$watch("time",function(e){n()});var i=null}]}}]);