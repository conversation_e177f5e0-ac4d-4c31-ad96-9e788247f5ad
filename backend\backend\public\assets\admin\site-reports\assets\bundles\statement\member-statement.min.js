"use strict";var AlphaReports=function(){function n(n,t){var i=this;this.format={monthFormat:"MMMM - yyyy",shortDateWithSlash_DatePicker:"MM/DD/YYYY",shortDateWithSlash_MonthPicker:"MM/dd/yyyy"};this.sort={none:0,ascending:1,descending:-1};this.keyCode={enter:13};this.numberDisplayedProductsOnFilter=1;this.maxTimesToShowTooltip=5;this.screenSmallThan768=function(){var n=screen.width||i.$(window).width();return n<768};this.screenLandscapeMode=function(){return screen.width>screen.height||i.$(window).width()>i.$(window).height()};this.splitCell=function(n,t,r){t===void 0&&(t="");r===void 0&&(r="bold");var u=i.$,f=i.$("."+n+"-combined");f.each(function(n){for(var e=u(this).find("li"),o=e.length,s="",i=0,h=o-1;i<o;i++)s+='<td class="hidden-stack-mode '+t+(i===h?" "+r:"")+'">'+u(e[i]).html()+"<\/td>";u(f[n]).before(s)})};this.combineConsecutiveCells=function(n,t,r,u){if(u===void 0&&(u=1),!(t<=1)){var f=i.$;n.each(function(i){for(var o='<td class="visible-stack-mode combinedcell '+r+'" colspan="'+u+'">                    <ul>',e=f(n[i]),s=!1,h=0;h<t;h++)o+="<li>"+e.html()+"<\/li>",e.next().length?e=e.next():s=!0;o+="<\/ul>                        <\/td>";s?e.after(o):e.prev().after(o)})}};this.localStorage={get:function(n){return i.store.enabled?i.store.get(n):i.$.cookie(n)},set:function(n,t){i.store.enabled?i.store.set(n,t):i.$.cookie(n,t,i.localStorage.getCookieExpireOptions())},getCookieExpireOptions:function(){var n={expires:365,path:window.location.pathname};return i.isIe()&&(n={expires:365}),n}};this.isIe=function(){var n=window.navigator.userAgent,t=n.indexOf("MSIE ");return t>0||!!navigator.userAgent.match(/Trident.*rv\:11\./)};this.replaceQueryStringValue=function(n,t,i){if(typeof n=="undefined"||n===null)return"";var r=new RegExp("([?&])"+t+"=[^&]*","i");return r.test(n)?n.replace(r,"$1"+t+"="+i):n+(n.indexOf("?")>=0?"&":"?")+t+"="+i};this.correctEvenRowClass=function(n){var r=i.$,t=!1;n.find("tbody > tr").each(function(){var n=r(this);n.hasClass("even-row")&&!t?n.removeClass("even-row"):!n.hasClass("even-row")&&t&&n.addClass("even-row");t=!t})};this.convertAttributeToBool=function(n){return!!n&&n.toString().toLowerCase()==="true"};this.formatNumber=function(n){return n.toLocaleString("en-US")};this.formatCurrency=function(n){return n.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")};this.$=n;this.store=t}return n.prototype.getFormData=function(n){var i=n.serializeArray(),t={};return $.map(i,function(n){t[n.name]=n.value}),t},n}(),alphaReports=new AlphaReports($,store),BaseReport=function(){function n(n,t){var i=this;this.onClickIconFilter=function(){var t=i.$reportForm,n=t.hasClass("hide");n?(i.$pageTitle.removeClass("hide-filter"),i.$reportForm.removeClass("hide")):(i.$pageTitle.addClass("hide-filter"),i.$reportForm.addClass("hide"));i.replaceHrefParam("IsFilterVisible",n)};this.replaceHrefParam=function(n,t){i.linkBuilderComponent.updateParam(n,t)};this.initHelp=function(){i.$loadLegendURL=i.$("#LoadLegendURL");i.$iconInformation=i.$("#icon-information");i.$iconInformationContainer=i.$("#icon-information-container");i.loadLegend();i.$iconInformation.on("click",i.showLegend)};this.loadLegend=function(){i.$.ajax({method:"GET",url:i.$loadLegendURL.val(),cache:!1,data:{},success:i.renderLegend,complete:i.$.unblockUI})};this.renderLegend=function(n){i.$reportLegend&&i.$reportLegend.remove();i.$("body").append(n);i.$.trim(n)===""?i.$iconInformationContainer.remove():i.$iconInformationContainer.removeClass("hide");i.$reportLegend=i.$("#report-legend")};this.showLegend=function(){i.$reportLegend.modal({show:!0,backdrop:!1});var n=i.$iconInformation.position();i.$reportLegend.css({bottom:"auto",top:n.top+60})};this.$=n;this.linkBuilderComponent=t}return n.prototype.cacheElements=function(){this.$reportForm=this.$("#report-form");this.$pageTitle=this.$(".page-title");this.$tableReport=this.$("#tbl-report");this.$isFilterVisible=this.$("#IsFilterVisible");this.$exportExcelForm=this.$("#export-excel-form")},n.prototype.registerEvents=function(){this.$(".icon-filter").on("click",this.onClickIconFilter)},n}(),DateRangePickerOption,DateRangePickerComponent,ClientPagerOption,ClientPagerComponent;DateRangePickerOption=function(){function n(n,t,i,r){n===void 0&&(n="daterange-picker");t===void 0&&(t="report-form");i===void 0&&(i=null);r===void 0&&(r=null);this.selectorId=n;this.reportFormId=t;this.onChange=i;this.onBeforeQuickChooseChange=r;this.selectorId=n;this.reportFormId=t;this.onChange=i;this.onBeforeQuickChooseChange=r}return n}();DateRangePickerComponent=function(){function n(n){var t=this;this.options=new DateRangePickerOption;this.quickChooseType={specificDates:7};this.isCompleteInitDateRangePicker=!1;this.render=function(n){n===void 0&&(n=new DateRangePickerOption);t.options=t.$.extend({},t.options,n);var i=t.$("#"+t.options.selectorId),u=i.data("currentdate"),f=i.data("mindate"),r=i.data("maxdate");return t.$reportForm=t.$("#"+t.options.reportFormId),i.nexDatePicker({datePickerFormat:{dateFormat:alphaReports.format.shortDateWithSlash_DatePicker},selectedItem:i.data("selecteddaterange"),minDate:new Date(f),maxDate:r===""?null:r,currentDate:new Date(u),onSelectDatePicker:function(){t.options.onChange&&t.options.onChange()},onQuickChooseChange:function(n){t.options.onBeforeQuickChooseChange&&t.options.onBeforeQuickChooseChange();t.isCompleteInitDateRangePicker&&n!==t.quickChooseType.specificDates&&t.$reportForm.submit()}}),t.isCompleteInitDateRangePicker=!0,i};this.$=n}return n}();ClientPagerOption=function(){function n(n,t,i,r,u,f){n===void 0&&(n=null);t===void 0&&(t="report-pager");i===void 0&&(i="report-form");r===void 0&&(r="PageIndex");u===void 0&&(u="PageSize");f===void 0&&(f="tbl-report");this.onSelectPage=n;this.pagerId=t;this.formId=i;this.pageIndexId=r;this.pageSizeId=u;this.tableReportId=f}return n}();ClientPagerComponent=function(){function n(n){var t=this;this.pageList=[1e3,2e3,3e3,5e3];this.smallPageList=[10,50,100,500];this.options=new ClientPagerOption;this.render=function(n){n===void 0&&(n=new ClientPagerOption);var r=t.$.extend({},t.options,n),i=t.$("#"+r.pagerId);return t.$reportForm=t.$("#"+t.options.formId),t.$bodyRows=t.$("#"+t.options.tableReportId+" > tbody > tr"),t.totalRecords=parseInt(i.data("totalrecords"),0),t.pageSize=parseInt(i.data("pagesize"),0),t.pageIndex=parseInt(i.data("pageindex"),0),t.$pageIndex=t.$('<input type="hidden" id="'+r.pageIndexId+'" name="'+r.pageIndexId+'" value="'+t.pageIndex+'" />'),t.$pageSize=t.$('<input type="hidden" id="'+r.pageSizeId+'" name="'+r.pageSizeId+'" value="'+t.pageSize+'" />'),t.$reportForm.append(t.$pageIndex).append(t.$pageSize),i.pagination({pageList:t.pageList,total:t.totalRecords,pageSize:t.pageSize,pageNumber:t.pageIndex,showRefresh:!1,onSelectPage:t.onSelectPage,beforePageText:i.data("lablepage"),afterPageText:i.data("labelof")+" {pages}",displayMsg:i.data("labeldisplayitems")}),t.showBodyRows(),i.removeClass("hide"),i};this.onSelectPage=function(n,i){t.updateHiddenFields(n,i);t.showBodyRows()};this.updateHiddenFields=function(n,i){t.$pageIndex.val(n);t.$pageSize.val(i);t.pageSize=i;t.pageIndex=n};this.showBodyRows=function(){var i,r,n;if(t.totalRecords)for(i=t.pageSize*(t.pageIndex-1),r=i+t.pageSize,t.$bodyRows.addClass("hide"),n=i;n<r&&n<t.totalRecords;n++)t.$(t.$bodyRows[n]).removeClass("hide")};this.$=n}return n}();"use strict";var SingleSelectionDropdownOption=function(){function n(n,t,i){n===void 0&&(n="dropdown-single");t===void 0&&(t="UserSelectedId");i===void 0&&(i=null);this.dropdownId=n;this.inputId=t;this.onChange=i}return n}(),SingleSelectionDropdownComponent=function(){function n(n){var t=this;this.options=new SingleSelectionDropdownOption;this.render=function(n){var i,r,u;n===void 0&&(n=new SingleSelectionDropdownOption);t.options=t.$.extend({},t.options,n);i=t.$("#"+t.options.dropdownId);t.$dropdown=i;r=t.$("#"+t.options.inputId);t.$hiddenInput=r;u=t;t.selectedValue=u.getSelectedItem();t.$dropdown.multiselect({multiple:!1,maxHeight:300,numberDisplayed:1,includeSelectAllOption:!1,onChange:t.onchange})};this.getSelectedItem=function(){var n=[],i=t.$dropdown.find("option:selected"),r=t.$;return i.each(function(){n.push([r(this).val()])}),n.length?n[0].toString():""};this.updateSelectedValue=function(){t.$hiddenInput.val(t.selectedValue)};this.onchange=function(){t.selectedValue=t.getSelectedItem();t.options.onChange&&t.options.onChange()};this.$=n}return n}(),LinkBuilderOption=function(){function n(n){n===void 0&&(n=["a.breadcrumb-link,a.downline-link"]);this.linkClasses=n}return n}(),LinkBuilderComponent=function(){function n(n){var t=this;this.options=new LinkBuilderOption;this.render=function(n){n===void 0&&(n=new LinkBuilderOption);t.options=t.$.extend({},t.options,n)};this.updateParam=function(n,i){var r=t;t.options.linkClasses.forEach(function(u){var f=t.$(u);f.each(function(t,u){var e=r.$(u),f=e.attr("href");f=alphaReports.replaceQueryStringValue(f,n,i);e.attr("href",f)})})};this.$=n}return n}(),__extends=this&&this.__extends||function(){var n=function(t,i){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])},n(t,i)};return function(t,i){function r(){this.constructor=t}n(t,i);t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}(),StatementView=function(n){function t(t,i,r,u,f){var e=n.call(this,t,f)||this;return e.onClickIconFilter=function(){var n=e.$reportForm,t=n.hasClass("hide");t?(e.$pageTitle.removeClass("hide-filter"),e.$reportForm.removeClass("hide")):(e.$pageTitle.addClass("hide-filter"),e.$reportForm.addClass("hide"))},e.onExportExcel=function(){e.$exportExcelForm.submit()},e.onSubmitReport=function(){e.$isFilterVisible.val(String(alphaReports.screenSmallThan768()?!1:!e.$reportForm.is(":hidden")));e.$.blockUI()},e.loadStatementDetail=function(n){var t={TransferDate:n,CustId:e.$custId.val(),CustName:e.$custName.val()},i=e.$statementDetailsURL.val()+"?"+e.$.param(t);e.topWindow.popupManager.open(i,null,800)},e.$=t,e.dateRangePickerComponent=i,e.pagerComponent=r,e.topWindow=u,e}return __extends(t,n),t.prototype.init=function(){this.cacheElements();this.dateRangePickerComponent.render();this.pagerComponent.pageList=this.pagerComponent.smallPageList;this.pagerComponent.render();this.$tableReport.stickyTableHeaders();this.registerEvents();this.initHelp()},t.prototype.registerEvents=function(){this.$(".icon-filter").on("click",this.onClickIconFilter);this.$("#icon-excel").on("click",this.onExportExcel);this.$reportForm.submit(this.onSubmitReport);var n=this;this.$(".column-transfer-amount").on("click",function(){n.loadStatementDetail(n.$(this).data("transfer-date"))})},t.prototype.cacheElements=function(){n.prototype.cacheElements.call(this);this.$statementDetailsURL=this.$("#StatementDetailsURL");this.$custId=this.$("#CustId");this.$custName=this.$("#CustName")},t}(BaseReport),__extends=this&&this.__extends||function(){var n=function(t,i){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])},n(t,i)};return function(t,i){function r(){this.constructor=t}n(t,i);t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}(),MemberStatementView=function(n){function t(t,i,r,u,f,e,o){var s=n.call(this,t,i,r,u,o)||this;return s.transactionType={WinLoss:1,VoidCancelled:2,Outstanding:3,GrandPrize:4},s.changeReportType=function(){var n=s.reportTypeComponent.$dropdown.find("option:selected").data("url");s.windowLocation.href=n},s.viewBetList=function(n,t){var i={custid:s.custId,winlossdate:n.data("transfer-date"),transtype:t,custname:s.custName},r=s.betListUrl+"?"+jQuery.param(i);s.topWindow.popupManager.open(r,s.betListTitle,900)},s.reportTypeComponent=f,s.windowLocation=e,s}return __extends(t,n),t.prototype.init=function(){this.$.blockUI();n.prototype.init.call(this);var t=new SingleSelectionDropdownOption("dropdown-report-type","ReportType",this.changeReportType);this.reportTypeComponent.render(t);this.betListUrl=this.$("#BetListUrl").val();this.betListTitle=this.$("#BetListTitle").val();this.custId=this.$("#CustId").val();this.custName=this.$("#CustName").val();this.$.unblockUI()},t.prototype.registerEvents=function(){n.prototype.registerEvents.call(this);var t=this;this.$tableReport.find(".col-void-amount").click(function(){t.viewBetList(t.$(this),t.transactionType.VoidCancelled)});this.$tableReport.find(".col-outstanding").click(function(){t.viewBetList(t.$(this),t.transactionType.Outstanding)});this.$tableReport.find(".col-win-loss").click(function(){t.viewBetList(t.$(this),t.transactionType.WinLoss)});this.$tableReport.find(".col-grand-prize").click(function(){t.viewBetList(t.$(this),t.transactionType.GrandPrize)})},t}(StatementView);$(function(){var n=new MemberStatementView($,new DateRangePickerComponent($),new ClientPagerComponent($),window.top,new SingleSelectionDropdownComponent($),window.location,new LinkBuilderComponent($));n.init()});