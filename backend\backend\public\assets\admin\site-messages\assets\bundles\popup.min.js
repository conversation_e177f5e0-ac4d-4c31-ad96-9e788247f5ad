var Popup=function(){function n(n,t){var i=this;this.domElements={announcementModal:null,announcementIframe:null,announcementContent:null};this.init=function(){(i.cacheElements(),i.hasEmptyAnnouncementData())||i.displayAnnouncementModal()};this.cacheElements=function(){i.domElements.announcementModal=i.topWindow.$("#announcement-modal");i.domElements.announcementIframe=i.topWindow.$("#annoucement-iframe");i.domElements.announcementContent=i.$("#popupContent")};this.hasEmptyAnnouncementData=function(){return i.domElements.announcementContent.length==0};this.displayAnnouncementModal=function(){i.calculateIframeHeight();i.domElements.announcementModal.modal({show:!0,backdrop:!1}).tinyDraggable({exclude:"button.icon.icon-close.close, .modal-body, p, a"})};this.calculateIframeHeight=function(){var n=i.domElements.announcementIframe;n[0].onload=function(){var t=n.contents().find("body").height(),i=t+10;n.css("height",i)}};this.$=n;this.topWindow=t}return n}();$(document).ready(function(){var n=new Popup($,window.top);n.init()});