﻿@import url("/assets/styles/base/icon-fonts.css");#diverrmsg{width:100%;display:none;margin-bottom:8px;-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}.msgerr:before,.EnrichErrmsg:before,.poup-sc .msgerr:before,.msgscc:before,.EnrichSuccmsg:before,.EnrichWarmsg:before{font-family:Iconalpha;font-size:24px;float:left;margin-right:4px;}.msgerr,.EnrichErrmsg,.msgscc,.EnrichSuccmsg,.EnrichWarmsg{line-height:20px;margin-top:8px;padding:8px;text-align:left;vertical-align:middle;position:relative;-webkit-border-radius:2px;-khtml-border-radius:2px;-moz-border-radius:2px;-o-border-radius:2px;border-radius:2px;-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}.msgerr,.EnrichErrmsg{background-color:#fadddd;border:1px solid #ef2d2d;color:#fe0000;}.msgerr:before,.EnrichErrmsg:before{content:'';}.poup-sc .msgerr:before{content:'';}.msgscc,.EnrichSuccmsg{background-color:#eefbe9;border:1px solid #1db51a;color:#52a220;}.msgscc:before,.EnrichSuccmsg:before{content:'';}.EnrichWarmsg{color:#7e3d00;}.EnrichWarmsg:before{content:'';}.EnrichScc,.Enrich{background-color:#fdfbec;border:1px solid #d9d9d9;padding:0;text-align:left;}.Enrich{-webkit-border-radius:2px;-khtml-border-radius:2px;-moz-border-radius:2px;-o-border-radius:2px;border-radius:2px;-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}.Enrich .EnrichWarmsg:before,.Enrich .EnrichSuccmsg:before,.Enrich .EnrichErrmsg:before,.msgscc .EnrichWarmsg:before,.msgscc .EnrichSuccmsg:before,.msgscc .EnrichErrmsg:before,.msgerr .EnrichWarmsg:before,.msgerr .EnrichSuccmsg:before,.msgerr .EnrichErrmsg:before{font-size:20px;left:16px;top:4px;margin-right:0;position:absolute;}.Enrich .EnrichWarmsg,.Enrich .EnrichSuccmsg,.Enrich .EnrichErrmsg,.msgscc .EnrichWarmsg,.msgscc .EnrichSuccmsg,.msgscc .EnrichErrmsg,.msgerr .EnrichWarmsg,.msgerr .EnrichSuccmsg,.msgerr .EnrichErrmsg{border:none;background:none;padding:4px 8px 4px 42px;}.Enrich.container,.msgscc.container,.msgerr.container{padding:0;}.Enrich.container:before,.msgscc.container:before,.msgerr.container:before{display:none;}.EnrichUserName{color:#333;font-weight:bold;height:28px;line-height:28px;padding:0 20px;}