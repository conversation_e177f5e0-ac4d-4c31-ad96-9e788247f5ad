{"name": "boom", "description": "HTTP-friendly error objects", "version": "2.10.1", "repository": "git://github.com/hapijs/boom", "main": "lib/index.js", "keywords": ["error", "http"], "engines": {"node": ">=0.10.40"}, "dependencies": {"hoek": "2.x.x"}, "devDependencies": {"code": "1.x.x", "lab": "7.x.x"}, "scripts": {"test": "lab -a code -t 100 -L", "test-cov-html": "lab -a code -r html -o coverage.html -L"}, "license": "BSD-3-<PERSON><PERSON>"}