{"name": "kue", "version": "0.11.6", "description": "Feature rich priority job queue backed by redis", "homepage": "http://automattic.github.io/kue/", "keywords": ["job", "queue", "worker", "redis"], "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/Automattic/kue.git"}, "bugs": {"url": "https://github.com/Automattic/kue/issues"}, "dependencies": {"body-parser": "^1.12.2", "express": "^4.12.2", "lodash": "^4.0.0", "nib": "~1.1.2", "node-redis-warlock": "~0.2.0", "pug": "^2.0.0-beta3", "redis": "~2.6.0-2", "stylus": "~0.54.5", "yargs": "^4.0.0"}, "devDependencies": {"async": "^1.4.2", "chai": "^3.3.0", "coffee-script": "~1.10.0", "mocha": "^2.3.3", "should": "^3.1.0", "sinon": "^1.17.2", "supertest": "^1.1.0"}, "main": "index", "bin": {"kue-dashboard": "bin/kue-dashboard"}, "scripts": {"test": "make test-all"}, "optionalDependencies": {"reds": "^0.2.5"}}