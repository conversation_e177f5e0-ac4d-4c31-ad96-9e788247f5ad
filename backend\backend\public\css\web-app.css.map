{"version": 3, "sources": ["odd.less", "app.css", "app.less"], "names": [], "mappings": "AAEE;;EAEE,oCAAA;CCDH;ADMC;;EACC,oCAAA;CCHF;ADMC;;EACC,0BAAA;CCHF;ADXD;EAsBG,yBAAA;CCRF;ADdD;EA0BE,sBAAA;EACA,wBAAA;CCTD;ADaD;EACI,mBAAA;EACA,WAAA;EACA,WAAA;EACA,8BAAA;CCXH;ACnBD;;;EACC,iBAAA;CDuBA;ACpBD;EACC,gBAAA;CDsBA;ACnBD;EAEE,cAAA;CDoBD;ACdC;;;;EACC,oCAAA;CDmBF;ACfA;EACC,oCAAA;CDiBD;ACdA;EACC,oCAAA;CDgBD;ACbA;EACC,oCAAA;CDeD;ACNC;;;;;;;;EACC,oCAAA;CDeF;ACVD;EACC,gBAAA;CDYA;ACTD;EACC,cAAA;CDWA;ACRD;EACK,gBAAA;CDUJ", "file": "web-app.css", "sourcesContent": ["#UnderOver {\n\t.hover-yello{\n\t\t&:hover{\n\t\t\t.bgcpe, .live{\n\t\t\t\tbackground-color: #f5eeb8!important;\t\n\t\t\t}\n\t\t}\n\t}\n\t.bgcpe, .live {\n\t\t&:hover{\n\t\t\tbackground-color: #f5eeb8!important;\n\t\t}\n\t\t//background-color: #c6d4f1;\n\t\t&.inplay{\n\t\t\tbackground-color: rgb(255, 204, 188);\n\t\t}\n\t\t// &.liveligh {\n\t\t// \tbackground-color: #ffddd2 !important;\n\t\t// }\n\t}\n\t.FavOddsClass{\n\t\tspan{\n\t\t\tcolor: #B50000!important;\n\t\t}\n\t}\n\t.oddsTable tr:first-child td {\n\t\tborder-top-width: 0px;\n\t\tborder-top-style: solid;\n\t}\n}\n\n.sidebar .newsGaming {\n    position: absolute;\n    top: -40px;\n    z-index: 9;\n    border-top: 2px solid #7c7c7e;\n}", "#UnderOver .hover-yello:hover .bgcpe,\n#UnderOver .hover-yello:hover .live {\n  background-color: #f5eeb8!important;\n}\n#UnderOver .bgcpe:hover,\n#UnderOver .live:hover {\n  background-color: #f5eeb8!important;\n}\n#UnderOver .bgcpe.inplay,\n#UnderOver .live.inplay {\n  background-color: #ffccbc;\n}\n#UnderOver .FavOddsClass span {\n  color: #B50000!important;\n}\n#UnderOver .oddsTable tr:first-child td {\n  border-top-width: 0px;\n  border-top-style: solid;\n}\n.sidebar .newsGaming {\n  position: absolute;\n  top: -40px;\n  z-index: 9;\n  border-top: 2px solid #7c7c7e;\n}\nbody,\nlabel,\n.checkbox label {\n  font-weight: 300;\n}\n.pointer {\n  cursor: pointer;\n}\n.hide-span span {\n  display: none;\n}\n.BetInfo.bgcpelight:hover,\n.BetInfo.bgcpe:hover,\n.BetInfo.liveligh:hover,\n.BetInfo.Void:hover {\n  background-color: #f5eeb8!important;\n}\n.BetInfo.void-cache {\n  background-color: #f5eeb8!important;\n}\n.BetInfo.white {\n  background-color: #F8F8F8!important;\n}\n.BetInfo.red {\n  background-color: #FFCCBC!important;\n}\n.statement-detail .bgcpelight:hover,\n.statement .bgcpelight:hover,\n.statement-detail .bgcpe:hover,\n.statement .bgcpe:hover,\n.statement-detail .liveligh:hover,\n.statement .liveligh:hover,\n.statement-detail .Void:hover,\n.statement .Void:hover {\n  background-color: #f5eeb8!important;\n}\n.button {\n  cursor: pointer;\n}\n.hide {\n  display: none;\n}\n.multiple .BetInfo:first-child {\n  margin-top: 3px;\n}\n", "// @import \"bootstrap/bootstrap\";\n@import \"odd.less\";\n@btn-font-weight: 300;\n@font-family-sans-serif: \"Roboto\", Helvetica, Arial, sans-serif;\n\nbody, label, .checkbox label {\n\tfont-weight: 300;\n}\n\n.pointer{\n\tcursor: pointer;\n}\n\n.hide-span{\n\tspan{\n\t\tdisplay: none;\n\t}\n}\n\n.BetInfo{\n\t&.bgcpelight, &.bgcpe, &.liveligh, &.Void{\n\t\t&:hover{\n\t\t\tbackground-color: #f5eeb8!important;\n\t\t}\n\t}\n\n\t&.void-cache {\n\t\tbackground-color: #f5eeb8!important;\n\t}\n\n\t&.white {\n\t\tbackground-color: #F8F8F8!important;\t\n\t}\n\n\t&.red {\n\t\tbackground-color: #FFCCBC!important;\t\n\t}\n}\n\n\n\n\n.statement-detail, .statement {\n\t.bgcpelight, .bgcpe, .liveligh, .Void{\n\t\t&:hover{\n\t\t\tbackground-color: #f5eeb8!important;\n\t\t}\n\t}\t\n}\n\n.button{\n\tcursor: pointer;\n}\n\n.hide{\n\tdisplay: none;\n}\n\n.multiple .BetInfo:first-child {\n     margin-top: 3px; \n}"]}