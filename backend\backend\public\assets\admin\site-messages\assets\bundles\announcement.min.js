"use strict";var AlphaMessages=function(){function n(n){this.format={monthFormat:"MMMM - yyyy",shortDateWithSlash_DatePicker:"MM/DD/YYYY",shortDateWithSlash_MonthPicker:"MM/dd/yyyy"};this.sort={none:0,ascending:1,descending:-1};this.$=n}return n}(),alphaMessages=new AlphaMessages($),Announcement=function(){function n(n,t){var i=this;this.constraintFromTo=6;this.init=function(){i.registerEvents()};this.registerEvents=function(){var n=i;i.$(".nav-tabs > li").on("click",function(){n.$.blockUI();n.$(this).find("span").val("")});i.$(".btn-submit").on("click",function(){i.$.blockUI()});i.dateRangePickerComponent.render(new DateRangePickerOption("daterange-picker","report-form",i.constraintFromTo))};this.dateRangePickerComponent=t;this.$=n}return n}(),DateRangePickerOption,DateRangePickerComponent;$(function(){var n=new Announcement($,new DateRangePickerComponent($));n.init()});DateRangePickerOption=function(){function n(n,t,i,r){n===void 0&&(n="daterange-picker");t===void 0&&(t="report-form");i===void 0&&(i=6);r===void 0&&(r=null);this.selectorId=n;this.reportFormId=t;this.constraintFromTo=i;this.onChange=r;this.selectorId=n;this.reportFormId=t;this.onChange=r;this.constraintFromTo=i}return n}();DateRangePickerComponent=function(){function n(n){var t=this;this.options=new DateRangePickerOption;this.quickChooseType={specificDates:7};this.isCompleteInitDateRangePicker=!1;this.render=function(n){n===void 0&&(n=new DateRangePickerOption);t.options=t.$.extend({},t.options,n);var i=t.$("#"+t.options.selectorId),u=i.data("currentdate"),f=i.data("mindate"),r=i.data("maxdate");return t.$reportForm=t.$("#"+t.options.reportFormId),i.nexDatePicker({datePickerFormat:{dateFormat:alphaMessages.format.shortDateWithSlash_DatePicker},selectedItem:i.data("selecteddaterange"),minDate:new Date(f),maxDate:r===""?null:r,currentDate:new Date(u),onSelectDatePicker:function(){t.options.onChange&&t.options.onChange()},onQuickChooseChange:function(n){t.isCompleteInitDateRangePicker&&n!==t.quickChooseType.specificDates&&t.$reportForm.submit()},ConstraintFromTo:n.constraintFromTo}),t.isCompleteInitDateRangePicker=!0,i};this.$=n}return n}();