{"name": "caller-path", "version": "0.1.0", "description": "Get the path of the caller module", "license": "MIT", "repository": "sindresorhus/caller-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["caller", "calling", "module", "path", "parent", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^0.2.0"}, "devDependencies": {"mocha": "*"}}