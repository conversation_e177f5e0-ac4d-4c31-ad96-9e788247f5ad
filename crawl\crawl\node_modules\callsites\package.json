{"name": "callsites", "version": "0.2.0", "description": "Get callsites from the V8 stack trace API", "license": "MIT", "repository": "sindresorhus/callsites", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["callsites", "callsite", "v8", "stacktrace", "stack", "trace", "function", "file", "line", "debug"], "devDependencies": {"mocha": "*"}}