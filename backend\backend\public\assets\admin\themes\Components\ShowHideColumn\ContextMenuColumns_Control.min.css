﻿@import url("/assets/styles/base/icon-fonts.css");.none{display:none;}label{cursor:pointer;}#Popup{padding-right:0;}.padding{padding-left:4px;cursor:pointer;}input[type='checkbox']{position:relative;top:2px;}.divMenuPopup{background-color:#fff;color:#333;display:none;position:absolute;white-space:nowrap;border:1px solid #dfdfdf;padding:8px 0;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-moz-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);}.divMenuPopup a:hover{color:#045ace;text-decoration:none;padding-left:24px;display:block;}.divMenuPopup tr td{padding:6px 12px;}.divMenuPopup tr td:hover{background-color:#ddd;}#menuPopupAd{display:none;position:absolute;height:45px;}#menuPopupAd .bg{background-color:#fff;border:1px solid #cecece;padding:8px 30px 8px 8px;position:relative;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-moz-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);}#menuPopupAd .bg:after{font-family:'Iconalpha';font-size:25px;color:#da1624;content:'';position:absolute;top:2px;right:4px;}#menuPopupAd .normal{color:#333;line-height:20px;}