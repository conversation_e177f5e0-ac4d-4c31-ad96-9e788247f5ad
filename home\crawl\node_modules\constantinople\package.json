{"name": "constantinople", "version": "3.1.2", "main": "lib/index.js", "types": "lib/index.d.ts", "description": "Determine whether a JavaScript expression evaluates to a constant (using acorn)", "keywords": ["acorn", "ast", "tooling"], "dependencies": {"@types/babel-types": "^7.0.0", "@types/babylon": "^6.16.2", "babel-types": "^6.26.0", "babylon": "^6.18.0"}, "devDependencies": {"@types/node": "^9.4.4", "mocha": "*", "typescript": "^2.7.1"}, "scripts": {"prepublish": "npm run build", "build": "tsc", "pretest": "npm run build", "test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/constantinople.git"}, "author": "ForbesLindesay", "license": "MIT"}