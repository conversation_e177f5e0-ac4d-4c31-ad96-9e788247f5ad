/*! DataTables 1.10.21
 * \u00a92008-2020 SpryMedia Ltd - datatables.net/license
 */
(function(n){"use strict";typeof define=="function"&&define.amd?define(["jquery"],function(t){return n(t,window,document)}):typeof exports=="object"?module.exports=function(t,i){return t||(t=window),i||(i=typeof window!="undefined"?require("jquery"):require("jquery")(t)),n(i,t,t.document)}:n(jQuery,window,document)})(function(n,t,i,r){"use strict";function vt(t){var f="a aa ai ao as b fn i m o s ",i,r,u={};n.each(t,function(n){i=n.match(/^([^A-Z]+?)([A-Z])/);i&&f.indexOf(i[1]+" ")!==-1&&(r=n.replace(i[0],i[2].toLowerCase()),u[r]=n,i[1]==="o"&&vt(t[n]))});t._hungarianMap=u}function nt(t,i,u){t._hungarianMap||vt(t);var f;n.each(i,function(e){f=t._hungarianMap[e];f!==r&&(u||i[f]===r)&&(f.charAt(0)==="o"?(i[f]||(i[f]={}),n.extend(!0,i[f],i[e]),nt(t[f],i[f],u)):i[f]=i[e])})}function er(n){var i=u.defaults.oLanguage,r=i.sDecimal,f,t;r&&lu(r);n&&(f=n.sZeroRecords,!n.sEmptyTable&&f&&i.sEmptyTable==="No data available in table"&&k(n,n,"sZeroRecords","sEmptyTable"),!n.sLoadingRecords&&f&&i.sLoadingRecords==="Loading..."&&k(n,n,"sZeroRecords","sLoadingRecords"),n.sInfoThousands&&(n.sThousands=n.sInfoThousands),t=n.sDecimal,t&&r!==t&&lu(t))}function bu(n){var t,i,r;if(a(n,"ordering","bSort"),a(n,"orderMulti","bSortMulti"),a(n,"orderClasses","bSortClasses"),a(n,"orderCellsTop","bSortCellsTop"),a(n,"order","aaSorting"),a(n,"orderFixed","aaSortingFixed"),a(n,"paging","bPaginate"),a(n,"pagingType","sPaginationType"),a(n,"pageLength","iDisplayLength"),a(n,"searching","bFilter"),typeof n.sScrollX=="boolean"&&(n.sScrollX=n.sScrollX?"100%":""),typeof n.scrollX=="boolean"&&(n.scrollX=n.scrollX?"100%":""),t=n.aoSearchCols,t)for(i=0,r=t.length;i<r;i++)t[i]&&nt(u.models.oSearch,t[i])}function ku(t){a(t,"orderable","bSortable");a(t,"orderData","aDataSort");a(t,"orderSequence","asSorting");a(t,"orderDataType","sortDataType");var i=t.aDataSort;typeof i!="number"||n.isArray(i)||(t.aDataSort=[i])}function du(i){var r;if(!u.__browser){r={};u.__browser=r;var e=n("<div/>").css({position:"fixed",top:0,left:n(t).scrollLeft()*-1,height:1,width:1,overflow:"hidden"}).append(n("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(n("<div/>").css({width:"100%",height:10}))).appendTo("body"),f=e.children(),o=f.children();r.barWidth=f[0].offsetWidth-f[0].clientWidth;r.bScrollOversize=o[0].offsetWidth===100&&f[0].clientWidth!==100;r.bScrollbarLeft=Math.round(o.offset().left)!==1;r.bBounding=e[0].getBoundingClientRect().width?!0:!1;e.remove()}n.extend(i.oBrowser,u.__browser);i.oScroll.iBarWidth=u.__browser.barWidth}function gu(n,t,i,u,f,e){var o=u,s,h=!1;for(i!==r&&(s=i,h=!0);o!==f;)n.hasOwnProperty(o)&&(s=h?t(s,n[o],o,n):n[o],h=!0,o+=e);return s}function or(t,r){var f=u.defaults.column,e=t.aoColumns.length,s=n.extend({},u.models.oColumn,f,{nTh:r?r:i.createElement("th"),sTitle:f.sTitle?f.sTitle:r?r.innerHTML:"",aDataSort:f.aDataSort?f.aDataSort:[e],mData:f.mData?f.mData:e,idx:e}),o;t.aoColumns.push(s);o=t.aoPreSearchCols;o[e]=n.extend({},u.models.oSearch,o[e]);ei(t,e,n(r).data())}function ei(t,i,f){var e=t.aoColumns[i],o=t.oClasses,h=n(e.nTh),a,c,l;e.sWidthOrig||(e.sWidthOrig=h.attr("width")||null,a=(h.attr("style")||"").match(/width:\s*(\d+[pxem%]+)/),a&&(e.sWidthOrig=a[1]));f!==r&&f!==null&&(ku(f),nt(u.defaults.column,f,!0),f.mDataProp===r||f.mData||(f.mData=f.mDataProp),f.sType&&(e._sManualType=f.sType),f.className&&!f.sClass&&(f.sClass=f.className),f.sClass&&h.addClass(f.sClass),n.extend(e,f),k(e,f,"sWidth","sWidthOrig"),f.iDataSort!==r&&(e.aDataSort=[f.iDataSort]),k(e,f,"aDataSort"));var s=e.mData,p=et(s),y=e.mRender?et(e.mRender):null,v=function(n){return typeof n=="string"&&n.indexOf("@")!==-1};e._bAttrSrc=n.isPlainObject(s)&&(v(s.sort)||v(s.type)||v(s.filter));e._setter=null;e.fnGetData=function(n,t,i){var u=p(n,t,r,i);return y&&t?y(u,t,n,i):u};e.fnSetData=function(n,t,i){return rt(s)(n,t,i)};typeof s!="number"&&(t._rowReadObject=!0);t.oFeatures.bSort||(e.bSortable=!1,h.addClass(o.sSortableNone));c=n.inArray("asc",e.asSorting)!==-1;l=n.inArray("desc",e.asSorting)!==-1;e.bSortable&&(c||l)?c&&!l?(e.sSortingClass=o.sSortableAsc,e.sSortingClassJUI=o.sSortJUIAscAllowed):!c&&l?(e.sSortingClass=o.sSortableDesc,e.sSortingClassJUI=o.sSortJUIDescAllowed):(e.sSortingClass=o.sSortable,e.sSortingClassJUI=o.sSortJUI):(e.sSortingClass=o.sSortableNone,e.sSortingClassJUI="")}function yt(n){var i,t,u,r;if(n.oFeatures.bAutoWidth!==!1)for(i=n.aoColumns,kr(n),t=0,u=i.length;t<u;t++)i[t].nTh.style.width=i[t].sWidth;r=n.oScroll;(r.sY!==""||r.sX!=="")&&wi(n);o(n,null,"column-sizing",[n])}function pt(n,t){var i=oi(n,"bVisible");return typeof i[t]=="number"?i[t]:null}function wt(t,i){var u=oi(t,"bVisible"),r=n.inArray(i,u);return r!==-1?r:null}function ht(t){var i=0;return n.each(t.aoColumns,function(t,r){r.bVisible&&n(r.nTh).css("display")!=="none"&&i++}),i}function oi(t,i){var r=[];return n.map(t.aoColumns,function(n,t){n[i]&&r.push(t)}),r}function sr(n){for(var c=n.aoColumns,y=n.aoData,h=u.ext.type.detect,e,a,i,v,t,o,s,f=0,l=c.length;f<l;f++)if(t=c[f],s=[],!t.sType&&t._sManualType)t.sType=t._sManualType;else if(!t.sType){for(e=0,a=h.length;e<a;e++){for(i=0,v=y.length;i<v;i++){if(s[i]===r&&(s[i]=p(n,i,f,"type")),o=h[e](s[i],n),!o&&e!==h.length-1)break;if(o==="html")break}if(o){t.sType=o;break}}t.sType||(t.sType="string")}}function nf(t,i,u,f){var s,a,o,v,c,y,h,l=t.aoColumns,e;if(i)for(s=i.length-1;s>=0;s--)for(h=i[s],e=h.targets!==r?h.targets:h.aTargets,n.isArray(e)||(e=[e]),o=0,v=e.length;o<v;o++)if(typeof e[o]=="number"&&e[o]>=0){while(l.length<=e[o])or(t);f(e[o],h)}else if(typeof e[o]=="number"&&e[o]<0)f(l.length+e[o],h);else if(typeof e[o]=="string")for(c=0,y=l.length;c<y;c++)(e[o]=="_all"||n(l[c].nTh).hasClass(e[o]))&&f(c,h);if(u)for(s=0,a=u.length;s<a;s++)f(s,u[s])}function it(t,i,f,e){var o=t.aoData.length,h=n.extend(!0,{},u.models.oRow,{src:f?"dom":"data",idx:o}),c,s,a,l;for(h._aData=i,t.aoData.push(h),c=t.aoColumns,s=0,a=c.length;s<a;s++)c[s].sType=null;return t.aiDisplayMaster.push(o),l=t.rowIdFn(i),l!==r&&(t.aIds[l]=h),(f||!t.oFeatures.bDeferRender)&&ar(t,o,f,e),o}function si(t,i){var r;return i instanceof n||(i=n(i)),i.map(function(n,i){return r=lr(t,i),it(t,r.data,i,r.cells)})}function de(n,t){return t._DT_RowIndex!==r?t._DT_RowIndex:null}function ge(t,i,r){return n.inArray(r,t.aoData[i].anCells)}function p(n,t,i,u){var h=n.iDraw,e=n.aoColumns[i],s=n.aoData[t]._aData,o=e.sDefaultContent,f=e.fnGetData(s,u,{settings:n,row:t,col:i});if(f===r)return n.iDrawError!=h&&o===null&&(tt(n,0,"Requested unknown parameter "+(typeof e.mData=="function"?"{function}":"'"+e.mData+"'")+" for row "+t+", column "+i,4),n.iDrawError=h),o;if((f===s||f===null)&&o!==null&&u!==r)f=o;else if(typeof f=="function")return f.call(s);return f===null&&u=="display"?"":f}function tf(n,t,i,r){var u=n.aoColumns[i],f=n.aoData[t]._aData;u.fnSetData(f,r,{settings:n,row:t,col:i})}function hr(t){return n.map(t.match(/(\\.|[^\.])+/g)||[""],function(n){return n.replace(/\\\./g,".")})}function et(t){var i,u;return n.isPlainObject(t)?(i={},n.each(t,function(n,t){t&&(i[n]=et(t))}),function(n,t,u,f){var e=i[t]||i._;return e!==r?e(n,t,u,f):n}):t===null?function(n){return n}:typeof t=="function"?function(n,i,r,u){return t(n,i,r,u)}:typeof t=="string"&&(t.indexOf(".")!==-1||t.indexOf("[")!==-1||t.indexOf("(")!==-1)?(u=function(t,i,f){var s,a,h,v,e,o,y,c,p,l;if(f!=="")for(e=hr(f),o=0,y=e.length;o<y;o++){if(s=e[o].match(ct),a=e[o].match(ft),s){if(e[o]=e[o].replace(ct,""),e[o]!==""&&(t=t[e[o]]),h=[],e.splice(0,o+1),v=e.join("."),n.isArray(t))for(c=0,p=t.length;c<p;c++)h.push(u(t[c],i,v));l=s[0].substring(1,s[0].length-1);t=l===""?h:h.join(l);break}else if(a){e[o]=e[o].replace(ft,"");t=t[e[o]]();continue}if(t===null||t[e[o]]===r)return r;t=t[e[o]]}return t},function(n,i){return u(n,i,t)}):function(n){return n[t]}}function rt(t){if(n.isPlainObject(t))return rt(t._);if(t===null)return function(){};if(typeof t=="function")return function(n,i,r){t(n,"set",i,r)};if(typeof t=="string"&&(t.indexOf(".")!==-1||t.indexOf("[")!==-1||t.indexOf("(")!==-1)){var i=function(t,u,f){for(var s,p,e=hr(f),h,c=e[e.length-1],a,v,l,y,o=0,w=e.length-1;o<w;o++){if(a=e[o].match(ct),v=e[o].match(ft),a){if(e[o]=e[o].replace(ct,""),t[e[o]]=[],h=e.slice(),h.splice(0,o+1),y=h.join("."),n.isArray(u))for(s=0,p=u.length;s<p;s++)l={},i(l,u[s],y),t[e[o]].push(l);else t[e[o]]=u;return}v&&(e[o]=e[o].replace(ft,""),t=t[e[o]](u));(t[e[o]]===null||t[e[o]]===r)&&(t[e[o]]={});t=t[e[o]]}c.match(ft)?t=t[c.replace(ft,"")](u):t[c.replace(ct,"")]=u};return function(n,r){return i(n,r,t)}}return function(n,i){n[t]=i}}function cr(n){return w(n.aoData,"_aData")}function hi(n){n.aoData.length=0;n.aiDisplayMaster.length=0;n.aiDisplay.length=0;n.aIds={}}function ci(n,t,i){for(var f=-1,u=0,e=n.length;u<e;u++)n[u]==t?f=u:n[u]>t&&n[u]--;f!=-1&&i===r&&n.splice(f,1)}function bt(n,t,i,u){var e=n.aoData[t],f,s,c=function(i,r){while(i.childNodes.length)i.removeChild(i.firstChild);i.innerHTML=p(n,t,r,"display")},o,h;if(i!=="dom"&&(i&&i!=="auto"||e.src!=="dom")){if(o=e.anCells,o)if(u!==r)c(o[u],u);else for(f=0,s=o.length;f<s;f++)c(o[f],f)}else e._aData=lr(n,e,u,u===r?r:e._aData).data;if(e._aSortData=null,e._aFilterData=null,h=n.aoColumns,u!==r)h[u].sType=null;else{for(f=0,s=h.length;f<s;f++)h[f].sType=null;vr(n,e)}}function lr(t,i,u,f){var s=[],o=i.firstChild,v,e,h=0,c,d=t.aoColumns,b=t._rowReadObject,l,y,a,k,p,w;if(f=f!==r?f:b?{}:[],l=function(n,t){var i,r,u;typeof n=="string"&&(i=n.indexOf("@"),i!==-1&&(r=n.substring(i+1),u=rt(n),u(f,t.getAttribute(r))))},y=function(t){if(u===r||u===h)if(e=d[h],c=n.trim(t.innerHTML),e&&e._bAttrSrc){var i=rt(e.mData._);i(f,c);l(e.mData.sort,t);l(e.mData.type,t);l(e.mData.filter,t)}else b?(e._setter||(e._setter=rt(e.mData)),e._setter(f,c)):f[h]=c;h++},o)while(o)v=o.nodeName.toUpperCase(),(v=="TD"||v=="TH")&&(y(o),s.push(o)),o=o.nextSibling;else for(s=i.anCells,a=0,k=s.length;a<k;a++)y(s[a]);return p=i.firstChild?i:i.nTr,p&&(w=p.getAttribute("id"),w&&rt(t.rowId)(f,w)),{data:f,cells:s}}function ar(t,r,u,f){var c=t.aoData[r],y=c._aData,a=[],l,h,e,s,w,v;if(c.nTr===null){for(l=u||i.createElement("tr"),c.nTr=l,c.anCells=a,l._DT_RowIndex=r,vr(t,c),s=0,w=t.aoColumns.length;s<w;s++)e=t.aoColumns[s],v=u?!1:!0,h=v?i.createElement(e.sCellType):f[s],h._DT_CellIndex={row:r,column:s},a.push(h),(v||(!u||e.mRender||e.mData!==s)&&(!n.isPlainObject(e.mData)||e.mData._!==s+".display"))&&(h.innerHTML=p(t,r,s,"display")),e.sClass&&(h.className+=" "+e.sClass),e.bVisible&&!u?l.appendChild(h):!e.bVisible&&u&&h.parentNode.removeChild(h),e.fnCreatedCell&&e.fnCreatedCell.call(t.oInstance,h,p(t,r,s),y,r,s);o(t,"aoRowCreatedCallback",null,[l,y,r,a])}c.nTr.setAttribute("role","row")}function vr(t,i){var u=i.nTr,r=i._aData,f,e;u&&(f=t.rowIdFn(r),f&&(u.id=f),r.DT_RowClass&&(e=r.DT_RowClass.split(" "),i.__rowc=i.__rowc?fi(i.__rowc.concat(e)):e,n(u).removeClass(i.__rowc.join(" ")).addClass(r.DT_RowClass)),r.DT_RowAttr&&n(u).attr(r.DT_RowAttr),r.DT_RowData&&n(u).data(r.DT_RowData))}function rf(t){var r,e,u,l,i,f=t.nTHead,a=t.nTFoot,o=n("th, td",f).length===0,s=t.oClasses,h=t.aoColumns,c;for(o&&(l=n("<tr/>").appendTo(f)),r=0,e=h.length;r<e;r++)i=h[r],u=n(i.nTh).addClass(i.sClass),o&&u.appendTo(l),t.oFeatures.bSort&&(u.addClass(i.sSortingClass),i.bSortable!==!1&&(u.attr("tabindex",t.iTabIndex).attr("aria-controls",t.sTableId),gr(t,i.nTh,r))),i.sTitle!=u[0].innerHTML&&u.html(i.sTitle),ru(t,"header")(t,u,i,s);if(o&&dt(t.aoHeader,f),n(f).find(">tr").attr("role","row"),n(f).find(">tr>th, >tr>td").addClass(s.sHeaderTH),n(a).find(">tr>th, >tr>td").addClass(s.sFooterTH),a!==null)for(c=t.aoFooter[0],r=0,e=c.length;r<e;r++)i=h[r],i.nTf=c[r].cell,i.sClass&&n(i.nTf).addClass(i.sClass)}function kt(t,i,u){var f,a,e,y,v,p,c,o=[],l=[],w=t.aoColumns.length,s,h;if(i){for(u===r&&(u=!1),f=0,a=i.length;f<a;f++){for(o[f]=i[f].slice(),o[f].nTr=i[f].nTr,e=w-1;e>=0;e--)t.aoColumns[e].bVisible||u||o[f].splice(e,1);l.push([])}for(f=0,a=o.length;f<a;f++){if(c=o[f].nTr,c)while(p=c.firstChild)c.removeChild(p);for(e=0,y=o[f].length;e<y;e++)if(s=1,h=1,l[f][e]===r){for(c.appendChild(o[f][e].cell),l[f][e]=1;o[f+s]!==r&&o[f][e].cell==o[f+s][e].cell;)l[f+s][e]=1,s++;while(o[f][e+h]!==r&&o[f][e].cell==o[f][e+h].cell){for(v=0;v<s;v++)l[f+v][e+h]=1;h++}n(o[f][e].cell).attr("rowspan",s).attr("colspan",h)}}}}function ut(t){var ut=o(t,"aoPreDrawCallback","preDraw",[t]),c,l,it,rt,f,a,i,v,p,w,tt;if(n.inArray(!1,ut)!==-1){b(t,!1);return}var k=[],d=0,g=t.asStripeClasses,nt=g.length,ft=t.aoOpenRows.length,e=t.oLanguage,u=t.iInitDisplayStart,s=y(t)=="ssp",h=t.aiDisplay;if(t.bDrawing=!0,u!==r&&u!==-1&&(t._iDisplayStart=s?u:u>=t.fnRecordsDisplay()?0:u,t.iInitDisplayStart=-1),c=t._iDisplayStart,l=t.fnDisplayEnd(),t.bDeferLoading)t.bDeferLoading=!1,t.iDraw++,b(t,!1);else if(s){if(!t.bDestroying&&!ff(t))return}else t.iDraw++;if(h.length!==0)for(it=s?0:c,rt=s?t.aoData.length:l,f=it;f<rt;f++)a=h[f],i=t.aoData[a],i.nTr===null&&ar(t,a),v=i.nTr,nt!==0&&(p=g[d%nt],i._sRowStripe!=p&&(n(v).removeClass(i._sRowStripe).addClass(p),i._sRowStripe=p)),o(t,"aoRowCallback",null,[v,i._aData,d,f,a]),k.push(v),d++;else w=e.sZeroRecords,t.iDraw==1&&y(t)=="ajax"?w=e.sLoadingRecords:e.sEmptyTable&&t.fnRecordsTotal()===0&&(w=e.sEmptyTable),k[0]=n("<tr/>",{"class":nt?g[0]:""}).append(n("<td />",{valign:"top",colSpan:ht(t),"class":t.oClasses.sRowEmpty}).html(w))[0];o(t,"aoHeaderCallback","header",[n(t.nTHead).children("tr")[0],cr(t),c,l,h]);o(t,"aoFooterCallback","footer",[n(t.nTFoot).children("tr")[0],cr(t),c,l,h]);tt=n(t.nTBody);tt.children().detach();tt.append(n(k));o(t,"aoDrawCallback","draw",[t]);t.bSorted=!1;t.bFiltered=!1;t.bDrawing=!1}function ot(n,t){var i=n.oFeatures,r=i.bSort,u=i.bFilter;r&&fe(n);u?gt(n,n.oPreviousSearch):n.aiDisplay=n.aiDisplayMaster.slice();t!==!0&&(n._iDisplayStart=0);n._drawHold=t;ut(n);n._drawHold=!1}function uf(t){var v=t.oClasses,g=n(t.nTable),k=n("<div/>").insertBefore(g),h=t.oFeatures,o=n("<div/>",{id:t.sTableId+"_wrapper","class":v.sWrapper+(t.nTFoot?"":" "+v.sNoFooter)}),c,f,i,s,y,r,l,e,p,w,a,d,b;for(t.nHolding=k[0],t.nTableWrapper=o[0],t.nTableReinsertBefore=t.nTable.nextSibling,c=t.sDom.split(""),e=0;e<c.length;e++){if(f=null,i=c[e],i=="<"){if(s=n("<div/>")[0],y=c[e+1],y=="'"||y=='"'){for(r="",l=2;c[e+l]!=y;)r+=c[e+l],l++;r=="H"?r=v.sJUIHeader:r=="F"&&(r=v.sJUIFooter);r.indexOf(".")!=-1?(p=r.split("."),s.id=p[0].substr(1,p[0].length-1),s.className=p[1]):r.charAt(0)=="#"?s.id=r.substr(1,r.length-1):s.className=r;e+=l}o.append(s);o=n(s)}else if(i==">")o=o.parent();else if(i=="l"&&h.bPaginate&&h.bLengthChange)f=kf(t);else if(i=="f"&&h.bFilter)f=sf(t);else if(i=="r"&&h.bProcessing)f=gf(t);else if(i=="t")f=ne(t);else if(i=="i"&&h.bInfo)f=pf(t);else if(i=="p"&&h.bPaginate)f=df(t);else if(u.ext.feature.length!==0)for(w=u.ext.feature,a=0,d=w.length;a<d;a++)if(i==w[a].cFeature){f=w[a].fnInit(t);break}f&&(b=t.aanFeatures,b[i]||(b[i]=[]),b[i].push(f),o.append(f))}k.replaceWith(o);t.nHolding=null}function dt(t,i){var c=n(i).children("tr"),l,u,r,o,s,h,a,v,f,e,y,p=function(n,t,i){for(var r=n[t];r[i];)i++;return i};for(t.splice(0,t.length),r=0,h=c.length;r<h;r++)t.push([]);for(r=0,h=c.length;r<h;r++)for(l=c[r],v=0,u=l.firstChild;u;){if(u.nodeName.toUpperCase()=="TD"||u.nodeName.toUpperCase()=="TH")for(f=u.getAttribute("colspan")*1,e=u.getAttribute("rowspan")*1,f=!f||f===0||f===1?1:f,e=!e||e===0||e===1?1:e,a=p(t,r,v),y=f===1?!0:!1,s=0;s<f;s++)for(o=0;o<e;o++)t[r+o][a+s]={cell:u,unique:y},t[r+o].nTr=l;u=u.nextSibling}}function li(n,t,i){var f=[],u,e,r,o;for(i||(i=n.aoHeader,t&&(i=[],dt(i,t))),u=0,e=i.length;u<e;u++)for(r=0,o=i[u].length;r<o;r++)!i[u][r].unique||f[r]&&n.bSortCellsTop||(f[r]=i[u][r].cell);return f}function ai(t,i,r){var f,l,s,c;o(t,"aoServerParams","serverParams",[i]);i&&n.isArray(i)&&(f={},l=/(.*?)\[\]$/,n.each(i,function(n,t){var r=t.name.match(l),i;r?(i=r[0],f[i]||(f[i]=[]),f[i].push(t.value)):f[t.name]=t.value}),i=f);var e,u=t.ajax,a=t.oInstance,h=function(n){o(t,null,"xhr",[t,n,t.jqXHR]);r(n)};n.isPlainObject(u)&&u.data&&(e=u.data,s=typeof e=="function"?e(i,t):e,i=typeof e=="function"&&s?s:n.extend(!0,i,s),delete u.data);c={data:i,success:function(n){var i=n.error||n.sError;i&&tt(t,0,i);t.json=n;h(n)},dataType:"json",cache:!1,type:t.sServerMethod,error:function(i,r){var u=o(t,null,"xhr",[t,null,t.jqXHR]);n.inArray(!0,u)===-1&&(r=="parsererror"?tt(t,0,"Invalid JSON response",1):i.readyState===4&&tt(t,0,"Ajax error",7));b(t,!1)}};t.oAjaxData=i;o(t,null,"preXhr",[t,i]);t.fnServerData?t.fnServerData.call(a,t.sAjaxSource,n.map(i,function(n,t){return{name:t,value:n}}),h,t):t.sAjaxSource||typeof u=="string"?t.jqXHR=n.ajax(n.extend(c,{url:u||t.sAjaxSource})):typeof u=="function"?t.jqXHR=u.call(a,i,h,t):(t.jqXHR=n.ajax(n.extend(c,u)),u.data=e)}function ff(n){return n.bAjaxDataGet?(n.iDraw++,b(n,!0),ai(n,ef(n),function(t){of(n,t)}),!1):!0}function ef(t){var c=t.aoColumns,y=c.length,e=t.oFeatures,h=t.oPreviousSearch,d=t.aoPreSearchCols,r,l=[],a,f,o,p=lt(t),b=t._iDisplayStart,k=e.bPaginate!==!1?t._iDisplayLength:-1,i=function(n,t){l.push({name:n,value:t})},s,v;for(i("sEcho",t.iDraw),i("iColumns",y),i("sColumns",w(c,"sName").join(",")),i("iDisplayStart",b),i("iDisplayLength",k),s={draw:t.iDraw,columns:[],order:[],start:b,length:k,search:{value:h.sSearch,regex:h.bRegex}},r=0;r<y;r++)f=c[r],o=d[r],a=typeof f.mData=="function"?"function":f.mData,s.columns.push({data:a,name:f.sName,searchable:f.bSearchable,orderable:f.bSortable,search:{value:o.sSearch,regex:o.bRegex}}),i("mDataProp_"+r,a),e.bFilter&&(i("sSearch_"+r,o.sSearch),i("bRegex_"+r,o.bRegex),i("bSearchable_"+r,f.bSearchable)),e.bSort&&i("bSortable_"+r,f.bSortable);return(e.bFilter&&(i("sSearch",h.sSearch),i("bRegex",h.bRegex)),e.bSort&&(n.each(p,function(n,t){s.order.push({column:t.col,dir:t.dir});i("iSortCol_"+n,t.col);i("sSortDir_"+n,t.dir)}),i("iSortingCols",p.length)),v=u.ext.legacy.ajax,v===null)?t.sAjaxSource?l:s:v?l:s}function of(n,t){var u=function(n,i){return t[n]!==r?t[n]:t[i]},e=vi(n,t),f=u("sEcho","draw"),s=u("iTotalRecords","recordsTotal"),h=u("iTotalDisplayRecords","recordsFiltered"),i,o;if(f!==r){if(f*1<n.iDraw)return;n.iDraw=f*1}for(hi(n),n._iRecordsTotal=parseInt(s,10),n._iRecordsDisplay=parseInt(h,10),i=0,o=e.length;i<o;i++)it(n,e[i]);n.aiDisplay=n.aiDisplayMaster.slice();n.bAjaxDataGet=!1;ut(n);n._bInitComplete||pi(n,t);n.bAjaxDataGet=!0;b(n,!1)}function vi(t,i){var u=n.isPlainObject(t.ajax)&&t.ajax.dataSrc!==r?t.ajax.dataSrc:t.sAjaxDataProp;return u==="data"?i.aaData||i[u]:u!==""?et(u)(i):i}function sf(t){var o=t.oClasses,s=t.sTableId,h=t.oLanguage,r=t.oPreviousSearch,c=t.aanFeatures,l='<input type="search" class="'+o.sFilterInput+'"/>',u=h.sSearch;u=u.match(/_INPUT_/)?u.replace("_INPUT_",l):u+l;var a=n("<div/>",{id:c.f?null:s+"_filter","class":o.sFilter}).append(n("<label/>").append(u)),f=function(){var i=c.f,n=this.value?this.value:"";n!=r.sSearch&&(gt(t,{sSearch:n,bRegex:r.bRegex,bSmart:r.bSmart,bCaseInsensitive:r.bCaseInsensitive}),t._iDisplayStart=0,ut(t))},v=t.searchDelay!==null?t.searchDelay:y(t)==="ssp"?400:0,e=n("input",a).val(r.sSearch).attr("placeholder",h.sSearchPlaceholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",v?bi(f,v):f).on("mouseup",function(){setTimeout(function(){f.call(e[0])},10)}).on("keypress.DT",function(n){if(n.keyCode==13)return!1}).attr("aria-controls",s);n(t.nTable).on("search.dt.DT",function(n,u){if(t===u)try{e[0]!==i.activeElement&&e.val(r.sSearch)}catch(f){}});return a[0]}function gt(n,t,i){var e=n.oPreviousSearch,f=n.aoPreSearchCols,s=function(n){e.sSearch=n.sSearch;e.bRegex=n.bRegex;e.bSmart=n.bSmart;e.bCaseInsensitive=n.bCaseInsensitive},h=function(n){return n.bEscapeRegex!==r?!n.bEscapeRegex:n.bRegex},u;if(sr(n),y(n)!="ssp"){for(lf(n,t.sSearch,i,h(t),t.bSmart,t.bCaseInsensitive),s(t),u=0;u<f.length;u++)cf(n,f[u].sSearch,u,h(f[u]),f[u].bSmart,f[u].bCaseInsensitive);hf(n)}else s(t);n.bFiltered=!0;o(n,null,"search",[n])}function hf(t){for(var s,i,c,h=u.ext.search,r=t.aiDisplay,e,f,o=0,l=h.length;o<l;o++){for(s=[],i=0,c=r.length;i<c;i++)f=r[i],e=t.aoData[f],h[o](t,e._aFilterData,f,e._aData,i)&&s.push(f);r.length=0;n.merge(r,s)}}function cf(n,t,i,r,u,f){var e;if(t!==""){var s,h=[],o=n.aiDisplay,c=yr(t,r,u,f);for(e=0;e<o.length;e++)s=n.aoData[o[e]]._aFilterData[i],c.test(s)&&h.push(o[e]);n.aiDisplay=h}}function lf(n,t,i,r,f,e){var v=yr(t,r,f,e),h=n.oPreviousSearch.sSearch,c=n.aiDisplayMaster,s,l,o,a=[];if(u.ext.search.length!==0&&(i=!0),l=af(n),t.length<=0)n.aiDisplay=c.slice();else{for((l||i||r||h.length>t.length||t.indexOf(h)!==0||n.bSorted)&&(n.aiDisplay=c.slice()),s=n.aiDisplay,o=0;o<s.length;o++)v.test(n.aoData[s[o]]._sFilterRow)&&a.push(s[o]);n.aiDisplay=a}}function yr(t,i,r,u){if(t=i?t:pr(t),r){var f=n.map(t.match(/"[^"]+"|[^ ]+/g)||[""],function(n){if(n.charAt(0)==='"'){var t=n.match(/^"(.*)"$/);n=t?t[1]:n}return n.replace('"',"")});t="^(?=.*?"+f.join(")(?=.*?")+").*$"}return new RegExp(t,u?"i":"")}function af(n){for(var s=n.aoColumns,f,r,c,e,t,o,l=u.ext.type.search,a=!1,i=0,h=n.aoData.length;i<h;i++)if(o=n.aoData[i],!o._aFilterData){for(e=[],r=0,c=s.length;r<c;r++)f=s[r],f.bSearchable?(t=p(n,i,r,"filter"),l[f.sType]&&(t=l[f.sType](t)),t===null&&(t=""),typeof t!="string"&&t.toString&&(t=t.toString())):t="",t.indexOf&&t.indexOf("&")!==-1&&(yi.innerHTML=t,t=no?yi.textContent:yi.innerText),t.replace&&(t=t.replace(/[\r\n\u2028]/g,"")),e.push(t);o._aFilterData=e;o._sFilterRow=e.join("  ");a=!0}return a}function vf(n){return{search:n.sSearch,smart:n.bSmart,regex:n.bRegex,caseInsensitive:n.bCaseInsensitive}}function yf(n){return{sSearch:n.search,bSmart:n.smart,bRegex:n.regex,bCaseInsensitive:n.caseInsensitive}}function pf(t){var i=t.sTableId,r=t.aanFeatures.i,u=n("<div/>",{"class":t.oClasses.sInfo,id:r?null:i+"_info"});return r||(t.aoDrawCallback.push({fn:wf,sName:"information"}),u.attr("role","status").attr("aria-live","polite"),n(t.nTable).attr("aria-describedby",i+"_info")),u[0]}function wf(t){var e=t.aanFeatures.i,f;if(e.length!==0){var r=t.oLanguage,s=t._iDisplayStart+1,h=t.fnDisplayEnd(),o=t.fnRecordsTotal(),u=t.fnRecordsDisplay(),i=u?r.sInfo:r.sInfoEmpty;u!==o&&(i+=" "+r.sInfoFiltered);i+=r.sInfoPostFix;i=bf(t,i);f=r.fnInfoCallback;f!==null&&(i=f.call(t.oInstance,t,s,h,o,u,i));n(e).html(i)}}function bf(n,t){var i=n.fnFormatNumber,u=n._iDisplayStart+1,r=n._iDisplayLength,f=n.fnRecordsDisplay(),e=r===-1;return t.replace(/_START_/g,i.call(n,u)).replace(/_END_/g,i.call(n,n.fnDisplayEnd())).replace(/_MAX_/g,i.call(n,n.fnRecordsTotal())).replace(/_TOTAL_/g,i.call(n,f)).replace(/_PAGE_/g,i.call(n,e?1:Math.ceil(u/r))).replace(/_PAGES_/g,i.call(n,e?1:Math.ceil(f/r)))}function ni(n){var t,u,e=n.iInitDisplayStart,f=n.aoColumns,i,s=n.oFeatures,c=n.bDeferLoading,r;if(!n.bInitialised){setTimeout(function(){ni(n)},200);return}for(uf(n),rf(n),kt(n,n.aoHeader),kt(n,n.aoFooter),b(n,!0),s.bAutoWidth&&kr(n),t=0,u=f.length;t<u;t++)i=f[t],i.sWidth&&(i.nTh.style.width=h(i.sWidth));o(n,null,"preInit",[n]);ot(n);r=y(n);(r!="ssp"||c)&&(r=="ajax"?ai(n,[],function(i){var r=vi(n,i);for(t=0;t<r.length;t++)it(n,r[t]);n.iInitDisplayStart=e;ot(n);b(n,!1);pi(n,i)},n):(b(n,!1),pi(n)))}function pi(n,t){n._bInitComplete=!0;(t||n.oInit.aaData)&&yt(n);o(n,null,"plugin-init",[n,t]);o(n,"aoInitComplete","init",[n,t])}function wr(n,t){var i=parseInt(t,10);n._iDisplayLength=i;iu(n);o(n,null,"length",[n,i])}function kf(t){for(var r,o=t.oClasses,f=t.sTableId,u=t.aLengthMenu,s=n.isArray(u[0]),h=s?u[0]:u,e=s?u[1]:u,c=n("<select/>",{name:f+"_length","aria-controls":f,"class":o.sLengthSelect}),i=0,l=h.length;i<l;i++)c[0][i]=new Option(typeof e[i]=="number"?t.fnFormatNumber(e[i]):e[i],h[i]);r=n("<div><label/><\/div>").addClass(o.sLength);t.aanFeatures.l||(r[0].id=f+"_length");r.children().append(t.oLanguage.sLengthMenu.replace("_MENU_",c[0].outerHTML));n("select",r).val(t._iDisplayLength).on("change.DT",function(){wr(t,n(this).val());ut(t)});n(t.nTable).on("length.dt.DT",function(i,u,f){t===u&&n("select",r).val(f)});return r[0]}function df(t){var e=t.sPaginationType,i=u.ext.pager[e],o=typeof i=="function",s=function(n){ut(n)},r=n("<div/>").addClass(t.oClasses.sPaging+e)[0],f=t.aanFeatures;return o||i.fnInit(t,r,s),f.p||(r.id=t.sTableId+"_paginate",t.aoDrawCallback.push({fn:function(n){if(o)for(var l=n._iDisplayStart,r=n._iDisplayLength,a=n.fnRecordsDisplay(),u=r===-1,e=u?0:Math.ceil(l/r),h=u?1:Math.ceil(a/r),v=i(e,h),t=0,c=f.p.length;t<c;t++)ru(n,"pageButton")(n,f.p[t],t,v,e,h);else i.fnUpdate(n,s)},sName:"pagination"})),r}function br(n,t,i){var r=n._iDisplayStart,u=n._iDisplayLength,f=n.fnRecordsDisplay(),e;return f===0||u===-1?r=0:typeof t=="number"?(r=t*u,r>f&&(r=0)):t=="first"?r=0:t=="previous"?(r=u>=0?r-u:0,r<0&&(r=0)):t=="next"?r+u<f&&(r+=u):t=="last"?r=Math.floor((f-1)/u)*u:tt(n,0,"Unknown paging action: "+t,5),e=n._iDisplayStart!==r,n._iDisplayStart=r,e&&(o(n,null,"page",[n]),i&&ut(n)),e}function gf(t){return n("<div/>",{id:t.aanFeatures.r?null:t.sTableId+"_processing","class":t.oClasses.sProcessing}).html(t.oLanguage.sProcessing).insertBefore(t.nTable)[0]}function b(t,i){t.oFeatures.bProcessing&&n(t.aanFeatures.r).css("display",i?"block":"none");o(t,null,"processing",[t,i])}function ne(t){var i=n(t.nTable),r,c;if(i.attr("role","grid"),r=t.oScroll,r.sX===""&&r.sY==="")return t.nTable;var u=r.sX,y=r.sY,f=t.oClasses,s=i.children("caption"),p=s.length?s[0]._captionSide:null,k=n(i[0].cloneNode(!1)),d=n(i[0].cloneNode(!1)),o=i.children("tfoot"),e="<div/>",a=function(n){return n?h(n):null};o.length||(o=null);c=n(e,{"class":f.sScrollWrapper}).append(n(e,{"class":f.sScrollHead}).css({overflow:"hidden",position:"relative",border:0,width:u?a(u):"100%"}).append(n(e,{"class":f.sScrollHeadInner}).css({"box-sizing":"content-box",width:r.sXInner||"100%"}).append(k.removeAttr("id").css("margin-left",0).append(p==="top"?s:null).append(i.children("thead"))))).append(n(e,{"class":f.sScrollBody}).css({position:"relative",overflow:"auto",width:a(u)}).append(i));o&&c.append(n(e,{"class":f.sScrollFoot}).css({overflow:"hidden",border:0,width:u?a(u):"100%"}).append(n(e,{"class":f.sScrollFootInner}).append(d.removeAttr("id").css("margin-left",0).append(p==="bottom"?s:null).append(i.children("tfoot")))));var v=c.children(),w=v[0],l=v[1],b=o?v[2]:null;if(u)n(l).on("scroll.DT",function(){var n=this.scrollLeft;w.scrollLeft=n;o&&(b.scrollLeft=n)});return n(l).css("max-height",y),r.bCollapse||n(l).css("height",y),t.nScrollHead=w,t.nScrollBody=l,t.nScrollFoot=b,t.aoDrawCallback.push({fn:wi,sName:"scrolling"}),c[0]}function wi(t){var y=t.oScroll,k=y.sX,g=y.sXInner,ri=y.sY,e=y.iBarWidth,nt=n(t.nScrollHead),ui=nt[0].style,ct=nt.children("div"),lt=ct[0].style,fi=ct.children("table"),u=t.nScrollBody,l=n(u),it=u.style,ei=n(t.nScrollFoot),rt=ei.children("div"),oi=rt.children("table"),at=n(t.nTHead),i=n(t.nTable),vt=i[0],p=vt.style,f=t.nTFoot?n(t.nTFoot):null,wt=t.oBrowser,ut=wt.bScrollOversize,si=w(t.aoColumns,"nTh"),bt,kt,a,o,b,dt,ft=[],et=[],gt=[],ni=[],ti,s,c,ii=function(n){var t=n.style;t.paddingTop="0";t.paddingBottom="0";t.borderTopWidth="0";t.borderBottomWidth="0";t.height=0},ot=u.scrollHeight>u.clientHeight,v,st,ht;if(t.scrollBarVis!==ot&&t.scrollBarVis!==r){t.scrollBarVis=ot;yt(t);return}t.scrollBarVis=ot;i.children("thead, tfoot").remove();f&&(dt=f.clone().prependTo(i),kt=f.find("tr"),o=dt.find("tr"));b=at.clone().prependTo(i);bt=at.find("tr");a=b.find("tr");b.find("th, td").removeAttr("tabindex");k||(it.width="100%",nt[0].style.width="100%");n.each(li(t,b),function(n,i){ti=pt(t,n);i.style.width=t.aoColumns[ti].sWidth});f&&d(function(n){n.style.width=""},o);c=i.outerWidth();k===""?(p.width="100%",ut&&(i.find("tbody").height()>u.offsetHeight||l.css("overflow-y")=="scroll")&&(p.width=h(i.outerWidth()-e)),c=i.outerWidth()):g!==""&&(p.width=h(g),c=i.outerWidth());d(ii,a);d(function(t){gt.push(t.innerHTML);ft.push(h(n(t).css("width")))},a);d(function(t,i){n.inArray(t,si)!==-1&&(t.style.width=ft[i])},bt);n(a).height(0);f&&(d(ii,o),d(function(t){ni.push(t.innerHTML);et.push(h(n(t).css("width")))},o),d(function(n,t){n.style.width=et[t]},kt),n(o).height(0));d(function(n,t){n.innerHTML='<div class="dataTables_sizing">'+gt[t]+"<\/div>";n.childNodes[0].style.height="0";n.childNodes[0].style.overflow="hidden";n.style.width=ft[t]},a);f&&d(function(n,t){n.innerHTML='<div class="dataTables_sizing">'+ni[t]+"<\/div>";n.childNodes[0].style.height="0";n.childNodes[0].style.overflow="hidden";n.style.width=et[t]},o);i.outerWidth()<c?(s=u.scrollHeight>u.offsetHeight||l.css("overflow-y")=="scroll"?c+e:c,ut&&(u.scrollHeight>u.offsetHeight||l.css("overflow-y")=="scroll")&&(p.width=h(s-e)),(k===""||g!=="")&&tt(t,1,"Possible column misalignment",6)):s="100%";it.width=h(s);ui.width=h(s);f&&(t.nScrollFoot.style.width=h(s));ri||ut&&(it.height=h(vt.offsetHeight+e));v=i.outerWidth();fi[0].style.width=h(v);lt.width=h(v);st=i.height()>u.clientHeight||l.css("overflow-y")=="scroll";ht="padding"+(wt.bScrollbarLeft?"Left":"Right");lt[ht]=st?e+"px":"0px";f&&(oi[0].style.width=h(v),rt[0].style.width=h(v),rt[0].style[ht]=st?e+"px":"0px");i.children("colgroup").insertBefore(i.children("thead"));l.trigger("scroll");(t.bSorted||t.bFiltered)&&!t._drawHold&&(u.scrollTop=0)}function d(n,t,i){for(var e=0,u=0,o=t.length,r,f;u<o;){for(r=t[u].firstChild,f=i?i[u].firstChild:null;r;)r.nodeType===1&&(i?n(r,f,e):n(r,e),e++),r=r.nextSibling,f=i?f.nextSibling:null;u++}}function kr(i){var c=i.nTable,l=i.aoColumns,y=i.oScroll,p=y.sY,a=y.sX,it=y.sXInner,w=l.length,e=oi(i,"bVisible"),o=n("th",i.nTHead),s=c.getAttribute("width"),v=c.parentNode,rt=!1,r,f,b,ut=i.oBrowser,ft=ut.bScrollOversize,k=c.style.width,d,u,et,ot,g,tt;for(k&&k.indexOf("%")!==-1&&(s=k),r=0;r<e.length;r++)f=l[e[r]],f.sWidth!==null&&(f.sWidth=ie(f.sWidthOrig,v),rt=!0);if(!ft&&(rt||a||p||w!=ht(i)||w!=o.length)){for(u=n(c).clone().css("visibility","hidden").removeAttr("id"),u.find("tbody tr").remove(),et=n("<tr/>").appendTo(u.find("tbody")),u.find("thead, tfoot").remove(),u.append(n(i.nTHead).clone()).append(n(i.nTFoot).clone()),u.find("tfoot th, tfoot td").css("width",""),o=li(i,u.find("thead")[0]),r=0;r<e.length;r++)f=l[e[r]],o[r].style.width=f.sWidthOrig!==null&&f.sWidthOrig!==""?h(f.sWidthOrig):"",f.sWidthOrig&&a&&n(o[r]).append(n("<div/>").css({width:f.sWidthOrig,margin:0,padding:0,border:0,height:1}));if(i.aoData.length)for(r=0;r<e.length;r++)b=e[r],f=l[b],n(re(i,b)).clone(!1).append(f.sContentPadding).appendTo(et);for(n("[name]",u).removeAttr("name"),ot=n("<div/>").css(a||p?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(u).appendTo(v),a&&it?u.width(it):a?(u.css("width","auto"),u.removeAttr("width"),u.width()<v.clientWidth&&s&&u.width(v.clientWidth)):p?u.width(v.clientWidth):s&&u.width(s),g=0,r=0;r<e.length;r++){var nt=n(o[r]),ct=nt.outerWidth()-nt.width(),st=ut.bBounding?Math.ceil(o[r].getBoundingClientRect().width):nt.outerWidth();g+=st;l[e[r]].sWidth=h(st-ct)}c.style.width=h(g);ot.remove()}else for(r=0;r<w;r++)d=pt(i,r),d!==null&&(l[d].sWidth=h(o.eq(r).width()));s&&(c.style.width=h(s));(s||a)&&!i._reszEvt&&(tt=function(){n(t).on("resize.DT-"+i.sInstance,bi(function(){yt(i)}))},ft?setTimeout(tt,1e3):tt(),i._reszEvt=!0)}function ie(t,r){if(!t)return 0;var u=n("<div/>").css("width",h(t)).appendTo(r||i.body),f=u[0].offsetWidth;return u.remove(),f}function re(t,i){var r=ue(t,i),u;return r<0?null:(u=t.aoData[r],u.nTr?u.anCells[i]:n("<td/>").html(p(t,r,i,"display"))[0])}function ue(n,t){for(var i,u=-1,f=-1,r=0,e=n.aoData.length;r<e;r++)i=p(n,r,t,"display")+"",i=i.replace(te,""),i=i.replace(/&nbsp;/g," "),i.length>u&&(u=i.length,f=r);return f}function h(n){return n===null?"0px":typeof n=="number"?n<0?"0px":n+"px":n.match(/\d$/)?n+"px":n}function lt(t){var i,o,y,p=[],c=t.aoColumns,l,s,a,v,e=t.aaSortingFixed,w=n.isPlainObject(e),f=[],h=function(t){t.length&&!n.isArray(t[0])?f.push(t):n.merge(f,t)};for(n.isArray(e)&&h(e),w&&e.pre&&h(e.pre),h(t.aaSorting),w&&e.post&&h(e.post),i=0;i<f.length;i++)for(v=f[i][0],l=c[v].aDataSort,o=0,y=l.length;o<y;o++)s=l[o],a=c[s].sType||"string",f[i]._idx===r&&(f[i]._idx=n.inArray(f[i][1],c[s].asSorting)),p.push({src:v,col:s,dir:f[i][1],index:f[i]._idx,type:a,formatter:u.ext.type.order[a+"-pre"]});return p}function fe(n){var t,s,h,r=[],c=u.ext.type.order,f=n.aoData,a=n.aoColumns,l=0,o,e=n.aiDisplayMaster,i;for(sr(n),i=lt(n),t=0,s=i.length;t<s;t++)o=i[t],o.formatter&&l++,oe(n,o.col);if(y(n)!="ssp"&&i.length!==0){for(t=0,h=e.length;t<h;t++)r[e[t]]=t;l===i.length?e.sort(function(n,t){for(var u,e,s,h,c=i.length,l=f[n]._aSortData,a=f[t]._aSortData,o=0;o<c;o++)if(h=i[o],u=l[h.col],e=a[h.col],s=u<e?-1:u>e?1:0,s!==0)return h.dir==="asc"?s:-s;return u=r[n],e=r[t],u<e?-1:u>e?1:0}):e.sort(function(n,t){for(var e,o,h,u,l,a=i.length,v=f[n]._aSortData,y=f[t]._aSortData,s=0;s<a;s++)if(u=i[s],e=v[u.col],o=y[u.col],l=c[u.type+"-"+u.dir]||c["string-"+u.dir],h=l(e,o),h!==0)return h;return e=r[n],o=r[t],e<o?-1:e>o?1:0})}n.bSorted=!0}function ee(n){for(var u,f,s=n.aoColumns,t=lt(n),h=n.oLanguage.oAria,i=0,l=s.length;i<l;i++){var r=s[i],e=r.asSorting,c=r.sTitle.replace(/<.*?>/g,""),o=r.nTh;o.removeAttribute("aria-sort");r.bSortable?(t.length>0&&t[0].col==i?(o.setAttribute("aria-sort",t[0].dir=="asc"?"ascending":"descending"),f=e[t[0].index+1]||e[0]):f=e[0],u=c+(f==="asc"?h.sSortAscending:h.sSortDescending)):u=c;o.setAttribute("aria-label",u)}}function dr(t,i,u,f){var l=t.aoColumns[i],e=t.aaSorting,s=l.asSorting,o,c=function(t,i){var u=t._idx;return u===r&&(u=n.inArray(t[1],s)),u+1<s.length?u+1:i?null:0},h;typeof e[0]=="number"&&(e=t.aaSorting=[e]);u&&t.oFeatures.bSortMulti?(h=n.inArray(i,w(e,"0")),h!==-1?(o=c(e[h],!0),o===null&&e.length===1&&(o=0),o===null?e.splice(h,1):(e[h][1]=s[o],e[h]._idx=o)):(e.push([i,s[0],0]),e[e.length-1]._idx=0)):e.length&&e[0][0]==i?(o=c(e[0]),e.length=1,e[0][1]=s[o],e[0]._idx=o):(e.length=0,e.push([i,s[0]]),e[0]._idx=0);ot(t);typeof f=="function"&&f(t)}function gr(n,t,i,r){var u=n.aoColumns[i];tu(t,{},function(t){u.bSortable!==!1&&(n.oFeatures.bProcessing?(b(n,!0),setTimeout(function(){dr(n,i,t.shiftKey,r);y(n)!=="ssp"&&b(n,!1)},0)):dr(n,i,t.shiftKey,r))})}function ki(t){var e=t.aLastSort,o=t.oClasses.sSortColumn,f=lt(t),s=t.oFeatures,i,r,u;if(s.bSort&&s.bSortClasses){for(i=0,r=e.length;i<r;i++)u=e[i].src,n(w(t.aoData,"anCells",u)).removeClass(o+(i<2?i+1:3));for(i=0,r=f.length;i<r;i++)u=f[i].src,n(w(t.aoData,"anCells",u)).addClass(o+(i<2?i+1:3))}t.aLastSort=f}function oe(n,t){var s=n.aoColumns[t],f=u.ext.order[s.sSortDataType],h,r,e,o,i,c;for(f&&(h=f.call(n.oInstance,n,t,wt(n,t))),o=u.ext.type.order[s.sType+"-pre"],i=0,c=n.aoData.length;i<c;i++)r=n.aoData[i],r._aSortData||(r._aSortData=[]),(!r._aSortData[t]||f)&&(e=f?h[i]:p(n,i,t,"sort"),r._aSortData[t]=o?o(e):e)}function di(t){if(t.oFeatures.bStateSave&&!t.bDestroying){var i={time:+new Date,start:t._iDisplayStart,length:t._iDisplayLength,order:n.extend(!0,[],t.aaSorting),search:vf(t.oPreviousSearch),columns:n.map(t.aoColumns,function(n,i){return{visible:n.bVisible,search:vf(t.aoPreSearchCols[i])}})};o(t,"aoStateSaveParams","stateSaveParams",[t,i]);t.oSavedState=i;t.fnStateSaveCallback.call(t.oInstance,t,i)}}function se(t,i,u){var f,h,e=t.aoColumns,c=function(i){var l,c,s;if(!i||!i.time){u();return}if(l=o(t,"aoStateLoadParams","stateLoadParams",[t,i]),n.inArray(!1,l)!==-1){u();return}if(c=t.iStateDuration,c>0&&i.time<+new Date-c*1e3){u();return}if(i.columns&&e.length!==i.columns.length){u();return}if(t.oLoadedState=n.extend(!0,{},i),i.start!==r&&(t._iDisplayStart=i.start,t.iInitDisplayStart=i.start),i.length!==r&&(t._iDisplayLength=i.length),i.order!==r&&(t.aaSorting=[],n.each(i.order,function(n,i){t.aaSorting.push(i[0]>=e.length?[0,i[1]]:i)})),i.search!==r&&n.extend(t.oPreviousSearch,yf(i.search)),i.columns)for(f=0,h=i.columns.length;f<h;f++)s=i.columns[f],s.visible!==r&&(e[f].bVisible=s.visible),s.search!==r&&n.extend(t.aoPreSearchCols[f],yf(s.search));o(t,"aoStateLoaded","stateLoaded",[t,i]);u()},s;if(!t.oFeatures.bStateSave){u();return}s=t.fnStateLoadCallback.call(t.oInstance,t,c);s!==r&&c(s)}function gi(t){var i=u.settings,r=n.inArray(t,w(i,"nTable"));return r!==-1?i[r]:null}function tt(n,i,r,f){if(r="DataTables warning: "+(n?"table id="+n.sTableId+" - ":"")+r,f&&(r+=". For more information about this error, please see http://datatables.net/tn/"+f),i)t.console&&console.log&&console.log(r);else{var s=u.ext,e=s.sErrMode||s.errMode;if(n&&o(n,null,"error",[n,f,r]),e=="alert")alert(r);else if(e=="throw")throw new Error(r);else typeof e=="function"&&e(n,f,r)}}function k(t,i,u,f){if(n.isArray(u)){n.each(u,function(r,u){n.isArray(u)?k(t,i,u[0],u[1]):k(t,i,u)});return}f===r&&(f=u);i[u]!==r&&(t[f]=i[u])}function nu(t,i,r){var f;for(var u in i)i.hasOwnProperty(u)&&(f=i[u],n.isPlainObject(f)?(n.isPlainObject(t[u])||(t[u]={}),n.extend(!0,t[u],f)):t[u]=r&&u!=="data"&&u!=="aaData"&&n.isArray(f)?f.slice():f);return t}function tu(t,i,r){n(t).on("click.DT",i,function(i){n(t).trigger("blur");r(i)}).on("keypress.DT",i,function(n){n.which===13&&(n.preventDefault(),r(n))}).on("selectstart.DT",function(){return!1})}function v(n,t,i,r){i&&n[t].push({fn:i,sName:r})}function o(t,i,r,u){var f=[],e;return i&&(f=n.map(t[i].slice().reverse(),function(n){return n.fn.apply(t.oInstance,u)})),r!==null&&(e=n.Event(r+".dt"),n(t.nTable).trigger(e,u),f.push(e.result)),f}function iu(n){var t=n._iDisplayStart,r=n.fnDisplayEnd(),i=n._iDisplayLength;t>=r&&(t=r-i);t-=t%i;(i===-1||t<0)&&(t=0);n._iDisplayStart=t}function ru(t,i){var r=t.renderer,f=u.ext.renderer[i];return n.isPlainObject(r)&&r[i]?f[r[i]]||f._:typeof r=="string"?f[r]||f._:f._}function y(n){return n.oFeatures.bServerSide?"ssp":n.ajax||n.sAjaxSource?"ajax":"dom"}function ii(n,t){var i=[],r=cu.numbers_length,u=Math.floor(r/2);return t<=r?i=st(0,t):n<=u?(i=st(0,r-2),i.push("ellipsis"),i.push(t-1)):n>=t-1-u?(i=st(t-(r-2),t),i.splice(0,0,"ellipsis"),i.splice(0,0,0)):(i=st(n-u+2,n+u-1),i.push("ellipsis"),i.push(t-1),i.splice(0,0,"ellipsis"),i.splice(0,0,0)),i.DT_el="span",i}function lu(t){n.each({num:function(n){return ri(n,t)},"num-fmt":function(n){return ri(n,t,ur)},"html-num":function(n){return ri(n,t,ui)},"html-num-fmt":function(n){return ri(n,t,ui,ur)}},function(n,i){c.type.order[n+t+"-pre"]=i;n.match(/^html\-/)&&(c.type.search[n+t]=c.type.search.html)})}function ve(n){return function(){var t=[gi(this[u.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));return u.ext.internal[n].apply(this,t)}}var u=function(t){var f;this.$=function(n,t){return this.api(!0).$(n,t)};this._=function(n,t){return this.api(!0).rows(n,t).data()};this.api=function(n){return n?new e(gi(this[c.iApiIndex])):new e(this)};this.fnAddData=function(t,i){var u=this.api(!0),f=n.isArray(t)&&(n.isArray(t[0])||n.isPlainObject(t[0]))?u.rows.add(t):u.row.add(t);return(i===r||i)&&u.draw(),f.flatten().toArray()};this.fnAdjustColumnSizing=function(n){var t=this.api(!0).columns.adjust(),i=t.settings()[0],u=i.oScroll;n===r||n?t.draw(!1):(u.sX!==""||u.sY!=="")&&wi(i)};this.fnClearTable=function(n){var t=this.api(!0).clear();(n===r||n)&&t.draw()};this.fnClose=function(n){this.api(!0).row(n).child.hide()};this.fnDeleteRow=function(n,t,i){var f=this.api(!0),u=f.rows(n),e=u.settings()[0],o=e.aoData[u[0][0]];return u.remove(),t&&t.call(this,e,o),(i===r||i)&&f.draw(),o};this.fnDestroy=function(n){this.api(!0).destroy(n)};this.fnDraw=function(n){this.api(!0).draw(n)};this.fnFilter=function(n,t,i,u,f,e){var o=this.api(!0);t===null||t===r?o.search(n,i,u,e):o.column(t).search(n,i,u,e);o.draw()};this.fnGetData=function(n,t){var i=this.api(!0),u;return n!==r?(u=n.nodeName?n.nodeName.toLowerCase():"",t!==r||u=="td"||u=="th"?i.cell(n,t).data():i.row(n).data()||null):i.data().toArray()};this.fnGetNodes=function(n){var t=this.api(!0);return n!==r?t.row(n).node():t.rows().nodes().flatten().toArray()};this.fnGetPosition=function(n){var r=this.api(!0),i=n.nodeName.toUpperCase(),t;return i=="TR"?r.row(n).index():i=="TD"||i=="TH"?(t=r.cell(n).index(),[t.row,t.columnVisible,t.column]):null};this.fnIsOpen=function(n){return this.api(!0).row(n).child.isShown()};this.fnOpen=function(n,t,i){return this.api(!0).row(n).child(t,i).show().child()[0]};this.fnPageChange=function(n,t){var i=this.api(!0).page(n);(t===r||t)&&i.draw(!1)};this.fnSetColumnVis=function(n,t,i){var u=this.api(!0).column(n).visible(t);(i===r||i)&&u.columns.adjust().draw()};this.fnSettings=function(){return gi(this[c.iApiIndex])};this.fnSort=function(n){this.api(!0).order(n).draw()};this.fnSortListener=function(n,t,i){this.api(!0).order.listener(n,t,i)};this.fnUpdate=function(n,t,i,u,f){var e=this.api(!0);return i===r||i===null?e.row(t).data(n):e.cell(t,i).data(n),(f===r||f)&&e.columns.adjust(),(u===r||u)&&e.draw(),0};this.fnVersionCheck=c.fnVersionCheck;var i=this,s=t===r,h=this.length;s&&(t={});this.oApi=this.internal=c.internal;for(f in u.ext.internal)f&&(this[f]=ve(f));return this.each(function(){var e=h>1?nu({},t,!0):t,c=0,w,b=this.getAttribute("id"),pt=!1,a=u.defaults,l=n(this),g,p,wt,bt,f,d,at,rt,st,ht,ut,ft,vt,ot,ct,yt;if(this.nodeName.toLowerCase()!="table"){tt(null,0,"Non-table node initialisation ("+this.nodeName+")",2);return}for(bu(a),ku(a.column),nt(a,a,!0),nt(a.column,a.column,!0),nt(a,n.extend(e,l.data()),!0),g=u.settings,c=0,w=g.length;c<w;c++){if(p=g[c],p.nTable==this||p.nTHead&&p.nTHead.parentNode==this||p.nTFoot&&p.nTFoot.parentNode==this){if(wt=e.bRetrieve!==r?e.bRetrieve:a.bRetrieve,bt=e.bDestroy!==r?e.bDestroy:a.bDestroy,s||wt)return p.oInstance;if(bt){p.oInstance.fnDestroy();break}else{tt(p,0,"Cannot reinitialise DataTable",3);return}}if(p.sTableId==this.id){g.splice(c,1);break}}if((b===null||b==="")&&(b="DataTables_Table_"+u.ext._unique++,this.id=b),f=n.extend(!0,{},u.models.oSettings,{sDestroyWidth:l[0].style.width,sInstance:b,sTableId:b}),f.nTable=this,f.oApi=i.internal,f.oInit=e,g.push(f),f.oInstance=i.length===1?i:l.dataTable(),bu(e),er(e.oLanguage),e.aLengthMenu&&!e.iDisplayLength&&(e.iDisplayLength=n.isArray(e.aLengthMenu[0])?e.aLengthMenu[0][0]:e.aLengthMenu[0]),e=nu(n.extend(!0,{},a),e),k(f.oFeatures,e,["bPaginate","bLengthChange","bFilter","bSort","bSortMulti","bInfo","bProcessing","bAutoWidth","bSortClasses","bServerSide","bDeferRender"]),k(f,e,["asStripeClasses","ajax","fnServerData","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","sAjaxSource","sAjaxDataProp","iStateDuration","sDom","bSortCellsTop","iTabIndex","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay","rowId",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]),k(f.oScroll,e,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),k(f.oLanguage,e,"fnInfoCallback"),v(f,"aoDrawCallback",e.fnDrawCallback,"user"),v(f,"aoServerParams",e.fnServerParams,"user"),v(f,"aoStateSaveParams",e.fnStateSaveParams,"user"),v(f,"aoStateLoadParams",e.fnStateLoadParams,"user"),v(f,"aoStateLoaded",e.fnStateLoaded,"user"),v(f,"aoRowCallback",e.fnRowCallback,"user"),v(f,"aoRowCreatedCallback",e.fnCreatedRow,"user"),v(f,"aoHeaderCallback",e.fnHeaderCallback,"user"),v(f,"aoFooterCallback",e.fnFooterCallback,"user"),v(f,"aoInitComplete",e.fnInitComplete,"user"),v(f,"aoPreDrawCallback",e.fnPreDrawCallback,"user"),f.rowIdFn=et(e.rowId),du(f),d=f.oClasses,n.extend(d,u.ext.classes,e.oClasses),l.addClass(d.sTable),f.iInitDisplayStart===r&&(f.iInitDisplayStart=e.iDisplayStart,f._iDisplayStart=e.iDisplayStart),e.iDeferLoading!==null&&(f.bDeferLoading=!0,at=n.isArray(e.iDeferLoading),f._iRecordsDisplay=at?e.iDeferLoading[0]:e.iDeferLoading,f._iRecordsTotal=at?e.iDeferLoading[1]:e.iDeferLoading),rt=f.oLanguage,n.extend(!0,rt,e.oLanguage),rt.sUrl&&(n.ajax({dataType:"json",url:rt.sUrl,success:function(t){er(t);nt(a.oLanguage,t);n.extend(!0,rt,t);ni(f)},error:function(){ni(f)}}),pt=!0),e.asStripeClasses===null&&(f.asStripeClasses=[d.sStripeOdd,d.sStripeEven]),st=f.asStripeClasses,ht=l.children("tbody").find("tr").eq(0),n.inArray(!0,n.map(st,function(n){return ht.hasClass(n)}))!==-1&&(n("tbody tr",this).removeClass(st.join(" ")),f.asDestroyStripes=st.slice()),ut=[],vt=this.getElementsByTagName("thead"),vt.length!==0&&(dt(f.aoHeader,vt[0]),ut=li(f)),e.aoColumns===null)for(ft=[],c=0,w=ut.length;c<w;c++)ft.push(null);else ft=e.aoColumns;for(c=0,w=ft.length;c<w;c++)or(f,ut?ut[c]:null);nf(f,e.aoColumnDefs,ft,function(n,t){ei(f,n,t)});ht.length&&(ot=function(n,t){return n.getAttribute("data-"+t)!==null?t:null},n(ht[0]).children("th, td").each(function(n,t){var e=f.aoColumns[n],i,u;e.mData===n&&(i=ot(t,"sort")||ot(t,"order"),u=ot(t,"filter")||ot(t,"search"),(i!==null||u!==null)&&(e.mData={_:n+".display",sort:i!==null?n+".@data-"+i:r,type:i!==null?n+".@data-"+i:r,filter:u!==null?n+".@data-"+u:r},ei(f,n)))}));ct=f.oFeatures;yt=function(){var s,h,i,u,t;if(e.aaSorting===r)for(s=f.aaSorting,c=0,w=s.length;c<w;c++)s[c][1]=f.aoColumns[c].asSorting[0];if(ki(f),ct.bSort&&v(f,"aoDrawCallback",function(){if(f.bSorted){var t=lt(f),i={};n.each(t,function(n,t){i[t.src]=t.dir});o(f,null,"order",[f,t,i]);ee(f)}}),v(f,"aoDrawCallback",function(){(f.bSorted||y(f)==="ssp"||ct.bDeferRender)&&ki(f)},"sc"),h=l.children("caption").each(function(){this._captionSide=n(this).css("caption-side")}),i=l.children("thead"),i.length===0&&(i=n("<thead/>").appendTo(l)),f.nTHead=i[0],u=l.children("tbody"),u.length===0&&(u=n("<tbody/>").appendTo(l)),f.nTBody=u[0],t=l.children("tfoot"),t.length===0&&h.length>0&&(f.oScroll.sX!==""||f.oScroll.sY!=="")&&(t=n("<tfoot/>").appendTo(l)),t.length===0||t.children().length===0?l.addClass(d.sNoFooter):t.length>0&&(f.nTFoot=t[0],dt(f.aoFooter,f.nTFoot)),e.aaData)for(c=0;c<e.aaData.length;c++)it(f,e.aaData[c]);else(f.bDeferLoading||y(f)=="dom")&&si(f,n(f.nTBody).children("tr"));f.aiDisplay=f.aiDisplayMaster.slice();f.bInitialised=!0;pt===!1&&ni(f)};e.bStateSave?(ct.bStateSave=!0,v(f,"aoDrawCallback",di,"state_save"),se(f,e,yt)):yt()}),i=null,this},c,e,f,s,rr={},au=/[\r\n\u2028]/g,ui=/<.*?>/g,ye=/^\d{2,4}[\.\/\-]\d{1,2}[\.\/\-]\d{1,2}([T ]{1}\d{1,2}[:\.]\d{2}([\.:]\d{2})?)?$/,pe=new RegExp("(\\/|\\.|\\*|\\+|\\?|\\||\\(|\\)|\\[|\\]|\\{|\\}|\\\\|\\$|\\^|\\-)","g"),ur=/[',$\u00a3\u20ac\u00a5%\u2009\u202F\u20BD\u20a9\u20BArfk\u0243\u039e]/gi,g=function(n){return!n||n===!0||n==="-"?!0:!1},vu=function(n){var t=parseInt(n,10);return!isNaN(t)&&isFinite(n)?t:null},yu=function(n,t){return rr[t]||(rr[t]=new RegExp(pr(t),"g")),typeof n=="string"&&t!=="."?n.replace(/\./g,"").replace(rr[t],"."):n},fr=function(n,t,i){var r=typeof n=="string";return g(n)?!0:(t&&r&&(n=yu(n,t)),i&&r&&(n=n.replace(ur,"")),!isNaN(parseFloat(n))&&isFinite(n))},we=function(n){return g(n)||typeof n=="string"},pu=function(n,t,i){if(g(n))return!0;var r=we(n);return r?fr(be(n),t,i)?!0:null:null},w=function(n,t,i){var f=[],u=0,e=n.length;if(i!==r)for(;u<e;u++)n[u]&&n[u][t]&&f.push(n[u][t][i]);else for(;u<e;u++)n[u]&&f.push(n[u][t]);return f},at=function(n,t,i,u){var e=[],f=0,o=t.length;if(u!==r)for(;f<o;f++)n[t[f]][i]&&e.push(n[t[f]][i][u]);else for(;f<o;f++)e.push(n[t[f]][i]);return e},st=function(n,t){var f=[],u,i;for(t===r?(t=0,u=n):(u=t,t=n),i=t;i<u;i++)f.push(i);return f},wu=function(n){for(var i=[],t=0,r=n.length;t<r;t++)n[t]&&i.push(n[t]);return i},be=function(n){return n.replace(ui,"")},ke=function(n){var t,r,i,u;if(n.length<2)return!0;for(t=n.slice().sort(),r=t[0],i=1,u=t.length;i<u;i++){if(t[i]===r)return!1;r=t[i]}return!0},fi=function(n){if(ke(n))return n.slice();var r=[],u,t,e=n.length,i,f=0;n:for(t=0;t<e;t++){for(u=n[t],i=0;i<f;i++)if(r[i]===u)continue n;r.push(u);f++}return r},a,ct,ft,te,bi,uu,fu,ae,cu,ri,ir;u.util={throttle:function(n,t){var u=t!==r?t:200,i,f;return function(){var t=this,e=+new Date,o=arguments;i&&e<i+u?(clearTimeout(f),f=setTimeout(function(){i=r;n.apply(t,o)},u)):(i=e,n.apply(t,o))}},escapeRegex:function(n){return n.replace(pe,"\\$1")}};a=function(n,t,i){n[t]!==r&&(n[i]=n[t])};ct=/\[.*?\]$/;ft=/\(\)$/;var pr=u.util.escapeRegex,yi=n("<div>")[0],no=yi.textContent!==r;te=/<.*?>/g;bi=u.util.throttle;var he=[],l=Array.prototype,to=function(t){var i,r,f=u.settings,e=n.map(f,function(n){return n.nTable});if(t){if(t.nTable&&t.oApi)return[t];if(t.nodeName&&t.nodeName.toLowerCase()==="table")return i=n.inArray(t,e),i!==-1?[f[i]]:null;if(t&&typeof t.settings=="function")return t.settings().toArray();typeof t=="string"?r=n(t):t instanceof n&&(r=t)}else return[];if(r)return r.map(function(){return i=n.inArray(this,e),i!==-1?f[i]:null}).toArray()};e=function(t,i){var r,f,u,o;if(!(this instanceof e))return new e(t,i);if(r=[],f=function(n){var t=to(n);t&&r.push.apply(r,t)},n.isArray(t))for(u=0,o=t.length;u<o;u++)f(t[u]);else f(t);this.context=fi(r);i&&n.merge(this,i);this.selector={rows:null,cols:null,opts:null};e.extend(this,this,he)};u.Api=e;n.extend(e.prototype,{any:function(){return this.count()!==0},concat:l.concat,context:[],count:function(){return this.flatten().length},each:function(n){for(var t=0,i=this.length;t<i;t++)n.call(this,this[t],t,this);return this},eq:function(n){var t=this.context;return t.length>n?new e(t[n],this[n]):null},filter:function(n){var i=[],t,r;if(l.filter)i=l.filter.call(this,n,this);else for(t=0,r=this.length;t<r;t++)n.call(this,this[t],t,this)&&i.push(this[t]);return new e(this.context,i)},flatten:function(){var n=[];return new e(this.context,n.concat.apply(n,this.toArray()))},join:l.join,indexOf:l.indexOf||function(n,t){for(var i=t||0,r=this.length;i<r;i++)if(this[i]===n)return i;return-1},iterator:function(n,t,i,u){var h=[],o,f,b,c,k,s=this.context,d,p,a,v=this.selector,l,w,y;for(typeof n=="string"&&(u=i,i=t,t=n,n=!1),f=0,b=s.length;f<b;f++)if(l=new e(s[f]),t==="table")o=i.call(l,s[f],f),o!==r&&h.push(o);else if(t==="columns"||t==="rows")o=i.call(l,s[f],this[f],f),o!==r&&h.push(o);else if(t==="column"||t==="column-rows"||t==="row"||t==="cell")for(p=this[f],t==="column-rows"&&(d=nr(s[f],v.opts)),c=0,k=p.length;c<k;c++)a=p[c],o=t==="cell"?i.call(l,s[f],a.row,a.column,f,c):i.call(l,s[f],a,f,c,d),o!==r&&h.push(o);return h.length||u?(w=new e(s,n?h.concat.apply([],h):h),y=w.selector,y.rows=v.rows,y.cols=v.cols,y.opts=v.opts,w):this},lastIndexOf:l.lastIndexOf||function(){return this.indexOf.apply(this.toArray.reverse(),arguments)},length:0,map:function(n){var i=[],t,r;if(l.map)i=l.map.call(this,n,this);else for(t=0,r=this.length;t<r;t++)i.push(n.call(this,this[t],t));return new e(this.context,i)},pluck:function(n){return this.map(function(t){return t[n]})},pop:l.pop,push:l.push,reduce:l.reduce||function(n,t){return gu(this,n,t,0,this.length,1)},reduceRight:l.reduceRight||function(n,t){return gu(this,n,t,this.length-1,-1,-1)},reverse:l.reverse,selector:null,shift:l.shift,slice:function(){return new e(this.context,this)},sort:l.sort,splice:l.splice,toArray:function(){return l.slice.call(this)},to$:function(){return n(this)},toJQuery:function(){return n(this)},unique:function(){return new e(this.context,fi(this))},unshift:l.unshift});e.extend=function(n,t,i){if(i.length&&t&&(t instanceof e||t.__dt_wrapper))for(var r,o=function(n,t,i){return function(){var r=t.apply(n,arguments);return e.extend(r,r,i.methodExt),r}},u=0,f=i.length;u<f;u++)r=i[u],t[r.name]=r.type==="function"?o(n,r.val,r):r.type==="object"?{}:r.val,t[r.name].__dt_wrapper=!0,e.extend(n,t[r.name],r.propExt)};e.register=f=function(t,i){var f,a,r;if(n.isArray(t)){for(f=0,a=t.length;f<a;f++)e.register(t[f],i);return}for(var o=t.split("."),h=he,c,l,v=function(n,t){for(var i=0,r=n.length;i<r;i++)if(n[i].name===t)return n[i];return null},u=0,s=o.length;u<s;u++)l=o[u].indexOf("()")!==-1,c=l?o[u].replace("()",""):o[u],r=v(h,c),r||(r={name:c,val:{},methodExt:[],propExt:[],type:"object"},h.push(r)),u===s-1?(r.val=i,r.type=typeof i=="function"?"function":n.isPlainObject(i)?"object":"other"):h=l?r.methodExt:r.propExt};e.registerPlural=s=function(t,i,u){e.register(t,u);e.register(i,function(){var t=u.apply(this,arguments);return t===this?this:t instanceof e?t.length?n.isArray(t[0])?new e(t.context,t[0]):t[0]:r:t})};uu=function(t,i){if(n.isArray(t))return n.map(t,function(n){return uu(n,i)});if(typeof t=="number")return[i[t]];var r=n.map(i,function(n){return n.nTable});return n(r).filter(t).map(function(){var t=n.inArray(this,r);return i[t]}).toArray()};f("tables()",function(n){return n!==r&&n!==null?new e(uu(n,this.context)):this});f("table()",function(n){var t=this.tables(n),i=t.context;return i.length?new e(i[0]):t});s("tables().nodes()","table().node()",function(){return this.iterator("table",function(n){return n.nTable},1)});s("tables().body()","table().body()",function(){return this.iterator("table",function(n){return n.nTBody},1)});s("tables().header()","table().header()",function(){return this.iterator("table",function(n){return n.nTHead},1)});s("tables().footer()","table().footer()",function(){return this.iterator("table",function(n){return n.nTFoot},1)});s("tables().containers()","table().container()",function(){return this.iterator("table",function(n){return n.nTableWrapper},1)});f("draw()",function(n){return this.iterator("table",function(t){n==="page"?ut(t):(typeof n=="string"&&(n=n==="full-hold"?!1:!0),ot(t,n===!1))})});f("page()",function(n){return n===r?this.page.info().page:this.iterator("table",function(t){br(t,n)})});f("page.info()",function(){if(this.context.length===0)return r;var n=this.context[0],i=n._iDisplayStart,t=n.oFeatures.bPaginate?n._iDisplayLength:-1,u=n.fnRecordsDisplay(),f=t===-1;return{page:f?0:Math.floor(i/t),pages:f?1:Math.ceil(u/t),start:i,end:n.fnDisplayEnd(),length:t,recordsTotal:n.fnRecordsTotal(),recordsDisplay:u,serverSide:y(n)==="ssp"}});f("page.len()",function(n){return n===r?this.context.length!==0?this.context[0]._iDisplayLength:r:this.iterator("table",function(t){wr(t,n)})});fu=function(n,t,i){var u,r;if(i){u=new e(n);u.one("draw",function(){i(u.ajax.json())})}y(n)=="ssp"?ot(n,t):(b(n,!0),r=n.jqXHR,r&&r.readyState!==4&&r.abort(),ai(n,[],function(i){var u,r,f;for(hi(n),u=vi(n,i),r=0,f=u.length;r<f;r++)it(n,u[r]);ot(n,t);b(n,!1)}))};f("ajax.json()",function(){var n=this.context;if(n.length>0)return n[0].json});f("ajax.params()",function(){var n=this.context;if(n.length>0)return n[0].oAjaxData});f("ajax.reload()",function(n,t){return this.iterator("table",function(i){fu(i,t===!1,n)})});f("ajax.url()",function(t){var i=this.context;return t===r?i.length===0?r:(i=i[0],i.ajax?n.isPlainObject(i.ajax)?i.ajax.url:i.ajax:i.sAjaxSource):this.iterator("table",function(i){n.isPlainObject(i.ajax)?i.ajax.url=t:i.ajax=t})});f("ajax.url().load()",function(n,t){return this.iterator("table",function(i){fu(i,t===!1,n)})});var eu=function(t,i,u,f,e){var h=[],a,l,o,v,s,p,w=typeof i,y;for(i&&w!=="string"&&w!=="function"&&i.length!==r||(i=[i]),o=0,v=i.length;o<v;o++)for(l=i[o]&&i[o].split&&!i[o].match(/[\[\(:]/)?i[o].split(","):[i[o]],s=0,p=l.length;s<p;s++)a=u(typeof l[s]=="string"?n.trim(l[s]):l[s]),a&&a.length&&(h=h.concat(a));if(y=c.selector[t],y.length)for(o=0,v=y.length;o<v;o++)h=y[o](f,e,h);return fi(h)},ou=function(t){return t||(t={}),t.filter&&t.search===r&&(t.search=t.filter),n.extend({search:"none",order:"current",page:"all"},t)},su=function(n){for(var t=0,i=n.length;t<i;t++)if(n[t].length>0)return n[0]=n[t],n[0].length=1,n.length=1,n.context=[n.context[t]],n;return n.length=0,n},nr=function(t,i){var h,u=[],o=t.aiDisplay,c=t.aiDisplayMaster,f=i.search,s=i.order,a=i.page,l,r,e;if(y(t)=="ssp")return f==="removed"?[]:st(0,c.length);if(a=="current")for(r=t._iDisplayStart,e=t.fnDisplayEnd();r<e;r++)u.push(o[r]);else if(s=="current"||s=="applied"){if(f=="none")u=c.slice();else if(f=="applied")u=o.slice();else if(f=="removed"){for(l={},r=0,e=o.length;r<e;r++)l[o[r]]=null;u=n.map(c,function(n){return l.hasOwnProperty(n)?null:n})}}else if(s=="index"||s=="original")for(r=0,e=t.aoData.length;r<e;r++)f=="none"?u.push(r):(h=n.inArray(r,o),(h===-1&&f=="removed"||h>=0&&f=="applied")&&u.push(r));return u},io=function(t,i,u){var f,e=function(i){var e=vu(i),o=t.aoData,s,h,c,l,a;return e!==null&&!u?[e]:(f||(f=nr(t,u)),e!==null&&n.inArray(e,f)!==-1)?[e]:i===null||i===r||i===""?f:typeof i=="function"?n.map(f,function(n){var t=o[n];return i(n,t._aData,t.nTr)?n:null}):i.nodeName?(s=i._DT_RowIndex,h=i._DT_CellIndex,s!==r?o[s]&&o[s].nTr===i?[s]:[]:h?o[h.row]&&o[h.row].nTr===i.parentNode?[h.row]:[]:(c=n(i).closest("*[data-dt-row]"),c.length?[c.data("dt-row")]:[])):typeof i=="string"&&i.charAt(0)==="#"&&(l=t.aIds[i.replace(/^#/,"")],l!==r)?[l.idx]:(a=wu(at(t.aoData,f,"nTr")),n(a).filter(i).map(function(){return this._DT_RowIndex}).toArray())};return eu("row",i,e,t,u)};f("rows()",function(t,i){t===r?t="":n.isPlainObject(t)&&(i=t,t="");i=ou(i);var u=this.iterator("table",function(n){return io(n,t,i)},1);return u.selector.rows=t,u.selector.opts=i,u});f("rows().nodes()",function(){return this.iterator("row",function(n,t){return n.aoData[t].nTr||r},1)});f("rows().data()",function(){return this.iterator(!0,"rows",function(n,t){return at(n.aoData,t,"_aData")},1)});s("rows().cache()","row().cache()",function(n){return this.iterator("row",function(t,i){var r=t.aoData[i];return n==="search"?r._aFilterData:r._aSortData},1)});s("rows().invalidate()","row().invalidate()",function(n){return this.iterator("row",function(t,i){bt(t,i,n)})});s("rows().indexes()","row().index()",function(){return this.iterator("row",function(n,t){return t},1)});s("rows().ids()","row().id()",function(n){for(var r,f,o,u=[],i=this.context,t=0,s=i.length;t<s;t++)for(r=0,f=this[t].length;r<f;r++)o=i[t].rowIdFn(i[t].aoData[this[t][r]]._aData),u.push((n===!0?"#":"")+o);return new e(i,u)});s("rows().remove()","row().remove()",function(){var n=this;return this.iterator("row",function(t,i,u){var e=t.aoData,v=e[i],f,l,o,a,s,h,c;for(e.splice(i,1),f=0,l=e.length;f<l;f++)if(s=e[f],h=s.anCells,s.nTr!==null&&(s.nTr._DT_RowIndex=f),h!==null)for(o=0,a=h.length;o<a;o++)h[o]._DT_CellIndex.row=f;ci(t.aiDisplayMaster,i);ci(t.aiDisplay,i);ci(n[u],i,!1);t._iRecordsDisplay>0&&t._iRecordsDisplay--;iu(t);c=t.rowIdFn(v._aData);c!==r&&delete t.aIds[c]}),this.iterator("table",function(n){for(var t=0,i=n.aoData.length;t<i;t++)n.aoData[t].idx=t}),this});f("rows.add()",function(t){var r=this.iterator("table",function(n){for(var i,u=[],r=0,f=t.length;r<f;r++)i=t[r],i.nodeName&&i.nodeName.toUpperCase()==="TR"?u.push(si(n,i)[0]):u.push(it(n,i));return u},1),i=this.rows(-1);return i.pop(),n.merge(i,r),i});f("row()",function(n,t){return su(this.rows(n,t))});f("row().data()",function(t){var i=this.context,u;return t===r?i.length&&this.length?i[0].aoData[this[0]]._aData:r:(u=i[0].aoData[this[0]],u._aData=t,n.isArray(t)&&u.nTr&&u.nTr.id&&rt(i[0].rowId)(t,u.nTr.id),bt(i[0],this[0],"data"),this)});f("row().node()",function(){var n=this.context;return n.length&&this.length?n[0].aoData[this[0]].nTr||null:null});f("row.add()",function(t){t instanceof n&&t.length&&(t=t[0]);var i=this.iterator("table",function(n){return t.nodeName&&t.nodeName.toUpperCase()==="TR"?si(n,t)[0]:it(n,t)});return this.row(i[0])});var ro=function(t,i,r,u){var f=[],e=function(i,r){var u,s,o;if(n.isArray(i)||i instanceof n){for(u=0,s=i.length;u<s;u++)e(i[u],r);return}i.nodeName&&i.nodeName.toLowerCase()==="tr"?f.push(i):(o=n("<tr><td/><\/tr>").addClass(r),n("td",o).addClass(r).html(i)[0].colSpan=ht(t),f.push(o[0]))};e(r,u);i._details&&i._details.detach();i._details=n(f);i._detailsShow&&i._details.insertAfter(i.nTr)},hu=function(n,t){var u=n.context,i;u.length&&(i=u[0].aoData[t!==r?t:n[0]],i&&i._details&&(i._details.remove(),i._detailsShow=r,i._details=r))},ce=function(n,t){var r=n.context,i;r.length&&n.length&&(i=r[0].aoData[n[0]],i._details&&(i._detailsShow=t,t?i._details.insertAfter(i.nTr):i._details.detach(),uo(r[0])))},uo=function(n){var t=new e(n),r=".dt.DT_details",u="draw"+r,f="column-visibility"+r,o="destroy"+r,i=n.aoData;if(t.off(u+" "+f+" "+o),w(i,"_details").length>0){t.on(u,function(r,u){n===u&&t.rows({page:"current"}).eq(0).each(function(n){var t=i[n];t._detailsShow&&t._details.insertAfter(t.nTr)})});t.on(f,function(t,r){var f,e,u,o;if(n===r)for(e=ht(r),u=0,o=i.length;u<o;u++)f=i[u],f._details&&f._details.children("td[colspan]").attr("colspan",e)});t.on(o,function(r,u){if(n===u)for(var f=0,e=i.length;f<e;f++)i[f]._details&&hu(t,f)})}},ti="row().child",tr=ti+"()";f(tr,function(n,t){var i=this.context;return n===r?i.length&&this.length?i[0].aoData[this[0]]._details:r:(n===!0?this.child.show():n===!1?hu(this):i.length&&this.length&&ro(i[0],i[0].aoData[this[0]],n,t),this)});f([ti+".show()",tr+".show()"],function(){return ce(this,!0),this});f([ti+".hide()",tr+".hide()"],function(){return ce(this,!1),this});f([ti+".remove()",tr+".remove()"],function(){return hu(this),this});f(ti+".isShown()",function(){var n=this.context;return n.length&&this.length?n[0].aoData[this[0]]._detailsShow||!1:!1});var fo=/^([^:]+):(name|visIdx|visible)$/,le=function(n,t,i,r,u){for(var e=[],f=0,o=u.length;f<o;f++)e.push(p(n,u[f],t));return e},eo=function(t,i,r){var u=t.aoColumns,e=w(u,"sName"),f=w(u,"nTh"),o=function(i){var s=vu(i),v,o,h,c,l,a;if(i==="")return st(u.length);if(s!==null)return[s>=0?s:u.length+s];if(typeof i=="function")return v=nr(t,r),n.map(u,function(n,r){return i(r,le(t,r,0,0,v),f[r])?r:null});if(o=typeof i=="string"?i.match(fo):"",o)switch(o[2]){case"visIdx":case"visible":return(h=parseInt(o[1],10),h<0)?(c=n.map(u,function(n,t){return n.bVisible?t:null}),[c[c.length+h]]):[pt(t,h)];case"name":return n.map(e,function(n,t){return n===o[1]?t:null});default:return[]}return i.nodeName&&i._DT_CellIndex?[i._DT_CellIndex.column]:(l=n(f).filter(i).map(function(){return n.inArray(this,f)}).toArray(),l.length||!i.nodeName)?l:(a=n(i).closest("*[data-dt-column]"),a.length?[a.data("dt-column")]:[])};return eu("column",i,o,t,r)},oo=function(t,i,u){var c=t.aoColumns,e=c[i],o=t.aoData,s,f,l,h,a;if(u===r)return e.bVisible;if(e.bVisible!==u){if(u)for(a=n.inArray(!0,w(c,"bVisible"),i+1),f=0,l=o.length;f<l;f++)h=o[f].nTr,s=o[f].anCells,h&&h.insertBefore(s[i],s[a]||null);else n(w(t.aoData,"anCells",i)).detach();e.bVisible=u}};return f("columns()",function(t,i){t===r?t="":n.isPlainObject(t)&&(i=t,t="");i=ou(i);var u=this.iterator("table",function(n){return eo(n,t,i)},1);return u.selector.cols=t,u.selector.opts=i,u}),s("columns().header()","column().header()",function(){return this.iterator("column",function(n,t){return n.aoColumns[t].nTh},1)}),s("columns().footer()","column().footer()",function(){return this.iterator("column",function(n,t){return n.aoColumns[t].nTf},1)}),s("columns().data()","column().data()",function(){return this.iterator("column-rows",le,1)}),s("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(n,t){return n.aoColumns[t].mData},1)}),s("columns().cache()","column().cache()",function(n){return this.iterator("column-rows",function(t,i,r,u,f){return at(t.aoData,f,n==="search"?"_aFilterData":"_aSortData",i)},1)}),s("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(n,t,i,r,u){return at(n.aoData,u,"anCells",t)},1)}),s("columns().visible()","column().visible()",function(t,i){var u=this,f=this.iterator("column",function(n,i){if(t===r)return n.aoColumns[i].bVisible;oo(n,i,t)});return t!==r&&this.iterator("table",function(f){kt(f,f.aoHeader);kt(f,f.aoFooter);f.aiDisplay.length||n(f.nTBody).find("td[colspan]").attr("colspan",ht(f));di(f);u.iterator("column",function(n,r){o(n,null,"column-visibility",[n,r,t,i])});(i===r||i)&&u.columns.adjust()}),f}),s("columns().indexes()","column().index()",function(n){return this.iterator("column",function(t,i){return n==="visible"?wt(t,i):i},1)}),f("columns.adjust()",function(){return this.iterator("table",function(n){yt(n)},1)}),f("column.index()",function(n,t){if(this.context.length!==0){var i=this.context[0];if(n==="fromVisible"||n==="toData")return pt(i,t);if(n==="fromData"||n==="toVisible")return wt(i,t)}}),f("column()",function(n,t){return su(this.columns(n,t))}),ae=function(t,i,u){var a=t.aoData,o=nr(t,u),y=wu(at(a,o,"anCells")),w=n([].concat.apply([],y)),s,b=t.aoColumns.length,h,c,v,e,l,f,k=function(i){var y=typeof i=="function",u;if(i===null||i===r||y){for(h=[],c=0,v=o.length;c<v;c++)for(s=o[c],e=0;e<b;e++)l={row:s,column:e},y?(f=a[s],i(l,p(t,s,e),f.anCells?f.anCells[e]:null)&&h.push(l)):h.push(l);return h}return n.isPlainObject(i)?i.column!==r&&i.row!==r&&n.inArray(i.row,o)!==-1?[i]:[]:(u=w.filter(i).map(function(n,t){return{row:t._DT_CellIndex.row,column:t._DT_CellIndex.column}}).toArray(),u.length||!i.nodeName)?u:(f=n(i).closest("*[data-dt-row]"),f.length?[{row:f.data("dt-row"),column:f.data("dt-column")}]:[])};return eu("cell",i,k,t,u)},f("cells()",function(t,i,u){if(n.isPlainObject(t)&&(t.row===r?(u=t,t=null):(u=i,i=null)),n.isPlainObject(i)&&(u=i,i=null),i===null||i===r)return this.iterator("table",function(n){return ae(n,t,ou(u))});var o=u?{page:u.page,order:u.order,search:u.search}:{},s=this.columns(i,o),h=this.rows(t,o),f,c,e,l,a=this.iterator("table",function(n,t){var i=[];for(f=0,c=h[t].length;f<c;f++)for(e=0,l=s[t].length;e<l;e++)i.push({row:h[t][f],column:s[t][e]});return i},1),v=u&&u.selected?this.cells(a,u):a;return n.extend(v.selector,{cols:i,rows:t,opts:u}),v}),s("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(n,t,i){var u=n.aoData[t];return u&&u.anCells?u.anCells[i]:r},1)}),f("cells().data()",function(){return this.iterator("cell",function(n,t,i){return p(n,t,i)},1)}),s("cells().cache()","cell().cache()",function(n){return n=n==="search"?"_aFilterData":"_aSortData",this.iterator("cell",function(t,i,r){return t.aoData[i][n][r]},1)}),s("cells().render()","cell().render()",function(n){return this.iterator("cell",function(t,i,r){return p(t,i,r,n)},1)}),s("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(n,t,i){return{row:t,column:i,columnVisible:wt(n,i)}},1)}),s("cells().invalidate()","cell().invalidate()",function(n){return this.iterator("cell",function(t,i,r){bt(t,i,n,r)})}),f("cell()",function(n,t,i){return su(this.cells(n,t,i))}),f("cell().data()",function(n){var i=this.context,t=this[0];return n===r?i.length&&t.length?p(i[0],t[0].row,t[0].column):r:(tf(i[0],t[0].row,t[0].column,n),bt(i[0],t[0].row,"data",t[0].column),this)}),f("order()",function(t,i){var u=this.context;return t===r?u.length!==0?u[0].aaSorting:r:(typeof t=="number"?t=[[t,i]]:t.length&&!n.isArray(t[0])&&(t=Array.prototype.slice.call(arguments)),this.iterator("table",function(n){n.aaSorting=t.slice()}))}),f("order.listener()",function(n,t,i){return this.iterator("table",function(r){gr(r,n,t,i)})}),f("order.fixed()",function(t){if(!t){var u=this.context,i=u.length?u[0].aaSortingFixed:r;return n.isArray(i)?{pre:i}:i}return this.iterator("table",function(i){i.aaSortingFixed=n.extend(!0,{},t)})}),f(["columns().order()","column().order()"],function(t){var i=this;return this.iterator("table",function(r,u){var f=[];n.each(i[u],function(n,i){f.push([i,t])});r.aaSorting=f})}),f("search()",function(t,i,u,f){var e=this.context;return t===r?e.length!==0?e[0].oPreviousSearch.sSearch:r:this.iterator("table",function(r){r.oFeatures.bFilter&&gt(r,n.extend({},r.oPreviousSearch,{sSearch:t+"",bRegex:i===null?!1:i,bSmart:u===null?!0:u,bCaseInsensitive:f===null?!0:f}),1)})}),s("columns().search()","column().search()",function(t,i,u,f){return this.iterator("column",function(e,o){var s=e.aoPreSearchCols;if(t===r)return s[o].sSearch;e.oFeatures.bFilter&&(n.extend(s[o],{sSearch:t+"",bRegex:i===null?!1:i,bSmart:u===null?!0:u,bCaseInsensitive:f===null?!0:f}),gt(e,e.oPreviousSearch,1))})}),f("state()",function(){return this.context.length?this.context[0].oSavedState:null}),f("state.clear()",function(){return this.iterator("table",function(n){n.fnStateSaveCallback.call(n.oInstance,n,{})})}),f("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null}),f("state.save()",function(){return this.iterator("table",function(n){di(n)})}),u.versionCheck=u.fnVersionCheck=function(n){for(var e=u.version.split("."),f=n.split("."),i,r,t=0,o=f.length;t<o;t++)if(i=parseInt(e[t],10)||0,r=parseInt(f[t],10)||0,i!==r)return i>r;return!0},u.isDataTable=u.fnIsDataTable=function(t){var i=n(t).get(0),r=!1;return t instanceof u.Api?!0:(n.each(u.settings,function(t,u){var f=u.nScrollHead?n("table",u.nScrollHead)[0]:null,e=u.nScrollFoot?n("table",u.nScrollFoot)[0]:null;(u.nTable===i||f===i||e===i)&&(r=!0)}),r)},u.tables=u.fnTables=function(t){var r=!1,i;return n.isPlainObject(t)&&(r=t.api,t=t.visible),i=n.map(u.settings,function(i){if(!t||t&&n(i.nTable).is(":visible"))return i.nTable}),r?new e(i):i},u.camelToHungarian=nt,f("$()",function(t,i){var u=this.rows(i).nodes(),r=n(u);return n([].concat(r.filter(t).toArray(),r.find(t).toArray()))}),n.each(["on","one","off"],function(t,i){f(i+"()",function(){var t=Array.prototype.slice.call(arguments),r;return t[0]=n.map(t[0].split(/\s/),function(n){return n.match(/\.dt\b/)?n:n+".dt"}).join(" "),r=n(this.tables().nodes()),r[i].apply(r,t),this})}),f("clear()",function(){return this.iterator("table",function(n){hi(n)})}),f("settings()",function(){return new e(this.context,this.context)}),f("init()",function(){var n=this.context;return n.length?n[0].oInit:null}),f("data()",function(){return this.iterator("table",function(n){return w(n.aoData,"_aData")}).flatten()}),f("destroy()",function(i){return i=i||!1,this.iterator("table",function(r){var w=r.nTableWrapper.parentNode,s=r.oClasses,h=r.nTable,d=r.nTBody,c=r.nTHead,l=r.nTFoot,f=n(h),a=n(d),b=n(r.nTableWrapper),k=n.map(r.aoData,function(n){return n.nTr}),v,y,p;r.bDestroying=!0;o(r,"aoDestroyCallback","destroy",[r]);i||new e(r).columns().visible(!0);b.off(".DT").find(":not(tbody *)").off(".DT");n(t).off(".DT-"+r.sInstance);h!=c.parentNode&&(f.children("thead").detach(),f.append(c));l&&h!=l.parentNode&&(f.children("tfoot").detach(),f.append(l));r.aaSorting=[];r.aaSortingFixed=[];ki(r);n(k).removeClass(r.asStripeClasses.join(" "));n("th, td",c).removeClass(s.sSortable+" "+s.sSortableAsc+" "+s.sSortableDesc+" "+s.sSortableNone);a.children().detach();a.append(k);y=i?"remove":"detach";f[y]();b[y]();!i&&w&&(w.insertBefore(h,r.nTableReinsertBefore),f.css("width",r.sDestroyWidth).removeClass(s.sTable),v=r.asDestroyStripes.length,v&&a.children().each(function(t){n(this).addClass(r.asDestroyStripes[t%v])}));p=n.inArray(r,u.settings);p!==-1&&u.settings.splice(p,1)})}),n.each(["column","row","cell"],function(n,t){f(t+"s().every()",function(n){var i=this.selector.opts,u=this;return this.iterator(t,function(f,e,o,s,h){n.call(u[t](e,t==="cell"?o:i,t==="cell"?i:r),e,o,s,h)})})}),f("i18n()",function(t,i,u){var e=this.context[0],f=et(t)(e.oLanguage);return f===r&&(f=i),u!==r&&n.isPlainObject(f)&&(f=f[u]!==r?f[u]:f._),f.replace("%d",u)}),u.version="1.10.21",u.settings=[],u.models={},u.models.oSearch={bCaseInsensitive:!0,sSearch:"",bRegex:!1,bSmart:!0},u.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,_sRowStripe:"",src:null,idx:-1},u.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,nTh:null,nTf:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sSortingClassJUI:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null},u.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],asStripeClasses:null,bAutoWidth:!0,bDeferRender:!1,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:!1,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(n){return n.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnServerData:null,fnServerParams:null,fnStateLoadCallback:function(n){try{return JSON.parse((n.iStateDuration===-1?sessionStorage:localStorage).getItem("DataTables_"+n.sInstance+"_"+location.pathname))}catch(t){return{}}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(n,t){try{(n.iStateDuration===-1?sessionStorage:localStorage).setItem("DataTables_"+n.sInstance+"_"+location.pathname,JSON.stringify(t))}catch(i){}},fnStateSaveParams:null,iStateDuration:7200,iDeferLoading:null,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{sSortAscending:": activate to sort column ascending",sSortDescending:": activate to sort column descending"},oPaginate:{sFirst:"First",sLast:"Last",sNext:"Next",sPrevious:"Previous"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ entries",sInfoEmpty:"Showing 0 to 0 of 0 entries",sInfoFiltered:"(filtered from _MAX_ total entries)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"Show _MENU_ entries",sLoadingRecords:"Loading...",sProcessing:"Processing...",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:n.extend({},u.models.oSearch),sAjaxDataProp:"data",sAjaxSource:null,sDom:"lfrtip",searchDelay:null,sPaginationType:"simple_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId"},vt(u.defaults),u.defaults.column={aDataSort:null,iDataSort:-1,asSorting:["asc","desc"],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null},vt(u.defaults.column),u.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:null,bLengthChange:null,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollOversize:!1,bScrollbarLeft:!1,bBounding:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],asStripeClasses:null,asDestroyStripes:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bDeferLoading:!1,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,sAjaxSource:null,sAjaxDataProp:null,bAjaxDataGet:!0,jqXHR:null,json:r,oAjaxData:r,fnServerData:null,aoServerParams:[],sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return y(this)=="ssp"?this._iRecordsTotal*1:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return y(this)=="ssp"?this._iRecordsDisplay*1:this.aiDisplay.length},fnDisplayEnd:function(){var n=this._iDisplayLength,t=this._iDisplayStart,r=t+n,i=this.aiDisplay.length,u=this.oFeatures,f=u.bPaginate;return u.bServerSide?f===!1||n===-1?t+i:Math.min(t+n,this._iRecordsDisplay):!f||r>i||n===-1?i:r},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null},u.ext=c={buttons:{},classes:{},builder:"-source-",errMode:"alert",feature:[],search:[],selector:{cell:[],column:[],row:[]},internal:{},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{detect:[],search:{},order:{}},_unique:0,fnVersionCheck:u.fnVersionCheck,iApiIndex:0,oJUIClasses:{},sVersion:u.version},n.extend(c,{afnFiltering:c.search,aTypes:c.type.detect,ofnSearch:c.type.search,oSort:c.type.order,afnSortData:c.order,aoFeatures:c.feature,oApi:c.internal,oStdClasses:c.classes,oPagination:c.pager}),n.extend(u.ext.classes,{sTable:"dataTable",sNoFooter:"no-footer",sPageButton:"paginate_button",sPageButtonActive:"current",sPageButtonDisabled:"disabled",sStripeOdd:"odd",sStripeEven:"even",sRowEmpty:"dataTables_empty",sWrapper:"dataTables_wrapper",sFilter:"dataTables_filter",sInfo:"dataTables_info",sPaging:"dataTables_paginate paging_",sLength:"dataTables_length",sProcessing:"dataTables_processing",sSortAsc:"sorting_asc",sSortDesc:"sorting_desc",sSortable:"sorting",sSortableAsc:"sorting_asc_disabled",sSortableDesc:"sorting_desc_disabled",sSortableNone:"sorting_disabled",sSortColumn:"sorting_",sFilterInput:"",sLengthSelect:"",sScrollWrapper:"dataTables_scroll",sScrollHead:"dataTables_scrollHead",sScrollHeadInner:"dataTables_scrollHeadInner",sScrollBody:"dataTables_scrollBody",sScrollFoot:"dataTables_scrollFoot",sScrollFootInner:"dataTables_scrollFootInner",sHeaderTH:"",sFooterTH:"",sSortJUIAsc:"",sSortJUIDesc:"",sSortJUI:"",sSortJUIAscAllowed:"",sSortJUIDescAllowed:"",sSortJUIWrapper:"",sSortIcon:"",sJUIHeader:"",sJUIFooter:""}),cu=u.ext.pager,n.extend(cu,{simple:function(){return["previous","next"]},full:function(){return["first","previous","next","last"]},numbers:function(n,t){return[ii(n,t)]},simple_numbers:function(n,t){return["previous",ii(n,t),"next"]},full_numbers:function(n,t){return["first","previous",ii(n,t),"next","last"]},first_last_numbers:function(n,t){return["first",ii(n,t),"last"]},_numbers:ii,numbers_length:7}),n.extend(!0,u.ext.renderer,{pageButton:{_:function(t,u,f,e,o,s){var a=t.oClasses,l=t.oLanguage.oPaginate,w=t.oLanguage.oAria.paginate||{},h,c,y=0,p=function(i,r){for(var d,u,e,b=a.sPageButtonDisabled,nt=function(n){br(t,n.data.action,!0)},g,v=0,k=r.length;v<k;v++)if(u=r[v],n.isArray(u))g=n("<"+(u.DT_el||"div")+"/>").appendTo(i),p(g,u);else{h=null;c=u;e=t.iTabIndex;switch(u){case"ellipsis":i.append('<span class="ellipsis">&#x2026;<\/span>');break;case"first":h=l.sFirst;o===0&&(e=-1,c+=" "+b);break;case"previous":h=l.sPrevious;o===0&&(e=-1,c+=" "+b);break;case"next":h=l.sNext;(s===0||o===s-1)&&(e=-1,c+=" "+b);break;case"last":h=l.sLast;o===s-1&&(e=-1,c+=" "+b);break;default:h=u+1;c=o===u?a.sPageButtonActive:""}h!==null&&(d=n("<a>",{"class":a.sPageButton+" "+c,"aria-controls":t.sTableId,"aria-label":w[u],"data-dt-idx":y,tabindex:e,id:f===0&&typeof u=="string"?t.sTableId+"_"+u:null}).html(h).appendTo(i),tu(d,{action:u},nt),y++)}},v;try{v=n(u).find(i.activeElement).data("dt-idx")}catch(b){}p(n(u).empty(),e);v!==r&&n(u).find("[data-dt-idx="+v+"]").trigger("focus")}}}),n.extend(u.ext.type.detect,[function(n,t){var i=t.oLanguage.sDecimal;return fr(n,i)?"num"+i:null},function(n){if(n&&!(n instanceof Date)&&!ye.test(n))return null;var t=Date.parse(n);return t!==null&&!isNaN(t)||g(n)?"date":null},function(n,t){var i=t.oLanguage.sDecimal;return fr(n,i,!0)?"num-fmt"+i:null},function(n,t){var i=t.oLanguage.sDecimal;return pu(n,i)?"html-num"+i:null},function(n,t){var i=t.oLanguage.sDecimal;return pu(n,i,!0)?"html-num-fmt"+i:null},function(n){return g(n)||typeof n=="string"&&n.indexOf("<")!==-1?"html":null}]),n.extend(u.ext.type.search,{html:function(n){return g(n)?n:typeof n=="string"?n.replace(au," ").replace(ui,""):""},string:function(n){return g(n)?n:typeof n=="string"?n.replace(au," "):n}}),ri=function(n,t,i,r){return n!==0&&(!n||n==="-")?-Infinity:(t&&(n=yu(n,t)),n.replace&&(i&&(n=n.replace(i,"")),r&&(n=n.replace(r,""))),n*1)},n.extend(c.type.order,{"date-pre":function(n){var t=Date.parse(n);return isNaN(t)?-Infinity:t},"html-pre":function(n){return g(n)?"":n.replace?n.replace(/<.*?>/g,"").toLowerCase():n+""},"string-pre":function(n){return g(n)?"":typeof n=="string"?n.toLowerCase():n.toString?n.toString():""},"string-asc":function(n,t){return n<t?-1:n>t?1:0},"string-desc":function(n,t){return n<t?1:n>t?-1:0}}),lu(""),n.extend(!0,u.ext.renderer,{header:{_:function(t,i,r,u){n(t.nTable).on("order.dt.DT",function(n,f,e,o){if(t===f){var s=r.idx;i.removeClass(r.sSortingClass+" "+u.sSortAsc+" "+u.sSortDesc).addClass(o[s]=="asc"?u.sSortAsc:o[s]=="desc"?u.sSortDesc:r.sSortingClass)}})},jqueryui:function(t,i,r,u){n("<div/>").addClass(u.sSortJUIWrapper).append(i.contents()).append(n("<span/>").addClass(u.sSortIcon+" "+r.sSortingClassJUI)).appendTo(i);n(t.nTable).on("order.dt.DT",function(n,f,e,o){if(t===f){var s=r.idx;i.removeClass(u.sSortAsc+" "+u.sSortDesc).addClass(o[s]=="asc"?u.sSortAsc:o[s]=="desc"?u.sSortDesc:r.sSortingClass);i.find("span."+u.sSortIcon).removeClass(u.sSortJUIAsc+" "+u.sSortJUIDesc+" "+u.sSortJUI+" "+u.sSortJUIAscAllowed+" "+u.sSortJUIDescAllowed).addClass(o[s]=="asc"?u.sSortJUIAsc:o[s]=="desc"?u.sSortJUIDesc:r.sSortingClassJUI)}})}}}),ir=function(n){return typeof n=="string"?n.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):n},u.render={number:function(n,t,i,r,u){return{display:function(f){var s,e,o,h;return typeof f!="number"&&typeof f!="string"?f:(s=f<0?"-":"",e=parseFloat(f),isNaN(e))?ir(f):(e=e.toFixed(i),f=Math.abs(e),o=parseInt(f,10),h=i?t+(f-o).toFixed(i).substring(2):"",s+(r||"")+o.toString().replace(/\B(?=(\d{3})+(?!\d))/g,n)+h+(u||""))}}},text:function(){return{display:ir,filter:ir}}},n.extend(u.ext.internal,{_fnExternApiFunc:ve,_fnBuildAjax:ai,_fnAjaxUpdate:ff,_fnAjaxParameters:ef,_fnAjaxUpdateDraw:of,_fnAjaxDataSrc:vi,_fnAddColumn:or,_fnColumnOptions:ei,_fnAdjustColumnSizing:yt,_fnVisibleToColumnIndex:pt,_fnColumnIndexToVisible:wt,_fnVisbleColumns:ht,_fnGetColumns:oi,_fnColumnTypes:sr,_fnApplyColumnDefs:nf,_fnHungarianMap:vt,_fnCamelToHungarian:nt,_fnLanguageCompat:er,_fnBrowserDetect:du,_fnAddData:it,_fnAddTr:si,_fnNodeToDataIndex:de,_fnNodeToColumnIndex:ge,_fnGetCellData:p,_fnSetCellData:tf,_fnSplitObjNotation:hr,_fnGetObjectDataFn:et,_fnSetObjectDataFn:rt,_fnGetDataMaster:cr,_fnClearTable:hi,_fnDeleteIndex:ci,_fnInvalidate:bt,_fnGetRowElements:lr,_fnCreateTr:ar,_fnBuildHead:rf,_fnDrawHead:kt,_fnDraw:ut,_fnReDraw:ot,_fnAddOptionsHtml:uf,_fnDetectHeader:dt,_fnGetUniqueThs:li,_fnFeatureHtmlFilter:sf,_fnFilterComplete:gt,_fnFilterCustom:hf,_fnFilterColumn:cf,_fnFilter:lf,_fnFilterCreateSearch:yr,_fnEscapeRegex:pr,_fnFilterData:af,_fnFeatureHtmlInfo:pf,_fnUpdateInfo:wf,_fnInfoMacros:bf,_fnInitialise:ni,_fnInitComplete:pi,_fnLengthChange:wr,_fnFeatureHtmlLength:kf,_fnFeatureHtmlPaginate:df,_fnPageChange:br,_fnFeatureHtmlProcessing:gf,_fnProcessingDisplay:b,_fnFeatureHtmlTable:ne,_fnScrollDraw:wi,_fnApplyToChildren:d,_fnCalculateColumnWidths:kr,_fnThrottle:bi,_fnConvertToWidth:ie,_fnGetWidestNode:re,_fnGetMaxLenString:ue,_fnStringToCss:h,_fnSortFlatten:lt,_fnSort:fe,_fnSortAria:ee,_fnSortListener:dr,_fnSortAttachListener:gr,_fnSortingClasses:ki,_fnSortData:oe,_fnSaveState:di,_fnLoadState:se,_fnSettingsFromNode:gi,_fnLog:tt,_fnMap:k,_fnBindAction:tu,_fnCallbackReg:v,_fnCallbackFire:o,_fnLengthOverflow:iu,_fnRenderer:ru,_fnDataSource:y,_fnRowAttributes:vr,_fnExtend:nu,_fnCalculateEnd:function(){}}),n.fn.dataTable=u,u.$=n,n.fn.dataTableSettings=u.settings,n.fn.dataTableExt=u.ext,n.fn.DataTable=function(t){return n(this).dataTable(t).api()},n.each(u,function(t,i){n.fn.DataTable[t]=i}),n.fn.dataTable});
/*! FixedHeader 3.1.7
 * \u00a92009-2020 SpryMedia Ltd - datatables.net/license
 */
(function(n){typeof define=="function"&&define.amd?define(["jquery","datatables.net"],function(t){return n(t,window,document)}):typeof exports=="object"?module.exports=function(t,i){return t||(t=window),i&&i.fn.dataTable||(i=require("datatables.net")(t,i).$),n(i,t,t.document)}:n(jQuery,window,document)})(function(n,t,i,r){"use strict";var u=n.fn.dataTable,e=0,f=function(i,r){if(!(this instanceof f))throw"FixedHeader must be initialised with the 'new' keyword.";r===!0&&(r={});i=new u.Api(i);this.c=n.extend(!0,{},f.defaults,r);this.s={dt:i,position:{theadTop:0,tbodyTop:0,tfootTop:0,tfootBottom:0,width:0,left:0,tfootHeight:0,theadHeight:0,windowHeight:n(t).height(),visible:!0},headerMode:null,footerMode:null,autoWidth:i.settings()[0].oFeatures.bAutoWidth,namespace:".dtfc"+e++,scrollLeft:{header:-1,footer:-1},enable:!0};this.dom={floatingHeader:null,thead:n(i.table().header()),tbody:n(i.table().body()),tfoot:n(i.table().footer()),header:{host:null,floating:null,placeholder:null},footer:{host:null,floating:null,placeholder:null}};this.dom.header.host=this.dom.thead.parent();this.dom.footer.host=this.dom.tfoot.parent();var o=i.settings()[0];if(o._fixedHeader)throw"FixedHeader already initialised on table "+o.nTable.id;o._fixedHeader=this;this._constructor()};n.extend(f.prototype,{destroy:function(){this.s.dt.off(".dtfc");n(t).off(this.s.namespace);this.c.header&&this._modeChange("in-place","header",!0);this.c.footer&&this.dom.tfoot.length&&this._modeChange("in-place","footer",!0)},enable:function(n,t){this.s.enable=n;(t||t===r)&&(this._positions(),this._scroll(!0))},enabled:function(){return this.s.enable},headerOffset:function(n){return n!==r&&(this.c.headerOffset=n,this.update()),this.c.headerOffset},footerOffset:function(n){return n!==r&&(this.c.footerOffset=n,this.update()),this.c.footerOffset},update:function(){var t=this.s.dt.table().node();n(t).is(":visible")?this.enable(!0,!1):this.enable(!1,!1);this._positions();this._scroll(!0)},_constructor:function(){var i=this,e=this.s.dt,r,f;n(t).on("scroll"+this.s.namespace,function(){i._scroll()}).on("resize"+this.s.namespace,u.util.throttle(function(){i.s.position.windowHeight=n(t).height();i.update()},50));r=n(".fh-fixedHeader");!this.c.headerOffset&&r.length&&(this.c.headerOffset=r.outerHeight());f=n(".fh-fixedFooter");!this.c.footerOffset&&f.length&&(this.c.footerOffset=f.outerHeight());e.on("column-reorder.dt.dtfc column-visibility.dt.dtfc draw.dt.dtfc column-sizing.dt.dtfc responsive-display.dt.dtfc",function(){i.update()});e.on("destroy.dtfc",function(){i.destroy()});this._positions();this._scroll()},_clone:function(t,i){var f=this.s.dt,r=this.dom[t],u=t==="header"?this.dom.thead:this.dom.tfoot;!i&&r.floating?r.floating.removeClass("fixedHeader-floating fixedHeader-locked"):(r.floating&&(r.placeholder.remove(),this._unsize(t),r.floating.children().detach(),r.floating.remove()),r.floating=n(f.table().node().cloneNode(!1)).attr("aria-hidden","true").removeAttr("id").append(u).appendTo("body"),r.placeholder=u.clone(!1),r.placeholder.find("*[id]").removeAttr("id"),r.host.prepend(r.placeholder),this._matchWidths())},_matchWidths:function(){var t=n("tr:first td","tbody").map(function(){return n(this).outerWidth()}).toArray();n("table.dataTable").each(function(i,r){const u=n(r).find("th.control").length>0?1:0;n(this).find("tr:eq("+u+") th").each(function(i){n(this).css({width:t[i],minWidth:t[i]})})})},_unsize:function(t){var i=this.dom[t].floating;i&&(t==="footer"||t==="header"&&!this.s.autoWidth)?n("th, td",i).css({width:"",minWidth:""}):i&&t==="header"&&n("th, td",i).css("min-width","")},_horizontal:function(n,t){var i=this.dom[n],u=this.s.position,r=this.s.scrollLeft;i.floating&&r[n]!==t&&(i.floating.css("left",u.left-t),r[n]=t)},_modeChange:function(t,r,u){var c=this.s.dt,f=this.dom[r],e=this.s.position,s=function(n){f.floating.attr("style",function(t,i){return(i||"")+"width: "+n+"px !important;"})},h=this.dom[r==="footer"?"tfoot":"thead"],o=n.contains(h[0],i.activeElement)?i.activeElement:null;o&&o.blur();t==="in-place"?(f.placeholder&&(f.placeholder.remove(),f.placeholder=null),this._unsize(r),r==="header"?f.host.prepend(h):f.host.append(h),f.floating&&(f.floating.remove(),f.floating=null)):t==="in"?(this._clone(r,u),f.floating.addClass("fixedHeader-floating").css(r==="header"?"top":"bottom",this.c[r+"Offset"]).css("left",e.left+"px"),s(e.width),r==="footer"&&f.floating.css("top","")):t==="below"?(this._clone(r,u),f.floating.addClass("fixedHeader-locked").css("top",e.tfootTop-e.theadHeight).css("left",e.left+"px"),s(e.width)):t==="above"&&(this._clone(r,u),f.floating.addClass("fixedHeader-locked").css("top",e.tbodyTop).css("left",e.left+"px"),s(e.width));o&&o!==i.activeElement&&setTimeout(function(){o.focus()},10);this.s.scrollLeft.header=-1;this.s.scrollLeft.footer=-1;this.s[r+"Mode"]=t},_positions:function(){var f=this.s.dt,e=f.table(),t=this.s.position,o=this.dom,i=n(e.node()),s=i.children("thead"),r=i.children("tfoot"),u=o.tbody;t.visible=i.is(":visible");t.width=i.outerWidth();t.left=i.offset().left;t.theadTop=s.offset().top;t.tbodyTop=u.offset().top;t.tbodyHeight=u.outerHeight();t.theadHeight=t.tbodyTop-t.theadTop;r.length?(t.tfootTop=r.offset().top,t.tfootBottom=t.tfootTop+r.outerHeight(),t.tfootHeight=t.tfootBottom-t.tfootTop):(t.tfootTop=t.tbodyTop+u.outerHeight(),t.tfootBottom=t.tfootTop,t.tfootHeight=t.tfootTop)},_scroll:function(t){var u=n(i).scrollTop(),o=n(i).scrollLeft(),r=this.s.position,f,e;this.c.header&&(f=this.s.enable?!r.visible||u<=r.theadTop-this.c.headerOffset?"in-place":u<=r.tfootTop-r.theadHeight-this.c.headerOffset?"in":"below":"in-place",(t||f!==this.s.headerMode)&&(this._positions(),this._modeChange(f,"header",t)),this._horizontal("header",o));this.c.footer&&this.dom.tfoot.length&&(e=this.s.enable?!r.visible||u+r.windowHeight>=r.tfootBottom+this.c.footerOffset?"in-place":r.windowHeight+u>r.tbodyTop+r.tfootHeight+this.c.footerOffset?"in":"above":"in-place",(t||e!==this.s.footerMode)&&this._modeChange(e,"footer",t),this._horizontal("footer",o))}});f.version="3.1.7";f.defaults={header:!0,footer:!1,headerOffset:0,footerOffset:0};n.fn.dataTable.FixedHeader=f;n.fn.DataTable.FixedHeader=f;n(i).on("init.dt.dtfh",function(t,i){var r,e,o;t.namespace==="dt"&&(r=i.oInit.fixedHeader,e=u.defaults.fixedHeader,(r||e)&&!i._fixedHeader&&(o=n.extend({},e,r),r!==!1&&new f(i,o)))});return u.Api.register("fixedHeader()",function(){}),u.Api.register("fixedHeader.adjust()",function(){return this.iterator("table",function(n){var t=n._fixedHeader;t&&t.update()})}),u.Api.register("fixedHeader.enable()",function(n){return this.iterator("table",function(t){var i=t._fixedHeader;n=n!==r?n:!0;i&&n!==i.enabled()&&i.enable(n)})}),u.Api.register("fixedHeader.enabled()",function(){if(this.context.length){var n=this.content[0]._fixedHeader;if(n)return n.enabled()}return!1}),u.Api.register("fixedHeader.disable()",function(){return this.iterator("table",function(n){var t=n._fixedHeader;t&&t.enabled()&&t.enable(!1)})}),n.each(["header","footer"],function(n,t){u.Api.register("fixedHeader."+t+"Offset()",function(n){var i=this.context;return n===r?i.length&&i[0]._fixedHeader?i[0]._fixedHeader[t+"Offset"]():r:this.iterator("table",function(i){var r=i._fixedHeader;r&&r[t+"Offset"](n)})})}),f});
/*! Responsive 1.0.7
 * 2014-2015 SpryMedia Ltd - datatables.net/license
 */
(function(n,t){var i=function(i,r){"use strict";var u=function(n,t){if(!r.versionCheck||!r.versionCheck("1.10.1"))throw"DataTables Responsive requires DataTables 1.10.1 or newer";(this.s={dt:new r.Api(n),columns:[]},this.s.dt.settings()[0].responsive)||(t&&typeof t.details=="string"&&(t.details={type:t.details}),this.c=i.extend(!0,{},u.defaults,r.defaults.responsive,t),n.responsive=this,this._constructor())},f;u.prototype={_constructor:function(){var r=this,t=this.s.dt,u;t.settings()[0]._responsive=this;i(n).on("orientationchange.dtr resize.dtr",t.settings()[0].oApi._fnThrottle(function(){r._collapseDetails();r._resize()}));t.on("destroy.dtr",function(){i(n).off("resize.dtr orientationchange.dtr draw.dtr")});if(this.c.breakpoints.sort(function(n,t){return n.width<t.width?1:n.width>t.width?-1:0}),this._classLogic(),this._resizeAuto(),u=this.c.details,u.type){r._detailsInit();this._detailsVis();t.on("column-visibility.dtr",function(){r._detailsVis()});t.on("draw.dtr",function(){t.rows({page:"current"}).iterator("row",function(n,i){var u=t.row(i),f;u.child.isShown()&&(f=r.c.details.renderer(t,i),u.child(f,"child").show())})});i(t.table().node()).addClass("dtr-"+u.type)}this._resize()},_columnsVisiblity:function(n){for(var c=this.s.dt,r=this.s.columns,u=i.map(r,function(t){return t.auto&&t.minWidth===null?!1:t.auto===!0?"-":i.inArray(n,t.includeIn)!==-1}),l=0,s,h,t=0,f=u.length;t<f;t++)u[t]===!0&&(l+=r[t].minWidth);var e=c.settings()[0].oScroll,a=e.sY||e.sX?e.iBarWidth:0,o=c.table().container().offsetWidth-a-l;for(t=0,f=u.length;t<f;t++)r[t].control&&(o-=r[t].minWidth);for(s=!1,t=0,f=u.length;t<f;t++)u[t]!=="-"||r[t].control||(s||o-r[t].minWidth<0?(s=!0,u[t]=!1):u[t]=!0,o-=r[t].minWidth);for(h=!1,t=0,f=r.length;t<f;t++)if(!r[t].control&&!r[t].never&&!u[t]){h=!0;break}for(t=0,f=r.length;t<f;t++)r[t].control&&(u[t]=h);return i.inArray(!0,u)===-1&&(u[0]=!0),u},_collapseDetails:function(){i(n).scrollTop(0);i("thead tr.parent").removeClass("parent");i("tbody tr.parent").each(function(n,t){i(t).find(".control").click()})},_isLandscape:function(){return n.orientation===90||n.orientation===-90},_getDeviceWidth:function(){return this._isLandscape()?screen.height:screen.width},_classLogic:function(){var u=this,n=this.c.breakpoints,t=this.s.dt.columns().eq(0).map(function(n){var t=this.column(n).header().className;return{className:t,includeIn:[],auto:!1,control:!1,never:t.match(/\bnever\b/)?!0:!1}}),r=function(n,r){var u=t[n].includeIn;i.inArray(r,u)===-1&&u.push(r)},f=function(i,f,e,o){var c,s,h;if(e){if(e==="max-")for(c=u._find(f).width,s=0,h=n.length;s<h;s++)n[s].width<=c&&r(i,n[s].name);else if(e==="min-")for(c=u._find(f).width,s=0,h=n.length;s<h;s++)n[s].width>=c&&r(i,n[s].name);else if(e==="not-")for(s=0,h=n.length;s<h;s++)n[s].name.indexOf(o)===-1&&r(i,n[s].name)}else t[i].includeIn.push(f)};t.each(function(t,r){for(var u,s=t.className.split(" "),e=!1,o=0,h=s.length;o<h;o++){if(u=i.trim(s[o]),u==="all"){e=!0;t.includeIn=i.map(n,function(n){return n.name});return}if(u==="none"||u==="never"){e=!0;return}if(u==="control"){e=!0;t.control=!0;return}i.each(n,function(n,t){var o=t.name.split("-"),s=new RegExp("(min\\-|max\\-|not\\-)?("+o[0]+")(\\-[_a-zA-Z0-9])?"),i=u.match(s);i&&(e=!0,i[2]===o[0]&&i[3]==="-"+o[1]?f(r,t.name,i[1],i[2]+i[3]):i[2]!==o[0]||i[3]||f(r,t.name,i[1],i[2]))})}e||(t.auto=!0)});this.s.columns=t},_detailsInit:function(){var f=this,n=this.s.dt,r=this.c.details,t,u;r.type==="inline"&&(r.target="td:first-child");t=r.target;u=typeof t=="string"?t:"td";i(n.table().body()).on("click",u,function(){var e,u,o;i(n.table().node()).hasClass("collapsed")&&n.row(i(this).closest("tr")).length&&(typeof t!="number"||(e=t<0?n.columns().eq(0).length+t:t,n.cell(this).index().column===e))&&(u=n.row(i(this).closest("tr")),u.child.isShown()?(u.child(!1),i(u.node()).removeClass("parent")):(o=f.c.details.renderer(n,u[0]),u.child(o,"child").show(),i(u.node()).addClass("parent")),r.bindingCallback())})},_detailsVis:function(){var u=this,n=this.s.dt,t=n.columns().indexes().filter(function(t){var r=n.column(t);return r.visible()?null:i(r.header()).hasClass("never")?null:t}),r=!0;(t.length===0||t.length===1&&this.s.columns[t[0]].control)&&(r=!1);r?n.rows({page:"current"}).eq(0).each(function(t){var i=n.row(t),r;i.child()&&(r=u.c.details.renderer(n,i[0]),r===!1?i.child.hide():i.child(r,"child").show())}):n.rows({page:"current"}).eq(0).each(function(t){n.row(t).child.hide()})},_find:function(n){for(var i=this.c.breakpoints,t=0,r=i.length;t<r;t++)if(i[t].name===n)return i[t]},_resize:function(){for(var u=this.s.dt,c=i(n).width(),r=this.c.breakpoints,o=r[0].name,s=this.s.columns,h,f,e,t=r.length-1;t>=0;t--)if(c<=r[t].width){o=r[t].name;break}for(f=this._columnsVisiblity(o),e=!1,t=0,h=s.length;t<h;t++)if(f[t]===!1&&!s[t].never){e=!0;break}i(u.table().node()).toggleClass("collapsed",e);u.columns().eq(0).each(function(n,t){u.column(n).visible(f[t])})},_resizeAuto:function(){var n=this.s.dt,r=this.s.columns,f,u;if(this.c.auto&&i.inArray(!0,i.map(r,function(n){return n.auto}))!==-1){var s=n.table().node().offsetWidth,h=n.columns,t=n.table().node().cloneNode(!1),e=i(n.table().header().cloneNode(!1)).appendTo(t),o=i(n.table().body().cloneNode(!1)).appendTo(t);i(n.table().footer()).clone(!1).appendTo(t);n.rows({page:"current"}).indexes().flatten().each(function(t){var r=n.row(t).node().cloneNode(!0);n.columns(":hidden").flatten().length&&i(r).append(n.cells(t,":hidden").nodes().to$().clone());i(r).appendTo(o)});f=n.columns().header().to$().clone(!1);i("<tr/>").append(f).appendTo(e);this.c.details.type==="inline"&&i(t).addClass("dtr-inline collapsed");u=i("<div/>").css({width:1,height:1,overflow:"hidden"}).append(t);u.find("th.never, td.never").remove();u.insertBefore(n.table().node());n.columns().eq(0).each(function(n){r[n].minWidth=r[n].className.indexOf("control")!==-1?60:f[n].offsetWidth||0});u.remove()}}};u.breakpoints=[{name:"desktop",width:Infinity},{name:"tablet-l",width:1024},{name:"tablet-p",width:768},{name:"mobile-l",width:480},{name:"mobile-p",width:320}];u.defaults={breakpoints:u.breakpoints,auto:!0,details:{renderer:function(n,t){var r=n.cells(t,":hidden").eq(0).map(function(t){var u=i(n.column(t.column).header()),f=n.cell(t).index();if(u.hasClass("control")||u.hasClass("never"))return"";var e=n.settings()[0],o=e.oApi._fnGetCellData(e,f.row,f.column,"display"),r=u.text();return r&&(r=r+":"),'<li data-dtr-index="'+f.column+'"><span class="dtr-title">'+r+'<\/span> <span class="dtr-data">'+o+"<\/span><\/li>"}).toArray().join("");return r?i('<ul data-dtr-index="'+t+'"/>').append(r):!1},target:0,type:"inline",bindingCallback:function(){}}};f=i.fn.dataTable.Api;f.register("responsive()",function(){return this});f.register("responsive.index()",function(n){return n=i(n),{column:n.data("dtr-index"),row:n.parent().data("dtr-index")}});f.register("responsive.rebuild()",function(){return this.iterator("table",function(n){n._responsive&&n._responsive._classLogic()})});f.register("responsive.recalc()",function(){return this.iterator("table",function(n){n._responsive&&(n._responsive._resizeAuto(),n._responsive._resize())})});u.version="1.0.7";i.fn.dataTable.Responsive=u;i.fn.DataTable.Responsive=u;i(t).on("init.dt.dtr",function(n,t){if(n.namespace==="dt"&&(i(t.nTable).hasClass("responsive")||i(t.nTable).hasClass("dt-responsive")||t.oInit.responsive||r.defaults.responsive)){var f=t.oInit.responsive;f!==!1&&new u(t,i.isPlainObject(f)?f:{})}});return u};typeof define=="function"&&define.amd?define(["jquery","datatables"],i):typeof exports=="object"?i(require("jquery"),require("datatables")):jQuery&&!jQuery.fn.dataTable.Responsive&&i(jQuery,jQuery.fn.dataTable)})(window,document),function(n){function h(n){var e=n._iDisplayStart,t=n._iDisplayLength,o=n.fnRecordsDisplay(),i=t===-1,r=i?0:Math.ceil(e/t),s=i?1:Math.ceil(o/t),u=r>0?"":n.oClasses.sPageButtonDisabled,f=r<s-1?"":n.oClasses.sPageButtonDisabled;return{first:u,previous:u,next:f,last:f}}function t(n){return Math.ceil(n._iDisplayStart/n._iDisplayLength)+1}function i(n){return Math.ceil(n.fnRecordsDisplay()/n._iDisplayLength)}var r="first",u="previous",f="next",e="last",c="paginate",o="paginate_input",s="paginate_total";n.fn.dataTableExt.oPagination.input={fnInit:function(h,l,a){var v=document.createElement("span"),y=document.createElement("span"),p=document.createElement("span"),w=document.createElement("span"),g=document.createElement("input"),nt=document.createElement("span"),tt=document.createElement("span"),b=h.oLanguage.oPaginate,d=h.oClasses,k=b.info||"Page _INPUT_ of _TOTAL_",it;v.innerHTML=b.sFirst;y.innerHTML=b.sPrevious;p.innerHTML=b.sNext;w.innerHTML=b.sLast;v.className=r+" "+d.sPageButton;y.className=u+" "+d.sPageButton;p.className=f+" "+d.sPageButton;w.className=e+" "+d.sPageButton;g.className=o;nt.className=s;h.sTableId!==""&&(l.setAttribute("id",h.sTableId+"_"+c),v.setAttribute("id",h.sTableId+"_"+r),y.setAttribute("id",h.sTableId+"_"+u),p.setAttribute("id",h.sTableId+"_"+f),w.setAttribute("id",h.sTableId+"_"+e));g.type="text";k=k.replace(/_INPUT_/g,"<\/span>"+g.outerHTML+"<span>");k=k.replace(/_TOTAL_/g,"<\/span>"+nt.outerHTML+"<span>");tt.innerHTML="<span>"+k+"<\/span>";l.appendChild(v);l.appendChild(y);n(tt).children().each(function(n,t){l.appendChild(t)});l.appendChild(p);l.appendChild(w);n(v).click(function(){var n=t(h);n!==1&&(h.oApi._fnPageChange(h,"first"),a(h))});n(y).click(function(){var n=t(h);n!==1&&(h.oApi._fnPageChange(h,"previous"),a(h))});n(p).click(function(){var n=t(h);n!==i(h)&&(h.oApi._fnPageChange(h,"next"),a(h))});n(w).click(function(){var n=t(h);n!==i(h)&&(h.oApi._fnPageChange(h,"last"),a(h))});n(l).find("."+o).keyup(function(n){if(n.which===38||n.which===39?this.value++:(n.which===37||n.which===40)&&this.value>1&&this.value--,this.value===""||this.value.match(/[^0-9]/)){this.value=this.value.replace(/[^\d]/g,"");return}var t=h._iDisplayLength*(this.value-1);t<0&&(t=0);t>=h.fnRecordsDisplay()&&(t=(Math.ceil(h.fnRecordsDisplay()/h._iDisplayLength)-1)*h._iDisplayLength);h._iDisplayStart=t;a(h)});n("span",l).bind("mousedown",function(){return!1});n("span",l).bind("selectstart",function(){return!1});it=i(h);it<=1&&n(l).hide()},fnUpdate:function(c){var a;if(c.aanFeatures.p){var v=i(c),y=t(c),l=c.aanFeatures.p;if(v<=1){n(l).hide();return}a=h(c);n(l).show();n(l).children("."+r).removeClass(c.oClasses.sPageButtonDisabled).addClass(a[r]);n(l).children("."+u).removeClass(c.oClasses.sPageButtonDisabled).addClass(a[u]);n(l).children("."+f).removeClass(c.oClasses.sPageButtonDisabled).addClass(a[f]);n(l).children("."+e).removeClass(c.oClasses.sPageButtonDisabled).addClass(a[e]);n(l).find("."+s).html(v);n(l).find("."+o).val(y)}}}}(jQuery);+function(n){"use strict";function i(i,r){return this.each(function(){var f=n(this),u=f.data("bs.modal"),e=n.extend({},t.DEFAULTS,f.data(),typeof i=="object"&&i);u||f.data("bs.modal",u=new t(this,e));typeof i=="string"?u[i](r):e.show&&u.show(r)})}var t=function(t,i){this.options=i;this.$body=n(document.body);this.$element=n(t);this.$dialog=this.$element.find(".modal-dialog");this.$backdrop=null;this.isShown=null;this.originalBodyPad=null;this.scrollbarWidth=0;this.ignoreBackdropClick=!1;this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,n.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))},r;t.VERSION="3.3.6";t.TRANSITION_DURATION=300;t.BACKDROP_TRANSITION_DURATION=150;t.DEFAULTS={backdrop:!0,keyboard:!0,show:!0};t.prototype.toggle=function(n){return this.isShown?this.hide():this.show(n)};t.prototype.show=function(i){var r=this,u=n.Event("show.bs.modal",{relatedTarget:i});if(this.$element.trigger(u),!this.isShown&&!u.isDefaultPrevented()){this.isShown=!0;this.checkScrollbar();this.setScrollbar();this.$body.addClass("modal-open");this.escape();this.resize();this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',n.proxy(this.hide,this));this.$dialog.on("mousedown.dismiss.bs.modal",function(){r.$element.one("mouseup.dismiss.bs.modal",function(t){n(t.target).is(r.$element)&&(r.ignoreBackdropClick=!0)})});this.backdrop(function(){var f=n.support.transition&&r.$element.hasClass("fade"),u;r.$element.parent().length||r.$element.appendTo(r.$body);r.$element.show().scrollTop(0);r.adjustDialog();f&&r.$element[0].offsetWidth;r.$element.addClass("in");r.enforceFocus();u=n.Event("shown.bs.modal",{relatedTarget:i});f?r.$dialog.one("bsTransitionEnd",function(){r.$element.trigger("focus").trigger(u)}).emulateTransitionEnd(t.TRANSITION_DURATION):r.$element.trigger("focus").trigger(u)})}};t.prototype.hide=function(i){(i&&i.preventDefault(),i=n.Event("hide.bs.modal"),this.$element.trigger(i),this.isShown&&!i.isDefaultPrevented())&&(this.isShown=!1,this.escape(),this.resize(),n(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),n.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",n.proxy(this.hideModal,this)).emulateTransitionEnd(t.TRANSITION_DURATION):this.hideModal())};t.prototype.enforceFocus=function(){n(document).off("focusin.bs.modal").on("focusin.bs.modal",n.proxy(function(n){this.$element[0]===n.target||this.$element.has(n.target).length||this.$element.trigger("focus")},this))};t.prototype.escape=function(){if(this.isShown&&this.options.keyboard)this.$element.on("keydown.dismiss.bs.modal",n.proxy(function(n){n.which==27&&this.hide()},this));else this.isShown||this.$element.off("keydown.dismiss.bs.modal")};t.prototype.resize=function(){if(this.isShown)n(window).on("resize.bs.modal",n.proxy(this.handleUpdate,this));else n(window).off("resize.bs.modal")};t.prototype.hideModal=function(){var n=this;this.$element.hide();this.backdrop(function(){n.$body.removeClass("modal-open");n.resetAdjustments();n.resetScrollbar();n.$element.trigger("hidden.bs.modal")})};t.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove();this.$backdrop=null};t.prototype.backdrop=function(i){var e=this,f=this.$element.hasClass("fade")?"fade":"",r,u;if(this.isShown&&this.options.backdrop){r=n.support.transition&&f;this.$backdrop=n(document.createElement("div")).addClass("modal-backdrop "+f).appendTo(this.$body);this.$element.on("click.dismiss.bs.modal",n.proxy(function(n){if(this.ignoreBackdropClick){this.ignoreBackdropClick=!1;return}n.target===n.currentTarget&&(this.options.backdrop=="static"?this.$element[0].focus():this.hide())},this));if(r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!i)return;r?this.$backdrop.one("bsTransitionEnd",i).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):i()}else!this.isShown&&this.$backdrop?(this.$backdrop.removeClass("in"),u=function(){e.removeBackdrop();i&&i()},n.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",u).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):u()):i&&i()};t.prototype.handleUpdate=function(){this.adjustDialog()};t.prototype.adjustDialog=function(){var n=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&n?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!n?this.scrollbarWidth:""})};t.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})};t.prototype.checkScrollbar=function(){var n=window.innerWidth,t;n||(t=document.documentElement.getBoundingClientRect(),n=t.right-Math.abs(t.left));this.bodyIsOverflowing=document.body.clientWidth<n;this.scrollbarWidth=this.measureScrollbar()};t.prototype.setScrollbar=function(){var n=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"";this.bodyIsOverflowing&&this.$body.css("padding-right",n+this.scrollbarWidth)};t.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad)};t.prototype.measureScrollbar=function(){var n=document.createElement("div"),t;return n.className="modal-scrollbar-measure",this.$body.append(n),t=n.offsetWidth-n.clientWidth,this.$body[0].removeChild(n),t};r=n.fn.modal;n.fn.modal=i;n.fn.modal.Constructor=t;n.fn.modal.noConflict=function(){return n.fn.modal=r,this};n(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var r=n(this),f=r.attr("href"),u=n(r.attr("data-target")||f&&f.replace(/.*(?=#[^\s]+$)/,"")),e=u.data("bs.modal")?"toggle":n.extend({remote:!/#/.test(f)&&f},u.data(),r.data());r.is("a")&&t.preventDefault();u.one("show.bs.modal",function(n){if(!n.isDefaultPrevented())u.one("hidden.bs.modal",function(){r.is(":visible")&&r.trigger("focus")})});i.call(u,e,this)})}(jQuery);
/*! jQuery UI - v1.11.4 - 2015-03-11
* http://jqueryui.com
* Includes: core.js, widget.js, mouse.js, position.js, accordion.js, autocomplete.js, button.js, datepicker.js, dialog.js, draggable.js, droppable.js, effect.js, effect-blind.js, effect-bounce.js, effect-clip.js, effect-drop.js, effect-explode.js, effect-fade.js, effect-fold.js, effect-highlight.js, effect-puff.js, effect-pulsate.js, effect-scale.js, effect-shake.js, effect-size.js, effect-slide.js, effect-transfer.js, menu.js, progressbar.js, resizable.js, selectable.js, selectmenu.js, slider.js, sortable.js, spinner.js, tabs.js, tooltip.js
* Copyright 2015 jQuery Foundation and other contributors; Licensed MIT */
(function(n){"function"==typeof define&&define.amd?define(["jquery"],n):n(jQuery)})(function(n){function h(t,i){var r,u,f,e=t.nodeName.toLowerCase();return"area"===e?(r=t.parentNode,u=r.name,t.href&&u&&"map"===r.nodeName.toLowerCase()?(f=n("img[usemap='#"+u+"']")[0],!!f&&c(f)):!1):(/^(input|select|textarea|button|object)$/.test(e)?!t.disabled:"a"===e?t.href||i:i)&&c(t)}function c(t){return n.expr.filters.visible(t)&&!n(t).parents().addBack().filter(function(){return"hidden"===n.css(this,"visibility")}).length}function k(n){for(var t,i;n.length&&n[0]!==document;){if(t=n.css("position"),("absolute"===t||"relative"===t||"fixed"===t)&&(i=parseInt(n.css("zIndex"),10),!isNaN(i)&&0!==i))return i;n=n.parent()}return 0}function l(){this._curInst=null;this._keyEvent=!1;this._disabledInputs=[];this._datepickerShowing=!1;this._inDialog=!1;this._mainDivId="ui-datepicker-div";this._inlineClass="ui-datepicker-inline";this._appendClass="ui-datepicker-append";this._triggerClass="ui-datepicker-trigger";this._dialogClass="ui-datepicker-dialog";this._disableClass="ui-datepicker-disabled";this._unselectableClass="ui-datepicker-unselectable";this._currentClass="ui-datepicker-current-day";this._dayOverClass="ui-datepicker-days-cell-over";this.regional=[];this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""};this._defaults={showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1};n.extend(this._defaults,this.regional[""]);this.regional.en=n.extend(!0,{},this.regional[""]);this.regional["en-US"]=n.extend(!0,{},this.regional.en);this.dpDiv=a(n("<div id='"+this._mainDivId+"' class='ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'><\/div>"))}function a(t){var i="button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a";return t.delegate(i,"mouseout",function(){n(this).removeClass("ui-state-hover");-1!==this.className.indexOf("ui-datepicker-prev")&&n(this).removeClass("ui-datepicker-prev-hover");-1!==this.className.indexOf("ui-datepicker-next")&&n(this).removeClass("ui-datepicker-next-hover")}).delegate(i,"mouseover",v)}function v(){n.datepicker._isDisabledDatepicker(i.inline?i.dpDiv.parent()[0]:i.input[0])||(n(this).parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),n(this).addClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&n(this).addClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&n(this).addClass("ui-datepicker-next-hover"))}function u(t,i){n.extend(t,i);for(var r in i)null==i[r]&&(t[r]=i[r]);return t}function t(n){return function(){var t=this.element.val();n.apply(this,arguments);this._refresh();t!==this.element.val()&&this._trigger("change")}}var y,f,r,i,o,s;n.ui=n.ui||{};n.extend(n.ui,{version:"1.11.4",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}});n.fn.extend({scrollParent:function(t){var i=this.css("position"),u="absolute"===i,f=t?/(auto|scroll|hidden)/:/(auto|scroll)/,r=this.parents().filter(function(){var t=n(this);return u&&"static"===t.css("position")?!1:f.test(t.css("overflow")+t.css("overflow-y")+t.css("overflow-x"))}).eq(0);return"fixed"!==i&&r.length?r:n(this[0].ownerDocument||document)},uniqueId:function(){var n=0;return function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++n)})}}(),removeUniqueId:function(){return this.each(function(){/^ui-id-\d+$/.test(this.id)&&n(this).removeAttr("id")})}});n.extend(n.expr[":"],{data:n.expr.createPseudo?n.expr.createPseudo(function(t){return function(i){return!!n.data(i,t)}}):function(t,i,r){return!!n.data(t,r[3])},focusable:function(t){return h(t,!isNaN(n.attr(t,"tabindex")))},tabbable:function(t){var i=n.attr(t,"tabindex"),r=isNaN(i);return(r||i>=0)&&h(t,!r)}});n("<a>").outerWidth(1).jquery||n.each(["Width","Height"],function(t,i){function r(t,i,r,u){return n.each(e,function(){i-=parseFloat(n.css(t,"padding"+this))||0;r&&(i-=parseFloat(n.css(t,"border"+this+"Width"))||0);u&&(i-=parseFloat(n.css(t,"margin"+this))||0)}),i}var e="Width"===i?["Left","Right"]:["Top","Bottom"],u=i.toLowerCase(),f={innerWidth:n.fn.innerWidth,innerHeight:n.fn.innerHeight,outerWidth:n.fn.outerWidth,outerHeight:n.fn.outerHeight};n.fn["inner"+i]=function(t){return void 0===t?f["inner"+i].call(this):this.each(function(){n(this).css(u,r(this,t)+"px")})};n.fn["outer"+i]=function(t,e){return"number"!=typeof t?f["outer"+i].call(this,t):this.each(function(){n(this).css(u,r(this,t,!0,e)+"px")})}});n.fn.addBack||(n.fn.addBack=function(n){return this.add(null==n?this.prevObject:this.prevObject.filter(n))});n("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(n.fn.removeData=function(t){return function(i){return arguments.length?t.call(this,n.camelCase(i)):t.call(this)}}(n.fn.removeData));n.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase());n.fn.extend({focus:function(t){return function(i,r){return"number"==typeof i?this.each(function(){var t=this;setTimeout(function(){n(t).focus();r&&r.call(t)},i)}):t.apply(this,arguments)}}(n.fn.focus),disableSelection:function(){var n="onselectstart"in document.createElement("div")?"selectstart":"mousedown";return function(){return this.bind(n+".ui-disableSelection",function(n){n.preventDefault()})}}(),enableSelection:function(){return this.unbind(".ui-disableSelection")},zIndex:function(t){if(void 0!==t)return this.css("zIndex",t);if(this.length)for(var r,u,i=n(this[0]);i.length&&i[0]!==document;){if(r=i.css("position"),("absolute"===r||"relative"===r||"fixed"===r)&&(u=parseInt(i.css("zIndex"),10),!isNaN(u)&&0!==u))return u;i=i.parent()}return 0}});n.ui.plugin={add:function(t,i,r){var u,f=n.ui[t].prototype;for(u in r)f.plugins[u]=f.plugins[u]||[],f.plugins[u].push([i,r[u]])},call:function(n,t,i,r){var u,f=n.plugins[t];if(f&&(r||n.element[0].parentNode&&11!==n.element[0].parentNode.nodeType))for(u=0;f.length>u;u++)n.options[f[u][0]]&&f[u][1].apply(n.element,i)}};y=0;f=Array.prototype.slice;n.cleanData=function(t){return function(i){for(var r,u,f=0;null!=(u=i[f]);f++)try{r=n._data(u,"events");r&&r.remove&&n(u).triggerHandler("remove")}catch(e){}t(i)}}(n.cleanData);n.widget=function(t,i,r){var s,f,u,o,h={},e=t.split(".")[0];return t=t.split(".")[1],s=e+"-"+t,r||(r=i,i=n.Widget),n.expr[":"][s.toLowerCase()]=function(t){return!!n.data(t,s)},n[e]=n[e]||{},f=n[e][t],u=n[e][t]=function(n,t){return this._createWidget?(arguments.length&&this._createWidget(n,t),void 0):new u(n,t)},n.extend(u,f,{version:r.version,_proto:n.extend({},r),_childConstructors:[]}),o=new i,o.options=n.widget.extend({},o.options),n.each(r,function(t,r){return n.isFunction(r)?(h[t]=function(){var n=function(){return i.prototype[t].apply(this,arguments)},u=function(n){return i.prototype[t].apply(this,n)};return function(){var t,i=this._super,f=this._superApply;return this._super=n,this._superApply=u,t=r.apply(this,arguments),this._super=i,this._superApply=f,t}}(),void 0):(h[t]=r,void 0)}),u.prototype=n.widget.extend(o,{widgetEventPrefix:f?o.widgetEventPrefix||t:t},h,{constructor:u,namespace:e,widgetName:t,widgetFullName:s}),f?(n.each(f._childConstructors,function(t,i){var r=i.prototype;n.widget(r.namespace+"."+r.widgetName,u,i._proto)}),delete f._childConstructors):i._childConstructors.push(u),n.widget.bridge(t,u),u};n.widget.extend=function(t){for(var i,r,e=f.call(arguments,1),u=0,o=e.length;o>u;u++)for(i in e[u])r=e[u][i],e[u].hasOwnProperty(i)&&void 0!==r&&(t[i]=n.isPlainObject(r)?n.isPlainObject(t[i])?n.widget.extend({},t[i],r):n.widget.extend({},r):r);return t};n.widget.bridge=function(t,i){var r=i.prototype.widgetFullName||t;n.fn[t]=function(u){var s="string"==typeof u,o=f.call(arguments,1),e=this;return s?this.each(function(){var i,f=n.data(this,r);return"instance"===u?(e=f,!1):f?n.isFunction(f[u])&&"_"!==u.charAt(0)?(i=f[u].apply(f,o),i!==f&&void 0!==i?(e=i&&i.jquery?e.pushStack(i.get()):i,!1):void 0):n.error("no such method '"+u+"' for "+t+" widget instance"):n.error("cannot call methods on "+t+" prior to initialization; attempted to call method '"+u+"'")}):(o.length&&(u=n.widget.extend.apply(null,[u].concat(o))),this.each(function(){var t=n.data(this,r);t?(t.option(u||{}),t._init&&t._init()):n.data(this,r,new i(u,this))})),e}};n.Widget=function(){};n.Widget._childConstructors=[];n.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(t,i){i=n(i||this.defaultElement||this)[0];this.element=n(i);this.uuid=y++;this.eventNamespace="."+this.widgetName+this.uuid;this.bindings=n();this.hoverable=n();this.focusable=n();i!==this&&(n.data(i,this.widgetFullName,this),this._on(!0,this.element,{remove:function(n){n.target===i&&this.destroy()}}),this.document=n(i.style?i.ownerDocument:i.document||i),this.window=n(this.document[0].defaultView||this.document[0].parentWindow));this.options=n.widget.extend({},this.options,this._getCreateOptions(),t);this._create();this._trigger("create",null,this._getCreateEventData());this._init()},_getCreateOptions:n.noop,_getCreateEventData:n.noop,_create:n.noop,_init:n.noop,destroy:function(){this._destroy();this.element.unbind(this.eventNamespace).removeData(this.widgetFullName).removeData(n.camelCase(this.widgetFullName));this.widget().unbind(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled ui-state-disabled");this.bindings.unbind(this.eventNamespace);this.hoverable.removeClass("ui-state-hover");this.focusable.removeClass("ui-state-focus")},_destroy:n.noop,widget:function(){return this.element},option:function(t,i){var r,u,f,e=t;if(0===arguments.length)return n.widget.extend({},this.options);if("string"==typeof t)if(e={},r=t.split("."),t=r.shift(),r.length){for(u=e[t]=n.widget.extend({},this.options[t]),f=0;r.length-1>f;f++)u[r[f]]=u[r[f]]||{},u=u[r[f]];if(t=r.pop(),1===arguments.length)return void 0===u[t]?null:u[t];u[t]=i}else{if(1===arguments.length)return void 0===this.options[t]?null:this.options[t];e[t]=i}return this._setOptions(e),this},_setOptions:function(n){for(var t in n)this._setOption(t,n[t]);return this},_setOption:function(n,t){return this.options[n]=t,"disabled"===n&&(this.widget().toggleClass(this.widgetFullName+"-disabled",!!t),t&&(this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus"))),this},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_on:function(t,i,r){var f,u=this;"boolean"!=typeof t&&(r=i,i=t,t=!1);r?(i=f=n(i),this.bindings=this.bindings.add(i)):(r=i,i=this.element,f=this.widget());n.each(r,function(r,e){function o(){if(t||u.options.disabled!==!0&&!n(this).hasClass("ui-state-disabled"))return("string"==typeof e?u[e]:e).apply(u,arguments)}"string"!=typeof e&&(o.guid=e.guid=e.guid||o.guid||n.guid++);var s=r.match(/^([\w:-]*)\s*(.*)$/),h=s[1]+u.eventNamespace,c=s[2];c?f.delegate(c,h,o):i.bind(h,o)})},_off:function(t,i){i=(i||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace;t.unbind(i).undelegate(i);this.bindings=n(this.bindings.not(t).get());this.focusable=n(this.focusable.not(t).get());this.hoverable=n(this.hoverable.not(t).get())},_delay:function(n,t){function r(){return("string"==typeof n?i[n]:n).apply(i,arguments)}var i=this;return setTimeout(r,t||0)},_hoverable:function(t){this.hoverable=this.hoverable.add(t);this._on(t,{mouseenter:function(t){n(t.currentTarget).addClass("ui-state-hover")},mouseleave:function(t){n(t.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(t){this.focusable=this.focusable.add(t);this._on(t,{focusin:function(t){n(t.currentTarget).addClass("ui-state-focus")},focusout:function(t){n(t.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(t,i,r){var u,f,e=this.options[t];if(r=r||{},i=n.Event(i),i.type=(t===this.widgetEventPrefix?t:this.widgetEventPrefix+t).toLowerCase(),i.target=this.element[0],f=i.originalEvent)for(u in f)u in i||(i[u]=f[u]);return this.element.trigger(i,r),!(n.isFunction(e)&&e.apply(this.element[0],[i].concat(r))===!1||i.isDefaultPrevented())}};n.each({show:"fadeIn",hide:"fadeOut"},function(t,i){n.Widget.prototype["_"+t]=function(r,u,f){"string"==typeof u&&(u={effect:u});var o,e=u?u===!0||"number"==typeof u?i:u.effect||i:t;u=u||{};"number"==typeof u&&(u={duration:u});o=!n.isEmptyObject(u);u.complete=f;u.delay&&r.delay(u.delay);o&&n.effects&&n.effects.effect[e]?r[t](u):e!==t&&r[e]?r[e](u.duration,u.easing,f):r.queue(function(i){n(this)[t]();f&&f.call(r[0]);i()})}});n.widget;r=!1;n(document).mouseup(function(){r=!1});n.widget("ui.mouse",{version:"1.11.4",options:{cancel:"input,textarea,button,select,option",distance:1,delay:0},_mouseInit:function(){var t=this;this.element.bind("mousedown."+this.widgetName,function(n){return t._mouseDown(n)}).bind("click."+this.widgetName,function(i){if(!0===n.data(i.target,t.widgetName+".preventClickEvent"))return(n.removeData(i.target,t.widgetName+".preventClickEvent"),i.stopImmediatePropagation(),!1)});this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName);this._mouseMoveDelegate&&this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(t){if(!r){this._mouseMoved=!1;this._mouseStarted&&this._mouseUp(t);this._mouseDownEvent=t;var i=this,u=1===t.which,f="string"==typeof this.options.cancel&&t.target.nodeName?n(t.target).closest(this.options.cancel).length:!1;return u&&!f&&this._mouseCapture(t)?(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){i.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=this._mouseStart(t)!==!1,!this._mouseStarted)?(t.preventDefault(),!0):(!0===n.data(t.target,this.widgetName+".preventClickEvent")&&n.removeData(t.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(n){return i._mouseMove(n)},this._mouseUpDelegate=function(n){return i._mouseUp(n)},this.document.bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),t.preventDefault(),r=!0,!0)):!0}},_mouseMove:function(t){return this._mouseMoved&&(n.ui.ie&&(!document.documentMode||9>document.documentMode)&&!t.button||!t.which)?this._mouseUp(t):((t.which||t.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(t),t.preventDefault()):(this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=this._mouseStart(this._mouseDownEvent,t)!==!1,this._mouseStarted?this._mouseDrag(t):this._mouseUp(t)),!this._mouseStarted))},_mouseUp:function(t){return this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,t.target===this._mouseDownEvent.target&&n.data(t.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(t)),r=!1,!1},_mouseDistanceMet:function(n){return Math.max(Math.abs(this._mouseDownEvent.pageX-n.pageX),Math.abs(this._mouseDownEvent.pageY-n.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),function(){function f(n,t,i){return[parseFloat(n[0])*(a.test(n[0])?t/100:1),parseFloat(n[1])*(a.test(n[1])?i/100:1)]}function i(t,i){return parseInt(n.css(t,i),10)||0}function v(t){var i=t[0];return 9===i.nodeType?{width:t.width(),height:t.height(),offset:{top:0,left:0}}:n.isWindow(i)?{width:t.width(),height:t.height(),offset:{top:t.scrollTop(),left:t.scrollLeft()}}:i.preventDefault?{width:0,height:0,offset:{top:i.pageY,left:i.pageX}}:{width:t.outerWidth(),height:t.outerHeight(),offset:t.offset()}}n.ui=n.ui||{};var u,e,r=Math.max,t=Math.abs,o=Math.round,s=/left|center|right/,h=/top|center|bottom/,c=/[\+\-]\d+(\.[\d]+)?%?/,l=/^\w+/,a=/%$/,y=n.fn.position;n.position={scrollbarWidth:function(){if(void 0!==u)return u;var r,i,t=n("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'><\/div><\/div>"),f=t.children()[0];return n("body").append(t),r=f.offsetWidth,t.css("overflow","scroll"),i=f.offsetWidth,r===i&&(i=t[0].clientWidth),t.remove(),u=r-i},getScrollInfo:function(t){var i=t.isWindow||t.isDocument?"":t.element.css("overflow-x"),r=t.isWindow||t.isDocument?"":t.element.css("overflow-y"),u="scroll"===i||"auto"===i&&t.width<t.element[0].scrollWidth,f="scroll"===r||"auto"===r&&t.height<t.element[0].scrollHeight;return{width:f?n.position.scrollbarWidth():0,height:u?n.position.scrollbarWidth():0}},getWithinInfo:function(t){var i=n(t||window),r=n.isWindow(i[0]),u=!!i[0]&&9===i[0].nodeType;return{element:i,isWindow:r,isDocument:u,offset:i.offset()||{left:0,top:0},scrollLeft:i.scrollLeft(),scrollTop:i.scrollTop(),width:r||u?i.width():i.outerWidth(),height:r||u?i.height():i.outerHeight()}}};n.fn.position=function(u){if(!u||!u.of)return y.apply(this,arguments);u=n.extend({},u);var k,a,p,b,w,g,nt=n(u.of),it=n.position.getWithinInfo(u.within),rt=n.position.getScrollInfo(it),d=(u.collision||"flip").split(" "),tt={};return g=v(nt),nt[0].preventDefault&&(u.at="left top"),a=g.width,p=g.height,b=g.offset,w=n.extend({},b),n.each(["my","at"],function(){var t,i,n=(u[this]||"").split(" ");1===n.length&&(n=s.test(n[0])?n.concat(["center"]):h.test(n[0])?["center"].concat(n):["center","center"]);n[0]=s.test(n[0])?n[0]:"center";n[1]=h.test(n[1])?n[1]:"center";t=c.exec(n[0]);i=c.exec(n[1]);tt[this]=[t?t[0]:0,i?i[0]:0];u[this]=[l.exec(n[0])[0],l.exec(n[1])[0]]}),1===d.length&&(d[1]=d[0]),"right"===u.at[0]?w.left+=a:"center"===u.at[0]&&(w.left+=a/2),"bottom"===u.at[1]?w.top+=p:"center"===u.at[1]&&(w.top+=p/2),k=f(tt.at,a,p),w.left+=k[0],w.top+=k[1],this.each(function(){var y,g,h=n(this),c=h.outerWidth(),l=h.outerHeight(),ut=i(this,"marginLeft"),ft=i(this,"marginTop"),et=c+ut+i(this,"marginRight")+rt.width,ot=l+ft+i(this,"marginBottom")+rt.height,s=n.extend({},w),v=f(tt.my,h.outerWidth(),h.outerHeight());"right"===u.my[0]?s.left-=c:"center"===u.my[0]&&(s.left-=c/2);"bottom"===u.my[1]?s.top-=l:"center"===u.my[1]&&(s.top-=l/2);s.left+=v[0];s.top+=v[1];e||(s.left=o(s.left),s.top=o(s.top));y={marginLeft:ut,marginTop:ft};n.each(["left","top"],function(t,i){n.ui.position[d[t]]&&n.ui.position[d[t]][i](s,{targetWidth:a,targetHeight:p,elemWidth:c,elemHeight:l,collisionPosition:y,collisionWidth:et,collisionHeight:ot,offset:[k[0]+v[0],k[1]+v[1]],my:u.my,at:u.at,within:it,elem:h})});u.using&&(g=function(n){var i=b.left-s.left,o=i+a-c,f=b.top-s.top,v=f+p-l,e={target:{element:nt,left:b.left,top:b.top,width:a,height:p},element:{element:h,left:s.left,top:s.top,width:c,height:l},horizontal:0>o?"left":i>0?"right":"center",vertical:0>v?"top":f>0?"bottom":"middle"};c>a&&a>t(i+o)&&(e.horizontal="center");l>p&&p>t(f+v)&&(e.vertical="middle");e.important=r(t(i),t(o))>r(t(f),t(v))?"horizontal":"vertical";u.using.call(this,n,e)});h.offset(n.extend(s,{using:g}))})};n.ui.position={fit:{left:function(n,t){var h,e=t.within,u=e.isWindow?e.scrollLeft:e.offset.left,o=e.width,s=n.left-t.collisionPosition.marginLeft,i=u-s,f=s+t.collisionWidth-o-u;t.collisionWidth>o?i>0&&0>=f?(h=n.left+i+t.collisionWidth-o-u,n.left+=i-h):n.left=f>0&&0>=i?u:i>f?u+o-t.collisionWidth:u:i>0?n.left+=i:f>0?n.left-=f:n.left=r(n.left-s,n.left)},top:function(n,t){var h,o=t.within,u=o.isWindow?o.scrollTop:o.offset.top,e=t.within.height,s=n.top-t.collisionPosition.marginTop,i=u-s,f=s+t.collisionHeight-e-u;t.collisionHeight>e?i>0&&0>=f?(h=n.top+i+t.collisionHeight-e-u,n.top+=i-h):n.top=f>0&&0>=i?u:i>f?u+e-t.collisionHeight:u:i>0?n.top+=i:f>0?n.top-=f:n.top=r(n.top-s,n.top)}},flip:{left:function(n,i){var o,s,r=i.within,y=r.offset.left+r.scrollLeft,c=r.width,h=r.isWindow?r.scrollLeft:r.offset.left,l=n.left-i.collisionPosition.marginLeft,a=l-h,v=l+i.collisionWidth-c-h,u="left"===i.my[0]?-i.elemWidth:"right"===i.my[0]?i.elemWidth:0,f="left"===i.at[0]?i.targetWidth:"right"===i.at[0]?-i.targetWidth:0,e=-2*i.offset[0];0>a?(o=n.left+u+f+e+i.collisionWidth-c-y,(0>o||t(a)>o)&&(n.left+=u+f+e)):v>0&&(s=n.left-i.collisionPosition.marginLeft+u+f+e-h,(s>0||v>t(s))&&(n.left+=u+f+e))},top:function(n,i){var o,s,r=i.within,y=r.offset.top+r.scrollTop,c=r.height,h=r.isWindow?r.scrollTop:r.offset.top,l=n.top-i.collisionPosition.marginTop,a=l-h,v=l+i.collisionHeight-c-h,p="top"===i.my[1],u=p?-i.elemHeight:"bottom"===i.my[1]?i.elemHeight:0,f="top"===i.at[1]?i.targetHeight:"bottom"===i.at[1]?-i.targetHeight:0,e=-2*i.offset[1];0>a?(s=n.top+u+f+e+i.collisionHeight-c-y,(0>s||t(a)>s)&&(n.top+=u+f+e)):v>0&&(o=n.top-i.collisionPosition.marginTop+u+f+e-h,(o>0||v>t(o))&&(n.top+=u+f+e))}},flipfit:{left:function(){n.ui.position.flip.left.apply(this,arguments);n.ui.position.fit.left.apply(this,arguments)},top:function(){n.ui.position.flip.top.apply(this,arguments);n.ui.position.fit.top.apply(this,arguments)}}},function(){var t,i,r,u,f,o=document.getElementsByTagName("body")[0],s=document.createElement("div");t=document.createElement(o?"div":"body");r={visibility:"hidden",width:0,height:0,border:0,margin:0,background:"none"};o&&n.extend(r,{position:"absolute",left:"-1000px",top:"-1000px"});for(f in r)t.style[f]=r[f];t.appendChild(s);i=o||document.documentElement;i.insertBefore(t,i.firstChild);s.style.cssText="position: absolute; left: 10.7432222px;";u=n(s).offset().left;e=u>10&&11>u;t.innerHTML="";i.removeChild(t)}()}();n.ui.position;n.widget("ui.accordion",{version:"1.11.4",options:{active:0,animate:{},collapsible:!1,event:"click",header:"> li > :first-child,> :not(li):even",heightStyle:"auto",icons:{activeHeader:"ui-icon-triangle-1-s",header:"ui-icon-triangle-1-e"},activate:null,beforeActivate:null},hideProps:{borderTopWidth:"hide",borderBottomWidth:"hide",paddingTop:"hide",paddingBottom:"hide",height:"hide"},showProps:{borderTopWidth:"show",borderBottomWidth:"show",paddingTop:"show",paddingBottom:"show",height:"show"},_create:function(){var t=this.options;this.prevShow=this.prevHide=n();this.element.addClass("ui-accordion ui-widget ui-helper-reset").attr("role","tablist");t.collapsible||t.active!==!1&&null!=t.active||(t.active=0);this._processPanels();0>t.active&&(t.active+=this.headers.length);this._refresh()},_getCreateEventData:function(){return{header:this.active,panel:this.active.length?this.active.next():n()}},_createIcons:function(){var t=this.options.icons;t&&(n("<span>").addClass("ui-accordion-header-icon ui-icon "+t.header).prependTo(this.headers),this.active.children(".ui-accordion-header-icon").removeClass(t.header).addClass(t.activeHeader),this.headers.addClass("ui-accordion-icons"))},_destroyIcons:function(){this.headers.removeClass("ui-accordion-icons").children(".ui-accordion-header-icon").remove()},_destroy:function(){var n;this.element.removeClass("ui-accordion ui-widget ui-helper-reset").removeAttr("role");this.headers.removeClass("ui-accordion-header ui-accordion-header-active ui-state-default ui-corner-all ui-state-active ui-state-disabled ui-corner-top").removeAttr("role").removeAttr("aria-expanded").removeAttr("aria-selected").removeAttr("aria-controls").removeAttr("tabIndex").removeUniqueId();this._destroyIcons();n=this.headers.next().removeClass("ui-helper-reset ui-widget-content ui-corner-bottom ui-accordion-content ui-accordion-content-active ui-state-disabled").css("display","").removeAttr("role").removeAttr("aria-hidden").removeAttr("aria-labelledby").removeUniqueId();"content"!==this.options.heightStyle&&n.css("height","")},_setOption:function(n,t){return"active"===n?(this._activate(t),void 0):("event"===n&&(this.options.event&&this._off(this.headers,this.options.event),this._setupEvents(t)),this._super(n,t),"collapsible"!==n||t||this.options.active!==!1||this._activate(0),"icons"===n&&(this._destroyIcons(),t&&this._createIcons()),"disabled"===n&&(this.element.toggleClass("ui-state-disabled",!!t).attr("aria-disabled",t),this.headers.add(this.headers.next()).toggleClass("ui-state-disabled",!!t)),void 0)},_keydown:function(t){if(!t.altKey&&!t.ctrlKey){var i=n.ui.keyCode,u=this.headers.length,f=this.headers.index(t.target),r=!1;switch(t.keyCode){case i.RIGHT:case i.DOWN:r=this.headers[(f+1)%u];break;case i.LEFT:case i.UP:r=this.headers[(f-1+u)%u];break;case i.SPACE:case i.ENTER:this._eventHandler(t);break;case i.HOME:r=this.headers[0];break;case i.END:r=this.headers[u-1]}r&&(n(t.target).attr("tabIndex",-1),n(r).attr("tabIndex",0),r.focus(),t.preventDefault())}},_panelKeyDown:function(t){t.keyCode===n.ui.keyCode.UP&&t.ctrlKey&&n(t.currentTarget).prev().focus()},refresh:function(){var t=this.options;this._processPanels();t.active===!1&&t.collapsible===!0||!this.headers.length?(t.active=!1,this.active=n()):t.active===!1?this._activate(0):this.active.length&&!n.contains(this.element[0],this.active[0])?this.headers.length===this.headers.find(".ui-state-disabled").length?(t.active=!1,this.active=n()):this._activate(Math.max(0,t.active-1)):t.active=this.headers.index(this.active);this._destroyIcons();this._refresh()},_processPanels:function(){var t=this.headers,n=this.panels;this.headers=this.element.find(this.options.header).addClass("ui-accordion-header ui-state-default ui-corner-all");this.panels=this.headers.next().addClass("ui-accordion-content ui-helper-reset ui-widget-content ui-corner-bottom").filter(":not(.ui-accordion-content-active)").hide();n&&(this._off(t.not(this.headers)),this._off(n.not(this.panels)))},_refresh:function(){var t,i=this.options,r=i.heightStyle,u=this.element.parent();this.active=this._findActive(i.active).addClass("ui-accordion-header-active ui-state-active ui-corner-top").removeClass("ui-corner-all");this.active.next().addClass("ui-accordion-content-active").show();this.headers.attr("role","tab").each(function(){var t=n(this),r=t.uniqueId().attr("id"),i=t.next(),u=i.uniqueId().attr("id");t.attr("aria-controls",u);i.attr("aria-labelledby",r)}).next().attr("role","tabpanel");this.headers.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}).next().attr({"aria-hidden":"true"}).hide();this.active.length?this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}).next().attr({"aria-hidden":"false"}):this.headers.eq(0).attr("tabIndex",0);this._createIcons();this._setupEvents(i.event);"fill"===r?(t=u.height(),this.element.siblings(":visible").each(function(){var i=n(this),r=i.css("position");"absolute"!==r&&"fixed"!==r&&(t-=i.outerHeight(!0))}),this.headers.each(function(){t-=n(this).outerHeight(!0)}),this.headers.next().each(function(){n(this).height(Math.max(0,t-n(this).innerHeight()+n(this).height()))}).css("overflow","auto")):"auto"===r&&(t=0,this.headers.next().each(function(){t=Math.max(t,n(this).css("height","").height())}).height(t))},_activate:function(t){var i=this._findActive(t)[0];i!==this.active[0]&&(i=i||this.active[0],this._eventHandler({target:i,currentTarget:i,preventDefault:n.noop}))},_findActive:function(t){return"number"==typeof t?this.headers.eq(t):n()},_setupEvents:function(t){var i={keydown:"_keydown"};t&&n.each(t.split(" "),function(n,t){i[t]="_eventHandler"});this._off(this.headers.add(this.headers.next()));this._on(this.headers,i);this._on(this.headers.next(),{keydown:"_panelKeyDown"});this._hoverable(this.headers);this._focusable(this.headers)},_eventHandler:function(t){var i=this.options,u=this.active,r=n(t.currentTarget),f=r[0]===u[0],e=f&&i.collapsible,s=e?n():r.next(),h=u.next(),o={oldHeader:u,oldPanel:h,newHeader:e?n():r,newPanel:s};t.preventDefault();f&&!i.collapsible||this._trigger("beforeActivate",t,o)===!1||(i.active=e?!1:this.headers.index(r),this.active=f?n():r,this._toggle(o),u.removeClass("ui-accordion-header-active ui-state-active"),i.icons&&u.children(".ui-accordion-header-icon").removeClass(i.icons.activeHeader).addClass(i.icons.header),f||(r.removeClass("ui-corner-all").addClass("ui-accordion-header-active ui-state-active ui-corner-top"),i.icons&&r.children(".ui-accordion-header-icon").removeClass(i.icons.header).addClass(i.icons.activeHeader),r.next().addClass("ui-accordion-content-active")))},_toggle:function(t){var r=t.newPanel,i=this.prevShow.length?this.prevShow:t.oldPanel;this.prevShow.add(this.prevHide).stop(!0,!0);this.prevShow=r;this.prevHide=i;this.options.animate?this._animate(r,i,t):(i.hide(),r.show(),this._toggleComplete(t));i.attr({"aria-hidden":"true"});i.prev().attr({"aria-selected":"false","aria-expanded":"false"});r.length&&i.length?i.prev().attr({tabIndex:-1,"aria-expanded":"false"}):r.length&&this.headers.filter(function(){return 0===parseInt(n(this).attr("tabIndex"),10)}).attr("tabIndex",-1);r.attr("aria-hidden","false").prev().attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_animate:function(n,t,i){var h,r,u,c=this,o=0,l=n.css("box-sizing"),a=n.length&&(!t.length||n.index()<t.index()),e=this.options.animate||{},f=a&&e.down||e,s=function(){c._toggleComplete(i)};return"number"==typeof f&&(u=f),"string"==typeof f&&(r=f),r=r||f.easing||e.easing,u=u||f.duration||e.duration,t.length?n.length?(h=n.show().outerHeight(),t.animate(this.hideProps,{duration:u,easing:r,step:function(n,t){t.now=Math.round(n)}}),n.hide().animate(this.showProps,{duration:u,easing:r,complete:s,step:function(n,i){i.now=Math.round(n);"height"!==i.prop?"content-box"===l&&(o+=i.now):"content"!==c.options.heightStyle&&(i.now=Math.round(h-t.outerHeight()-o),o=0)}}),void 0):t.animate(this.hideProps,u,r,s):n.animate(this.showProps,u,r,s)},_toggleComplete:function(n){var t=n.oldPanel;t.removeClass("ui-accordion-content-active").prev().removeClass("ui-corner-top").addClass("ui-corner-all");t.length&&(t.parent()[0].className=t.parent()[0].className);this._trigger("activate",null,n)}});n.widget("ui.menu",{version:"1.11.4",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-carat-1-e"},items:"> *",menus:"ul",position:{my:"left-1 top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element;this.mouseHandled=!1;this.element.uniqueId().addClass("ui-menu ui-widget ui-widget-content").toggleClass("ui-menu-icons",!!this.element.find(".ui-icon").length).attr({role:this.options.role,tabIndex:0});this.options.disabled&&this.element.addClass("ui-state-disabled").attr("aria-disabled","true");this._on({"mousedown .ui-menu-item":function(n){n.preventDefault()},"click .ui-menu-item":function(t){var i=n(t.target);!this.mouseHandled&&i.not(".ui-state-disabled").length&&(this.select(t),t.isPropagationStopped()||(this.mouseHandled=!0),i.has(".ui-menu").length?this.expand(t):!this.element.is(":focus")&&n(this.document[0].activeElement).closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer)))},"mouseenter .ui-menu-item":function(t){if(!this.previousFilter){var i=n(t.currentTarget);i.siblings(".ui-state-active").removeClass("ui-state-active");this.focus(t,i)}},mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(n,t){var i=this.active||this.element.find(this.options.items).eq(0);t||this.focus(n,i)},blur:function(t){this._delay(function(){n.contains(this.element[0],this.document[0].activeElement)||this.collapseAll(t)})},keydown:"_keydown"});this.refresh();this._on(this.document,{click:function(n){this._closeOnDocumentClick(n)&&this.collapseAll(n);this.mouseHandled=!1}})},_destroy:function(){this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeClass("ui-menu ui-widget ui-widget-content ui-menu-icons ui-front").removeAttr("role").removeAttr("tabIndex").removeAttr("aria-labelledby").removeAttr("aria-expanded").removeAttr("aria-hidden").removeAttr("aria-disabled").removeUniqueId().show();this.element.find(".ui-menu-item").removeClass("ui-menu-item").removeAttr("role").removeAttr("aria-disabled").removeUniqueId().removeClass("ui-state-hover").removeAttr("tabIndex").removeAttr("role").removeAttr("aria-haspopup").children().each(function(){var t=n(this);t.data("ui-menu-submenu-carat")&&t.remove()});this.element.find(".ui-menu-divider").removeClass("ui-menu-divider ui-widget-content")},_keydown:function(t){var i,u,r,f,e=!0;switch(t.keyCode){case n.ui.keyCode.PAGE_UP:this.previousPage(t);break;case n.ui.keyCode.PAGE_DOWN:this.nextPage(t);break;case n.ui.keyCode.HOME:this._move("first","first",t);break;case n.ui.keyCode.END:this._move("last","last",t);break;case n.ui.keyCode.UP:this.previous(t);break;case n.ui.keyCode.DOWN:this.next(t);break;case n.ui.keyCode.LEFT:this.collapse(t);break;case n.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(t);break;case n.ui.keyCode.ENTER:case n.ui.keyCode.SPACE:this._activate(t);break;case n.ui.keyCode.ESCAPE:this.collapse(t);break;default:e=!1;u=this.previousFilter||"";r=String.fromCharCode(t.keyCode);f=!1;clearTimeout(this.filterTimer);r===u?f=!0:r=u+r;i=this._filterMenuItems(r);i=f&&-1!==i.index(this.active.next())?this.active.nextAll(".ui-menu-item"):i;i.length||(r=String.fromCharCode(t.keyCode),i=this._filterMenuItems(r));i.length?(this.focus(t,i),this.previousFilter=r,this.filterTimer=this._delay(function(){delete this.previousFilter},1e3)):delete this.previousFilter}e&&t.preventDefault()},_activate:function(n){this.active.is(".ui-state-disabled")||(this.active.is("[aria-haspopup='true']")?this.expand(n):this.select(n))},refresh:function(){var i,t,u=this,f=this.options.icons.submenu,r=this.element.find(this.options.menus);this.element.toggleClass("ui-menu-icons",!!this.element.find(".ui-icon").length);r.filter(":not(.ui-menu)").addClass("ui-menu ui-widget ui-widget-content ui-front").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each(function(){var t=n(this),i=t.parent(),r=n("<span>").addClass("ui-menu-icon ui-icon "+f).data("ui-menu-submenu-carat",!0);i.attr("aria-haspopup","true").prepend(r);t.attr("aria-labelledby",i.attr("id"))});i=r.add(this.element);t=i.find(this.options.items);t.not(".ui-menu-item").each(function(){var t=n(this);u._isDivider(t)&&t.addClass("ui-widget-content ui-menu-divider")});t.not(".ui-menu-item, .ui-menu-divider").addClass("ui-menu-item").uniqueId().attr({tabIndex:-1,role:this._itemRole()});t.filter(".ui-state-disabled").attr("aria-disabled","true");this.active&&!n.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(n,t){"icons"===n&&this.element.find(".ui-menu-icon").removeClass(this.options.icons.submenu).addClass(t.submenu);"disabled"===n&&this.element.toggleClass("ui-state-disabled",!!t).attr("aria-disabled",t);this._super(n,t)},focus:function(n,t){var i,r;this.blur(n,n&&"focus"===n.type);this._scrollIntoView(t);this.active=t.first();r=this.active.addClass("ui-state-focus").removeClass("ui-state-active");this.options.role&&this.element.attr("aria-activedescendant",r.attr("id"));this.active.parent().closest(".ui-menu-item").addClass("ui-state-active");n&&"keydown"===n.type?this._close():this.timer=this._delay(function(){this._close()},this.delay);i=t.children(".ui-menu");i.length&&n&&/^mouse/.test(n.type)&&this._startOpening(i);this.activeMenu=t.parent();this._trigger("focus",n,{item:t})},_scrollIntoView:function(t){var e,o,i,r,u,f;this._hasScroll()&&(e=parseFloat(n.css(this.activeMenu[0],"borderTopWidth"))||0,o=parseFloat(n.css(this.activeMenu[0],"paddingTop"))||0,i=t.offset().top-this.activeMenu.offset().top-e-o,r=this.activeMenu.scrollTop(),u=this.activeMenu.height(),f=t.outerHeight(),0>i?this.activeMenu.scrollTop(r+i):i+f>u&&this.activeMenu.scrollTop(r+i-u+f))},blur:function(n,t){t||clearTimeout(this.timer);this.active&&(this.active.removeClass("ui-state-focus"),this.active=null,this._trigger("blur",n,{item:this.active}))},_startOpening:function(n){clearTimeout(this.timer);"true"===n.attr("aria-hidden")&&(this.timer=this._delay(function(){this._close();this._open(n)},this.delay))},_open:function(t){var i=n.extend({of:this.active},this.options.position);clearTimeout(this.timer);this.element.find(".ui-menu").not(t.parents(".ui-menu")).hide().attr("aria-hidden","true");t.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(i)},collapseAll:function(t,i){clearTimeout(this.timer);this.timer=this._delay(function(){var r=i?this.element:n(t&&t.target).closest(this.element.find(".ui-menu"));r.length||(r=this.element);this._close(r);this.blur(t);this.activeMenu=r},this.delay)},_close:function(n){n||(n=this.active?this.active.parent():this.element);n.find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false").end().find(".ui-state-active").not(".ui-state-focus").removeClass("ui-state-active")},_closeOnDocumentClick:function(t){return!n(t.target).closest(".ui-menu").length},_isDivider:function(n){return!/[^\-\u2014\u2013\s]/.test(n.text())},collapse:function(n){var t=this.active&&this.active.parent().closest(".ui-menu-item",this.element);t&&t.length&&(this._close(),this.focus(n,t))},expand:function(n){var t=this.active&&this.active.children(".ui-menu ").find(this.options.items).first();t&&t.length&&(this._open(t.parent()),this._delay(function(){this.focus(n,t)}))},next:function(n){this._move("next","first",n)},previous:function(n){this._move("prev","last",n)},isFirstItem:function(){return this.active&&!this.active.prevAll(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_move:function(n,t,i){var r;this.active&&(r="first"===n||"last"===n?this.active["first"===n?"prevAll":"nextAll"](".ui-menu-item").eq(-1):this.active[n+"All"](".ui-menu-item").eq(0));r&&r.length&&this.active||(r=this.activeMenu.find(this.options.items)[t]());this.focus(i,r)},nextPage:function(t){var i,r,u;return this.active?(this.isLastItem()||(this._hasScroll()?(r=this.active.offset().top,u=this.element.height(),this.active.nextAll(".ui-menu-item").each(function(){return i=n(this),0>i.offset().top-r-u}),this.focus(t,i)):this.focus(t,this.activeMenu.find(this.options.items)[this.active?"last":"first"]())),void 0):(this.next(t),void 0)},previousPage:function(t){var i,r,u;return this.active?(this.isFirstItem()||(this._hasScroll()?(r=this.active.offset().top,u=this.element.height(),this.active.prevAll(".ui-menu-item").each(function(){return i=n(this),i.offset().top-r+u>0}),this.focus(t,i)):this.focus(t,this.activeMenu.find(this.options.items).first())),void 0):(this.next(t),void 0)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(t){this.active=this.active||n(t.target).closest(".ui-menu-item");var i={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(t,!0);this._trigger("select",t,i)},_filterMenuItems:function(t){var i=t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"),r=RegExp("^"+i,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter(function(){return r.test(n.trim(n(this).text()))})}});n.widget("ui.autocomplete",{version:"1.11.4",defaultElement:"<input>",options:{appendTo:null,autoFocus:!1,delay:300,minLength:1,position:{my:"left top",at:"left bottom",collision:"none"},source:null,change:null,close:null,focus:null,open:null,response:null,search:null,select:null},requestIndex:0,pending:0,_create:function(){var t,i,r,u=this.element[0].nodeName.toLowerCase(),f="textarea"===u,e="input"===u;this.isMultiLine=f?!0:e?!1:this.element.prop("isContentEditable");this.valueMethod=this.element[f||e?"val":"text"];this.isNewMenu=!0;this.element.addClass("ui-autocomplete-input").attr("autocomplete","off");this._on(this.element,{keydown:function(u){if(this.element.prop("readOnly"))return t=!0,r=!0,i=!0,void 0;t=!1;r=!1;i=!1;var f=n.ui.keyCode;switch(u.keyCode){case f.PAGE_UP:t=!0;this._move("previousPage",u);break;case f.PAGE_DOWN:t=!0;this._move("nextPage",u);break;case f.UP:t=!0;this._keyEvent("previous",u);break;case f.DOWN:t=!0;this._keyEvent("next",u);break;case f.ENTER:this.menu.active&&(t=!0,u.preventDefault(),this.menu.select(u));break;case f.TAB:this.menu.active&&this.menu.select(u);break;case f.ESCAPE:this.menu.element.is(":visible")&&(this.isMultiLine||this._value(this.term),this.close(u),u.preventDefault());break;default:i=!0;this._searchTimeout(u)}},keypress:function(r){if(t)return t=!1,(!this.isMultiLine||this.menu.element.is(":visible"))&&r.preventDefault(),void 0;if(!i){var u=n.ui.keyCode;switch(r.keyCode){case u.PAGE_UP:this._move("previousPage",r);break;case u.PAGE_DOWN:this._move("nextPage",r);break;case u.UP:this._keyEvent("previous",r);break;case u.DOWN:this._keyEvent("next",r)}}},input:function(n){return r?(r=!1,n.preventDefault(),void 0):(this._searchTimeout(n),void 0)},focus:function(){this.selectedItem=null;this.previous=this._value()},blur:function(n){return this.cancelBlur?(delete this.cancelBlur,void 0):(clearTimeout(this.searching),this.close(n),this._change(n),void 0)}});this._initSource();this.menu=n("<ul>").addClass("ui-autocomplete ui-front").appendTo(this._appendTo()).menu({role:null}).hide().menu("instance");this._on(this.menu.element,{mousedown:function(t){t.preventDefault();this.cancelBlur=!0;this._delay(function(){delete this.cancelBlur});var i=this.menu.element[0];n(t.target).closest(".ui-menu-item").length||this._delay(function(){var t=this;this.document.one("mousedown",function(r){r.target===t.element[0]||r.target===i||n.contains(i,r.target)||t.close()})})},menufocus:function(t,i){var r,u;return this.isNewMenu&&(this.isNewMenu=!1,t.originalEvent&&/^mouse/.test(t.originalEvent.type))?(this.menu.blur(),this.document.one("mousemove",function(){n(t.target).trigger(t.originalEvent)}),void 0):(u=i.item.data("ui-autocomplete-item"),!1!==this._trigger("focus",t,{item:u})&&t.originalEvent&&/^key/.test(t.originalEvent.type)&&this._value(u.value),r=i.item.attr("aria-label")||u.value,r&&n.trim(r).length&&(this.liveRegion.children().hide(),n("<div>").text(r).appendTo(this.liveRegion)),void 0)},menuselect:function(n,t){var i=t.item.data("ui-autocomplete-item"),r=this.previous;this.element[0]!==this.document[0].activeElement&&(this.element.focus(),this.previous=r,this._delay(function(){this.previous=r;this.selectedItem=i}));!1!==this._trigger("select",n,{item:i})&&this._value(i.value);this.term=this._value();this.close(n);this.selectedItem=i}});this.liveRegion=n("<span>",{role:"status","aria-live":"assertive","aria-relevant":"additions"}).addClass("ui-helper-hidden-accessible").appendTo(this.document[0].body);this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_destroy:function(){clearTimeout(this.searching);this.element.removeClass("ui-autocomplete-input").removeAttr("autocomplete");this.menu.element.remove();this.liveRegion.remove()},_setOption:function(n,t){this._super(n,t);"source"===n&&this._initSource();"appendTo"===n&&this.menu.element.appendTo(this._appendTo());"disabled"===n&&t&&this.xhr&&this.xhr.abort()},_appendTo:function(){var t=this.options.appendTo;return t&&(t=t.jquery||t.nodeType?n(t):this.document.find(t).eq(0)),t&&t[0]||(t=this.element.closest(".ui-front")),t.length||(t=this.document[0].body),t},_initSource:function(){var i,r,t=this;n.isArray(this.options.source)?(i=this.options.source,this.source=function(t,r){r(n.ui.autocomplete.filter(i,t.term))}):"string"==typeof this.options.source?(r=this.options.source,this.source=function(i,u){t.xhr&&t.xhr.abort();t.xhr=n.ajax({url:r,data:i,dataType:"json",success:function(n){u(n)},error:function(){u([])}})}):this.source=this.options.source},_searchTimeout:function(n){clearTimeout(this.searching);this.searching=this._delay(function(){var t=this.term===this._value(),i=this.menu.element.is(":visible"),r=n.altKey||n.ctrlKey||n.metaKey||n.shiftKey;t&&(!t||i||r)||(this.selectedItem=null,this.search(null,n))},this.options.delay)},search:function(n,t){return n=null!=n?n:this._value(),this.term=this._value(),n.length<this.options.minLength?this.close(t):this._trigger("search",t)!==!1?this._search(n):void 0},_search:function(n){this.pending++;this.element.addClass("ui-autocomplete-loading");this.cancelSearch=!1;this.source({term:n},this._response())},_response:function(){var t=++this.requestIndex;return n.proxy(function(n){t===this.requestIndex&&this.__response(n);this.pending--;this.pending||this.element.removeClass("ui-autocomplete-loading")},this)},__response:function(n){n&&(n=this._normalize(n));this._trigger("response",null,{content:n});!this.options.disabled&&n&&n.length&&!this.cancelSearch?(this._suggest(n),this._trigger("open")):this._close()},close:function(n){this.cancelSearch=!0;this._close(n)},_close:function(n){this.menu.element.is(":visible")&&(this.menu.element.hide(),this.menu.blur(),this.isNewMenu=!0,this._trigger("close",n))},_change:function(n){this.previous!==this._value()&&this._trigger("change",n,{item:this.selectedItem})},_normalize:function(t){return t.length&&t[0].label&&t[0].value?t:n.map(t,function(t){return"string"==typeof t?{label:t,value:t}:n.extend({},t,{label:t.label||t.value,value:t.value||t.label})})},_suggest:function(t){var i=this.menu.element.empty();this._renderMenu(i,t);this.isNewMenu=!0;this.menu.refresh();i.show();this._resizeMenu();i.position(n.extend({of:this.element},this.options.position));this.options.autoFocus&&this.menu.next()},_resizeMenu:function(){var n=this.menu.element;n.outerWidth(Math.max(n.width("").outerWidth()+1,this.element.outerWidth()))},_renderMenu:function(t,i){var r=this;n.each(i,function(n,i){r._renderItemData(t,i)})},_renderItemData:function(n,t){return this._renderItem(n,t).data("ui-autocomplete-item",t)},_renderItem:function(t,i){return n("<li>").text(i.label).appendTo(t)},_move:function(n,t){return this.menu.element.is(":visible")?this.menu.isFirstItem()&&/^previous/.test(n)||this.menu.isLastItem()&&/^next/.test(n)?(this.isMultiLine||this._value(this.term),this.menu.blur(),void 0):(this.menu[n](t),void 0):(this.search(null,t),void 0)},widget:function(){return this.menu.element},_value:function(){return this.valueMethod.apply(this.element,arguments)},_keyEvent:function(n,t){(!this.isMultiLine||this.menu.element.is(":visible"))&&(this._move(n,t),t.preventDefault())}});n.extend(n.ui.autocomplete,{escapeRegex:function(n){return n.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")},filter:function(t,i){var r=RegExp(n.ui.autocomplete.escapeRegex(i),"i");return n.grep(t,function(n){return r.test(n.label||n.value||n)})}});n.widget("ui.autocomplete",n.ui.autocomplete,{options:{messages:{noResults:"No search results.",results:function(n){return n+(n>1?" results are":" result is")+" available, use up and down arrow keys to navigate."}}},__response:function(t){var i;this._superApply(arguments);this.options.disabled||this.cancelSearch||(i=t&&t.length?this.options.messages.results(t.length):this.options.messages.noResults,this.liveRegion.children().hide(),n("<div>").text(i).appendTo(this.liveRegion))}});n.ui.autocomplete;var e,p="ui-button ui-widget ui-state-default ui-corner-all",w="ui-button-icons-only ui-button-icon-only ui-button-text-icons ui-button-text-icon-primary ui-button-text-icon-secondary ui-button-text-only",d=function(){var t=n(this);setTimeout(function(){t.find(":ui-button").button("refresh")},1)},b=function(t){var i=t.name,r=t.form,u=n([]);return i&&(i=i.replace(/'/g,"\\'"),u=r?n(r).find("[name='"+i+"'][type=radio]"):n("[name='"+i+"'][type=radio]",t.ownerDocument).filter(function(){return!this.form})),u};n.widget("ui.button",{version:"1.11.4",defaultElement:"<button>",options:{disabled:null,text:!0,label:null,icons:{primary:null,secondary:null}},_create:function(){this.element.closest("form").unbind("reset"+this.eventNamespace).bind("reset"+this.eventNamespace,d);"boolean"!=typeof this.options.disabled?this.options.disabled=!!this.element.prop("disabled"):this.element.prop("disabled",this.options.disabled);this._determineButtonType();this.hasTitle=!!this.buttonElement.attr("title");var i=this,t=this.options,r="checkbox"===this.type||"radio"===this.type,u=r?"":"ui-state-active";null===t.label&&(t.label="input"===this.type?this.buttonElement.val():this.buttonElement.html());this._hoverable(this.buttonElement);this.buttonElement.addClass(p).attr("role","button").bind("mouseenter"+this.eventNamespace,function(){t.disabled||this===e&&n(this).addClass("ui-state-active")}).bind("mouseleave"+this.eventNamespace,function(){t.disabled||n(this).removeClass(u)}).bind("click"+this.eventNamespace,function(n){t.disabled&&(n.preventDefault(),n.stopImmediatePropagation())});this._on({focus:function(){this.buttonElement.addClass("ui-state-focus")},blur:function(){this.buttonElement.removeClass("ui-state-focus")}});r&&this.element.bind("change"+this.eventNamespace,function(){i.refresh()});"checkbox"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(t.disabled)return!1}):"radio"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(t.disabled)return!1;n(this).addClass("ui-state-active");i.buttonElement.attr("aria-pressed","true");var r=i.element[0];b(r).not(r).map(function(){return n(this).button("widget")[0]}).removeClass("ui-state-active").attr("aria-pressed","false")}):(this.buttonElement.bind("mousedown"+this.eventNamespace,function(){return t.disabled?!1:(n(this).addClass("ui-state-active"),e=this,i.document.one("mouseup",function(){e=null}),void 0)}).bind("mouseup"+this.eventNamespace,function(){return t.disabled?!1:(n(this).removeClass("ui-state-active"),void 0)}).bind("keydown"+this.eventNamespace,function(i){return t.disabled?!1:((i.keyCode===n.ui.keyCode.SPACE||i.keyCode===n.ui.keyCode.ENTER)&&n(this).addClass("ui-state-active"),void 0)}).bind("keyup"+this.eventNamespace+" blur"+this.eventNamespace,function(){n(this).removeClass("ui-state-active")}),this.buttonElement.is("a")&&this.buttonElement.keyup(function(t){t.keyCode===n.ui.keyCode.SPACE&&n(this).click()}));this._setOption("disabled",t.disabled);this._resetButton()},_determineButtonType:function(){var n,t,i;this.type=this.element.is("[type=checkbox]")?"checkbox":this.element.is("[type=radio]")?"radio":this.element.is("input")?"input":"button";"checkbox"===this.type||"radio"===this.type?(n=this.element.parents().last(),t="label[for='"+this.element.attr("id")+"']",this.buttonElement=n.find(t),this.buttonElement.length||(n=n.length?n.siblings():this.element.siblings(),this.buttonElement=n.filter(t),this.buttonElement.length||(this.buttonElement=n.find(t))),this.element.addClass("ui-helper-hidden-accessible"),i=this.element.is(":checked"),i&&this.buttonElement.addClass("ui-state-active"),this.buttonElement.prop("aria-pressed",i)):this.buttonElement=this.element},widget:function(){return this.buttonElement},_destroy:function(){this.element.removeClass("ui-helper-hidden-accessible");this.buttonElement.removeClass(p+" ui-state-active "+w).removeAttr("role").removeAttr("aria-pressed").html(this.buttonElement.find(".ui-button-text").html());this.hasTitle||this.buttonElement.removeAttr("title")},_setOption:function(n,t){return this._super(n,t),"disabled"===n?(this.widget().toggleClass("ui-state-disabled",!!t),this.element.prop("disabled",!!t),t&&("checkbox"===this.type||"radio"===this.type?this.buttonElement.removeClass("ui-state-focus"):this.buttonElement.removeClass("ui-state-focus ui-state-active")),void 0):(this._resetButton(),void 0)},refresh:function(){var t=this.element.is("input, button")?this.element.is(":disabled"):this.element.hasClass("ui-button-disabled");t!==this.options.disabled&&this._setOption("disabled",t);"radio"===this.type?b(this.element[0]).each(function(){n(this).is(":checked")?n(this).button("widget").addClass("ui-state-active").attr("aria-pressed","true"):n(this).button("widget").removeClass("ui-state-active").attr("aria-pressed","false")}):"checkbox"===this.type&&(this.element.is(":checked")?this.buttonElement.addClass("ui-state-active").attr("aria-pressed","true"):this.buttonElement.removeClass("ui-state-active").attr("aria-pressed","false"))},_resetButton:function(){if("input"===this.type)return this.options.label&&this.element.val(this.options.label),void 0;var i=this.buttonElement.removeClass(w),f=n("<span><\/span>",this.document[0]).addClass("ui-button-text").html(this.options.label).appendTo(i.empty()).text(),t=this.options.icons,u=t.primary&&t.secondary,r=[];t.primary||t.secondary?(this.options.text&&r.push("ui-button-text-icon"+(u?"s":t.primary?"-primary":"-secondary")),t.primary&&i.prepend("<span class='ui-button-icon-primary ui-icon "+t.primary+"'><\/span>"),t.secondary&&i.append("<span class='ui-button-icon-secondary ui-icon "+t.secondary+"'><\/span>"),this.options.text||(r.push(u?"ui-button-icons-only":"ui-button-icon-only"),this.hasTitle||i.attr("title",n.trim(f)))):r.push("ui-button-text-only");i.addClass(r.join(" "))}});n.widget("ui.buttonset",{version:"1.11.4",options:{items:"button, input[type=button], input[type=submit], input[type=reset], input[type=checkbox], input[type=radio], a, :data(ui-button)"},_create:function(){this.element.addClass("ui-buttonset")},_init:function(){this.refresh()},_setOption:function(n,t){"disabled"===n&&this.buttons.button("option",n,t);this._super(n,t)},refresh:function(){var i="rtl"===this.element.css("direction"),t=this.element.find(this.options.items),r=t.filter(":ui-button");t.not(":ui-button").button();r.button("refresh");this.buttons=t.map(function(){return n(this).button("widget")[0]}).removeClass("ui-corner-all ui-corner-left ui-corner-right").filter(":first").addClass(i?"ui-corner-right":"ui-corner-left").end().filter(":last").addClass(i?"ui-corner-left":"ui-corner-right").end().end()},_destroy:function(){this.element.removeClass("ui-buttonset");this.buttons.map(function(){return n(this).button("widget")[0]}).removeClass("ui-corner-left ui-corner-right").end().button("destroy")}});n.ui.button;n.extend(n.ui,{datepicker:{version:"1.11.4"}});n.extend(l.prototype,{markerClassName:"hasDatepicker",maxRows:4,_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(n){return u(this._defaults,n||{}),this},_attachDatepicker:function(t,i){var r,f,u;r=t.nodeName.toLowerCase();f="div"===r||"span"===r;t.id||(this.uuid+=1,t.id="dp"+this.uuid);u=this._newInst(n(t),f);u.settings=n.extend({},i||{});"input"===r?this._connectDatepicker(t,u):f&&this._inlineDatepicker(t,u)},_newInst:function(t,i){var r=t[0].id.replace(/([^A-Za-z0-9_\-])/g,"\\\\$1");return{id:r,input:t,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:i,dpDiv:i?a(n("<div class='"+this._inlineClass+" ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'><\/div>")):this.dpDiv}},_connectDatepicker:function(t,i){var r=n(t);i.append=n([]);i.trigger=n([]);r.hasClass(this.markerClassName)||(this._attachments(r,i),r.addClass(this.markerClassName).keydown(this._doKeyDown).keypress(this._doKeyPress).keyup(this._doKeyUp),this._autoSize(i),n.data(t,"datepicker",i),i.settings.disabled&&this._disableDatepicker(t))},_attachments:function(t,i){var u,r,f,e=this._get(i,"appendText"),o=this._get(i,"isRTL");i.append&&i.append.remove();e&&(i.append=n("<span class='"+this._appendClass+"'>"+e+"<\/span>"),t[o?"before":"after"](i.append));t.unbind("focus",this._showDatepicker);i.trigger&&i.trigger.remove();u=this._get(i,"showOn");("focus"===u||"both"===u)&&t.focus(this._showDatepicker);("button"===u||"both"===u)&&(r=this._get(i,"buttonText"),f=this._get(i,"buttonImage"),i.trigger=n(this._get(i,"buttonImageOnly")?n("<img/>").addClass(this._triggerClass).attr({src:f,alt:r,title:r}):n("<button type='button'><\/button>").addClass(this._triggerClass).html(f?n("<img/>").attr({src:f,alt:r,title:r}):r)),t[o?"before":"after"](i.trigger),i.trigger.click(function(){return n.datepicker._datepickerShowing&&n.datepicker._lastInput===t[0]?n.datepicker._hideDatepicker():n.datepicker._datepickerShowing&&n.datepicker._lastInput!==t[0]?(n.datepicker._hideDatepicker(),n.datepicker._showDatepicker(t[0])):n.datepicker._showDatepicker(t[0]),!1}))},_autoSize:function(n){if(this._get(n,"autoSize")&&!n.inline){var r,u,f,t,i=new Date(2009,11,20),e=this._get(n,"dateFormat");e.match(/[DM]/)&&(r=function(n){for(u=0,f=0,t=0;n.length>t;t++)n[t].length>u&&(u=n[t].length,f=t);return f},i.setMonth(r(this._get(n,e.match(/MM/)?"monthNames":"monthNamesShort"))),i.setDate(r(this._get(n,e.match(/DD/)?"dayNames":"dayNamesShort"))+20-i.getDay()));n.input.attr("size",this._formatDate(n,i).length)}},_inlineDatepicker:function(t,i){var r=n(t);r.hasClass(this.markerClassName)||(r.addClass(this.markerClassName).append(i.dpDiv),n.data(t,"datepicker",i),this._setDate(i,this._getDefaultDate(i),!0),this._updateDatepicker(i),this._updateAlternate(i),i.settings.disabled&&this._disableDatepicker(t),i.dpDiv.css("display","block"))},_dialogDatepicker:function(t,i,r,f,e){var s,h,c,l,a,o=this._dialogInst;return o||(this.uuid+=1,s="dp"+this.uuid,this._dialogInput=n("<input type='text' id='"+s+"' style='position: absolute; top: -100px; width: 0px;'/>"),this._dialogInput.keydown(this._doKeyDown),n("body").append(this._dialogInput),o=this._dialogInst=this._newInst(this._dialogInput,!1),o.settings={},n.data(this._dialogInput[0],"datepicker",o)),u(o.settings,f||{}),i=i&&i.constructor===Date?this._formatDate(o,i):i,this._dialogInput.val(i),this._pos=e?e.length?e:[e.pageX,e.pageY]:null,this._pos||(h=document.documentElement.clientWidth,c=document.documentElement.clientHeight,l=document.documentElement.scrollLeft||document.body.scrollLeft,a=document.documentElement.scrollTop||document.body.scrollTop,this._pos=[h/2-100+l,c/2-150+a]),this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px"),o.settings.onSelect=r,this._inDialog=!0,this.dpDiv.addClass(this._dialogClass),this._showDatepicker(this._dialogInput[0]),n.blockUI&&n.blockUI(this.dpDiv),n.data(this._dialogInput[0],"datepicker",o),this},_destroyDatepicker:function(t){var r,u=n(t),f=n.data(t,"datepicker");u.hasClass(this.markerClassName)&&(r=t.nodeName.toLowerCase(),n.removeData(t,"datepicker"),"input"===r?(f.append.remove(),f.trigger.remove(),u.removeClass(this.markerClassName).unbind("focus",this._showDatepicker).unbind("keydown",this._doKeyDown).unbind("keypress",this._doKeyPress).unbind("keyup",this._doKeyUp)):("div"===r||"span"===r)&&u.removeClass(this.markerClassName).empty(),i===f&&(i=null))},_enableDatepicker:function(t){var i,r,u=n(t),f=n.data(t,"datepicker");u.hasClass(this.markerClassName)&&(i=t.nodeName.toLowerCase(),"input"===i?(t.disabled=!1,f.trigger.filter("button").each(function(){this.disabled=!1}).end().filter("img").css({opacity:"1.0",cursor:""})):("div"===i||"span"===i)&&(r=u.children("."+this._inlineClass),r.children().removeClass("ui-state-disabled"),r.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!1)),this._disabledInputs=n.map(this._disabledInputs,function(n){return n===t?null:n}))},_disableDatepicker:function(t){var i,r,u=n(t),f=n.data(t,"datepicker");u.hasClass(this.markerClassName)&&(i=t.nodeName.toLowerCase(),"input"===i?(t.disabled=!0,f.trigger.filter("button").each(function(){this.disabled=!0}).end().filter("img").css({opacity:"0.5",cursor:"default"})):("div"===i||"span"===i)&&(r=u.children("."+this._inlineClass),r.children().addClass("ui-state-disabled"),r.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!0)),this._disabledInputs=n.map(this._disabledInputs,function(n){return n===t?null:n}),this._disabledInputs[this._disabledInputs.length]=t)},_isDisabledDatepicker:function(n){if(!n)return!1;for(var t=0;this._disabledInputs.length>t;t++)if(this._disabledInputs[t]===n)return!0;return!1},_getInst:function(t){try{return n.data(t,"datepicker")}catch(i){throw"Missing instance data for this datepicker";}},_optionDatepicker:function(t,i,r){var e,h,o,s,f=this._getInst(t);return 2===arguments.length&&"string"==typeof i?"defaults"===i?n.extend({},n.datepicker._defaults):f?"all"===i?n.extend({},f.settings):this._get(f,i):null:(e=i||{},"string"==typeof i&&(e={},e[i]=r),f&&(this._curInst===f&&this._hideDatepicker(),h=this._getDateDatepicker(t,!0),o=this._getMinMaxDate(f,"min"),s=this._getMinMaxDate(f,"max"),u(f.settings,e),null!==o&&void 0!==e.dateFormat&&void 0===e.minDate&&(f.settings.minDate=this._formatDate(f,o)),null!==s&&void 0!==e.dateFormat&&void 0===e.maxDate&&(f.settings.maxDate=this._formatDate(f,s)),"disabled"in e&&(e.disabled?this._disableDatepicker(t):this._enableDatepicker(t)),this._attachments(n(t),f),this._autoSize(f),this._setDate(f,h),this._updateAlternate(f),this._updateDatepicker(f)),void 0)},_changeDatepicker:function(n,t,i){this._optionDatepicker(n,t,i)},_refreshDatepicker:function(n){var t=this._getInst(n);t&&this._updateDatepicker(t)},_setDateDatepicker:function(n,t){var i=this._getInst(n);i&&(this._setDate(i,t),this._updateDatepicker(i),this._updateAlternate(i))},_getDateDatepicker:function(n,t){var i=this._getInst(n);return i&&!i.inline&&this._setDateFromField(i,t),i?this._getDate(i):null},_doKeyDown:function(t){var u,e,f,i=n.datepicker._getInst(t.target),r=!0,o=i.dpDiv.is(".ui-datepicker-rtl");if(i._keyEvent=!0,n.datepicker._datepickerShowing)switch(t.keyCode){case 9:n.datepicker._hideDatepicker();r=!1;break;case 13:return f=n("td."+n.datepicker._dayOverClass+":not(."+n.datepicker._currentClass+")",i.dpDiv),f[0]&&n.datepicker._selectDay(t.target,i.selectedMonth,i.selectedYear,f[0]),u=n.datepicker._get(i,"onSelect"),u?(e=n.datepicker._formatDate(i),u.apply(i.input?i.input[0]:null,[e,i])):n.datepicker._hideDatepicker(),!1;case 27:n.datepicker._hideDatepicker();break;case 33:n.datepicker._adjustDate(t.target,t.ctrlKey?-n.datepicker._get(i,"stepBigMonths"):-n.datepicker._get(i,"stepMonths"),"M");break;case 34:n.datepicker._adjustDate(t.target,t.ctrlKey?+n.datepicker._get(i,"stepBigMonths"):+n.datepicker._get(i,"stepMonths"),"M");break;case 35:(t.ctrlKey||t.metaKey)&&n.datepicker._clearDate(t.target);r=t.ctrlKey||t.metaKey;break;case 36:(t.ctrlKey||t.metaKey)&&n.datepicker._gotoToday(t.target);r=t.ctrlKey||t.metaKey;break;case 37:(t.ctrlKey||t.metaKey)&&n.datepicker._adjustDate(t.target,o?1:-1,"D");r=t.ctrlKey||t.metaKey;t.originalEvent.altKey&&n.datepicker._adjustDate(t.target,t.ctrlKey?-n.datepicker._get(i,"stepBigMonths"):-n.datepicker._get(i,"stepMonths"),"M");break;case 38:(t.ctrlKey||t.metaKey)&&n.datepicker._adjustDate(t.target,-7,"D");r=t.ctrlKey||t.metaKey;break;case 39:(t.ctrlKey||t.metaKey)&&n.datepicker._adjustDate(t.target,o?-1:1,"D");r=t.ctrlKey||t.metaKey;t.originalEvent.altKey&&n.datepicker._adjustDate(t.target,t.ctrlKey?+n.datepicker._get(i,"stepBigMonths"):+n.datepicker._get(i,"stepMonths"),"M");break;case 40:(t.ctrlKey||t.metaKey)&&n.datepicker._adjustDate(t.target,7,"D");r=t.ctrlKey||t.metaKey;break;default:r=!1}else 36===t.keyCode&&t.ctrlKey?n.datepicker._showDatepicker(this):r=!1;r&&(t.preventDefault(),t.stopPropagation())},_doKeyPress:function(t){var i,r,u=n.datepicker._getInst(t.target);if(n.datepicker._get(u,"constrainInput"))return(i=n.datepicker._possibleChars(n.datepicker._get(u,"dateFormat")),r=String.fromCharCode(null==t.charCode?t.keyCode:t.charCode),t.ctrlKey||t.metaKey||" ">r||!i||i.indexOf(r)>-1)},_doKeyUp:function(t){var r,i=n.datepicker._getInst(t.target);if(i.input.val()!==i.lastVal)try{r=n.datepicker.parseDate(n.datepicker._get(i,"dateFormat"),i.input?i.input.val():null,n.datepicker._getFormatConfig(i));r&&(n.datepicker._setDateFromField(i),n.datepicker._updateAlternate(i),n.datepicker._updateDatepicker(i))}catch(u){}return!0},_showDatepicker:function(t){if(t=t.target||t,"input"!==t.nodeName.toLowerCase()&&(t=n("input",t.parentNode)[0]),!n.datepicker._isDisabledDatepicker(t)&&n.datepicker._lastInput!==t){var i,o,s,r,f,e,h;i=n.datepicker._getInst(t);n.datepicker._curInst&&n.datepicker._curInst!==i&&(n.datepicker._curInst.dpDiv.stop(!0,!0),i&&n.datepicker._datepickerShowing&&n.datepicker._hideDatepicker(n.datepicker._curInst.input[0]));o=n.datepicker._get(i,"beforeShow");s=o?o.apply(t,[t,i]):{};s!==!1&&(u(i.settings,s),i.lastVal=null,n.datepicker._lastInput=t,n.datepicker._setDateFromField(i),n.datepicker._inDialog&&(t.value=""),n.datepicker._pos||(n.datepicker._pos=n.datepicker._findPos(t),n.datepicker._pos[1]+=t.offsetHeight),r=!1,n(t).parents().each(function(){return r|="fixed"===n(this).css("position"),!r}),f={left:n.datepicker._pos[0],top:n.datepicker._pos[1]},n.datepicker._pos=null,i.dpDiv.empty(),i.dpDiv.css({position:"absolute",display:"block",top:"-1000px"}),n.datepicker._updateDatepicker(i),f=n.datepicker._checkOffset(i,f,r),i.dpDiv.css({position:n.datepicker._inDialog&&n.blockUI?"static":r?"fixed":"absolute",display:"none",left:f.left+"px",top:f.top+"px"}),i.inline||(e=n.datepicker._get(i,"showAnim"),h=n.datepicker._get(i,"duration"),i.dpDiv.css("z-index",k(n(t))+1),n.datepicker._datepickerShowing=!0,n.effects&&n.effects.effect[e]?i.dpDiv.show(e,n.datepicker._get(i,"showOptions"),h):i.dpDiv[e||"show"](e?h:null),n.datepicker._shouldFocusInput(i)&&i.input.focus(),n.datepicker._curInst=i))}},_updateDatepicker:function(t){this.maxRows=4;i=t;t.dpDiv.empty().append(this._generateHTML(t));this._attachHandlers(t);var r,u=this._getNumberOfMonths(t),f=u[1],e=t.dpDiv.find("."+this._dayOverClass+" a");e.length>0&&v.apply(e.get(0));t.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width("");f>1&&t.dpDiv.addClass("ui-datepicker-multi-"+f).css("width",17*f+"em");t.dpDiv[(1!==u[0]||1!==u[1]?"add":"remove")+"Class"]("ui-datepicker-multi");t.dpDiv[(this._get(t,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl");t===n.datepicker._curInst&&n.datepicker._datepickerShowing&&n.datepicker._shouldFocusInput(t)&&t.input.focus();t.yearshtml&&(r=t.yearshtml,setTimeout(function(){r===t.yearshtml&&t.yearshtml&&t.dpDiv.find("select.ui-datepicker-year:first").replaceWith(t.yearshtml);r=t.yearshtml=null},0))},_shouldFocusInput:function(n){return n.input&&n.input.is(":visible")&&!n.input.is(":disabled")&&!n.input.is(":focus")},_checkOffset:function(t,i,r){var u=t.dpDiv.outerWidth(),f=t.dpDiv.outerHeight(),h=t.input?t.input.outerWidth():0,o=t.input?t.input.outerHeight():0,e=document.documentElement.clientWidth+(r?0:n(document).scrollLeft()),s=document.documentElement.clientHeight+(r?0:n(document).scrollTop());return i.left-=this._get(t,"isRTL")?u-h:0,i.left-=r&&i.left===t.input.offset().left?n(document).scrollLeft():0,i.top-=r&&i.top===t.input.offset().top+o?n(document).scrollTop():0,i.left-=Math.min(i.left,i.left+u>e&&e>u?Math.abs(i.left+u-e):0),i.top-=Math.min(i.top,i.top+f>s&&s>f?Math.abs(f+o):0),i},_findPos:function(t){for(var i,r=this._getInst(t),u=this._get(r,"isRTL");t&&("hidden"===t.type||1!==t.nodeType||n.expr.filters.hidden(t));)t=t[u?"previousSibling":"nextSibling"];return i=n(t).offset(),[i.left,i.top]},_hideDatepicker:function(t){var r,f,u,e,i=this._curInst;!i||t&&i!==n.data(t,"datepicker")||this._datepickerShowing&&(r=this._get(i,"showAnim"),f=this._get(i,"duration"),u=function(){n.datepicker._tidyDialog(i)},n.effects&&(n.effects.effect[r]||n.effects[r])?i.dpDiv.hide(r,n.datepicker._get(i,"showOptions"),f,u):i.dpDiv["slideDown"===r?"slideUp":"fadeIn"===r?"fadeOut":"hide"](r?f:null,u),r||u(),this._datepickerShowing=!1,e=this._get(i,"onClose"),e&&e.apply(i.input?i.input[0]:null,[i.input?i.input.val():"",i]),this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),n.blockUI&&(n.unblockUI(),n("body").append(this.dpDiv))),this._inDialog=!1)},_tidyDialog:function(n){n.dpDiv.removeClass(this._dialogClass).unbind(".ui-datepicker-calendar")},_checkExternalClick:function(t){if(n.datepicker._curInst){var i=n(t.target),r=n.datepicker._getInst(i[0]);(i[0].id===n.datepicker._mainDivId||0!==i.parents("#"+n.datepicker._mainDivId).length||i.hasClass(n.datepicker.markerClassName)||i.closest("."+n.datepicker._triggerClass).length||!n.datepicker._datepickerShowing||n.datepicker._inDialog&&n.blockUI)&&(!i.hasClass(n.datepicker.markerClassName)||n.datepicker._curInst===r)||n.datepicker._hideDatepicker()}},_adjustDate:function(t,i,r){var f=n(t),u=this._getInst(f[0]);this._isDisabledDatepicker(f[0])||(this._adjustInstDate(u,i+("M"===r?this._get(u,"showCurrentAtPos"):0),r),this._updateDatepicker(u))},_gotoToday:function(t){var r,u=n(t),i=this._getInst(u[0]);this._get(i,"gotoCurrent")&&i.currentDay?(i.selectedDay=i.currentDay,i.drawMonth=i.selectedMonth=i.currentMonth,i.drawYear=i.selectedYear=i.currentYear):(r=new Date,i.selectedDay=r.getDate(),i.drawMonth=i.selectedMonth=r.getMonth(),i.drawYear=i.selectedYear=r.getFullYear());this._notifyChange(i);this._adjustDate(u)},_selectMonthYear:function(t,i,r){var f=n(t),u=this._getInst(f[0]);u["selected"+("M"===r?"Month":"Year")]=u["draw"+("M"===r?"Month":"Year")]=parseInt(i.options[i.selectedIndex].value,10);this._notifyChange(u);this._adjustDate(f)},_selectDay:function(t,i,r,u){var f,e=n(t);n(u).hasClass(this._unselectableClass)||this._isDisabledDatepicker(e[0])||(f=this._getInst(e[0]),f.selectedDay=f.currentDay=n("a",u).html(),f.selectedMonth=f.currentMonth=i,f.selectedYear=f.currentYear=r,this._selectDate(t,this._formatDate(f,f.currentDay,f.currentMonth,f.currentYear)))},_clearDate:function(t){var i=n(t);this._selectDate(i,"")},_selectDate:function(t,i){var u,f=n(t),r=this._getInst(f[0]);i=null!=i?i:this._formatDate(r);r.input&&r.input.val(i);this._updateAlternate(r);u=this._get(r,"onSelect");u?u.apply(r.input?r.input[0]:null,[i,r]):r.input&&r.input.trigger("change");r.inline?this._updateDatepicker(r):(this._hideDatepicker(),this._lastInput=r.input[0],"object"!=typeof r.input[0]&&r.input.focus(),this._lastInput=null)},_updateAlternate:function(t){var i,r,u,f=this._get(t,"altField");f&&(i=this._get(t,"altFormat")||this._get(t,"dateFormat"),r=this._getDate(t),u=this.formatDate(i,r,this._getFormatConfig(t)),n(f).each(function(){n(this).val(u)}))},noWeekends:function(n){var t=n.getDay();return[t>0&&6>t,""]},iso8601Week:function(n){var i,t=new Date(n.getTime());return t.setDate(t.getDate()+4-(t.getDay()||7)),i=t.getTime(),t.setMonth(0),t.setDate(1),Math.floor(Math.round((i-t)/864e5)/7)+1},parseDate:function(t,i,r){if(null==t||null==i)throw"Invalid arguments";if(i="object"==typeof i?""+i:i+"",""===i)return null;for(var a,v,u,f=0,y=(r?r.shortYearCutoff:null)||this._defaults.shortYearCutoff,d="string"!=typeof y?y:(new Date).getFullYear()%100+parseInt(y,10),g=(r?r.dayNamesShort:null)||this._defaults.dayNamesShort,nt=(r?r.dayNames:null)||this._defaults.dayNames,tt=(r?r.monthNamesShort:null)||this._defaults.monthNamesShort,it=(r?r.monthNames:null)||this._defaults.monthNames,e=-1,s=-1,h=-1,p=-1,w=!1,l=function(n){var i=t.length>o+1&&t.charAt(o+1)===n;return i&&o++,i},c=function(n){var u=l(n),r="@"===n?14:"!"===n?20:"y"===n&&u?4:"o"===n?3:2,e="y"===n?r:1,o=RegExp("^\\d{"+e+","+r+"}"),t=i.substring(f).match(o);if(!t)throw"Missing number at position "+f;return f+=t[0].length,parseInt(t[0],10)},k=function(t,r,u){var e=-1,o=n.map(l(t)?u:r,function(n,t){return[[t,n]]}).sort(function(n,t){return-(n[1].length-t[1].length)});if(n.each(o,function(n,t){var r=t[1];if(i.substr(f,r.length).toLowerCase()===r.toLowerCase())return(e=t[0],f+=r.length,!1)}),-1!==e)return e+1;throw"Unknown name at position "+f;},b=function(){if(i.charAt(f)!==t.charAt(o))throw"Unexpected literal at position "+f;f++},o=0;t.length>o;o++)if(w)"'"!==t.charAt(o)||l("'")?b():w=!1;else switch(t.charAt(o)){case"d":h=c("d");break;case"D":k("D",g,nt);break;case"o":p=c("o");break;case"m":s=c("m");break;case"M":s=k("M",tt,it);break;case"y":e=c("y");break;case"@":u=new Date(c("@"));e=u.getFullYear();s=u.getMonth()+1;h=u.getDate();break;case"!":u=new Date((c("!")-this._ticksTo1970)/1e4);e=u.getFullYear();s=u.getMonth()+1;h=u.getDate();break;case"'":l("'")?b():w=!0;break;default:b()}if(i.length>f&&(v=i.substr(f),!/^\s+/.test(v)))throw"Extra/unparsed characters found in date: "+v;if(-1===e?e=(new Date).getFullYear():100>e&&(e+=(new Date).getFullYear()-(new Date).getFullYear()%100+(d>=e?0:-100)),p>-1)for(s=1,h=p;;){if(a=this._getDaysInMonth(e,s-1),a>=h)break;s++;h-=a}if(u=this._daylightSavingAdjust(new Date(e,s-1,h)),u.getFullYear()!==e||u.getMonth()+1!==s||u.getDate()!==h)throw"Invalid date";return u},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:864e9*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925)),formatDate:function(n,t,i){if(!t)return"";var u,h=(i?i.dayNamesShort:null)||this._defaults.dayNamesShort,c=(i?i.dayNames:null)||this._defaults.dayNames,l=(i?i.monthNamesShort:null)||this._defaults.monthNamesShort,a=(i?i.monthNames:null)||this._defaults.monthNames,f=function(t){var i=n.length>u+1&&n.charAt(u+1)===t;return i&&u++,i},e=function(n,t,i){var r=""+t;if(f(n))for(;i>r.length;)r="0"+r;return r},s=function(n,t,i,r){return f(n)?r[t]:i[t]},r="",o=!1;if(t)for(u=0;n.length>u;u++)if(o)"'"!==n.charAt(u)||f("'")?r+=n.charAt(u):o=!1;else switch(n.charAt(u)){case"d":r+=e("d",t.getDate(),2);break;case"D":r+=s("D",t.getDay(),h,c);break;case"o":r+=e("o",Math.round((new Date(t.getFullYear(),t.getMonth(),t.getDate()).getTime()-new Date(t.getFullYear(),0,0).getTime())/864e5),3);break;case"m":r+=e("m",t.getMonth()+1,2);break;case"M":r+=s("M",t.getMonth(),l,a);break;case"y":r+=f("y")?t.getFullYear():(10>t.getYear()%100?"0":"")+t.getYear()%100;break;case"@":r+=t.getTime();break;case"!":r+=1e4*t.getTime()+this._ticksTo1970;break;case"'":f("'")?r+="'":o=!0;break;default:r+=n.charAt(u)}return r},_possibleChars:function(n){for(var i="",r=!1,u=function(i){var r=n.length>t+1&&n.charAt(t+1)===i;return r&&t++,r},t=0;n.length>t;t++)if(r)"'"!==n.charAt(t)||u("'")?i+=n.charAt(t):r=!1;else switch(n.charAt(t)){case"d":case"m":case"y":case"@":i+="0123456789";break;case"D":case"M":return null;case"'":u("'")?i+="'":r=!0;break;default:i+=n.charAt(t)}return i},_get:function(n,t){return void 0!==n.settings[t]?n.settings[t]:this._defaults[t]},_setDateFromField:function(n,t){if(n.input.val()!==n.lastVal){var f=this._get(n,"dateFormat"),r=n.lastVal=n.input?n.input.val():null,u=this._getDefaultDate(n),i=u,e=this._getFormatConfig(n);try{i=this.parseDate(f,r,e)||u}catch(o){r=t?"":r}n.selectedDay=i.getDate();n.drawMonth=n.selectedMonth=i.getMonth();n.drawYear=n.selectedYear=i.getFullYear();n.currentDay=r?i.getDate():0;n.currentMonth=r?i.getMonth():0;n.currentYear=r?i.getFullYear():0;this._adjustInstDate(n)}},_getDefaultDate:function(n){return this._restrictMinMax(n,this._determineDate(n,this._get(n,"defaultDate"),new Date))},_determineDate:function(t,i,r){var f=function(n){var t=new Date;return t.setDate(t.getDate()+n),t},e=function(i){try{return n.datepicker.parseDate(n.datepicker._get(t,"dateFormat"),i,n.datepicker._getFormatConfig(t))}catch(h){}for(var o=(i.toLowerCase().match(/^c/)?n.datepicker._getDate(t):null)||new Date,f=o.getFullYear(),e=o.getMonth(),r=o.getDate(),s=/([+\-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,u=s.exec(i);u;){switch(u[2]||"d"){case"d":case"D":r+=parseInt(u[1],10);break;case"w":case"W":r+=7*parseInt(u[1],10);break;case"m":case"M":e+=parseInt(u[1],10);r=Math.min(r,n.datepicker._getDaysInMonth(f,e));break;case"y":case"Y":f+=parseInt(u[1],10);r=Math.min(r,n.datepicker._getDaysInMonth(f,e))}u=s.exec(i)}return new Date(f,e,r)},u=null==i||""===i?r:"string"==typeof i?e(i):"number"==typeof i?isNaN(i)?r:f(i):new Date(i.getTime());return u=u&&"Invalid Date"==""+u?r:u,u&&(u.setHours(0),u.setMinutes(0),u.setSeconds(0),u.setMilliseconds(0)),this._daylightSavingAdjust(u)},_daylightSavingAdjust:function(n){return n?(n.setHours(n.getHours()>12?n.getHours()+2:0),n):null},_setDate:function(n,t,i){var u=!t,f=n.selectedMonth,e=n.selectedYear,r=this._restrictMinMax(n,this._determineDate(n,t,new Date));n.selectedDay=n.currentDay=r.getDate();n.drawMonth=n.selectedMonth=n.currentMonth=r.getMonth();n.drawYear=n.selectedYear=n.currentYear=r.getFullYear();f===n.selectedMonth&&e===n.selectedYear||i||this._notifyChange(n);this._adjustInstDate(n);n.input&&n.input.val(u?"":this._formatDate(n))},_getDate:function(n){return!n.currentYear||n.input&&""===n.input.val()?null:this._daylightSavingAdjust(new Date(n.currentYear,n.currentMonth,n.currentDay))},_attachHandlers:function(t){var r=this._get(t,"stepMonths"),i="#"+t.id.replace(/\\\\/g,"\\");t.dpDiv.find("[data-handler]").map(function(){var t={prev:function(){n.datepicker._adjustDate(i,-r,"M")},next:function(){n.datepicker._adjustDate(i,+r,"M")},hide:function(){n.datepicker._hideDatepicker()},today:function(){n.datepicker._gotoToday(i)},selectDay:function(){return n.datepicker._selectDay(i,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this),!1},selectMonth:function(){return n.datepicker._selectMonthYear(i,this,"M"),!1},selectYear:function(){return n.datepicker._selectMonthYear(i,this,"Y"),!1}};n(this).bind(this.getAttribute("data-event"),t[this.getAttribute("data-handler")])})},_generateHTML:function(n){var b,s,rt,h,ut,k,ft,et,ri,c,ot,ui,fi,ei,oi,st,g,si,ht,nt,o,y,ct,p,lt,l,u,at,vt,yt,pt,tt,wt,i,bt,kt,d,a,it,dt=new Date,gt=this._daylightSavingAdjust(new Date(dt.getFullYear(),dt.getMonth(),dt.getDate())),f=this._get(n,"isRTL"),li=this._get(n,"showButtonPanel"),hi=this._get(n,"hideIfNoPrevNext"),ni=this._get(n,"navigationAsDateFormat"),e=this._getNumberOfMonths(n),ai=this._get(n,"showCurrentAtPos"),ci=this._get(n,"stepMonths"),ti=1!==e[0]||1!==e[1],ii=this._daylightSavingAdjust(n.currentDay?new Date(n.currentYear,n.currentMonth,n.currentDay):new Date(9999,9,9)),w=this._getMinMaxDate(n,"min"),v=this._getMinMaxDate(n,"max"),t=n.drawMonth-ai,r=n.drawYear;if(0>t&&(t+=12,r--),v)for(b=this._daylightSavingAdjust(new Date(v.getFullYear(),v.getMonth()-e[0]*e[1]+1,v.getDate())),b=w&&w>b?w:b;this._daylightSavingAdjust(new Date(r,t,1))>b;)t--,0>t&&(t=11,r--);for(n.drawMonth=t,n.drawYear=r,s=this._get(n,"prevText"),s=ni?this.formatDate(s,this._daylightSavingAdjust(new Date(r,t-ci,1)),this._getFormatConfig(n)):s,rt=this._canAdjustMonth(n,-1,r,t)?"<a class='ui-datepicker-prev ui-corner-all' data-handler='prev' data-event='click' title='"+s+"'><span class='ui-icon ui-icon-circle-triangle-"+(f?"e":"w")+"'>"+s+"<\/span><\/a>":hi?"":"<a class='ui-datepicker-prev ui-corner-all ui-state-disabled' title='"+s+"'><span class='ui-icon ui-icon-circle-triangle-"+(f?"e":"w")+"'>"+s+"<\/span><\/a>",h=this._get(n,"nextText"),h=ni?this.formatDate(h,this._daylightSavingAdjust(new Date(r,t+ci,1)),this._getFormatConfig(n)):h,ut=this._canAdjustMonth(n,1,r,t)?"<a class='ui-datepicker-next ui-corner-all' data-handler='next' data-event='click' title='"+h+"'><span class='ui-icon ui-icon-circle-triangle-"+(f?"w":"e")+"'>"+h+"<\/span><\/a>":hi?"":"<a class='ui-datepicker-next ui-corner-all ui-state-disabled' title='"+h+"'><span class='ui-icon ui-icon-circle-triangle-"+(f?"w":"e")+"'>"+h+"<\/span><\/a>",k=this._get(n,"currentText"),ft=this._get(n,"gotoCurrent")&&n.currentDay?ii:gt,k=ni?this.formatDate(k,ft,this._getFormatConfig(n)):k,et=n.inline?"":"<button type='button' class='ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all' data-handler='hide' data-event='click'>"+this._get(n,"closeText")+"<\/button>",ri=li?"<div class='ui-datepicker-buttonpane ui-widget-content'>"+(f?et:"")+(this._isInRange(n,ft)?"<button type='button' class='ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all' data-handler='today' data-event='click'>"+k+"<\/button>":"")+(f?"":et)+"<\/div>":"",c=parseInt(this._get(n,"firstDay"),10),c=isNaN(c)?0:c,ot=this._get(n,"showWeek"),ui=this._get(n,"dayNames"),fi=this._get(n,"dayNamesMin"),ei=this._get(n,"monthNames"),oi=this._get(n,"monthNamesShort"),st=this._get(n,"beforeShowDay"),g=this._get(n,"showOtherMonths"),si=this._get(n,"selectOtherMonths"),ht=this._getDefaultDate(n),nt="",y=0;e[0]>y;y++){for(ct="",this.maxRows=4,p=0;e[1]>p;p++){if(lt=this._daylightSavingAdjust(new Date(r,t,n.selectedDay)),l=" ui-corner-all",u="",ti){if(u+="<div class='ui-datepicker-group",e[1]>1)switch(p){case 0:u+=" ui-datepicker-group-first";l=" ui-corner-"+(f?"right":"left");break;case e[1]-1:u+=" ui-datepicker-group-last";l=" ui-corner-"+(f?"left":"right");break;default:u+=" ui-datepicker-group-middle";l=""}u+="'>"}for(u+="<div class='ui-datepicker-header ui-widget-header ui-helper-clearfix"+l+"'>"+(/all|left/.test(l)&&0===y?f?ut:rt:"")+(/all|right/.test(l)&&0===y?f?rt:ut:"")+this._generateMonthYearHeader(n,t,r,w,v,y>0||p>0,ei,oi)+"<\/div><table class='ui-datepicker-calendar'><thead><tr>",at=ot?"<th class='ui-datepicker-week-col'>"+this._get(n,"weekHeader")+"<\/th>":"",o=0;7>o;o++)vt=(o+c)%7,at+="<th scope='col'"+((o+c+6)%7>=5?" class='ui-datepicker-week-end'":"")+"><span title='"+ui[vt]+"'>"+fi[vt]+"<\/span><\/th>";for(u+=at+"<\/tr><\/thead><tbody>",yt=this._getDaysInMonth(r,t),r===n.selectedYear&&t===n.selectedMonth&&(n.selectedDay=Math.min(n.selectedDay,yt)),pt=(this._getFirstDayOfMonth(r,t)-c+7)%7,tt=Math.ceil((pt+yt)/7),wt=ti?this.maxRows>tt?this.maxRows:tt:tt,this.maxRows=wt,i=this._daylightSavingAdjust(new Date(r,t,1-pt)),bt=0;wt>bt;bt++){for(u+="<tr>",kt=ot?"<td class='ui-datepicker-week-col'>"+this._get(n,"calculateWeek")(i)+"<\/td>":"",o=0;7>o;o++)d=st?st.apply(n.input?n.input[0]:null,[i]):[!0,""],a=i.getMonth()!==t,it=a&&!si||!d[0]||w&&w>i||v&&i>v,kt+="<td class='"+((o+c+6)%7>=5?" ui-datepicker-week-end":"")+(a?" ui-datepicker-other-month":"")+(i.getTime()===lt.getTime()&&t===n.selectedMonth&&n._keyEvent||ht.getTime()===i.getTime()&&ht.getTime()===lt.getTime()?" "+this._dayOverClass:"")+(it?" "+this._unselectableClass+" ui-state-disabled":"")+(a&&!g?"":" "+d[1]+(i.getTime()===ii.getTime()?" "+this._currentClass:"")+(i.getTime()===gt.getTime()?" ui-datepicker-today":""))+"'"+(a&&!g||!d[2]?"":" title='"+d[2].replace(/'/g,"&#39;")+"'")+(it?"":" data-handler='selectDay' data-event='click' data-month='"+i.getMonth()+"' data-year='"+i.getFullYear()+"'")+">"+(a&&!g?"&#xa0;":it?"<span class='ui-state-default'>"+i.getDate()+"<\/span>":"<a class='ui-state-default"+(i.getTime()===gt.getTime()?" ui-state-highlight":"")+(i.getTime()===ii.getTime()?" ui-state-active":"")+(a?" ui-priority-secondary":"")+"' href='#'>"+i.getDate()+"<\/a>")+"<\/td>",i.setDate(i.getDate()+1),i=this._daylightSavingAdjust(i);u+=kt+"<\/tr>"}t++;t>11&&(t=0,r++);u+="<\/tbody><\/table>"+(ti?"<\/div>"+(e[0]>0&&p===e[1]-1?"<div class='ui-datepicker-row-break'><\/div>":""):"");ct+=u}nt+=ct}return nt+=ri,n._keyEvent=!1,nt},_generateMonthYearHeader:function(n,t,i,r,u,f,e,o){var k,d,h,v,y,p,s,a,w=this._get(n,"changeMonth"),b=this._get(n,"changeYear"),g=this._get(n,"showMonthAfterYear"),c="<div class='ui-datepicker-title'>",l="";if(f||!w)l+="<span class='ui-datepicker-month'>"+e[t]+"<\/span>";else{for(k=r&&r.getFullYear()===i,d=u&&u.getFullYear()===i,l+="<select class='ui-datepicker-month' data-handler='selectMonth' data-event='change'>",h=0;12>h;h++)(!k||h>=r.getMonth())&&(!d||u.getMonth()>=h)&&(l+="<option value='"+h+"'"+(h===t?" selected='selected'":"")+">"+o[h]+"<\/option>");l+="<\/select>"}if(g||(c+=l+(!f&&w&&b?"":"&#xa0;")),!n.yearshtml)if(n.yearshtml="",f||!b)c+="<span class='ui-datepicker-year'>"+i+"<\/span>";else{for(v=this._get(n,"yearRange").split(":"),y=(new Date).getFullYear(),p=function(n){var t=n.match(/c[+\-].*/)?i+parseInt(n.substring(1),10):n.match(/[+\-].*/)?y+parseInt(n,10):parseInt(n,10);return isNaN(t)?y:t},s=p(v[0]),a=Math.max(s,p(v[1]||"")),s=r?Math.max(s,r.getFullYear()):s,a=u?Math.min(a,u.getFullYear()):a,n.yearshtml+="<select class='ui-datepicker-year' data-handler='selectYear' data-event='change'>";a>=s;s++)n.yearshtml+="<option value='"+s+"'"+(s===i?" selected='selected'":"")+">"+s+"<\/option>";n.yearshtml+="<\/select>";c+=n.yearshtml;n.yearshtml=null}return c+=this._get(n,"yearSuffix"),g&&(c+=(!f&&w&&b?"":"&#xa0;")+l),c+"<\/div>"},_adjustInstDate:function(n,t,i){var u=n.drawYear+("Y"===i?t:0),f=n.drawMonth+("M"===i?t:0),e=Math.min(n.selectedDay,this._getDaysInMonth(u,f))+("D"===i?t:0),r=this._restrictMinMax(n,this._daylightSavingAdjust(new Date(u,f,e)));n.selectedDay=r.getDate();n.drawMonth=n.selectedMonth=r.getMonth();n.drawYear=n.selectedYear=r.getFullYear();("M"===i||"Y"===i)&&this._notifyChange(n)},_restrictMinMax:function(n,t){var i=this._getMinMaxDate(n,"min"),r=this._getMinMaxDate(n,"max"),u=i&&i>t?i:t;return r&&u>r?r:u},_notifyChange:function(n){var t=this._get(n,"onChangeMonthYear");t&&t.apply(n.input?n.input[0]:null,[n.selectedYear,n.selectedMonth+1,n])},_getNumberOfMonths:function(n){var t=this._get(n,"numberOfMonths");return null==t?[1,1]:"number"==typeof t?[1,t]:t},_getMinMaxDate:function(n,t){return this._determineDate(n,this._get(n,t+"Date"),null)},_getDaysInMonth:function(n,t){return 32-this._daylightSavingAdjust(new Date(n,t,32)).getDate()},_getFirstDayOfMonth:function(n,t){return new Date(n,t,1).getDay()},_canAdjustMonth:function(n,t,i,r){var f=this._getNumberOfMonths(n),u=this._daylightSavingAdjust(new Date(i,r+(0>t?t:f[0]*f[1]),1));return 0>t&&u.setDate(this._getDaysInMonth(u.getFullYear(),u.getMonth())),this._isInRange(n,u)},_isInRange:function(n,t){var i,f,e=this._getMinMaxDate(n,"min"),o=this._getMinMaxDate(n,"max"),r=null,u=null,s=this._get(n,"yearRange");return s&&(i=s.split(":"),f=(new Date).getFullYear(),r=parseInt(i[0],10),u=parseInt(i[1],10),i[0].match(/[+\-].*/)&&(r+=f),i[1].match(/[+\-].*/)&&(u+=f)),(!e||t.getTime()>=e.getTime())&&(!o||t.getTime()<=o.getTime())&&(!r||t.getFullYear()>=r)&&(!u||u>=t.getFullYear())},_getFormatConfig:function(n){var t=this._get(n,"shortYearCutoff");return t="string"!=typeof t?t:(new Date).getFullYear()%100+parseInt(t,10),{shortYearCutoff:t,dayNamesShort:this._get(n,"dayNamesShort"),dayNames:this._get(n,"dayNames"),monthNamesShort:this._get(n,"monthNamesShort"),monthNames:this._get(n,"monthNames")}},_formatDate:function(n,t,i,r){t||(n.currentDay=n.selectedDay,n.currentMonth=n.selectedMonth,n.currentYear=n.selectedYear);var u=t?"object"==typeof t?t:this._daylightSavingAdjust(new Date(r,i,t)):this._daylightSavingAdjust(new Date(n.currentYear,n.currentMonth,n.currentDay));return this.formatDate(this._get(n,"dateFormat"),u,this._getFormatConfig(n))}});n.fn.datepicker=function(t){if(!this.length)return this;n.datepicker.initialized||(n(document).mousedown(n.datepicker._checkExternalClick),n.datepicker.initialized=!0);0===n("#"+n.datepicker._mainDivId).length&&n("body").append(n.datepicker.dpDiv);var i=Array.prototype.slice.call(arguments,1);return"string"!=typeof t||"isDisabled"!==t&&"getDate"!==t&&"widget"!==t?"option"===t&&2===arguments.length&&"string"==typeof arguments[1]?n.datepicker["_"+t+"Datepicker"].apply(n.datepicker,[this[0]].concat(i)):this.each(function(){"string"==typeof t?n.datepicker["_"+t+"Datepicker"].apply(n.datepicker,[this].concat(i)):n.datepicker._attachDatepicker(this,t)}):n.datepicker["_"+t+"Datepicker"].apply(n.datepicker,[this[0]].concat(i))};n.datepicker=new l;n.datepicker.initialized=!1;n.datepicker.uuid=(new Date).getTime();n.datepicker.version="1.11.4";n.datepicker;n.widget("ui.draggable",n.ui.mouse,{version:"1.11.4",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"===this.options.helper&&this._setPositionRelative();this.options.addClasses&&this.element.addClass("ui-draggable");this.options.disabled&&this.element.addClass("ui-draggable-disabled");this._setHandleClassName();this._mouseInit()},_setOption:function(n,t){this._super(n,t);"handle"===n&&(this._removeHandleClassName(),this._setHandleClassName())},_destroy:function(){return(this.helper||this.element).is(".ui-draggable-dragging")?(this.destroyOnClear=!0,void 0):(this.element.removeClass("ui-draggable ui-draggable-dragging ui-draggable-disabled"),this._removeHandleClassName(),this._mouseDestroy(),void 0)},_mouseCapture:function(t){var i=this.options;return this._blurActiveElement(t),this.helper||i.disabled||n(t.target).closest(".ui-resizable-handle").length>0?!1:(this.handle=this._getHandle(t),this.handle?(this._blockFrames(i.iframeFix===!0?"iframe":i.iframeFix),!0):!1)},_blockFrames:function(t){this.iframeBlocks=this.document.find(t).map(function(){var t=n(this);return n("<div>").css("position","absolute").appendTo(t.parent()).outerWidth(t.outerWidth()).outerHeight(t.outerHeight()).offset(t.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_blurActiveElement:function(t){var i=this.document[0];if(this.handleElement.is(t.target))try{i.activeElement&&"body"!==i.activeElement.nodeName.toLowerCase()&&n(i.activeElement).blur()}catch(r){}},_mouseStart:function(t){var i=this.options;return this.helper=this._createHelper(t),this.helper.addClass("ui-draggable-dragging"),this._cacheHelperProportions(),n.ui.ddmanager&&(n.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(!0),this.offsetParent=this.helper.offsetParent(),this.hasFixedAncestor=this.helper.parents().filter(function(){return"fixed"===n(this).css("position")}).length>0,this.positionAbs=this.element.offset(),this._refreshOffsets(t),this.originalPosition=this.position=this._generatePosition(t,!1),this.originalPageX=t.pageX,this.originalPageY=t.pageY,i.cursorAt&&this._adjustOffsetFromHelper(i.cursorAt),this._setContainment(),this._trigger("start",t)===!1?(this._clear(),!1):(this._cacheHelperProportions(),n.ui.ddmanager&&!i.dropBehaviour&&n.ui.ddmanager.prepareOffsets(this,t),this._normalizeRightBottom(),this._mouseDrag(t,!0),n.ui.ddmanager&&n.ui.ddmanager.dragStart(this,t),!0)},_refreshOffsets:function(n){this.offset={top:this.positionAbs.top-this.margins.top,left:this.positionAbs.left-this.margins.left,scroll:!1,parent:this._getParentOffset(),relative:this._getRelativeOffset()};this.offset.click={left:n.pageX-this.offset.left,top:n.pageY-this.offset.top}},_mouseDrag:function(t,i){if(this.hasFixedAncestor&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(t,!0),this.positionAbs=this._convertPositionTo("absolute"),!i){var r=this._uiHash();if(this._trigger("drag",t,r)===!1)return this._mouseUp({}),!1;this.position=r.position}return this.helper[0].style.left=this.position.left+"px",this.helper[0].style.top=this.position.top+"px",n.ui.ddmanager&&n.ui.ddmanager.drag(this,t),!1},_mouseStop:function(t){var r=this,i=!1;return n.ui.ddmanager&&!this.options.dropBehaviour&&(i=n.ui.ddmanager.drop(this,t)),this.dropped&&(i=this.dropped,this.dropped=!1),"invalid"===this.options.revert&&!i||"valid"===this.options.revert&&i||this.options.revert===!0||n.isFunction(this.options.revert)&&this.options.revert.call(this.element,i)?n(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),function(){r._trigger("stop",t)!==!1&&r._clear()}):this._trigger("stop",t)!==!1&&this._clear(),!1},_mouseUp:function(t){return this._unblockFrames(),n.ui.ddmanager&&n.ui.ddmanager.dragStop(this,t),this.handleElement.is(t.target)&&this.element.focus(),n.ui.mouse.prototype._mouseUp.call(this,t)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp({}):this._clear(),this},_getHandle:function(t){return this.options.handle?!!n(t.target).closest(this.element.find(this.options.handle)).length:!0},_setHandleClassName:function(){this.handleElement=this.options.handle?this.element.find(this.options.handle):this.element;this.handleElement.addClass("ui-draggable-handle")},_removeHandleClassName:function(){this.handleElement.removeClass("ui-draggable-handle")},_createHelper:function(t){var r=this.options,u=n.isFunction(r.helper),i=u?n(r.helper.apply(this.element[0],[t])):"clone"===r.helper?this.element.clone().removeAttr("id"):this.element;return i.parents("body").length||i.appendTo("parent"===r.appendTo?this.element[0].parentNode:r.appendTo),u&&i[0]===this.element[0]&&this._setPositionRelative(),i[0]===this.element[0]||/(fixed|absolute)/.test(i.css("position"))||i.css("position","absolute"),i},_setPositionRelative:function(){/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative")},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" "));n.isArray(t)&&(t={left:+t[0],top:+t[1]||0});"left"in t&&(this.offset.click.left=t.left+this.margins.left);"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left);"top"in t&&(this.offset.click.top=t.top+this.margins.top);"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_isRootNode:function(n){return/(html|body)/i.test(n.tagName)||n===this.document[0]},_getParentOffset:function(){var t=this.offsetParent.offset(),i=this.document[0];return"absolute"===this.cssPosition&&this.scrollParent[0]!==i&&n.contains(this.scrollParent[0],this.offsetParent[0])&&(t.left+=this.scrollParent.scrollLeft(),t.top+=this.scrollParent.scrollTop()),this._isRootNode(this.offsetParent[0])&&(t={top:0,left:0}),{top:t.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:t.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition)return{top:0,left:0};var n=this.element.position(),t=this._isRootNode(this.scrollParent[0]);return{top:n.top-(parseInt(this.helper.css("top"),10)||0)+(t?0:this.scrollParent.scrollTop()),left:n.left-(parseInt(this.helper.css("left"),10)||0)+(t?0:this.scrollParent.scrollLeft())}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var f,t,i,r=this.options,u=this.document[0];return this.relativeContainer=null,r.containment?"window"===r.containment?(this.containment=[n(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,n(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,n(window).scrollLeft()+n(window).width()-this.helperProportions.width-this.margins.left,n(window).scrollTop()+(n(window).height()||u.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top],void 0):"document"===r.containment?(this.containment=[0,0,n(u).width()-this.helperProportions.width-this.margins.left,(n(u).height()||u.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top],void 0):r.containment.constructor===Array?(this.containment=r.containment,void 0):("parent"===r.containment&&(r.containment=this.helper[0].parentNode),t=n(r.containment),i=t[0],i&&(f=/(scroll|auto)/.test(t.css("overflow")),this.containment=[(parseInt(t.css("borderLeftWidth"),10)||0)+(parseInt(t.css("paddingLeft"),10)||0),(parseInt(t.css("borderTopWidth"),10)||0)+(parseInt(t.css("paddingTop"),10)||0),(f?Math.max(i.scrollWidth,i.offsetWidth):i.offsetWidth)-(parseInt(t.css("borderRightWidth"),10)||0)-(parseInt(t.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(f?Math.max(i.scrollHeight,i.offsetHeight):i.offsetHeight)-(parseInt(t.css("borderBottomWidth"),10)||0)-(parseInt(t.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relativeContainer=t),void 0):(this.containment=null,void 0)},_convertPositionTo:function(n,t){t||(t=this.position);var i="absolute"===n?1:-1,r=this._isRootNode(this.scrollParent[0]);return{top:t.top+this.offset.relative.top*i+this.offset.parent.top*i-("fixed"===this.cssPosition?-this.offset.scroll.top:r?0:this.offset.scroll.top)*i,left:t.left+this.offset.relative.left*i+this.offset.parent.left*i-("fixed"===this.cssPosition?-this.offset.scroll.left:r?0:this.offset.scroll.left)*i}},_generatePosition:function(n,t){var i,s,u,f,r=this.options,h=this._isRootNode(this.scrollParent[0]),e=n.pageX,o=n.pageY;return h&&this.offset.scroll||(this.offset.scroll={top:this.scrollParent.scrollTop(),left:this.scrollParent.scrollLeft()}),t&&(this.containment&&(this.relativeContainer?(s=this.relativeContainer.offset(),i=[this.containment[0]+s.left,this.containment[1]+s.top,this.containment[2]+s.left,this.containment[3]+s.top]):i=this.containment,n.pageX-this.offset.click.left<i[0]&&(e=i[0]+this.offset.click.left),n.pageY-this.offset.click.top<i[1]&&(o=i[1]+this.offset.click.top),n.pageX-this.offset.click.left>i[2]&&(e=i[2]+this.offset.click.left),n.pageY-this.offset.click.top>i[3]&&(o=i[3]+this.offset.click.top)),r.grid&&(u=r.grid[1]?this.originalPageY+Math.round((o-this.originalPageY)/r.grid[1])*r.grid[1]:this.originalPageY,o=i?u-this.offset.click.top>=i[1]||u-this.offset.click.top>i[3]?u:u-this.offset.click.top>=i[1]?u-r.grid[1]:u+r.grid[1]:u,f=r.grid[0]?this.originalPageX+Math.round((e-this.originalPageX)/r.grid[0])*r.grid[0]:this.originalPageX,e=i?f-this.offset.click.left>=i[0]||f-this.offset.click.left>i[2]?f:f-this.offset.click.left>=i[0]?f-r.grid[0]:f+r.grid[0]:f),"y"===r.axis&&(e=this.originalPageX),"x"===r.axis&&(o=this.originalPageY)),{top:o-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.offset.scroll.top:h?0:this.offset.scroll.top),left:e-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.offset.scroll.left:h?0:this.offset.scroll.left)}},_clear:function(){this.helper.removeClass("ui-draggable-dragging");this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove();this.helper=null;this.cancelHelperRemoval=!1;this.destroyOnClear&&this.destroy()},_normalizeRightBottom:function(){"y"!==this.options.axis&&"auto"!==this.helper.css("right")&&(this.helper.width(this.helper.width()),this.helper.css("right","auto"));"x"!==this.options.axis&&"auto"!==this.helper.css("bottom")&&(this.helper.height(this.helper.height()),this.helper.css("bottom","auto"))},_trigger:function(t,i,r){return r=r||this._uiHash(),n.ui.plugin.call(this,t,[i,r,this],!0),/^(drag|start|stop)/.test(t)&&(this.positionAbs=this._convertPositionTo("absolute"),r.offset=this.positionAbs),n.Widget.prototype._trigger.call(this,t,i,r)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}});n.ui.plugin.add("draggable","connectToSortable",{start:function(t,i,r){var u=n.extend({},i,{item:r.element});r.sortables=[];n(r.options.connectToSortable).each(function(){var i=n(this).sortable("instance");i&&!i.options.disabled&&(r.sortables.push(i),i.refreshPositions(),i._trigger("activate",t,u))})},stop:function(t,i,r){var u=n.extend({},i,{item:r.element});r.cancelHelperRemoval=!1;n.each(r.sortables,function(){var n=this;n.isOver?(n.isOver=0,r.cancelHelperRemoval=!0,n.cancelHelperRemoval=!1,n._storedCSS={position:n.placeholder.css("position"),top:n.placeholder.css("top"),left:n.placeholder.css("left")},n._mouseStop(t),n.options.helper=n.options._helper):(n.cancelHelperRemoval=!0,n._trigger("deactivate",t,u))})},drag:function(t,i,r){n.each(r.sortables,function(){var f=!1,u=this;u.positionAbs=r.positionAbs;u.helperProportions=r.helperProportions;u.offset.click=r.offset.click;u._intersectsWith(u.containerCache)&&(f=!0,n.each(r.sortables,function(){return this.positionAbs=r.positionAbs,this.helperProportions=r.helperProportions,this.offset.click=r.offset.click,this!==u&&this._intersectsWith(this.containerCache)&&n.contains(u.element[0],this.element[0])&&(f=!1),f}));f?(u.isOver||(u.isOver=1,r._parent=i.helper.parent(),u.currentItem=i.helper.appendTo(u.element).data("ui-sortable-item",!0),u.options._helper=u.options.helper,u.options.helper=function(){return i.helper[0]},t.target=u.currentItem[0],u._mouseCapture(t,!0),u._mouseStart(t,!0,!0),u.offset.click.top=r.offset.click.top,u.offset.click.left=r.offset.click.left,u.offset.parent.left-=r.offset.parent.left-u.offset.parent.left,u.offset.parent.top-=r.offset.parent.top-u.offset.parent.top,r._trigger("toSortable",t),r.dropped=u.element,n.each(r.sortables,function(){this.refreshPositions()}),r.currentItem=r.element,u.fromOutside=r),u.currentItem&&(u._mouseDrag(t),i.position=u.position)):u.isOver&&(u.isOver=0,u.cancelHelperRemoval=!0,u.options._revert=u.options.revert,u.options.revert=!1,u._trigger("out",t,u._uiHash(u)),u._mouseStop(t,!0),u.options.revert=u.options._revert,u.options.helper=u.options._helper,u.placeholder&&u.placeholder.remove(),i.helper.appendTo(r._parent),r._refreshOffsets(t),i.position=r._generatePosition(t,!0),r._trigger("fromSortable",t),r.dropped=!1,n.each(r.sortables,function(){this.refreshPositions()}))})}});n.ui.plugin.add("draggable","cursor",{start:function(t,i,r){var u=n("body"),f=r.options;u.css("cursor")&&(f._cursor=u.css("cursor"));u.css("cursor",f.cursor)},stop:function(t,i,r){var u=r.options;u._cursor&&n("body").css("cursor",u._cursor)}});n.ui.plugin.add("draggable","opacity",{start:function(t,i,r){var u=n(i.helper),f=r.options;u.css("opacity")&&(f._opacity=u.css("opacity"));u.css("opacity",f.opacity)},stop:function(t,i,r){var u=r.options;u._opacity&&n(i.helper).css("opacity",u._opacity)}});n.ui.plugin.add("draggable","scroll",{start:function(n,t,i){i.scrollParentNotHidden||(i.scrollParentNotHidden=i.helper.scrollParent(!1));i.scrollParentNotHidden[0]!==i.document[0]&&"HTML"!==i.scrollParentNotHidden[0].tagName&&(i.overflowOffset=i.scrollParentNotHidden.offset())},drag:function(t,i,r){var u=r.options,o=!1,e=r.scrollParentNotHidden[0],f=r.document[0];e!==f&&"HTML"!==e.tagName?(u.axis&&"x"===u.axis||(r.overflowOffset.top+e.offsetHeight-t.pageY<u.scrollSensitivity?e.scrollTop=o=e.scrollTop+u.scrollSpeed:t.pageY-r.overflowOffset.top<u.scrollSensitivity&&(e.scrollTop=o=e.scrollTop-u.scrollSpeed)),u.axis&&"y"===u.axis||(r.overflowOffset.left+e.offsetWidth-t.pageX<u.scrollSensitivity?e.scrollLeft=o=e.scrollLeft+u.scrollSpeed:t.pageX-r.overflowOffset.left<u.scrollSensitivity&&(e.scrollLeft=o=e.scrollLeft-u.scrollSpeed))):(u.axis&&"x"===u.axis||(t.pageY-n(f).scrollTop()<u.scrollSensitivity?o=n(f).scrollTop(n(f).scrollTop()-u.scrollSpeed):n(window).height()-(t.pageY-n(f).scrollTop())<u.scrollSensitivity&&(o=n(f).scrollTop(n(f).scrollTop()+u.scrollSpeed))),u.axis&&"y"===u.axis||(t.pageX-n(f).scrollLeft()<u.scrollSensitivity?o=n(f).scrollLeft(n(f).scrollLeft()-u.scrollSpeed):n(window).width()-(t.pageX-n(f).scrollLeft())<u.scrollSensitivity&&(o=n(f).scrollLeft(n(f).scrollLeft()+u.scrollSpeed))));o!==!1&&n.ui.ddmanager&&!u.dropBehaviour&&n.ui.ddmanager.prepareOffsets(r,t)}});n.ui.plugin.add("draggable","snap",{start:function(t,i,r){var u=r.options;r.snapElements=[];n(u.snap.constructor!==String?u.snap.items||":data(ui-draggable)":u.snap).each(function(){var t=n(this),i=t.offset();this!==r.element[0]&&r.snapElements.push({item:this,width:t.outerWidth(),height:t.outerHeight(),top:i.top,left:i.left})})},drag:function(t,i,r){for(var e,o,s,h,c,a,l,v,w,b=r.options,f=b.snapTolerance,y=i.offset.left,k=y+r.helperProportions.width,p=i.offset.top,d=p+r.helperProportions.height,u=r.snapElements.length-1;u>=0;u--)c=r.snapElements[u].left-r.margins.left,a=c+r.snapElements[u].width,l=r.snapElements[u].top-r.margins.top,v=l+r.snapElements[u].height,c-f>k||y>a+f||l-f>d||p>v+f||!n.contains(r.snapElements[u].item.ownerDocument,r.snapElements[u].item)?(r.snapElements[u].snapping&&r.options.snap.release&&r.options.snap.release.call(r.element,t,n.extend(r._uiHash(),{snapItem:r.snapElements[u].item})),r.snapElements[u].snapping=!1):("inner"!==b.snapMode&&(e=f>=Math.abs(l-d),o=f>=Math.abs(v-p),s=f>=Math.abs(c-k),h=f>=Math.abs(a-y),e&&(i.position.top=r._convertPositionTo("relative",{top:l-r.helperProportions.height,left:0}).top),o&&(i.position.top=r._convertPositionTo("relative",{top:v,left:0}).top),s&&(i.position.left=r._convertPositionTo("relative",{top:0,left:c-r.helperProportions.width}).left),h&&(i.position.left=r._convertPositionTo("relative",{top:0,left:a}).left)),w=e||o||s||h,"outer"!==b.snapMode&&(e=f>=Math.abs(l-p),o=f>=Math.abs(v-d),s=f>=Math.abs(c-y),h=f>=Math.abs(a-k),e&&(i.position.top=r._convertPositionTo("relative",{top:l,left:0}).top),o&&(i.position.top=r._convertPositionTo("relative",{top:v-r.helperProportions.height,left:0}).top),s&&(i.position.left=r._convertPositionTo("relative",{top:0,left:c}).left),h&&(i.position.left=r._convertPositionTo("relative",{top:0,left:a-r.helperProportions.width}).left)),!r.snapElements[u].snapping&&(e||o||s||h||w)&&r.options.snap.snap&&r.options.snap.snap.call(r.element,t,n.extend(r._uiHash(),{snapItem:r.snapElements[u].item})),r.snapElements[u].snapping=e||o||s||h||w)}});n.ui.plugin.add("draggable","stack",{start:function(t,i,r){var f,e=r.options,u=n.makeArray(n(e.stack)).sort(function(t,i){return(parseInt(n(t).css("zIndex"),10)||0)-(parseInt(n(i).css("zIndex"),10)||0)});u.length&&(f=parseInt(n(u[0]).css("zIndex"),10)||0,n(u).each(function(t){n(this).css("zIndex",f+t)}),this.css("zIndex",f+u.length))}});n.ui.plugin.add("draggable","zIndex",{start:function(t,i,r){var u=n(i.helper),f=r.options;u.css("zIndex")&&(f._zIndex=u.css("zIndex"));u.css("zIndex",f.zIndex)},stop:function(t,i,r){var u=r.options;u._zIndex&&n(i.helper).css("zIndex",u._zIndex)}});n.ui.draggable;n.widget("ui.resizable",n.ui.mouse,{version:"1.11.4",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(n){return parseInt(n,10)||0},_isNumber:function(n){return!isNaN(parseInt(n,10))},_hasScroll:function(t,i){if("hidden"===n(t).css("overflow"))return!1;var r=i&&"left"===i?"scrollLeft":"scrollTop",u=!1;return t[r]>0?!0:(t[r]=1,u=t[r]>0,t[r]=0,u)},_create:function(){var e,f,u,i,o,r=this,t=this.options;if(this.element.addClass("ui-resizable"),n.extend(this,{_aspectRatio:!!t.aspectRatio,aspectRatio:t.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:t.helper||t.ghost||t.animate?t.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/^(canvas|textarea|input|select|button|img)$/i)&&(this.element.wrap(n("<div class='ui-wrapper' style='overflow: hidden;'><\/div>").css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,this.element.css({marginLeft:this.originalElement.css("marginLeft"),marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom")}),this.originalElement.css({marginLeft:0,marginTop:0,marginRight:0,marginBottom:0}),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css({margin:this.originalElement.css("margin")}),this._proportionallyResize()),this.handles=t.handles||(n(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this._handles=n(),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),e=this.handles.split(","),this.handles={},f=0;e.length>f;f++)u=n.trim(e[f]),o="ui-resizable-"+u,i=n("<div class='ui-resizable-handle "+o+"'><\/div>"),i.css({zIndex:t.zIndex}),"se"===u&&i.addClass("ui-icon ui-icon-gripsmall-diagonal-se"),this.handles[u]=".ui-resizable-"+u,this.element.append(i);this._renderAxis=function(t){var i,u,f,e;t=t||this.element;for(i in this.handles)this.handles[i].constructor===String?this.handles[i]=this.element.children(this.handles[i]).first().show():(this.handles[i].jquery||this.handles[i].nodeType)&&(this.handles[i]=n(this.handles[i]),this._on(this.handles[i],{mousedown:r._mouseDown})),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/^(textarea|input|select|button)$/i)&&(u=n(this.handles[i],this.element),e=/sw|ne|nw|se|n|s/.test(i)?u.outerHeight():u.outerWidth(),f=["padding",/ne|nw|n/.test(i)?"Top":/se|sw|s/.test(i)?"Bottom":/^e$/.test(i)?"Right":"Left"].join(""),t.css(f,e),this._proportionallyResize()),this._handles=this._handles.add(this.handles[i])};this._renderAxis(this.element);this._handles=this._handles.add(this.element.find(".ui-resizable-handle"));this._handles.disableSelection();this._handles.mouseover(function(){r.resizing||(this.className&&(i=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),r.axis=i&&i[1]?i[1]:"se")});t.autoHide&&(this._handles.hide(),n(this.element).addClass("ui-resizable-autohide").mouseenter(function(){t.disabled||(n(this).removeClass("ui-resizable-autohide"),r._handles.show())}).mouseleave(function(){t.disabled||r.resizing||(n(this).addClass("ui-resizable-autohide"),r._handles.hide())}));this._mouseInit()},_destroy:function(){this._mouseDestroy();var t,i=function(t){n(t).removeClass("ui-resizable ui-resizable-disabled ui-resizable-resizing").removeData("resizable").removeData("ui-resizable").unbind(".resizable").find(".ui-resizable-handle").remove()};return this.elementIsWrapper&&(i(this.element),t=this.element,this.originalElement.css({position:t.css("position"),width:t.outerWidth(),height:t.outerHeight(),top:t.css("top"),left:t.css("left")}).insertAfter(t),t.remove()),this.originalElement.css("resize",this.originalResizeStyle),i(this.originalElement),this},_mouseCapture:function(t){var r,i,u=!1;for(r in this.handles)i=n(this.handles[r])[0],(i===t.target||n.contains(i,t.target))&&(u=!0);return!this.options.disabled&&u},_mouseStart:function(t){var u,f,e,r=this.options,i=this.element;return this.resizing=!0,this._renderProxy(),u=this._num(this.helper.css("left")),f=this._num(this.helper.css("top")),r.containment&&(u+=n(r.containment).scrollLeft()||0,f+=n(r.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:u,top:f},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:i.width(),height:i.height()},this.originalSize=this._helper?{width:i.outerWidth(),height:i.outerHeight()}:{width:i.width(),height:i.height()},this.sizeDiff={width:i.outerWidth()-i.width(),height:i.outerHeight()-i.height()},this.originalPosition={left:u,top:f},this.originalMousePosition={left:t.pageX,top:t.pageY},this.aspectRatio="number"==typeof r.aspectRatio?r.aspectRatio:this.originalSize.width/this.originalSize.height||1,e=n(".ui-resizable-"+this.axis).css("cursor"),n("body").css("cursor","auto"===e?this.axis+"-resize":e),i.addClass("ui-resizable-resizing"),this._propagate("start",t),!0},_mouseDrag:function(t){var i,r,u=this.originalMousePosition,e=this.axis,o=t.pageX-u.left||0,s=t.pageY-u.top||0,f=this._change[e];return this._updatePrevProperties(),f?(i=f.apply(this,[t,o,s]),this._updateVirtualBoundaries(t.shiftKey),(this._aspectRatio||t.shiftKey)&&(i=this._updateRatio(i,t)),i=this._respectSize(i,t),this._updateCache(i),this._propagate("resize",t),r=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),n.isEmptyObject(r)||(this._updatePrevProperties(),this._trigger("resize",t,this.ui()),this._applyChanges()),!1):!1},_mouseStop:function(t){this.resizing=!1;var r,u,f,e,o,s,h,c=this.options,i=this;return this._helper&&(r=this._proportionallyResizeElements,u=r.length&&/textarea/i.test(r[0].nodeName),f=u&&this._hasScroll(r[0],"left")?0:i.sizeDiff.height,e=u?0:i.sizeDiff.width,o={width:i.helper.width()-e,height:i.helper.height()-f},s=parseInt(i.element.css("left"),10)+(i.position.left-i.originalPosition.left)||null,h=parseInt(i.element.css("top"),10)+(i.position.top-i.originalPosition.top)||null,c.animate||this.element.css(n.extend(o,{top:h,left:s})),i.helper.height(i.size.height),i.helper.width(i.size.width),this._helper&&!c.animate&&this._proportionallyResize()),n("body").css("cursor","auto"),this.element.removeClass("ui-resizable-resizing"),this._propagate("stop",t),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left};this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var n={};return this.position.top!==this.prevPosition.top&&(n.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(n.left=this.position.left+"px"),this.size.width!==this.prevSize.width&&(n.width=this.size.width+"px"),this.size.height!==this.prevSize.height&&(n.height=this.size.height+"px"),this.helper.css(n),n},_updateVirtualBoundaries:function(n){var r,u,f,e,t,i=this.options;t={minWidth:this._isNumber(i.minWidth)?i.minWidth:0,maxWidth:this._isNumber(i.maxWidth)?i.maxWidth:1/0,minHeight:this._isNumber(i.minHeight)?i.minHeight:0,maxHeight:this._isNumber(i.maxHeight)?i.maxHeight:1/0};(this._aspectRatio||n)&&(r=t.minHeight*this.aspectRatio,f=t.minWidth/this.aspectRatio,u=t.maxHeight*this.aspectRatio,e=t.maxWidth/this.aspectRatio,r>t.minWidth&&(t.minWidth=r),f>t.minHeight&&(t.minHeight=f),t.maxWidth>u&&(t.maxWidth=u),t.maxHeight>e&&(t.maxHeight=e));this._vBoundaries=t},_updateCache:function(n){this.offset=this.helper.offset();this._isNumber(n.left)&&(this.position.left=n.left);this._isNumber(n.top)&&(this.position.top=n.top);this._isNumber(n.height)&&(this.size.height=n.height);this._isNumber(n.width)&&(this.size.width=n.width)},_updateRatio:function(n){var t=this.position,i=this.size,r=this.axis;return this._isNumber(n.height)?n.width=n.height*this.aspectRatio:this._isNumber(n.width)&&(n.height=n.width/this.aspectRatio),"sw"===r&&(n.left=t.left+(i.width-n.width),n.top=null),"nw"===r&&(n.top=t.top+(i.height-n.height),n.left=t.left+(i.width-n.width)),n},_respectSize:function(n){var t=this._vBoundaries,i=this.axis,r=this._isNumber(n.width)&&t.maxWidth&&t.maxWidth<n.width,u=this._isNumber(n.height)&&t.maxHeight&&t.maxHeight<n.height,f=this._isNumber(n.width)&&t.minWidth&&t.minWidth>n.width,e=this._isNumber(n.height)&&t.minHeight&&t.minHeight>n.height,o=this.originalPosition.left+this.originalSize.width,s=this.position.top+this.size.height,h=/sw|nw|w/.test(i),c=/nw|ne|n/.test(i);return f&&(n.width=t.minWidth),e&&(n.height=t.minHeight),r&&(n.width=t.maxWidth),u&&(n.height=t.maxHeight),f&&h&&(n.left=o-t.minWidth),r&&h&&(n.left=o-t.maxWidth),e&&c&&(n.top=s-t.minHeight),u&&c&&(n.top=s-t.maxHeight),n.width||n.height||n.left||!n.top?n.width||n.height||n.top||!n.left||(n.left=null):n.top=null,n},_getPaddingPlusBorderDimensions:function(n){for(var t=0,i=[],r=[n.css("borderTopWidth"),n.css("borderRightWidth"),n.css("borderBottomWidth"),n.css("borderLeftWidth")],u=[n.css("paddingTop"),n.css("paddingRight"),n.css("paddingBottom"),n.css("paddingLeft")];4>t;t++)i[t]=parseInt(r[t],10)||0,i[t]+=parseInt(u[t],10)||0;return{height:i[0]+i[2],width:i[1]+i[3]}},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var n,t=0,i=this.helper||this.element;this._proportionallyResizeElements.length>t;t++)n=this._proportionallyResizeElements[t],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(n)),n.css({height:i.height()-this.outerDimensions.height||0,width:i.width()-this.outerDimensions.width||0})},_renderProxy:function(){var t=this.element,i=this.options;this.elementOffset=t.offset();this._helper?(this.helper=this.helper||n("<div style='overflow:hidden;'><\/div>"),this.helper.addClass(this._helper).css({width:this.element.outerWidth()-1,height:this.element.outerHeight()-1,position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++i.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(n,t){return{width:this.originalSize.width+t}},w:function(n,t){var i=this.originalSize,r=this.originalPosition;return{left:r.left+t,width:i.width-t}},n:function(n,t,i){var r=this.originalSize,u=this.originalPosition;return{top:u.top+i,height:r.height-i}},s:function(n,t,i){return{height:this.originalSize.height+i}},se:function(t,i,r){return n.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[t,i,r]))},sw:function(t,i,r){return n.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[t,i,r]))},ne:function(t,i,r){return n.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[t,i,r]))},nw:function(t,i,r){return n.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[t,i,r]))}},_propagate:function(t,i){n.ui.plugin.call(this,t,[i,this.ui()]);"resize"!==t&&this._trigger(t,i,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}});n.ui.plugin.add("resizable","animate",{stop:function(t){var i=n(this).resizable("instance"),u=i.options,r=i._proportionallyResizeElements,f=r.length&&/textarea/i.test(r[0].nodeName),s=f&&i._hasScroll(r[0],"left")?0:i.sizeDiff.height,h=f?0:i.sizeDiff.width,c={width:i.size.width-h,height:i.size.height-s},e=parseInt(i.element.css("left"),10)+(i.position.left-i.originalPosition.left)||null,o=parseInt(i.element.css("top"),10)+(i.position.top-i.originalPosition.top)||null;i.element.animate(n.extend(c,o&&e?{top:o,left:e}:{}),{duration:u.animateDuration,easing:u.animateEasing,step:function(){var u={width:parseInt(i.element.css("width"),10),height:parseInt(i.element.css("height"),10),top:parseInt(i.element.css("top"),10),left:parseInt(i.element.css("left"),10)};r&&r.length&&n(r[0]).css({width:u.width,height:u.height});i._updateCache(u);i._propagate("resize",t)}})}});n.ui.plugin.add("resizable","containment",{start:function(){var r,f,e,o,s,h,c,t=n(this).resizable("instance"),l=t.options,a=t.element,u=l.containment,i=u instanceof n?u.get(0):/parent/.test(u)?a.parent().get(0):u;i&&(t.containerElement=n(i),/document/.test(u)||u===document?(t.containerOffset={left:0,top:0},t.containerPosition={left:0,top:0},t.parentData={element:n(document),left:0,top:0,width:n(document).width(),height:n(document).height()||document.body.parentNode.scrollHeight}):(r=n(i),f=[],n(["Top","Right","Left","Bottom"]).each(function(n,i){f[n]=t._num(r.css("padding"+i))}),t.containerOffset=r.offset(),t.containerPosition=r.position(),t.containerSize={height:r.innerHeight()-f[3],width:r.innerWidth()-f[1]},e=t.containerOffset,o=t.containerSize.height,s=t.containerSize.width,h=t._hasScroll(i,"left")?i.scrollWidth:s,c=t._hasScroll(i)?i.scrollHeight:o,t.parentData={element:i,left:e.left,top:e.top,width:h,height:c}))},resize:function(t){var o,s,h,c,i=n(this).resizable("instance"),v=i.options,r=i.containerOffset,l=i.position,f=i._aspectRatio||t.shiftKey,e={top:0,left:0},a=i.containerElement,u=!0;a[0]!==document&&/static/.test(a.css("position"))&&(e=r);l.left<(i._helper?r.left:0)&&(i.size.width=i.size.width+(i._helper?i.position.left-r.left:i.position.left-e.left),f&&(i.size.height=i.size.width/i.aspectRatio,u=!1),i.position.left=v.helper?r.left:0);l.top<(i._helper?r.top:0)&&(i.size.height=i.size.height+(i._helper?i.position.top-r.top:i.position.top),f&&(i.size.width=i.size.height*i.aspectRatio,u=!1),i.position.top=i._helper?r.top:0);h=i.containerElement.get(0)===i.element.parent().get(0);c=/relative|absolute/.test(i.containerElement.css("position"));h&&c?(i.offset.left=i.parentData.left+i.position.left,i.offset.top=i.parentData.top+i.position.top):(i.offset.left=i.element.offset().left,i.offset.top=i.element.offset().top);o=Math.abs(i.sizeDiff.width+(i._helper?i.offset.left-e.left:i.offset.left-r.left));s=Math.abs(i.sizeDiff.height+(i._helper?i.offset.top-e.top:i.offset.top-r.top));o+i.size.width>=i.parentData.width&&(i.size.width=i.parentData.width-o,f&&(i.size.height=i.size.width/i.aspectRatio,u=!1));s+i.size.height>=i.parentData.height&&(i.size.height=i.parentData.height-s,f&&(i.size.width=i.size.height*i.aspectRatio,u=!1));u||(i.position.left=i.prevPosition.left,i.position.top=i.prevPosition.top,i.size.width=i.prevSize.width,i.size.height=i.prevSize.height)},stop:function(){var t=n(this).resizable("instance"),r=t.options,u=t.containerOffset,f=t.containerPosition,e=t.containerElement,i=n(t.helper),o=i.offset(),s=i.outerWidth()-t.sizeDiff.width,h=i.outerHeight()-t.sizeDiff.height;t._helper&&!r.animate&&/relative/.test(e.css("position"))&&n(this).css({left:o.left-f.left-u.left,width:s,height:h});t._helper&&!r.animate&&/static/.test(e.css("position"))&&n(this).css({left:o.left-f.left-u.left,width:s,height:h})}});n.ui.plugin.add("resizable","alsoResize",{start:function(){var t=n(this).resizable("instance"),i=t.options;n(i.alsoResize).each(function(){var t=n(this);t.data("ui-resizable-alsoresize",{width:parseInt(t.width(),10),height:parseInt(t.height(),10),left:parseInt(t.css("left"),10),top:parseInt(t.css("top"),10)})})},resize:function(t,i){var r=n(this).resizable("instance"),e=r.options,u=r.originalSize,f=r.originalPosition,o={height:r.size.height-u.height||0,width:r.size.width-u.width||0,top:r.position.top-f.top||0,left:r.position.left-f.left||0};n(e.alsoResize).each(function(){var t=n(this),u=n(this).data("ui-resizable-alsoresize"),r={},f=t.parents(i.originalElement[0]).length?["width","height"]:["width","height","top","left"];n.each(f,function(n,t){var i=(u[t]||0)+(o[t]||0);i&&i>=0&&(r[t]=i||null)});t.css(r)})},stop:function(){n(this).removeData("resizable-alsoresize")}});n.ui.plugin.add("resizable","ghost",{start:function(){var t=n(this).resizable("instance"),i=t.options,r=t.size;t.ghost=t.originalElement.clone();t.ghost.css({opacity:.25,display:"block",position:"relative",height:r.height,width:r.width,margin:0,left:0,top:0}).addClass("ui-resizable-ghost").addClass("string"==typeof i.ghost?i.ghost:"");t.ghost.appendTo(t.helper)},resize:function(){var t=n(this).resizable("instance");t.ghost&&t.ghost.css({position:"relative",height:t.size.height,width:t.size.width})},stop:function(){var t=n(this).resizable("instance");t.ghost&&t.helper&&t.helper.get(0).removeChild(t.ghost.get(0))}});n.ui.plugin.add("resizable","grid",{resize:function(){var h,t=n(this).resizable("instance"),i=t.options,y=t.size,o=t.originalSize,s=t.originalPosition,c=t.axis,l="number"==typeof i.grid?[i.grid,i.grid]:i.grid,f=l[0]||1,e=l[1]||1,a=Math.round((y.width-o.width)/f)*f,v=Math.round((y.height-o.height)/e)*e,r=o.width+a,u=o.height+v,p=i.maxWidth&&r>i.maxWidth,w=i.maxHeight&&u>i.maxHeight,b=i.minWidth&&i.minWidth>r,k=i.minHeight&&i.minHeight>u;i.grid=l;b&&(r+=f);k&&(u+=e);p&&(r-=f);w&&(u-=e);/^(se|s|e)$/.test(c)?(t.size.width=r,t.size.height=u):/^(ne)$/.test(c)?(t.size.width=r,t.size.height=u,t.position.top=s.top-v):/^(sw)$/.test(c)?(t.size.width=r,t.size.height=u,t.position.left=s.left-a):((0>=u-e||0>=r-f)&&(h=t._getPaddingPlusBorderDimensions(this)),u-e>0?(t.size.height=u,t.position.top=s.top-v):(u=e-h.height,t.size.height=u,t.position.top=s.top+o.height-u),r-f>0?(t.size.width=r,t.position.left=s.left-a):(r=f-h.width,t.size.width=r,t.position.left=s.left+o.width-r))}});n.ui.resizable;n.widget("ui.dialog",{version:"1.11.4",options:{appendTo:"body",autoOpen:!0,buttons:[],closeOnEscape:!0,closeText:"Close",dialogClass:"",draggable:!0,hide:null,height:"auto",maxHeight:null,maxWidth:null,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",of:window,collision:"fit",using:function(t){var i=n(this).css(t).offset().top;0>i&&n(this).css("top",t.top-i)}},resizable:!0,show:null,title:null,width:300,beforeClose:null,close:null,drag:null,dragStart:null,dragStop:null,focus:null,open:null,resize:null,resizeStart:null,resizeStop:null},sizeRelatedOptions:{buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},resizableRelatedOptions:{maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0},_create:function(){this.originalCss={display:this.element[0].style.display,width:this.element[0].style.width,minHeight:this.element[0].style.minHeight,maxHeight:this.element[0].style.maxHeight,height:this.element[0].style.height};this.originalPosition={parent:this.element.parent(),index:this.element.parent().children().index(this.element)};this.originalTitle=this.element.attr("title");this.options.title=this.options.title||this.originalTitle;this._createWrapper();this.element.show().removeAttr("title").addClass("ui-dialog-content ui-widget-content").appendTo(this.uiDialog);this._createTitlebar();this._createButtonPane();this.options.draggable&&n.fn.draggable&&this._makeDraggable();this.options.resizable&&n.fn.resizable&&this._makeResizable();this._isOpen=!1;this._trackFocus()},_init:function(){this.options.autoOpen&&this.open()},_appendTo:function(){var t=this.options.appendTo;return t&&(t.jquery||t.nodeType)?n(t):this.document.find(t||"body").eq(0)},_destroy:function(){var n,t=this.originalPosition;this._untrackInstance();this._destroyOverlay();this.element.removeUniqueId().removeClass("ui-dialog-content ui-widget-content").css(this.originalCss).detach();this.uiDialog.stop(!0,!0).remove();this.originalTitle&&this.element.attr("title",this.originalTitle);n=t.parent.children().eq(t.index);n.length&&n[0]!==this.element[0]?n.before(this.element):t.parent.append(this.element)},widget:function(){return this.uiDialog},disable:n.noop,enable:n.noop,close:function(t){var i,r=this;if(this._isOpen&&this._trigger("beforeClose",t)!==!1){if(this._isOpen=!1,this._focusedElement=null,this._destroyOverlay(),this._untrackInstance(),!this.opener.filter(":focusable").focus().length)try{i=this.document[0].activeElement;i&&"body"!==i.nodeName.toLowerCase()&&n(i).blur()}catch(u){}this._hide(this.uiDialog,this.options.hide,function(){r._trigger("close",t)})}},isOpen:function(){return this._isOpen},moveToTop:function(){this._moveToTop()},_moveToTop:function(t,i){var r=!1,f=this.uiDialog.siblings(".ui-front:visible").map(function(){return+n(this).css("z-index")}).get(),u=Math.max.apply(null,f);return u>=+this.uiDialog.css("z-index")&&(this.uiDialog.css("z-index",u+1),r=!0),r&&!i&&this._trigger("focus",t),r},open:function(){var t=this;return this._isOpen?(this._moveToTop()&&this._focusTabbable(),void 0):(this._isOpen=!0,this.opener=n(this.document[0].activeElement),this._size(),this._position(),this._createOverlay(),this._moveToTop(null,!0),this.overlay&&this.overlay.css("z-index",this.uiDialog.css("z-index")-1),this._show(this.uiDialog,this.options.show,function(){t._focusTabbable();t._trigger("focus")}),this._makeFocusTarget(),this._trigger("open"),void 0)},_focusTabbable:function(){var n=this._focusedElement;n||(n=this.element.find("[autofocus]"));n.length||(n=this.element.find(":tabbable"));n.length||(n=this.uiDialogButtonPane.find(":tabbable"));n.length||(n=this.uiDialogTitlebarClose.filter(":tabbable"));n.length||(n=this.uiDialog);n.eq(0).focus()},_keepFocus:function(t){function i(){var t=this.document[0].activeElement,i=this.uiDialog[0]===t||n.contains(this.uiDialog[0],t);i||this._focusTabbable()}t.preventDefault();i.call(this);this._delay(i)},_createWrapper:function(){this.uiDialog=n("<div>").addClass("ui-dialog ui-widget ui-widget-content ui-corner-all ui-front "+this.options.dialogClass).hide().attr({tabIndex:-1,role:"dialog"}).appendTo(this._appendTo());this._on(this.uiDialog,{keydown:function(t){if(this.options.closeOnEscape&&!t.isDefaultPrevented()&&t.keyCode&&t.keyCode===n.ui.keyCode.ESCAPE)return t.preventDefault(),this.close(t),void 0;if(t.keyCode===n.ui.keyCode.TAB&&!t.isDefaultPrevented()){var i=this.uiDialog.find(":tabbable"),r=i.filter(":first"),u=i.filter(":last");t.target!==u[0]&&t.target!==this.uiDialog[0]||t.shiftKey?t.target!==r[0]&&t.target!==this.uiDialog[0]||!t.shiftKey||(this._delay(function(){u.focus()}),t.preventDefault()):(this._delay(function(){r.focus()}),t.preventDefault())}},mousedown:function(n){this._moveToTop(n)&&this._focusTabbable()}});this.element.find("[aria-describedby]").length||this.uiDialog.attr({"aria-describedby":this.element.uniqueId().attr("id")})},_createTitlebar:function(){var t;this.uiDialogTitlebar=n("<div>").addClass("ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix").prependTo(this.uiDialog);this._on(this.uiDialogTitlebar,{mousedown:function(t){n(t.target).closest(".ui-dialog-titlebar-close")||this.uiDialog.focus()}});this.uiDialogTitlebarClose=n("<button type='button'><\/button>").button({label:this.options.closeText,icons:{primary:"ui-icon-closethick"},text:!1}).addClass("ui-dialog-titlebar-close").appendTo(this.uiDialogTitlebar);this._on(this.uiDialogTitlebarClose,{click:function(n){n.preventDefault();this.close(n)}});t=n("<span>").uniqueId().addClass("ui-dialog-title").prependTo(this.uiDialogTitlebar);this._title(t);this.uiDialog.attr({"aria-labelledby":t.attr("id")})},_title:function(n){this.options.title||n.html("&#160;");n.text(this.options.title)},_createButtonPane:function(){this.uiDialogButtonPane=n("<div>").addClass("ui-dialog-buttonpane ui-widget-content ui-helper-clearfix");this.uiButtonSet=n("<div>").addClass("ui-dialog-buttonset").appendTo(this.uiDialogButtonPane);this._createButtons()},_createButtons:function(){var i=this,t=this.options.buttons;return this.uiDialogButtonPane.remove(),this.uiButtonSet.empty(),n.isEmptyObject(t)||n.isArray(t)&&!t.length?(this.uiDialog.removeClass("ui-dialog-buttons"),void 0):(n.each(t,function(t,r){var u,f;r=n.isFunction(r)?{click:r,text:t}:r;r=n.extend({type:"button"},r);u=r.click;r.click=function(){u.apply(i.element[0],arguments)};f={icons:r.icons,text:r.showText};delete r.icons;delete r.showText;n("<button><\/button>",r).button(f).appendTo(i.uiButtonSet)}),this.uiDialog.addClass("ui-dialog-buttons"),this.uiDialogButtonPane.appendTo(this.uiDialog),void 0)},_makeDraggable:function(){function i(n){return{position:n.position,offset:n.offset}}var t=this,r=this.options;this.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(r,u){n(this).addClass("ui-dialog-dragging");t._blockFrames();t._trigger("dragStart",r,i(u))},drag:function(n,r){t._trigger("drag",n,i(r))},stop:function(u,f){var e=f.offset.left-t.document.scrollLeft(),o=f.offset.top-t.document.scrollTop();r.position={my:"left top",at:"left"+(e>=0?"+":"")+e+" top"+(o>=0?"+":"")+o,of:t.window};n(this).removeClass("ui-dialog-dragging");t._unblockFrames();t._trigger("dragStop",u,i(f))}})},_makeResizable:function(){function r(n){return{originalPosition:n.originalPosition,originalSize:n.originalSize,position:n.position,size:n.size}}var t=this,i=this.options,u=i.resizable,f=this.uiDialog.css("position"),e="string"==typeof u?u:"n,e,s,w,se,sw,ne,nw";this.uiDialog.resizable({cancel:".ui-dialog-content",containment:"document",alsoResize:this.element,maxWidth:i.maxWidth,maxHeight:i.maxHeight,minWidth:i.minWidth,minHeight:this._minHeight(),handles:e,start:function(i,u){n(this).addClass("ui-dialog-resizing");t._blockFrames();t._trigger("resizeStart",i,r(u))},resize:function(n,i){t._trigger("resize",n,r(i))},stop:function(u,f){var e=t.uiDialog.offset(),o=e.left-t.document.scrollLeft(),s=e.top-t.document.scrollTop();i.height=t.uiDialog.height();i.width=t.uiDialog.width();i.position={my:"left top",at:"left"+(o>=0?"+":"")+o+" top"+(s>=0?"+":"")+s,of:t.window};n(this).removeClass("ui-dialog-resizing");t._unblockFrames();t._trigger("resizeStop",u,r(f))}}).css("position",f)},_trackFocus:function(){this._on(this.widget(),{focusin:function(t){this._makeFocusTarget();this._focusedElement=n(t.target)}})},_makeFocusTarget:function(){this._untrackInstance();this._trackingInstances().unshift(this)},_untrackInstance:function(){var t=this._trackingInstances(),i=n.inArray(this,t);-1!==i&&t.splice(i,1)},_trackingInstances:function(){var n=this.document.data("ui-dialog-instances");return n||(n=[],this.document.data("ui-dialog-instances",n)),n},_minHeight:function(){var n=this.options;return"auto"===n.height?n.minHeight:Math.min(n.minHeight,n.height)},_position:function(){var n=this.uiDialog.is(":visible");n||this.uiDialog.show();this.uiDialog.position(this.options.position);n||this.uiDialog.hide()},_setOptions:function(t){var i=this,r=!1,u={};n.each(t,function(n,t){i._setOption(n,t);n in i.sizeRelatedOptions&&(r=!0);n in i.resizableRelatedOptions&&(u[n]=t)});r&&(this._size(),this._position());this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option",u)},_setOption:function(n,t){var u,r,i=this.uiDialog;"dialogClass"===n&&i.removeClass(this.options.dialogClass).addClass(t);"disabled"!==n&&(this._super(n,t),"appendTo"===n&&this.uiDialog.appendTo(this._appendTo()),"buttons"===n&&this._createButtons(),"closeText"===n&&this.uiDialogTitlebarClose.button({label:""+t}),"draggable"===n&&(u=i.is(":data(ui-draggable)"),u&&!t&&i.draggable("destroy"),!u&&t&&this._makeDraggable()),"position"===n&&this._position(),"resizable"===n&&(r=i.is(":data(ui-resizable)"),r&&!t&&i.resizable("destroy"),r&&"string"==typeof t&&i.resizable("option","handles",t),r||t===!1||this._makeResizable()),"title"===n&&this._title(this.uiDialogTitlebar.find(".ui-dialog-title")))},_size:function(){var t,i,r,n=this.options;this.element.show().css({width:"auto",minHeight:0,maxHeight:"none",height:0});n.minWidth>n.width&&(n.width=n.minWidth);t=this.uiDialog.css({height:"auto",width:n.width}).outerHeight();i=Math.max(0,n.minHeight-t);r="number"==typeof n.maxHeight?Math.max(0,n.maxHeight-t):"none";"auto"===n.height?this.element.css({minHeight:i,maxHeight:r,height:"auto"}):this.element.height(Math.max(0,n.height-t));this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())},_blockFrames:function(){this.iframeBlocks=this.document.find("iframe").map(function(){var t=n(this);return n("<div>").css({position:"absolute",width:t.outerWidth(),height:t.outerHeight()}).appendTo(t.parent()).offset(t.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_allowInteraction:function(t){return n(t.target).closest(".ui-dialog").length?!0:!!n(t.target).closest(".ui-datepicker").length},_createOverlay:function(){if(this.options.modal){var t=!0;this._delay(function(){t=!1});this.document.data("ui-dialog-overlays")||this._on(this.document,{focusin:function(n){t||this._allowInteraction(n)||(n.preventDefault(),this._trackingInstances()[0]._focusTabbable())}});this.overlay=n("<div>").addClass("ui-widget-overlay ui-front").appendTo(this._appendTo());this._on(this.overlay,{mousedown:"_keepFocus"});this.document.data("ui-dialog-overlays",(this.document.data("ui-dialog-overlays")||0)+1)}},_destroyOverlay:function(){if(this.options.modal&&this.overlay){var n=this.document.data("ui-dialog-overlays")-1;n?this.document.data("ui-dialog-overlays",n):this.document.unbind("focusin").removeData("ui-dialog-overlays");this.overlay.remove();this.overlay=null}}});n.widget("ui.droppable",{version:"1.11.4",widgetEventPrefix:"drop",options:{accept:"*",activeClass:!1,addClasses:!0,greedy:!1,hoverClass:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var t,i=this.options,r=i.accept;this.isover=!1;this.isout=!0;this.accept=n.isFunction(r)?r:function(n){return n.is(r)};this.proportions=function(){return arguments.length?(t=arguments[0],void 0):t?t:t={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight}};this._addToManager(i.scope);i.addClasses&&this.element.addClass("ui-droppable")},_addToManager:function(t){n.ui.ddmanager.droppables[t]=n.ui.ddmanager.droppables[t]||[];n.ui.ddmanager.droppables[t].push(this)},_splice:function(n){for(var t=0;n.length>t;t++)n[t]===this&&n.splice(t,1)},_destroy:function(){var t=n.ui.ddmanager.droppables[this.options.scope];this._splice(t);this.element.removeClass("ui-droppable ui-droppable-disabled")},_setOption:function(t,i){if("accept"===t)this.accept=n.isFunction(i)?i:function(n){return n.is(i)};else if("scope"===t){var r=n.ui.ddmanager.droppables[this.options.scope];this._splice(r);this._addToManager(i)}this._super(t,i)},_activate:function(t){var i=n.ui.ddmanager.current;this.options.activeClass&&this.element.addClass(this.options.activeClass);i&&this._trigger("activate",t,this.ui(i))},_deactivate:function(t){var i=n.ui.ddmanager.current;this.options.activeClass&&this.element.removeClass(this.options.activeClass);i&&this._trigger("deactivate",t,this.ui(i))},_over:function(t){var i=n.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this.options.hoverClass&&this.element.addClass(this.options.hoverClass),this._trigger("over",t,this.ui(i)))},_out:function(t){var i=n.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("out",t,this.ui(i)))},_drop:function(t,i){var r=i||n.ui.ddmanager.current,u=!1;return r&&(r.currentItem||r.element)[0]!==this.element[0]?(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each(function(){var i=n(this).droppable("instance");if(i.options.greedy&&!i.options.disabled&&i.options.scope===r.options.scope&&i.accept.call(i.element[0],r.currentItem||r.element)&&n.ui.intersect(r,n.extend(i,{offset:i.element.offset()}),i.options.tolerance,t))return(u=!0,!1)}),u?!1:this.accept.call(this.element[0],r.currentItem||r.element)?(this.options.activeClass&&this.element.removeClass(this.options.activeClass),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("drop",t,this.ui(r)),this.element):!1):!1},ui:function(n){return{draggable:n.currentItem||n.element,helper:n.helper,position:n.position,offset:n.positionAbs}}});n.ui.intersect=function(){function n(n,t,i){return n>=t&&t+i>n}return function(t,i,r,u){if(!i.offset)return!1;var o=(t.positionAbs||t.position.absolute).left+t.margins.left,s=(t.positionAbs||t.position.absolute).top+t.margins.top,h=o+t.helperProportions.width,c=s+t.helperProportions.height,f=i.offset.left,e=i.offset.top,l=f+i.proportions().width,a=e+i.proportions().height;switch(r){case"fit":return o>=f&&l>=h&&s>=e&&a>=c;case"intersect":return o+t.helperProportions.width/2>f&&l>h-t.helperProportions.width/2&&s+t.helperProportions.height/2>e&&a>c-t.helperProportions.height/2;case"pointer":return n(u.pageY,e,i.proportions().height)&&n(u.pageX,f,i.proportions().width);case"touch":return(s>=e&&a>=s||c>=e&&a>=c||e>s&&c>a)&&(o>=f&&l>=o||h>=f&&l>=h||f>o&&h>l);default:return!1}}}();n.ui.ddmanager={current:null,droppables:{"default":[]},prepareOffsets:function(t,i){var r,f,u=n.ui.ddmanager.droppables[t.options.scope]||[],o=i?i.type:null,e=(t.currentItem||t.element).find(":data(ui-droppable)").addBack();n:for(r=0;u.length>r;r++)if(!(u[r].options.disabled||t&&!u[r].accept.call(u[r].element[0],t.currentItem||t.element))){for(f=0;e.length>f;f++)if(e[f]===u[r].element[0]){u[r].proportions().height=0;continue n}u[r].visible="none"!==u[r].element.css("display");u[r].visible&&("mousedown"===o&&u[r]._activate.call(u[r],i),u[r].offset=u[r].element.offset(),u[r].proportions({width:u[r].element[0].offsetWidth,height:u[r].element[0].offsetHeight}))}},drop:function(t,i){var r=!1;return n.each((n.ui.ddmanager.droppables[t.options.scope]||[]).slice(),function(){this.options&&(!this.options.disabled&&this.visible&&n.ui.intersect(t,this,this.options.tolerance,i)&&(r=this._drop.call(this,i)||r),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],t.currentItem||t.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,i)))}),r},dragStart:function(t,i){t.element.parentsUntil("body").bind("scroll.droppable",function(){t.options.refreshPositions||n.ui.ddmanager.prepareOffsets(t,i)})},drag:function(t,i){t.options.refreshPositions&&n.ui.ddmanager.prepareOffsets(t,i);n.each(n.ui.ddmanager.droppables[t.options.scope]||[],function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var r,e,f,o=n.ui.intersect(t,this,this.options.tolerance,i),u=!o&&this.isover?"isout":o&&!this.isover?"isover":null;u&&(this.options.greedy&&(e=this.options.scope,f=this.element.parents(":data(ui-droppable)").filter(function(){return n(this).droppable("instance").options.scope===e}),f.length&&(r=n(f[0]).droppable("instance"),r.greedyChild="isover"===u)),r&&"isover"===u&&(r.isover=!1,r.isout=!0,r._out.call(r,i)),this[u]=!0,this["isout"===u?"isover":"isout"]=!1,this["isover"===u?"_over":"_out"].call(this,i),r&&"isout"===u&&(r.isout=!1,r.isover=!0,r._over.call(r,i)))}})},dragStop:function(t,i){t.element.parentsUntil("body").unbind("scroll.droppable");t.options.refreshPositions||n.ui.ddmanager.prepareOffsets(t,i)}};n.ui.droppable;o="ui-effects-";s=n;n.effects={effect:{}},function(n,t){function f(n,t,i){var r=h[t.type]||{};return null==n?i||!t.def?null:t.def:(n=r.floor?~~n:parseFloat(n),isNaN(n)?t.def:r.mod?(n+r.mod)%r.mod:0>n?0:n>r.max?r.max:n)}function s(f){var o=i(),s=o._rgba=[];return f=f.toLowerCase(),r(v,function(n,i){var r,h=i.re.exec(f),c=h&&i.parse(h),e=i.space||"rgba";return c?(r=o[e](c),o[u[e].cache]=r[u[e].cache],s=o._rgba=r._rgba,!1):t}),s.length?("0,0,0,0"===s.join()&&n.extend(s,e.transparent),o):e[f]}function o(n,t,i){return i=(i+1)%1,1>6*i?n+6*(t-n)*i:1>2*i?t:2>3*i?n+6*(t-n)*(2/3-i):n}var e,a=/^([\-+])=\s*(\d+\.?\d*)/,v=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(n){return[n[1],n[2],n[3],n[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(n){return[2.55*n[1],2.55*n[2],2.55*n[3],n[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})/,parse:function(n){return[parseInt(n[1],16),parseInt(n[2],16),parseInt(n[3],16)]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])/,parse:function(n){return[parseInt(n[1]+n[1],16),parseInt(n[2]+n[2],16),parseInt(n[3]+n[3],16)]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(n){return[n[1],n[2]/100,n[3]/100,n[4]]}}],i=n.Color=function(t,i,r,u){return new n.Color.fn.parse(t,i,r,u)},u={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},h={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},c=i.support={},l=n("<p>")[0],r=n.each;l.style.cssText="background-color:rgba(1,1,1,.5)";c.rgba=l.style.backgroundColor.indexOf("rgba")>-1;r(u,function(n,t){t.cache="_"+n;t.props.alpha={idx:3,type:"percent",def:1}});i.fn=n.extend(i.prototype,{parse:function(o,h,c,l){if(o===t)return this._rgba=[null,null,null,null],this;(o.jquery||o.nodeType)&&(o=n(o).css(h),h=t);var a=this,v=n.type(o),y=this._rgba=[];return h!==t&&(o=[o,h,c,l],v="array"),"string"===v?this.parse(s(o)||e._default):"array"===v?(r(u.rgba.props,function(n,t){y[t.idx]=f(o[t.idx],t)}),this):"object"===v?(o instanceof i?r(u,function(n,t){o[t.cache]&&(a[t.cache]=o[t.cache].slice())}):r(u,function(t,i){var u=i.cache;r(i.props,function(n,t){if(!a[u]&&i.to){if("alpha"===n||null==o[n])return;a[u]=i.to(a._rgba)}a[u][t.idx]=f(o[n],t,!0)});a[u]&&0>n.inArray(null,a[u].slice(0,3))&&(a[u][3]=1,i.from&&(a._rgba=i.from(a[u])))}),this):t},is:function(n){var o=i(n),f=!0,e=this;return r(u,function(n,i){var s,u=o[i.cache];return u&&(s=e[i.cache]||i.to&&i.to(e._rgba)||[],r(i.props,function(n,i){return null!=u[i.idx]?f=u[i.idx]===s[i.idx]:t})),f}),f},_space:function(){var n=[],t=this;return r(u,function(i,r){t[r.cache]&&n.push(i)}),n.pop()},transition:function(n,t){var e=i(n),c=e._space(),o=u[c],l=0===this.alpha()?i("transparent"):this,a=l[o.cache]||o.to(l._rgba),s=a.slice();return e=e[o.cache],r(o.props,function(n,i){var c=i.idx,r=a[c],u=e[c],o=h[i.type]||{};null!==u&&(null===r?s[c]=u:(o.mod&&(u-r>o.mod/2?r+=o.mod:r-u>o.mod/2&&(r-=o.mod)),s[c]=f((u-r)*t+r,i)))}),this[c](s)},blend:function(t){if(1===this._rgba[3])return this;var r=this._rgba.slice(),u=r.pop(),f=i(t)._rgba;return i(n.map(r,function(n,t){return(1-u)*f[t]+u*n}))},toRgbaString:function(){var i="rgba(",t=n.map(this._rgba,function(n,t){return null==n?t>2?1:0:n});return 1===t[3]&&(t.pop(),i="rgb("),i+t.join()+")"},toHslaString:function(){var i="hsla(",t=n.map(this.hsla(),function(n,t){return null==n&&(n=t>2?1:0),t&&3>t&&(n=Math.round(100*n)+"%"),n});return 1===t[3]&&(t.pop(),i="hsl("),i+t.join()+")"},toHexString:function(t){var i=this._rgba.slice(),r=i.pop();return t&&i.push(~~(255*r)),"#"+n.map(i,function(n){return n=(n||0).toString(16),1===n.length?"0"+n:n}).join("")},toString:function(){return 0===this._rgba[3]?"transparent":this.toRgbaString()}});i.fn.parse.prototype=i.fn;u.hsla.to=function(n){if(null==n[0]||null==n[1]||null==n[2])return[null,null,null,n[3]];var s,h,i=n[0]/255,r=n[1]/255,f=n[2]/255,c=n[3],u=Math.max(i,r,f),e=Math.min(i,r,f),t=u-e,o=u+e,l=.5*o;return s=e===u?0:i===u?60*(r-f)/t+360:r===u?60*(f-i)/t+120:60*(i-r)/t+240,h=0===t?0:.5>=l?t/o:t/(2-o),[Math.round(s)%360,h,l,null==c?1:c]};u.hsla.from=function(n){if(null==n[0]||null==n[1]||null==n[2])return[null,null,null,n[3]];var r=n[0]/360,u=n[1],t=n[2],e=n[3],i=.5>=t?t*(1+u):t+u-t*u,f=2*t-i;return[Math.round(255*o(f,i,r+1/3)),Math.round(255*o(f,i,r)),Math.round(255*o(f,i,r-1/3)),e]};r(u,function(u,e){var s=e.props,o=e.cache,h=e.to,c=e.from;i.fn[u]=function(u){if(h&&!this[o]&&(this[o]=h(this._rgba)),u===t)return this[o].slice();var l,a=n.type(u),v="array"===a||"object"===a?u:arguments,e=this[o].slice();return r(s,function(n,t){var i=v["object"===a?n:t.idx];null==i&&(i=e[t.idx]);e[t.idx]=f(i,t)}),c?(l=i(c(e)),l[o]=e,l):i(e)};r(s,function(t,r){i.fn[t]||(i.fn[t]=function(i){var f,e=n.type(i),h="alpha"===t?this._hsla?"hsla":"rgba":u,o=this[h](),s=o[r.idx];return"undefined"===e?s:("function"===e&&(i=i.call(this,s),e=n.type(i)),null==i&&r.empty?this:("string"===e&&(f=a.exec(i),f&&(i=s+parseFloat(f[2])*("+"===f[1]?1:-1))),o[r.idx]=i,this[h](o)))})})});i.hook=function(t){var u=t.split(" ");r(u,function(t,r){n.cssHooks[r]={set:function(t,u){var o,f,e="";if("transparent"!==u&&("string"!==n.type(u)||(o=s(u)))){if(u=i(o||u),!c.rgba&&1!==u._rgba[3]){for(f="backgroundColor"===r?t.parentNode:t;(""===e||"transparent"===e)&&f&&f.style;)try{e=n.css(f,"backgroundColor");f=f.parentNode}catch(h){}u=u.blend(e&&"transparent"!==e?e:"_default")}u=u.toRgbaString()}try{t.style[r]=u}catch(h){}}};n.fx.step[r]=function(t){t.colorInit||(t.start=i(t.elem,r),t.end=i(t.end),t.colorInit=!0);n.cssHooks[r].set(t.elem,t.start.transition(t.end,t.pos))}})};i.hook("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor");n.cssHooks.borderColor={expand:function(n){var t={};return r(["Top","Right","Bottom","Left"],function(i,r){t["border"+r+"Color"]=n}),t}};e=n.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"}}(s),function(){function t(t){var r,u,i=t.ownerDocument.defaultView?t.ownerDocument.defaultView.getComputedStyle(t,null):t.currentStyle,f={};if(i&&i.length&&i[0]&&i[i[0]])for(u=i.length;u--;)r=i[u],"string"==typeof i[r]&&(f[n.camelCase(r)]=i[r]);else for(r in i)"string"==typeof i[r]&&(f[r]=i[r]);return f}function i(t,i){var r,f,e={};for(r in i)f=i[r],t[r]!==f&&(u[r]||(n.fx.step[r]||!isNaN(parseFloat(f)))&&(e[r]=f));return e}var r=["add","remove","toggle"],u={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};n.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(t,i){n.fx.step[i]=function(n){("none"===n.end||n.setAttr)&&(1!==n.pos||n.setAttr)||(s.style(n.elem,i,n.end),n.setAttr=!0)}});n.fn.addBack||(n.fn.addBack=function(n){return this.add(null==n?this.prevObject:this.prevObject.filter(n))});n.effects.animateClass=function(u,f,e,o){var s=n.speed(f,e,o);return this.queue(function(){var o,e=n(this),h=e.attr("class")||"",f=s.children?e.find("*").addBack():e;f=f.map(function(){var i=n(this);return{el:i,start:t(this)}});o=function(){n.each(r,function(n,t){u[t]&&e[t+"Class"](u[t])})};o();f=f.map(function(){return this.end=t(this.el[0]),this.diff=i(this.start,this.end),this});e.attr("class",h);f=f.map(function(){var i=this,t=n.Deferred(),r=n.extend({},s,{queue:!1,complete:function(){t.resolve(i)}});return this.el.animate(this.diff,r),t.promise()});n.when.apply(n,f.get()).done(function(){o();n.each(arguments,function(){var t=this.el;n.each(this.diff,function(n){t.css(n,"")})});s.complete.call(e[0])})})};n.fn.extend({addClass:function(t){return function(i,r,u,f){return r?n.effects.animateClass.call(this,{add:i},r,u,f):t.apply(this,arguments)}}(n.fn.addClass),removeClass:function(t){return function(i,r,u,f){return arguments.length>1?n.effects.animateClass.call(this,{remove:i},r,u,f):t.apply(this,arguments)}}(n.fn.removeClass),toggleClass:function(t){return function(i,r,u,f,e){return"boolean"==typeof r||void 0===r?u?n.effects.animateClass.call(this,r?{add:i}:{remove:i},u,f,e):t.apply(this,arguments):n.effects.animateClass.call(this,{toggle:i},r,u,f)}}(n.fn.toggleClass),switchClass:function(t,i,r,u,f){return n.effects.animateClass.call(this,{add:i,remove:t},r,u,f)}})}(),function(){function t(t,i,r,u){return n.isPlainObject(t)&&(i=t,t=t.effect),t={effect:t},null==i&&(i={}),n.isFunction(i)&&(u=i,r=null,i={}),("number"==typeof i||n.fx.speeds[i])&&(u=r,r=i,i={}),n.isFunction(r)&&(u=r,r=null),i&&n.extend(t,i),r=r||i.duration,t.duration=n.fx.off?0:"number"==typeof r?r:r in n.fx.speeds?n.fx.speeds[r]:n.fx.speeds._default,t.complete=u||i.complete,t}function i(t){return!t||"number"==typeof t||n.fx.speeds[t]?!0:"string"!=typeof t||n.effects.effect[t]?n.isFunction(t)?!0:"object"!=typeof t||t.effect?!1:!0:!0}n.extend(n.effects,{version:"1.11.4",save:function(n,t){for(var i=0;t.length>i;i++)null!==t[i]&&n.data(o+t[i],n[0].style[t[i]])},restore:function(n,t){for(var r,i=0;t.length>i;i++)null!==t[i]&&(r=n.data(o+t[i]),void 0===r&&(r=""),n.css(t[i],r))},setMode:function(n,t){return"toggle"===t&&(t=n.is(":hidden")?"show":"hide"),t},getBaseline:function(n,t){var i,r;switch(n[0]){case"top":i=0;break;case"middle":i=.5;break;case"bottom":i=1;break;default:i=n[0]/t.height}switch(n[1]){case"left":r=0;break;case"center":r=.5;break;case"right":r=1;break;default:r=n[1]/t.width}return{x:r,y:i}},createWrapper:function(t){if(t.parent().is(".ui-effects-wrapper"))return t.parent();var i={width:t.outerWidth(!0),height:t.outerHeight(!0),float:t.css("float")},u=n("<div><\/div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),f={width:t.width(),height:t.height()},r=document.activeElement;try{r.id}catch(e){r=document.body}return t.wrap(u),(t[0]===r||n.contains(t[0],r))&&n(r).focus(),u=t.parent(),"static"===t.css("position")?(u.css({position:"relative"}),t.css({position:"relative"})):(n.extend(i,{position:t.css("position"),zIndex:t.css("z-index")}),n.each(["top","left","bottom","right"],function(n,r){i[r]=t.css(r);isNaN(parseInt(i[r],10))&&(i[r]="auto")}),t.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),t.css(f),u.css(i).show()},removeWrapper:function(t){var i=document.activeElement;return t.parent().is(".ui-effects-wrapper")&&(t.parent().replaceWith(t),(t[0]===i||n.contains(t[0],i))&&n(i).focus()),t},setTransition:function(t,i,r,u){return u=u||{},n.each(i,function(n,i){var f=t.cssUnit(i);f[0]>0&&(u[i]=f[0]*r+f[1])}),u}});n.fn.extend({effect:function(){function r(t){function f(){n.isFunction(o)&&o.call(r[0]);n.isFunction(t)&&t()}var r=n(this),o=i.complete,u=i.mode;(r.is(":hidden")?"hide"===u:"show"===u)?(r[u](),f()):e.call(r[0],i,f)}var i=t.apply(this,arguments),u=i.mode,f=i.queue,e=n.effects.effect[i.effect];return n.fx.off||!e?u?this[u](i.duration,i.complete):this.each(function(){i.complete&&i.complete.call(this)}):f===!1?this.each(r):this.queue(f||"fx",r)},show:function(n){return function(r){if(i(r))return n.apply(this,arguments);var u=t.apply(this,arguments);return u.mode="show",this.effect.call(this,u)}}(n.fn.show),hide:function(n){return function(r){if(i(r))return n.apply(this,arguments);var u=t.apply(this,arguments);return u.mode="hide",this.effect.call(this,u)}}(n.fn.hide),toggle:function(n){return function(r){if(i(r)||"boolean"==typeof r)return n.apply(this,arguments);var u=t.apply(this,arguments);return u.mode="toggle",this.effect.call(this,u)}}(n.fn.toggle),cssUnit:function(t){var i=this.css(t),r=[];return n.each(["em","px","%","pt"],function(n,t){i.indexOf(t)>0&&(r=[parseFloat(i),t])}),r}})}(),function(){var t={};n.each(["Quad","Cubic","Quart","Quint","Expo"],function(n,i){t[i]=function(t){return Math.pow(t,n+2)}});n.extend(t,{Sine:function(n){return 1-Math.cos(n*Math.PI/2)},Circ:function(n){return 1-Math.sqrt(1-n*n)},Elastic:function(n){return 0===n||1===n?n:-Math.pow(2,8*(n-1))*Math.sin((80*(n-1)-7.5)*Math.PI/15)},Back:function(n){return n*n*(3*n-2)},Bounce:function(n){for(var t,i=4;((t=Math.pow(2,--i))-1)/11>n;);return 1/Math.pow(4,3-i)-7.5625*Math.pow((3*t-2)/22-n,2)}});n.each(t,function(t,i){n.easing["easeIn"+t]=i;n.easing["easeOut"+t]=function(n){return 1-i(1-n)};n.easing["easeInOut"+t]=function(n){return.5>n?i(2*n)/2:1-i(-2*n+2)/2}})}();n.effects;n.effects.effect.blind=function(t,i){var u,f,e,r=n(this),s=["position","top","bottom","left","right","height","width"],v=n.effects.setMode(r,t.mode||"hide"),y=t.direction||"up",o=/up|down|vertical/.test(y),h=o?"height":"width",c=o?"top":"left",p=/up|left|vertical|horizontal/.test(y),l={},a="show"===v;r.parent().is(".ui-effects-wrapper")?n.effects.save(r.parent(),s):n.effects.save(r,s);r.show();u=n.effects.createWrapper(r).css({overflow:"hidden"});f=u[h]();e=parseFloat(u.css(c))||0;l[h]=a?f:0;p||(r.css(o?"bottom":"right",0).css(o?"top":"left","auto").css({position:"absolute"}),l[c]=a?e:f+e);a&&(u.css(h,0),p||u.css(c,e+f));u.animate(l,{duration:t.duration,easing:t.easing,queue:!1,complete:function(){"hide"===v&&r.hide();n.effects.restore(r,s);n.effects.removeWrapper(r);i()}})};n.effects.effect.bounce=function(t,i){var v,f,e,r=n(this),y=["position","top","bottom","left","right","height","width"],k=n.effects.setMode(r,t.mode||"effect"),o="hide"===k,p="show"===k,h=t.direction||"up",u=t.distance,w=t.times||5,d=2*w+(p||o?1:0),c=t.duration/d,l=t.easing,s="up"===h||"down"===h?"top":"left",b="up"===h||"left"===h,a=r.queue(),g=a.length;for((p||o)&&y.push("opacity"),n.effects.save(r,y),r.show(),n.effects.createWrapper(r),u||(u=r["top"===s?"outerHeight":"outerWidth"]()/3),p&&(e={opacity:1},e[s]=0,r.css("opacity",0).css(s,b?2*-u:2*u).animate(e,c,l)),o&&(u/=Math.pow(2,w-1)),e={},e[s]=0,v=0;w>v;v++)f={},f[s]=(b?"-=":"+=")+u,r.animate(f,c,l).animate(e,c,l),u=o?2*u:u/2;o&&(f={opacity:0},f[s]=(b?"-=":"+=")+u,r.animate(f,c,l));r.queue(function(){o&&r.hide();n.effects.restore(r,y);n.effects.removeWrapper(r);i()});g>1&&a.splice.apply(a,[1,0].concat(a.splice(g,d+1)));r.dequeue()};n.effects.effect.clip=function(t,i){var h,u,f,r=n(this),c=["position","top","bottom","left","right","height","width"],v=n.effects.setMode(r,t.mode||"hide"),e="show"===v,y=t.direction||"vertical",l="vertical"===y,o=l?"height":"width",a=l?"top":"left",s={};n.effects.save(r,c);r.show();h=n.effects.createWrapper(r).css({overflow:"hidden"});u="IMG"===r[0].tagName?h:r;f=u[o]();e&&(u.css(o,0),u.css(a,f/2));s[o]=e?f:0;s[a]=e?0:f/2;u.animate(s,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){e||r.hide();n.effects.restore(r,c);n.effects.removeWrapper(r);i()}})};n.effects.effect.drop=function(t,i){var u,r=n(this),h=["position","top","bottom","left","right","opacity","height","width"],c=n.effects.setMode(r,t.mode||"hide"),e="show"===c,f=t.direction||"left",o="up"===f||"down"===f?"top":"left",s="up"===f||"left"===f?"pos":"neg",l={opacity:e?1:0};n.effects.save(r,h);r.show();n.effects.createWrapper(r);u=t.distance||r["top"===o?"outerHeight":"outerWidth"](!0)/2;e&&r.css("opacity",0).css(o,"pos"===s?-u:u);l[o]=(e?"pos"===s?"+=":"-=":"pos"===s?"-=":"+=")+u;r.animate(l,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){"hide"===c&&r.hide();n.effects.restore(r,h);n.effects.removeWrapper(r);i()}})};n.effects.effect.explode=function(t,i){function b(){p.push(this);p.length===o*c&&k()}function k(){r.css({visibility:"visible"});n(p).remove();u||r.hide();i()}for(var e,l,a,v,y,o=t.pieces?Math.round(Math.sqrt(t.pieces)):3,c=o,r=n(this),d=n.effects.setMode(r,t.mode||"hide"),u="show"===d,w=r.show().css("visibility","hidden").offset(),s=Math.ceil(r.outerWidth()/c),h=Math.ceil(r.outerHeight()/o),p=[],f=0;o>f;f++)for(a=w.top+f*h,y=f-(o-1)/2,e=0;c>e;e++)l=w.left+e*s,v=e-(c-1)/2,r.clone().appendTo("body").wrap("<div><\/div>").css({position:"absolute",visibility:"visible",left:-e*s,top:-f*h}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:s,height:h,left:l+(u?v*s:0),top:a+(u?y*h:0),opacity:u?0:1}).animate({left:l+(u?0:v*s),top:a+(u?0:y*h),opacity:u?1:0},t.duration||500,t.easing,b)};n.effects.effect.fade=function(t,i){var r=n(this),u=n.effects.setMode(r,t.mode||"toggle");r.animate({opacity:u},{queue:!1,duration:t.duration,easing:t.easing,complete:i})};n.effects.effect.fold=function(t,i){var r,e,u=n(this),s=["position","top","bottom","left","right","height","width"],h=n.effects.setMode(u,t.mode||"hide"),o="show"===h,c="hide"===h,f=t.size||15,l=/([0-9]+)%/.exec(f),a=!!t.horizFirst,v=o!==a,y=v?["width","height"]:["height","width"],p=t.duration/2,w={},b={};n.effects.save(u,s);u.show();r=n.effects.createWrapper(u).css({overflow:"hidden"});e=v?[r.width(),r.height()]:[r.height(),r.width()];l&&(f=parseInt(l[1],10)/100*e[c?0:1]);o&&r.css(a?{height:0,width:f}:{height:f,width:0});w[y[0]]=o?e[0]:f;b[y[1]]=o?e[1]:0;r.animate(w,p,t.easing).animate(b,p,t.easing,function(){c&&u.hide();n.effects.restore(u,s);n.effects.removeWrapper(u);i()})};n.effects.effect.highlight=function(t,i){var r=n(this),u=["backgroundImage","backgroundColor","opacity"],f=n.effects.setMode(r,t.mode||"show"),e={backgroundColor:r.css("backgroundColor")};"hide"===f&&(e.opacity=0);n.effects.save(r,u);r.show().css({backgroundImage:"none",backgroundColor:t.color||"#ffff99"}).animate(e,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){"hide"===f&&r.hide();n.effects.restore(r,u);i()}})};n.effects.effect.size=function(t,i){var f,l,u,r=n(this),w=["position","top","bottom","left","right","width","height","overflow","opacity"],a=["width","height","overflow"],v=["fontSize"],e=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],o=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],h=n.effects.setMode(r,t.mode||"effect"),y=t.restore||"effect"!==h,c=t.scale||"both",b=t.origin||["middle","center"],k=r.css("position"),s=y?w:["position","top","bottom","left","right","overflow","opacity"],p={height:0,width:0,outerHeight:0,outerWidth:0};"show"===h&&r.show();f={height:r.height(),width:r.width(),outerHeight:r.outerHeight(),outerWidth:r.outerWidth()};"toggle"===t.mode&&"show"===h?(r.from=t.to||p,r.to=t.from||f):(r.from=t.from||("show"===h?p:f),r.to=t.to||("hide"===h?p:f));u={from:{y:r.from.height/f.height,x:r.from.width/f.width},to:{y:r.to.height/f.height,x:r.to.width/f.width}};("box"===c||"both"===c)&&(u.from.y!==u.to.y&&(s=s.concat(e),r.from=n.effects.setTransition(r,e,u.from.y,r.from),r.to=n.effects.setTransition(r,e,u.to.y,r.to)),u.from.x!==u.to.x&&(s=s.concat(o),r.from=n.effects.setTransition(r,o,u.from.x,r.from),r.to=n.effects.setTransition(r,o,u.to.x,r.to)));("content"===c||"both"===c)&&u.from.y!==u.to.y&&(s=s.concat(v).concat(a),r.from=n.effects.setTransition(r,v,u.from.y,r.from),r.to=n.effects.setTransition(r,v,u.to.y,r.to));n.effects.save(r,s);r.show();n.effects.createWrapper(r);r.css("overflow","hidden").css(r.from);b&&(l=n.effects.getBaseline(b,f),r.from.top=(f.outerHeight-r.outerHeight())*l.y,r.from.left=(f.outerWidth-r.outerWidth())*l.x,r.to.top=(f.outerHeight-r.to.outerHeight)*l.y,r.to.left=(f.outerWidth-r.to.outerWidth)*l.x);r.css(r.from);("content"===c||"both"===c)&&(e=e.concat(["marginTop","marginBottom"]).concat(v),o=o.concat(["marginLeft","marginRight"]),a=w.concat(e).concat(o),r.find("*[width]").each(function(){var i=n(this),r={height:i.height(),width:i.width(),outerHeight:i.outerHeight(),outerWidth:i.outerWidth()};y&&n.effects.save(i,a);i.from={height:r.height*u.from.y,width:r.width*u.from.x,outerHeight:r.outerHeight*u.from.y,outerWidth:r.outerWidth*u.from.x};i.to={height:r.height*u.to.y,width:r.width*u.to.x,outerHeight:r.height*u.to.y,outerWidth:r.width*u.to.x};u.from.y!==u.to.y&&(i.from=n.effects.setTransition(i,e,u.from.y,i.from),i.to=n.effects.setTransition(i,e,u.to.y,i.to));u.from.x!==u.to.x&&(i.from=n.effects.setTransition(i,o,u.from.x,i.from),i.to=n.effects.setTransition(i,o,u.to.x,i.to));i.css(i.from);i.animate(i.to,t.duration,t.easing,function(){y&&n.effects.restore(i,a)})}));r.animate(r.to,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){0===r.to.opacity&&r.css("opacity",r.from.opacity);"hide"===h&&r.hide();n.effects.restore(r,s);y||("static"===k?r.css({position:"relative",top:r.to.top,left:r.to.left}):n.each(["top","left"],function(n,t){r.css(t,function(t,i){var f=parseInt(i,10),u=n?r.to.left:r.to.top;return"auto"===i?u+"px":f+u+"px"})}));n.effects.removeWrapper(r);i()}})};n.effects.effect.scale=function(t,i){var u=n(this),r=n.extend(!0,{},t),f=n.effects.setMode(u,t.mode||"effect"),s=parseInt(t.percent,10)||(0===parseInt(t.percent,10)?0:"hide"===f?0:100),h=t.direction||"both",c=t.origin,e={height:u.height(),width:u.width(),outerHeight:u.outerHeight(),outerWidth:u.outerWidth()},o={y:"horizontal"!==h?s/100:1,x:"vertical"!==h?s/100:1};r.effect="size";r.queue=!1;r.complete=i;"effect"!==f&&(r.origin=c||["middle","center"],r.restore=!0);r.from=t.from||("show"===f?{height:0,width:0,outerHeight:0,outerWidth:0}:e);r.to={height:e.height*o.y,width:e.width*o.x,outerHeight:e.outerHeight*o.y,outerWidth:e.outerWidth*o.x};r.fade&&("show"===f&&(r.from.opacity=0,r.to.opacity=1),"hide"===f&&(r.from.opacity=1,r.to.opacity=0));u.effect(r)};n.effects.effect.puff=function(t,i){var r=n(this),e=n.effects.setMode(r,t.mode||"hide"),o="hide"===e,s=parseInt(t.percent,10)||150,f=s/100,u={height:r.height(),width:r.width(),outerHeight:r.outerHeight(),outerWidth:r.outerWidth()};n.extend(t,{effect:"scale",queue:!1,fade:!0,mode:e,complete:i,percent:o?s:100,from:o?u:{height:u.height*f,width:u.width*f,outerHeight:u.outerHeight*f,outerWidth:u.outerWidth*f}});r.effect(t)};n.effects.effect.pulsate=function(t,i){var e,r=n(this),o=n.effects.setMode(r,t.mode||"show"),h="show"===o,a="hide"===o,v=h||"hide"===o,s=2*(t.times||5)+(v?1:0),c=t.duration/s,u=0,f=r.queue(),l=f.length;for((h||!r.is(":visible"))&&(r.css("opacity",0).show(),u=1),e=1;s>e;e++)r.animate({opacity:u},c,t.easing),u=1-u;r.animate({opacity:u},c,t.easing);r.queue(function(){a&&r.hide();i()});l>1&&f.splice.apply(f,[1,0].concat(f.splice(l,s+1)));r.dequeue()};n.effects.effect.shake=function(t,i){var o,r=n(this),v=["position","top","bottom","left","right","height","width"],k=n.effects.setMode(r,t.mode||"effect"),f=t.direction||"left",s=t.distance||20,y=t.times||3,p=2*y+1,u=Math.round(t.duration/p),h="up"===f||"down"===f?"top":"left",c="up"===f||"left"===f,l={},a={},w={},e=r.queue(),b=e.length;for(n.effects.save(r,v),r.show(),n.effects.createWrapper(r),l[h]=(c?"-=":"+=")+s,a[h]=(c?"+=":"-=")+2*s,w[h]=(c?"-=":"+=")+2*s,r.animate(l,u,t.easing),o=1;y>o;o++)r.animate(a,u,t.easing).animate(w,u,t.easing);r.animate(a,u,t.easing).animate(l,u/2,t.easing).queue(function(){"hide"===k&&r.hide();n.effects.restore(r,v);n.effects.removeWrapper(r);i()});b>1&&e.splice.apply(e,[1,0].concat(e.splice(b,p+1)));r.dequeue()};n.effects.effect.slide=function(t,i){var u,r=n(this),s=["position","top","bottom","left","right","width","height"],h=n.effects.setMode(r,t.mode||"show"),c="show"===h,f=t.direction||"left",e="up"===f||"down"===f?"top":"left",o="up"===f||"left"===f,l={};n.effects.save(r,s);r.show();u=t.distance||r["top"===e?"outerHeight":"outerWidth"](!0);n.effects.createWrapper(r).css({overflow:"hidden"});c&&r.css(e,o?isNaN(u)?"-"+u:-u:u);l[e]=(c?o?"+=":"-=":o?"-=":"+=")+u;r.animate(l,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){"hide"===h&&r.hide();n.effects.restore(r,s);n.effects.removeWrapper(r);i()}})};n.effects.effect.transfer=function(t,i){var u=n(this),r=n(t.to),f="fixed"===r.css("position"),e=n("body"),o=f?e.scrollTop():0,s=f?e.scrollLeft():0,h=r.offset(),l={top:h.top-o,left:h.left-s,height:r.innerHeight(),width:r.innerWidth()},c=u.offset(),a=n("<div class='ui-effects-transfer'><\/div>").appendTo(document.body).addClass(t.className).css({top:c.top-o,left:c.left-s,height:u.innerHeight(),width:u.innerWidth(),position:f?"fixed":"absolute"}).animate(l,t.duration,t.easing,function(){a.remove();i()})};n.widget("ui.progressbar",{version:"1.11.4",options:{max:100,value:0,change:null,complete:null},min:0,_create:function(){this.oldValue=this.options.value=this._constrainedValue();this.element.addClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").attr({role:"progressbar","aria-valuemin":this.min});this.valueDiv=n("<div class='ui-progressbar-value ui-widget-header ui-corner-left'><\/div>").appendTo(this.element);this._refreshValue()},_destroy:function(){this.element.removeClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").removeAttr("role").removeAttr("aria-valuemin").removeAttr("aria-valuemax").removeAttr("aria-valuenow");this.valueDiv.remove()},value:function(n){return void 0===n?this.options.value:(this.options.value=this._constrainedValue(n),this._refreshValue(),void 0)},_constrainedValue:function(n){return void 0===n&&(n=this.options.value),this.indeterminate=n===!1,"number"!=typeof n&&(n=0),this.indeterminate?!1:Math.min(this.options.max,Math.max(this.min,n))},_setOptions:function(n){var t=n.value;delete n.value;this._super(n);this.options.value=this._constrainedValue(t);this._refreshValue()},_setOption:function(n,t){"max"===n&&(t=Math.max(this.min,t));"disabled"===n&&this.element.toggleClass("ui-state-disabled",!!t).attr("aria-disabled",t);this._super(n,t)},_percentage:function(){return this.indeterminate?100:100*(this.options.value-this.min)/(this.options.max-this.min)},_refreshValue:function(){var t=this.options.value,i=this._percentage();this.valueDiv.toggle(this.indeterminate||t>this.min).toggleClass("ui-corner-right",t===this.options.max).width(i.toFixed(0)+"%");this.element.toggleClass("ui-progressbar-indeterminate",this.indeterminate);this.indeterminate?(this.element.removeAttr("aria-valuenow"),this.overlayDiv||(this.overlayDiv=n("<div class='ui-progressbar-overlay'><\/div>").appendTo(this.valueDiv))):(this.element.attr({"aria-valuemax":this.options.max,"aria-valuenow":t}),this.overlayDiv&&(this.overlayDiv.remove(),this.overlayDiv=null));this.oldValue!==t&&(this.oldValue=t,this._trigger("change"));t===this.options.max&&this._trigger("complete")}});n.widget("ui.selectable",n.ui.mouse,{version:"1.11.4",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var t,i=this;this.element.addClass("ui-selectable");this.dragged=!1;this.refresh=function(){t=n(i.options.filter,i.element[0]);t.addClass("ui-selectee");t.each(function(){var t=n(this),i=t.offset();n.data(this,"selectable-item",{element:this,$element:t,left:i.left,top:i.top,right:i.left+t.outerWidth(),bottom:i.top+t.outerHeight(),startselected:!1,selected:t.hasClass("ui-selected"),selecting:t.hasClass("ui-selecting"),unselecting:t.hasClass("ui-unselecting")})})};this.refresh();this.selectees=t.addClass("ui-selectee");this._mouseInit();this.helper=n("<div class='ui-selectable-helper'><\/div>")},_destroy:function(){this.selectees.removeClass("ui-selectee").removeData("selectable-item");this.element.removeClass("ui-selectable ui-selectable-disabled");this._mouseDestroy()},_mouseStart:function(t){var i=this,r=this.options;this.opos=[t.pageX,t.pageY];this.options.disabled||(this.selectees=n(r.filter,this.element[0]),this._trigger("start",t),n(r.appendTo).append(this.helper),this.helper.css({left:t.pageX,top:t.pageY,width:0,height:0}),r.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var r=n.data(this,"selectable-item");r.startselected=!0;t.metaKey||t.ctrlKey||(r.$element.removeClass("ui-selected"),r.selected=!1,r.$element.addClass("ui-unselecting"),r.unselecting=!0,i._trigger("unselecting",t,{unselecting:r.element}))}),n(t.target).parents().addBack().each(function(){var u,r=n.data(this,"selectable-item");if(r)return(u=!t.metaKey&&!t.ctrlKey||!r.$element.hasClass("ui-selected"),r.$element.removeClass(u?"ui-unselecting":"ui-selected").addClass(u?"ui-selecting":"ui-unselecting"),r.unselecting=!u,r.selecting=u,r.selected=u,u?i._trigger("selecting",t,{selecting:r.element}):i._trigger("unselecting",t,{unselecting:r.element}),!1)}))},_mouseDrag:function(t){if(this.dragged=!0,!this.options.disabled){var e,o=this,s=this.options,i=this.opos[0],r=this.opos[1],u=t.pageX,f=t.pageY;return i>u&&(e=u,u=i,i=e),r>f&&(e=f,f=r,r=e),this.helper.css({left:i,top:r,width:u-i,height:f-r}),this.selectees.each(function(){var e=n.data(this,"selectable-item"),h=!1;e&&e.element!==o.element[0]&&("touch"===s.tolerance?h=!(e.left>u||i>e.right||e.top>f||r>e.bottom):"fit"===s.tolerance&&(h=e.left>i&&u>e.right&&e.top>r&&f>e.bottom),h?(e.selected&&(e.$element.removeClass("ui-selected"),e.selected=!1),e.unselecting&&(e.$element.removeClass("ui-unselecting"),e.unselecting=!1),e.selecting||(e.$element.addClass("ui-selecting"),e.selecting=!0,o._trigger("selecting",t,{selecting:e.element}))):(e.selecting&&((t.metaKey||t.ctrlKey)&&e.startselected?(e.$element.removeClass("ui-selecting"),e.selecting=!1,e.$element.addClass("ui-selected"),e.selected=!0):(e.$element.removeClass("ui-selecting"),e.selecting=!1,e.startselected&&(e.$element.addClass("ui-unselecting"),e.unselecting=!0),o._trigger("unselecting",t,{unselecting:e.element}))),e.selected&&(t.metaKey||t.ctrlKey||e.startselected||(e.$element.removeClass("ui-selected"),e.selected=!1,e.$element.addClass("ui-unselecting"),e.unselecting=!0,o._trigger("unselecting",t,{unselecting:e.element})))))}),!1}},_mouseStop:function(t){var i=this;return this.dragged=!1,n(".ui-unselecting",this.element[0]).each(function(){var r=n.data(this,"selectable-item");r.$element.removeClass("ui-unselecting");r.unselecting=!1;r.startselected=!1;i._trigger("unselected",t,{unselected:r.element})}),n(".ui-selecting",this.element[0]).each(function(){var r=n.data(this,"selectable-item");r.$element.removeClass("ui-selecting").addClass("ui-selected");r.selecting=!1;r.selected=!0;r.startselected=!0;i._trigger("selected",t,{selected:r.element})}),this._trigger("stop",t),this.helper.remove(),!1}});n.widget("ui.selectmenu",{version:"1.11.4",defaultElement:"<select>",options:{appendTo:null,disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:null,change:null,close:null,focus:null,open:null,select:null},_create:function(){var n=this.element.uniqueId().attr("id");this.ids={element:n,button:n+"-button",menu:n+"-menu"};this._drawButton();this._drawMenu();this.options.disabled&&this.disable()},_drawButton:function(){var t=this;this.label=n("label[for='"+this.ids.element+"']").attr("for",this.ids.button);this._on(this.label,{click:function(n){this.button.focus();n.preventDefault()}});this.element.hide();this.button=n("<span>",{"class":"ui-selectmenu-button ui-widget ui-state-default ui-corner-all",tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true"}).insertAfter(this.element);n("<span>",{"class":"ui-icon "+this.options.icons.button}).prependTo(this.button);this.buttonText=n("<span>",{"class":"ui-selectmenu-text"}).appendTo(this.button);this._setText(this.buttonText,this.element.find("option:selected").text());this._resizeButton();this._on(this.button,this._buttonEvents);this.button.one("focusin",function(){t.menuItems||t._refreshMenu()});this._hoverable(this.button);this._focusable(this.button)},_drawMenu:function(){var t=this;this.menu=n("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu});this.menuWrap=n("<div>",{"class":"ui-selectmenu-menu ui-front"}).append(this.menu).appendTo(this._appendTo());this.menuInstance=this.menu.menu({role:"listbox",select:function(n,i){n.preventDefault();t._setSelection();t._select(i.item.data("ui-selectmenu-item"),n)},focus:function(n,i){var r=i.item.data("ui-selectmenu-item");null!=t.focusIndex&&r.index!==t.focusIndex&&(t._trigger("focus",n,{item:r}),t.isOpen||t._select(r,n));t.focusIndex=r.index;t.button.attr("aria-activedescendant",t.menuItems.eq(r.index).attr("id"))}}).menu("instance");this.menu.addClass("ui-corner-bottom").removeClass("ui-corner-all");this.menuInstance._off(this.menu,"mouseleave");this.menuInstance._closeOnDocumentClick=function(){return!1};this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu();this._setText(this.buttonText,this._getSelectedItem().text());this.options.width||this._resizeButton()},_refreshMenu:function(){this.menu.empty();var n,t=this.element.find("option");t.length&&(this._parseOptions(t),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup"),n=this._getSelectedItem(),this.menuInstance.focus(null,n),this._setAria(n.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(n){this.options.disabled||(this.menuItems?(this.menu.find(".ui-state-focus").removeClass("ui-state-focus"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",n))},_position:function(){this.menuWrap.position(n.extend({of:this.button},this.options.position))},close:function(n){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",n))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderMenu:function(t,i){var u=this,r="";n.each(i,function(i,f){f.optgroup!==r&&(n("<li>",{"class":"ui-selectmenu-optgroup ui-menu-divider"+(f.element.parent("optgroup").prop("disabled")?" ui-state-disabled":""),text:f.optgroup}).appendTo(t),r=f.optgroup);u._renderItemData(t,f)})},_renderItemData:function(n,t){return this._renderItem(n,t).data("ui-selectmenu-item",t)},_renderItem:function(t,i){var r=n("<li>");return i.disabled&&r.addClass("ui-state-disabled"),this._setText(r,i.label),r.appendTo(t)},_setText:function(n,t){t?n.text(t):n.html("&#160;")},_move:function(n,t){var i,r,u=".ui-menu-item";this.isOpen?i=this.menuItems.eq(this.focusIndex):(i=this.menuItems.eq(this.element[0].selectedIndex),u+=":not(.ui-state-disabled)");r="first"===n||"last"===n?i["first"===n?"prevAll":"nextAll"](u).eq(-1):i[n+"All"](u).eq(0);r.length&&this.menuInstance.focus(t,r)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex)},_toggle:function(n){this[this.isOpen?"close":"open"](n)},_setSelection:function(){var n;this.range&&(window.getSelection?(n=window.getSelection(),n.removeAllRanges(),n.addRange(this.range)):this.range.select(),this.button.focus())},_documentClick:{mousedown:function(t){this.isOpen&&(n(t.target).closest(".ui-selectmenu-menu, #"+this.ids.button).length||this.close(t))}},_buttonEvents:{mousedown:function(){var n;window.getSelection?(n=window.getSelection(),n.rangeCount&&(this.range=n.getRangeAt(0))):this.range=document.selection.createRange()},click:function(n){this._setSelection();this._toggle(n)},keydown:function(t){var i=!0;switch(t.keyCode){case n.ui.keyCode.TAB:case n.ui.keyCode.ESCAPE:this.close(t);i=!1;break;case n.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(t);break;case n.ui.keyCode.UP:t.altKey?this._toggle(t):this._move("prev",t);break;case n.ui.keyCode.DOWN:t.altKey?this._toggle(t):this._move("next",t);break;case n.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(t):this._toggle(t);break;case n.ui.keyCode.LEFT:this._move("prev",t);break;case n.ui.keyCode.RIGHT:this._move("next",t);break;case n.ui.keyCode.HOME:case n.ui.keyCode.PAGE_UP:this._move("first",t);break;case n.ui.keyCode.END:case n.ui.keyCode.PAGE_DOWN:this._move("last",t);break;default:this.menu.trigger(t);i=!1}i&&t.preventDefault()}},_selectFocusedItem:function(n){var t=this.menuItems.eq(this.focusIndex);t.hasClass("ui-state-disabled")||this._select(t.data("ui-selectmenu-item"),n)},_select:function(n,t){var i=this.element[0].selectedIndex;this.element[0].selectedIndex=n.index;this._setText(this.buttonText,n.label);this._setAria(n);this._trigger("select",t,{item:n});n.index!==i&&this._trigger("change",t,{item:n});this.close(t)},_setAria:function(n){var t=this.menuItems.eq(n.index).attr("id");this.button.attr({"aria-labelledby":t,"aria-activedescendant":t});this.menu.attr("aria-activedescendant",t)},_setOption:function(n,t){"icons"===n&&this.button.find("span.ui-icon").removeClass(this.options.icons.button).addClass(t.button);this._super(n,t);"appendTo"===n&&this.menuWrap.appendTo(this._appendTo());"disabled"===n&&(this.menuInstance.option("disabled",t),this.button.toggleClass("ui-state-disabled",t).attr("aria-disabled",t),this.element.prop("disabled",t),t?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0));"width"===n&&this._resizeButton()},_appendTo:function(){var t=this.options.appendTo;return t&&(t=t.jquery||t.nodeType?n(t):this.document.find(t).eq(0)),t&&t[0]||(t=this.element.closest(".ui-front")),t.length||(t=this.document[0].body),t},_toggleAttr:function(){this.button.toggleClass("ui-corner-top",this.isOpen).toggleClass("ui-corner-all",!this.isOpen).attr("aria-expanded",this.isOpen);this.menuWrap.toggleClass("ui-selectmenu-open",this.isOpen);this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var n=this.options.width;n||(n=this.element.show().outerWidth(),this.element.hide());this.button.outerWidth(n)},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){return{disabled:this.element.prop("disabled")}},_parseOptions:function(t){var i=[];t.each(function(t,r){var u=n(r),f=u.parent("optgroup");i.push({element:u,index:t,value:u.val(),label:u.text(),optgroup:f.attr("label")||"",disabled:f.prop("disabled")||u.prop("disabled")})});this.items=i},_destroy:function(){this.menuWrap.remove();this.button.remove();this.element.show();this.element.removeUniqueId();this.label.attr("for",this.ids.element)}});n.widget("ui.slider",n.ui.mouse,{version:"1.11.4",widgetEventPrefix:"slide",options:{animate:!1,distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1;this._mouseSliding=!1;this._animateOff=!0;this._handleIndex=null;this._detectOrientation();this._mouseInit();this._calculateNewMax();this.element.addClass("ui-slider ui-slider-"+this.orientation+" ui-widget ui-widget-content ui-corner-all");this._refresh();this._setOption("disabled",this.options.disabled);this._animateOff=!1},_refresh:function(){this._createRange();this._createHandles();this._setupEvents();this._refreshValue()},_createHandles:function(){var r,i,u=this.options,t=this.element.find(".ui-slider-handle").addClass("ui-state-default ui-corner-all"),f=[];for(i=u.values&&u.values.length||1,t.length>i&&(t.slice(i).remove(),t=t.slice(0,i)),r=t.length;i>r;r++)f.push("<span class='ui-slider-handle ui-state-default ui-corner-all' tabindex='0'><\/span>");this.handles=t.add(n(f.join("")).appendTo(this.element));this.handle=this.handles.eq(0);this.handles.each(function(t){n(this).data("ui-slider-handle-index",t)})},_createRange:function(){var t=this.options,i="";t.range?(t.range===!0&&(t.values?t.values.length&&2!==t.values.length?t.values=[t.values[0],t.values[0]]:n.isArray(t.values)&&(t.values=t.values.slice(0)):t.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?this.range.removeClass("ui-slider-range-min ui-slider-range-max").css({left:"",bottom:""}):(this.range=n("<div><\/div>").appendTo(this.element),i="ui-slider-range ui-widget-header ui-corner-all"),this.range.addClass(i+("min"===t.range||"max"===t.range?" ui-slider-range-"+t.range:""))):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles);this._on(this.handles,this._handleEvents);this._hoverable(this.handles);this._focusable(this.handles)},_destroy:function(){this.handles.remove();this.range&&this.range.remove();this.element.removeClass("ui-slider ui-slider-horizontal ui-slider-vertical ui-widget ui-widget-content ui-corner-all");this._mouseDestroy()},_mouseCapture:function(t){var s,f,r,i,u,h,e,c,o=this,l=this.options;return l.disabled?!1:(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),s={x:t.pageX,y:t.pageY},f=this._normValueFromMouse(s),r=this._valueMax()-this._valueMin()+1,this.handles.each(function(t){var e=Math.abs(f-o.values(t));(r>e||r===e&&(t===o._lastChangedValue||o.values(t)===l.min))&&(r=e,i=n(this),u=t)}),h=this._start(t,u),h===!1?!1:(this._mouseSliding=!0,this._handleIndex=u,i.addClass("ui-state-active").focus(),e=i.offset(),c=!n(t.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=c?{left:0,top:0}:{left:t.pageX-e.left-i.width()/2,top:t.pageY-e.top-i.height()/2-(parseInt(i.css("borderTopWidth"),10)||0)-(parseInt(i.css("borderBottomWidth"),10)||0)+(parseInt(i.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(t,u,f),this._animateOff=!0,!0))},_mouseStart:function(){return!0},_mouseDrag:function(n){var t={x:n.pageX,y:n.pageY},i=this._normValueFromMouse(t);return this._slide(n,this._handleIndex,i),!1},_mouseStop:function(n){return this.handles.removeClass("ui-state-active"),this._mouseSliding=!1,this._stop(n,this._handleIndex),this._change(n,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1,!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(n){var i,r,t,u,f;return"horizontal"===this.orientation?(i=this.elementSize.width,r=n.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(i=this.elementSize.height,r=n.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)),t=r/i,t>1&&(t=1),0>t&&(t=0),"vertical"===this.orientation&&(t=1-t),u=this._valueMax()-this._valueMin(),f=this._valueMin()+t*u,this._trimAlignValue(f)},_start:function(n,t){var i={handle:this.handles[t],value:this.value()};return this.options.values&&this.options.values.length&&(i.value=this.values(t),i.values=this.values()),this._trigger("start",n,i)},_slide:function(n,t,i){var r,f,u;this.options.values&&this.options.values.length?(r=this.values(t?0:1),2===this.options.values.length&&this.options.range===!0&&(0===t&&i>r||1===t&&r>i)&&(i=r),i!==this.values(t)&&(f=this.values(),f[t]=i,u=this._trigger("slide",n,{handle:this.handles[t],value:i,values:f}),r=this.values(t?0:1),u!==!1&&this.values(t,i))):i!==this.value()&&(u=this._trigger("slide",n,{handle:this.handles[t],value:i}),u!==!1&&this.value(i))},_stop:function(n,t){var i={handle:this.handles[t],value:this.value()};this.options.values&&this.options.values.length&&(i.value=this.values(t),i.values=this.values());this._trigger("stop",n,i)},_change:function(n,t){if(!this._keySliding&&!this._mouseSliding){var i={handle:this.handles[t],value:this.value()};this.options.values&&this.options.values.length&&(i.value=this.values(t),i.values=this.values());this._lastChangedValue=t;this._trigger("change",n,i)}},value:function(n){return arguments.length?(this.options.value=this._trimAlignValue(n),this._refreshValue(),this._change(null,0),void 0):this._value()},values:function(t,i){var u,f,r;if(arguments.length>1)return this.options.values[t]=this._trimAlignValue(i),this._refreshValue(),this._change(null,t),void 0;if(!arguments.length)return this._values();if(!n.isArray(arguments[0]))return this.options.values&&this.options.values.length?this._values(t):this.value();for(u=this.options.values,f=arguments[0],r=0;u.length>r;r+=1)u[r]=this._trimAlignValue(f[r]),this._change(null,r);this._refreshValue()},_setOption:function(t,i){var r,u=0;switch("range"===t&&this.options.range===!0&&("min"===i?(this.options.value=this._values(0),this.options.values=null):"max"===i&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),n.isArray(this.options.values)&&(u=this.options.values.length),"disabled"===t&&this.element.toggleClass("ui-state-disabled",!!i),this._super(t,i),t){case"orientation":this._detectOrientation();this.element.removeClass("ui-slider-horizontal ui-slider-vertical").addClass("ui-slider-"+this.orientation);this._refreshValue();this.handles.css("horizontal"===i?"bottom":"left","");break;case"value":this._animateOff=!0;this._refreshValue();this._change(null,0);this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),r=0;u>r;r+=1)this._change(null,r);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0;this._calculateNewMax();this._refreshValue();this._animateOff=!1;break;case"range":this._animateOff=!0;this._refresh();this._animateOff=!1}},_value:function(){var n=this.options.value;return this._trimAlignValue(n)},_values:function(n){var r,t,i;if(arguments.length)return r=this.options.values[n],r=this._trimAlignValue(r);if(this.options.values&&this.options.values.length){for(t=this.options.values.slice(),i=0;t.length>i;i+=1)t[i]=this._trimAlignValue(t[i]);return t}return[]},_trimAlignValue:function(n){if(this._valueMin()>=n)return this._valueMin();if(n>=this._valueMax())return this._valueMax();var t=this.options.step>0?this.options.step:1,i=(n-this._valueMin())%t,r=n-i;return 2*Math.abs(i)>=t&&(r+=i>0?t:-t),parseFloat(r.toFixed(5))},_calculateNewMax:function(){var n=this.options.max,t=this._valueMin(),i=this.options.step,r=Math.floor(+(n-t).toFixed(this._precision())/i)*i;n=r+t;this.max=parseFloat(n.toFixed(this._precision()))},_precision:function(){var n=this._precisionOf(this.options.step);return null!==this.options.min&&(n=Math.max(n,this._precisionOf(this.options.min))),n},_precisionOf:function(n){var t=""+n,i=t.indexOf(".");return-1===i?0:t.length-i-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshValue:function(){var s,t,c,f,h,e=this.options.range,i=this.options,r=this,u=this._animateOff?!1:i.animate,o={};this.options.values&&this.options.values.length?this.handles.each(function(f){t=100*((r.values(f)-r._valueMin())/(r._valueMax()-r._valueMin()));o["horizontal"===r.orientation?"left":"bottom"]=t+"%";n(this).stop(1,1)[u?"animate":"css"](o,i.animate);r.options.range===!0&&("horizontal"===r.orientation?(0===f&&r.range.stop(1,1)[u?"animate":"css"]({left:t+"%"},i.animate),1===f&&r.range[u?"animate":"css"]({width:t-s+"%"},{queue:!1,duration:i.animate})):(0===f&&r.range.stop(1,1)[u?"animate":"css"]({bottom:t+"%"},i.animate),1===f&&r.range[u?"animate":"css"]({height:t-s+"%"},{queue:!1,duration:i.animate})));s=t}):(c=this.value(),f=this._valueMin(),h=this._valueMax(),t=h!==f?100*((c-f)/(h-f)):0,o["horizontal"===this.orientation?"left":"bottom"]=t+"%",this.handle.stop(1,1)[u?"animate":"css"](o,i.animate),"min"===e&&"horizontal"===this.orientation&&this.range.stop(1,1)[u?"animate":"css"]({width:t+"%"},i.animate),"max"===e&&"horizontal"===this.orientation&&this.range[u?"animate":"css"]({width:100-t+"%"},{queue:!1,duration:i.animate}),"min"===e&&"vertical"===this.orientation&&this.range.stop(1,1)[u?"animate":"css"]({height:t+"%"},i.animate),"max"===e&&"vertical"===this.orientation&&this.range[u?"animate":"css"]({height:100-t+"%"},{queue:!1,duration:i.animate}))},_handleEvents:{keydown:function(t){var e,r,i,u,f=n(t.target).data("ui-slider-handle-index");switch(t.keyCode){case n.ui.keyCode.HOME:case n.ui.keyCode.END:case n.ui.keyCode.PAGE_UP:case n.ui.keyCode.PAGE_DOWN:case n.ui.keyCode.UP:case n.ui.keyCode.RIGHT:case n.ui.keyCode.DOWN:case n.ui.keyCode.LEFT:if(t.preventDefault(),!this._keySliding&&(this._keySliding=!0,n(t.target).addClass("ui-state-active"),e=this._start(t,f),e===!1))return}switch(u=this.options.step,r=i=this.options.values&&this.options.values.length?this.values(f):this.value(),t.keyCode){case n.ui.keyCode.HOME:i=this._valueMin();break;case n.ui.keyCode.END:i=this._valueMax();break;case n.ui.keyCode.PAGE_UP:i=this._trimAlignValue(r+(this._valueMax()-this._valueMin())/this.numPages);break;case n.ui.keyCode.PAGE_DOWN:i=this._trimAlignValue(r-(this._valueMax()-this._valueMin())/this.numPages);break;case n.ui.keyCode.UP:case n.ui.keyCode.RIGHT:if(r===this._valueMax())return;i=this._trimAlignValue(r+u);break;case n.ui.keyCode.DOWN:case n.ui.keyCode.LEFT:if(r===this._valueMin())return;i=this._trimAlignValue(r-u)}this._slide(t,f,i)},keyup:function(t){var i=n(t.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(t,i),this._change(t,i),n(t.target).removeClass("ui-state-active"))}}});n.widget("ui.sortable",n.ui.mouse,{version:"1.11.4",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_isOverAxis:function(n,t,i){return n>=t&&t+i>n},_isFloating:function(n){return/left|right/.test(n.css("float"))||/inline|table-cell/.test(n.css("display"))},_create:function(){this.containerCache={};this.element.addClass("ui-sortable");this.refresh();this.offset=this.element.offset();this._mouseInit();this._setHandleClassName();this.ready=!0},_setOption:function(n,t){this._super(n,t);"handle"===n&&this._setHandleClassName()},_setHandleClassName:function(){this.element.find(".ui-sortable-handle").removeClass("ui-sortable-handle");n.each(this.items,function(){(this.instance.options.handle?this.item.find(this.instance.options.handle):this.item).addClass("ui-sortable-handle")})},_destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled").find(".ui-sortable-handle").removeClass("ui-sortable-handle");this._mouseDestroy();for(var n=this.items.length-1;n>=0;n--)this.items[n].item.removeData(this.widgetName+"-item");return this},_mouseCapture:function(t,i){var r=null,f=!1,u=this;return this.reverting?!1:this.options.disabled||"static"===this.options.type?!1:(this._refreshItems(t),n(t.target).parents().each(function(){if(n.data(this,u.widgetName+"-item")===u)return(r=n(this),!1)}),n.data(t.target,u.widgetName+"-item")===u&&(r=n(t.target)),r?!this.options.handle||i||(n(this.options.handle,r).find("*").addBack().each(function(){this===t.target&&(f=!0)}),f)?(this.currentItem=r,this._removeCurrentsFromItems(),!0):!1:!1)},_mouseStart:function(t,i,r){var f,e,u=this.options;if(this.currentContainer=this,this.refreshPositions(),this.helper=this._createHelper(t),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},n.extend(this.offset,{click:{left:t.pageX-this.offset.left,top:t.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),this.originalPosition=this._generatePosition(t),this.originalPageX=t.pageX,this.originalPageY=t.pageY,u.cursorAt&&this._adjustOffsetFromHelper(u.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),u.containment&&this._setContainment(),u.cursor&&"auto"!==u.cursor&&(e=this.document.find("body"),this.storedCursor=e.css("cursor"),e.css("cursor",u.cursor),this.storedStylesheet=n("<style>*{ cursor: "+u.cursor+" !important; }<\/style>").appendTo(e)),u.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",u.opacity)),u.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",u.zIndex)),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",t,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!r)for(f=this.containers.length-1;f>=0;f--)this.containers[f]._trigger("activate",t,this._uiHash(this));return n.ui.ddmanager&&(n.ui.ddmanager.current=this),n.ui.ddmanager&&!u.dropBehaviour&&n.ui.ddmanager.prepareOffsets(this,t),this.dragging=!0,this.helper.addClass("ui-sortable-helper"),this._mouseDrag(t),!0},_mouseDrag:function(t){var e,u,f,o,i=this.options,r=!1;for(this.position=this._generatePosition(t),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs),this.options.scroll&&(this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-t.pageY<i.scrollSensitivity?this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop+i.scrollSpeed:t.pageY-this.overflowOffset.top<i.scrollSensitivity&&(this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop-i.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-t.pageX<i.scrollSensitivity?this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft+i.scrollSpeed:t.pageX-this.overflowOffset.left<i.scrollSensitivity&&(this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft-i.scrollSpeed)):(t.pageY-this.document.scrollTop()<i.scrollSensitivity?r=this.document.scrollTop(this.document.scrollTop()-i.scrollSpeed):this.window.height()-(t.pageY-this.document.scrollTop())<i.scrollSensitivity&&(r=this.document.scrollTop(this.document.scrollTop()+i.scrollSpeed)),t.pageX-this.document.scrollLeft()<i.scrollSensitivity?r=this.document.scrollLeft(this.document.scrollLeft()-i.scrollSpeed):this.window.width()-(t.pageX-this.document.scrollLeft())<i.scrollSensitivity&&(r=this.document.scrollLeft(this.document.scrollLeft()+i.scrollSpeed))),r!==!1&&n.ui.ddmanager&&!i.dropBehaviour&&n.ui.ddmanager.prepareOffsets(this,t)),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),e=this.items.length-1;e>=0;e--)if(u=this.items[e],f=u.item[0],o=this._intersectsWithPointer(u),o&&u.instance===this.currentContainer&&f!==this.currentItem[0]&&this.placeholder[1===o?"next":"prev"]()[0]!==f&&!n.contains(this.placeholder[0],f)&&("semi-dynamic"===this.options.type?!n.contains(this.element[0],f):!0)){if(this.direction=1===o?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(u))break;this._rearrange(t,u);this._trigger("change",t,this._uiHash());break}return this._contactContainers(t),n.ui.ddmanager&&n.ui.ddmanager.drag(this,t),this._trigger("sort",t,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(t,i){if(t){if(n.ui.ddmanager&&!this.options.dropBehaviour&&n.ui.ddmanager.drop(this,t),this.options.revert){var e=this,f=this.placeholder.offset(),r=this.options.axis,u={};r&&"x"!==r||(u.left=f.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollLeft));r&&"y"!==r||(u.top=f.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollTop));this.reverting=!0;n(this.helper).animate(u,parseInt(this.options.revert,10)||500,function(){e._clear(t)})}else this._clear(t,i);return!1}},cancel:function(){if(this.dragging){this._mouseUp({target:null});"original"===this.options.helper?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):this.currentItem.show();for(var t=this.containers.length-1;t>=0;t--)this.containers[t]._trigger("deactivate",null,this._uiHash(this)),this.containers[t].containerCache.over&&(this.containers[t]._trigger("out",null,this._uiHash(this)),this.containers[t].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),n.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?n(this.domPosition.prev).after(this.currentItem):n(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(t){var r=this._getItemsAsjQuery(t&&t.connected),i=[];return t=t||{},n(r).each(function(){var r=(n(t.item||this).attr(t.attribute||"id")||"").match(t.expression||/(.+)[\-=_](.+)/);r&&i.push((t.key||r[1]+"[]")+"="+(t.key&&t.expression?r[1]:r[2]))}),!i.length&&t.key&&i.push(t.key+"="),i.join("&")},toArray:function(t){var r=this._getItemsAsjQuery(t&&t.connected),i=[];return t=t||{},r.each(function(){i.push(n(t.item||this).attr(t.attribute||"id")||"")}),i},_intersectsWith:function(n){var t=this.positionAbs.left,h=t+this.helperProportions.width,i=this.positionAbs.top,c=i+this.helperProportions.height,r=n.left,f=r+n.width,u=n.top,e=u+n.height,o=this.offset.click.top,s=this.offset.click.left,l="x"===this.options.axis||i+o>u&&e>i+o,a="y"===this.options.axis||t+s>r&&f>t+s,v=l&&a;return"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>n[this.floating?"width":"height"]?v:t+this.helperProportions.width/2>r&&f>h-this.helperProportions.width/2&&i+this.helperProportions.height/2>u&&e>c-this.helperProportions.height/2},_intersectsWithPointer:function(n){var r="x"===this.options.axis||this._isOverAxis(this.positionAbs.top+this.offset.click.top,n.top,n.height),u="y"===this.options.axis||this._isOverAxis(this.positionAbs.left+this.offset.click.left,n.left,n.width),f=r&&u,t=this._getDragVerticalDirection(),i=this._getDragHorizontalDirection();return f?this.floating?i&&"right"===i||"down"===t?2:1:t&&("down"===t?2:1):!1},_intersectsWithSides:function(n){var r=this._isOverAxis(this.positionAbs.top+this.offset.click.top,n.top+n.height/2,n.height),u=this._isOverAxis(this.positionAbs.left+this.offset.click.left,n.left+n.width/2,n.width),t=this._getDragVerticalDirection(),i=this._getDragHorizontalDirection();return this.floating&&i?"right"===i&&u||"left"===i&&!u:t&&("down"===t&&r||"up"===t&&!r)},_getDragVerticalDirection:function(){var n=this.positionAbs.top-this.lastPositionAbs.top;return 0!==n&&(n>0?"down":"up")},_getDragHorizontalDirection:function(){var n=this.positionAbs.left-this.lastPositionAbs.left;return 0!==n&&(n>0?"right":"left")},refresh:function(n){return this._refreshItems(n),this._setHandleClassName(),this.refreshPositions(),this},_connectWith:function(){var n=this.options;return n.connectWith.constructor===String?[n.connectWith]:n.connectWith},_getItemsAsjQuery:function(t){function h(){s.push(this)}var r,u,e,i,s=[],f=[],o=this._connectWith();if(o&&t)for(r=o.length-1;r>=0;r--)for(e=n(o[r],this.document[0]),u=e.length-1;u>=0;u--)i=n.data(e[u],this.widgetFullName),i&&i!==this&&!i.options.disabled&&f.push([n.isFunction(i.options.items)?i.options.items.call(i.element):n(i.options.items,i.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),i]);for(f.push([n.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):n(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),r=f.length-1;r>=0;r--)f[r][0].each(h);return n(s)},_removeCurrentsFromItems:function(){var t=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=n.grep(this.items,function(n){for(var i=0;t.length>i;i++)if(t[i]===n.item[0])return!1;return!0})},_refreshItems:function(t){this.items=[];this.containers=[this];var r,u,e,i,o,s,h,l,a=this.items,f=[[n.isFunction(this.options.items)?this.options.items.call(this.element[0],t,{item:this.currentItem}):n(this.options.items,this.element),this]],c=this._connectWith();if(c&&this.ready)for(r=c.length-1;r>=0;r--)for(e=n(c[r],this.document[0]),u=e.length-1;u>=0;u--)i=n.data(e[u],this.widgetFullName),i&&i!==this&&!i.options.disabled&&(f.push([n.isFunction(i.options.items)?i.options.items.call(i.element[0],t,{item:this.currentItem}):n(i.options.items,i.element),i]),this.containers.push(i));for(r=f.length-1;r>=0;r--)for(o=f[r][1],s=f[r][0],u=0,l=s.length;l>u;u++)h=n(s[u]),h.data(this.widgetName+"-item",o),a.push({item:h,instance:o,width:0,height:0,left:0,top:0})},refreshPositions:function(t){this.floating=this.items.length?"x"===this.options.axis||this._isFloating(this.items[0].item):!1;this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset());for(var r,f,u,i=this.items.length-1;i>=0;i--)r=this.items[i],r.instance!==this.currentContainer&&this.currentContainer&&r.item[0]!==this.currentItem[0]||(f=this.options.toleranceElement?n(this.options.toleranceElement,r.item):r.item,t||(r.width=f.outerWidth(),r.height=f.outerHeight()),u=f.offset(),r.left=u.left,r.top=u.top);if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(i=this.containers.length-1;i>=0;i--)u=this.containers[i].element.offset(),this.containers[i].containerCache.left=u.left,this.containers[i].containerCache.top=u.top,this.containers[i].containerCache.width=this.containers[i].element.outerWidth(),this.containers[i].containerCache.height=this.containers[i].element.outerHeight();return this},_createPlaceholder:function(t){t=t||this;var r,i=t.options;i.placeholder&&i.placeholder.constructor!==String||(r=i.placeholder,i.placeholder={element:function(){var u=t.currentItem[0].nodeName.toLowerCase(),i=n("<"+u+">",t.document[0]).addClass(r||t.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper");return"tbody"===u?t._createTrPlaceholder(t.currentItem.find("tr").eq(0),n("<tr>",t.document[0]).appendTo(i)):"tr"===u?t._createTrPlaceholder(t.currentItem,i):"img"===u&&i.attr("src",t.currentItem.attr("src")),r||i.css("visibility","hidden"),i},update:function(n,u){(!r||i.forcePlaceholderSize)&&(u.height()||u.height(t.currentItem.innerHeight()-parseInt(t.currentItem.css("paddingTop")||0,10)-parseInt(t.currentItem.css("paddingBottom")||0,10)),u.width()||u.width(t.currentItem.innerWidth()-parseInt(t.currentItem.css("paddingLeft")||0,10)-parseInt(t.currentItem.css("paddingRight")||0,10)))}});t.placeholder=n(i.placeholder.element.call(t.element,t.currentItem));t.currentItem.after(t.placeholder);i.placeholder.update(t,t.placeholder)},_createTrPlaceholder:function(t,i){var r=this;t.children().each(function(){n("<td>&#160;<\/td>",r.document[0]).attr("colspan",n(this).attr("colspan")||1).appendTo(i)})},_contactContainers:function(t){for(var u,c,f,a,v,o,l,s,h,e=null,i=null,r=this.containers.length-1;r>=0;r--)if(!n.contains(this.currentItem[0],this.containers[r].element[0]))if(this._intersectsWith(this.containers[r].containerCache)){if(e&&n.contains(this.containers[r].element[0],e.element[0]))continue;e=this.containers[r];i=r}else this.containers[r].containerCache.over&&(this.containers[r]._trigger("out",t,this._uiHash(this)),this.containers[r].containerCache.over=0);if(e)if(1===this.containers.length)this.containers[i].containerCache.over||(this.containers[i]._trigger("over",t,this._uiHash(this)),this.containers[i].containerCache.over=1);else{for(c=1e4,f=null,s=e.floating||this._isFloating(this.currentItem),a=s?"left":"top",v=s?"width":"height",h=s?"clientX":"clientY",u=this.items.length-1;u>=0;u--)n.contains(this.containers[i].element[0],this.items[u].item[0])&&this.items[u].item[0]!==this.currentItem[0]&&(o=this.items[u].item.offset()[a],l=!1,t[h]-o>this.items[u][v]/2&&(l=!0),c>Math.abs(t[h]-o)&&(c=Math.abs(t[h]-o),f=this.items[u],this.direction=l?"up":"down"));if(!f&&!this.options.dropOnEmpty)return;if(this.currentContainer===this.containers[i])return this.currentContainer.containerCache.over||(this.containers[i]._trigger("over",t,this._uiHash()),this.currentContainer.containerCache.over=1),void 0;f?this._rearrange(t,f,null,!0):this._rearrange(t,null,this.containers[i].element,!0);this._trigger("change",t,this._uiHash());this.containers[i]._trigger("change",t,this._uiHash(this));this.currentContainer=this.containers[i];this.options.placeholder.update(this.currentContainer,this.placeholder);this.containers[i]._trigger("over",t,this._uiHash(this));this.containers[i].containerCache.over=1}},_createHelper:function(t){var r=this.options,i=n.isFunction(r.helper)?n(r.helper.apply(this.element[0],[t,this.currentItem])):"clone"===r.helper?this.currentItem.clone():this.currentItem;return i.parents("body").length||n("parent"!==r.appendTo?r.appendTo:this.currentItem[0].parentNode)[0].appendChild(i[0]),i[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),(!i[0].style.width||r.forceHelperSize)&&i.width(this.currentItem.width()),(!i[0].style.height||r.forceHelperSize)&&i.height(this.currentItem.height()),i},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" "));n.isArray(t)&&(t={left:+t[0],top:+t[1]||0});"left"in t&&(this.offset.click.left=t.left+this.margins.left);"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left);"top"in t&&(this.offset.click.top=t.top+this.margins.top);"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var t=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==this.document[0]&&n.contains(this.scrollParent[0],this.offsetParent[0])&&(t.left+=this.scrollParent.scrollLeft(),t.top+=this.scrollParent.scrollTop()),(this.offsetParent[0]===this.document[0].body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&n.ui.ie)&&(t={top:0,left:0}),{top:t.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:t.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"===this.cssPosition){var n=this.currentItem.position();return{top:n.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:n.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var t,r,u,i=this.options;"parent"===i.containment&&(i.containment=this.helper[0].parentNode);("document"===i.containment||"window"===i.containment)&&(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,"document"===i.containment?this.document.width():this.window.width()-this.helperProportions.width-this.margins.left,("document"===i.containment?this.document.width():this.window.height()||this.document[0].body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]);/^(document|window|parent)$/.test(i.containment)||(t=n(i.containment)[0],r=n(i.containment).offset(),u="hidden"!==n(t).css("overflow"),this.containment=[r.left+(parseInt(n(t).css("borderLeftWidth"),10)||0)+(parseInt(n(t).css("paddingLeft"),10)||0)-this.margins.left,r.top+(parseInt(n(t).css("borderTopWidth"),10)||0)+(parseInt(n(t).css("paddingTop"),10)||0)-this.margins.top,r.left+(u?Math.max(t.scrollWidth,t.offsetWidth):t.offsetWidth)-(parseInt(n(t).css("borderLeftWidth"),10)||0)-(parseInt(n(t).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,r.top+(u?Math.max(t.scrollHeight,t.offsetHeight):t.offsetHeight)-(parseInt(n(t).css("borderTopWidth"),10)||0)-(parseInt(n(t).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(t,i){i||(i=this.position);var r="absolute"===t?1:-1,u="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&n.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,f=/(html|body)/i.test(u[0].tagName);return{top:i.top+this.offset.relative.top*r+this.offset.parent.top*r-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():f?0:u.scrollTop())*r,left:i.left+this.offset.relative.left*r+this.offset.parent.left*r-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():f?0:u.scrollLeft())*r}},_generatePosition:function(t){var r,u,i=this.options,f=t.pageX,e=t.pageY,o="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&n.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,s=/(html|body)/i.test(o[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(t.pageX-this.offset.click.left<this.containment[0]&&(f=this.containment[0]+this.offset.click.left),t.pageY-this.offset.click.top<this.containment[1]&&(e=this.containment[1]+this.offset.click.top),t.pageX-this.offset.click.left>this.containment[2]&&(f=this.containment[2]+this.offset.click.left),t.pageY-this.offset.click.top>this.containment[3]&&(e=this.containment[3]+this.offset.click.top)),i.grid&&(r=this.originalPageY+Math.round((e-this.originalPageY)/i.grid[1])*i.grid[1],e=this.containment?r-this.offset.click.top>=this.containment[1]&&r-this.offset.click.top<=this.containment[3]?r:r-this.offset.click.top>=this.containment[1]?r-i.grid[1]:r+i.grid[1]:r,u=this.originalPageX+Math.round((f-this.originalPageX)/i.grid[0])*i.grid[0],f=this.containment?u-this.offset.click.left>=this.containment[0]&&u-this.offset.click.left<=this.containment[2]?u:u-this.offset.click.left>=this.containment[0]?u-i.grid[0]:u+i.grid[0]:u)),{top:e-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():s?0:o.scrollTop()),left:f-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():s?0:o.scrollLeft())}},_rearrange:function(n,t,i,r){i?i[0].appendChild(this.placeholder[0]):t.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?t.item[0]:t.item[0].nextSibling);this.counter=this.counter?++this.counter:1;var u=this.counter;this._delay(function(){u===this.counter&&this.refreshPositions(!r)})},_clear:function(n,t){function u(n,t,i){return function(r){i._trigger(n,r,t._uiHash(t))}}this.reverting=!1;var i,r=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(i in this._storedCSS)("auto"===this._storedCSS[i]||"static"===this._storedCSS[i])&&(this._storedCSS[i]="");this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else this.currentItem.show();for(this.fromOutside&&!t&&r.push(function(n){this._trigger("receive",n,this._uiHash(this.fromOutside))}),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||t||r.push(function(n){this._trigger("update",n,this._uiHash())}),this!==this.currentContainer&&(t||(r.push(function(n){this._trigger("remove",n,this._uiHash())}),r.push(function(n){return function(t){n._trigger("receive",t,this._uiHash(this))}}.call(this,this.currentContainer)),r.push(function(n){return function(t){n._trigger("update",t,this._uiHash(this))}}.call(this,this.currentContainer)))),i=this.containers.length-1;i>=0;i--)t||r.push(u("deactivate",this,this.containers[i])),this.containers[i].containerCache.over&&(r.push(u("out",this,this.containers[i])),this.containers[i].containerCache.over=0);if(this.storedCursor&&(this.document.find("body").css("cursor",this.storedCursor),this.storedStylesheet.remove()),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,t||this._trigger("beforeStop",n,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.cancelHelperRemoval||(this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null),!t){for(i=0;r.length>i;i++)r[i].call(this,n);this._trigger("stop",n,this._uiHash())}return this.fromOutside=!1,!this.cancelHelperRemoval},_trigger:function(){n.Widget.prototype._trigger.apply(this,arguments)===!1&&this.cancel()},_uiHash:function(t){var i=t||this;return{helper:i.helper,placeholder:i.placeholder||n([]),position:i.position,originalPosition:i.originalPosition,offset:i.positionAbs,item:i.currentItem,sender:t?t.element:null}}});n.widget("ui.spinner",{version:"1.11.4",defaultElement:"<input>",widgetEventPrefix:"spin",options:{culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max);this._setOption("min",this.options.min);this._setOption("step",this.options.step);""!==this.value()&&this._value(this.element.val(),!0);this._draw();this._on(this._events);this._refresh();this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var t={},i=this.element;return n.each(["min","max","step"],function(n,r){var u=i.attr(r);void 0!==u&&u.length&&(t[r]=u)}),t},_events:{keydown:function(n){this._start(n)&&this._keydown(n)&&n.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(n){return this.cancelBlur?(delete this.cancelBlur,void 0):(this._stop(),this._refresh(),this.previous!==this.element.val()&&this._trigger("change",n),void 0)},mousewheel:function(n,t){if(t){if(!this.spinning&&!this._start(n))return!1;this._spin((t>0?1:-1)*this.options.step,n);clearTimeout(this.mousewheelTimer);this.mousewheelTimer=this._delay(function(){this.spinning&&this._stop(n)},100);n.preventDefault()}},"mousedown .ui-spinner-button":function(t){function r(){var n=this.element[0]===this.document[0].activeElement;n||(this.element.focus(),this.previous=i,this._delay(function(){this.previous=i}))}var i;i=this.element[0]===this.document[0].activeElement?this.previous:this.element.val();t.preventDefault();r.call(this);this.cancelBlur=!0;this._delay(function(){delete this.cancelBlur;r.call(this)});this._start(t)!==!1&&this._repeat(null,n(t.currentTarget).hasClass("ui-spinner-up")?1:-1,t)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(t){if(n(t.currentTarget).hasClass("ui-state-active"))return this._start(t)===!1?!1:(this._repeat(null,n(t.currentTarget).hasClass("ui-spinner-up")?1:-1,t),void 0)},"mouseleave .ui-spinner-button":"_stop"},_draw:function(){var n=this.uiSpinner=this.element.addClass("ui-spinner-input").attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml());this.element.attr("role","spinbutton");this.buttons=n.find(".ui-spinner-button").attr("tabIndex",-1).button().removeClass("ui-corner-all");this.buttons.height()>Math.ceil(.5*n.height())&&n.height()>0&&n.height(n.height());this.options.disabled&&this.disable()},_keydown:function(t){var r=this.options,i=n.ui.keyCode;switch(t.keyCode){case i.UP:return this._repeat(null,1,t),!0;case i.DOWN:return this._repeat(null,-1,t),!0;case i.PAGE_UP:return this._repeat(null,r.page,t),!0;case i.PAGE_DOWN:return this._repeat(null,-r.page,t),!0}return!1},_uiSpinnerHtml:function(){return"<span class='ui-spinner ui-widget ui-widget-content ui-corner-all'><\/span>"},_buttonHtml:function(){return"<a class='ui-spinner-button ui-spinner-up ui-corner-tr'><span class='ui-icon "+this.options.icons.up+"'>&#9650;<\/span><\/a><a class='ui-spinner-button ui-spinner-down ui-corner-br'><span class='ui-icon "+this.options.icons.down+"'>&#9660;<\/span><\/a>"},_start:function(n){return this.spinning||this._trigger("start",n)!==!1?(this.counter||(this.counter=1),this.spinning=!0,!0):!1},_repeat:function(n,t,i){n=n||500;clearTimeout(this.timer);this.timer=this._delay(function(){this._repeat(40,t,i)},n);this._spin(t*this.options.step,i)},_spin:function(n,t){var i=this.value()||0;this.counter||(this.counter=1);i=this._adjustValue(i+n*this._increment(this.counter));this.spinning&&this._trigger("spin",t,{value:i})===!1||(this._value(i),this.counter++)},_increment:function(t){var i=this.options.incremental;return i?n.isFunction(i)?i(t):Math.floor(t*t*t/5e4-t*t/500+17*t/200+1):1},_precision:function(){var n=this._precisionOf(this.options.step);return null!==this.options.min&&(n=Math.max(n,this._precisionOf(this.options.min))),n},_precisionOf:function(n){var t=""+n,i=t.indexOf(".");return-1===i?0:t.length-i-1},_adjustValue:function(n){var r,i,t=this.options;return r=null!==t.min?t.min:0,i=n-r,i=Math.round(i/t.step)*t.step,n=r+i,n=parseFloat(n.toFixed(this._precision())),null!==t.max&&n>t.max?t.max:null!==t.min&&t.min>n?t.min:n},_stop:function(n){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",n))},_setOption:function(n,t){if("culture"===n||"numberFormat"===n){var i=this._parse(this.element.val());return this.options[n]=t,this.element.val(this._format(i)),void 0}("max"===n||"min"===n||"step"===n)&&"string"==typeof t&&(t=this._parse(t));"icons"===n&&(this.buttons.first().find(".ui-icon").removeClass(this.options.icons.up).addClass(t.up),this.buttons.last().find(".ui-icon").removeClass(this.options.icons.down).addClass(t.down));this._super(n,t);"disabled"===n&&(this.widget().toggleClass("ui-state-disabled",!!t),this.element.prop("disabled",!!t),this.buttons.button(t?"disable":"enable"))},_setOptions:t(function(n){this._super(n)}),_parse:function(n){return"string"==typeof n&&""!==n&&(n=window.Globalize&&this.options.numberFormat?Globalize.parseFloat(n,10,this.options.culture):+n),""===n||isNaN(n)?null:n},_format:function(n){return""===n?"":window.Globalize&&this.options.numberFormat?Globalize.format(n,this.options.numberFormat,this.options.culture):n},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var n=this.value();return null===n?!1:n===this._adjustValue(n)},_value:function(n,t){var i;""!==n&&(i=this._parse(n),null!==i&&(t||(i=this._adjustValue(i)),n=this._format(i)));this.element.val(n);this._refresh()},_destroy:function(){this.element.removeClass("ui-spinner-input").prop("disabled",!1).removeAttr("autocomplete").removeAttr("role").removeAttr("aria-valuemin").removeAttr("aria-valuemax").removeAttr("aria-valuenow");this.uiSpinner.replaceWith(this.element)},stepUp:t(function(n){this._stepUp(n)}),_stepUp:function(n){this._start()&&(this._spin((n||1)*this.options.step),this._stop())},stepDown:t(function(n){this._stepDown(n)}),_stepDown:function(n){this._start()&&(this._spin((n||1)*-this.options.step),this._stop())},pageUp:t(function(n){this._stepUp((n||1)*this.options.page)}),pageDown:t(function(n){this._stepDown((n||1)*this.options.page)}),value:function(n){return arguments.length?(t(this._value).call(this,n),void 0):this._parse(this.element.val())},widget:function(){return this.uiSpinner}});n.widget("ui.tabs",{version:"1.11.4",delay:300,options:{active:null,collapsible:!1,event:"click",heightStyle:"content",hide:null,show:null,activate:null,beforeActivate:null,beforeLoad:null,load:null},_isLocal:function(){var n=/#.*$/;return function(t){var i,r;t=t.cloneNode(!1);i=t.href.replace(n,"");r=location.href.replace(n,"");try{i=decodeURIComponent(i)}catch(u){}try{r=decodeURIComponent(r)}catch(u){}return t.hash.length>1&&i===r}}(),_create:function(){var i=this,t=this.options;this.running=!1;this.element.addClass("ui-tabs ui-widget ui-widget-content ui-corner-all").toggleClass("ui-tabs-collapsible",t.collapsible);this._processTabs();t.active=this._initialActive();n.isArray(t.disabled)&&(t.disabled=n.unique(t.disabled.concat(n.map(this.tabs.filter(".ui-state-disabled"),function(n){return i.tabs.index(n)}))).sort());this.active=this.options.active!==!1&&this.anchors.length?this._findActive(t.active):n();this._refresh();this.active.length&&this.load(t.active)},_initialActive:function(){var t=this.options.active,i=this.options.collapsible,r=location.hash.substring(1);return null===t&&(r&&this.tabs.each(function(i,u){if(n(u).attr("aria-controls")===r)return(t=i,!1)}),null===t&&(t=this.tabs.index(this.tabs.filter(".ui-tabs-active"))),(null===t||-1===t)&&(t=this.tabs.length?0:!1)),t!==!1&&(t=this.tabs.index(this.tabs.eq(t)),-1===t&&(t=i?!1:0)),!i&&t===!1&&this.anchors.length&&(t=0),t},_getCreateEventData:function(){return{tab:this.active,panel:this.active.length?this._getPanelForTab(this.active):n()}},_tabKeydown:function(t){var r=n(this.document[0].activeElement).closest("li"),i=this.tabs.index(r),u=!0;if(!this._handlePageNav(t)){switch(t.keyCode){case n.ui.keyCode.RIGHT:case n.ui.keyCode.DOWN:i++;break;case n.ui.keyCode.UP:case n.ui.keyCode.LEFT:u=!1;i--;break;case n.ui.keyCode.END:i=this.anchors.length-1;break;case n.ui.keyCode.HOME:i=0;break;case n.ui.keyCode.SPACE:return t.preventDefault(),clearTimeout(this.activating),this._activate(i),void 0;case n.ui.keyCode.ENTER:return t.preventDefault(),clearTimeout(this.activating),this._activate(i===this.options.active?!1:i),void 0;default:return}t.preventDefault();clearTimeout(this.activating);i=this._focusNextTab(i,u);t.ctrlKey||t.metaKey||(r.attr("aria-selected","false"),this.tabs.eq(i).attr("aria-selected","true"),this.activating=this._delay(function(){this.option("active",i)},this.delay))}},_panelKeydown:function(t){this._handlePageNav(t)||t.ctrlKey&&t.keyCode===n.ui.keyCode.UP&&(t.preventDefault(),this.active.focus())},_handlePageNav:function(t){return t.altKey&&t.keyCode===n.ui.keyCode.PAGE_UP?(this._activate(this._focusNextTab(this.options.active-1,!1)),!0):t.altKey&&t.keyCode===n.ui.keyCode.PAGE_DOWN?(this._activate(this._focusNextTab(this.options.active+1,!0)),!0):void 0},_findNextTab:function(t,i){function u(){return t>r&&(t=0),0>t&&(t=r),t}for(var r=this.tabs.length-1;-1!==n.inArray(u(),this.options.disabled);)t=i?t+1:t-1;return t},_focusNextTab:function(n,t){return n=this._findNextTab(n,t),this.tabs.eq(n).focus(),n},_setOption:function(n,t){return"active"===n?(this._activate(t),void 0):"disabled"===n?(this._setupDisabled(t),void 0):(this._super(n,t),"collapsible"===n&&(this.element.toggleClass("ui-tabs-collapsible",t),t||this.options.active!==!1||this._activate(0)),"event"===n&&this._setupEvents(t),"heightStyle"===n&&this._setupHeightStyle(t),void 0)},_sanitizeSelector:function(n){return n?n.replace(/[!"$%&'()*+,.\/:;<=>?@\[\]\^`{|}~]/g,"\\$&"):""},refresh:function(){var t=this.options,i=this.tablist.children(":has(a[href])");t.disabled=n.map(i.filter(".ui-state-disabled"),function(n){return i.index(n)});this._processTabs();t.active!==!1&&this.anchors.length?this.active.length&&!n.contains(this.tablist[0],this.active[0])?this.tabs.length===t.disabled.length?(t.active=!1,this.active=n()):this._activate(this._findNextTab(Math.max(0,t.active-1),!1)):t.active=this.tabs.index(this.active):(t.active=!1,this.active=n());this._refresh()},_refresh:function(){this._setupDisabled(this.options.disabled);this._setupEvents(this.options.event);this._setupHeightStyle(this.options.heightStyle);this.tabs.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1});this.panels.not(this._getPanelForTab(this.active)).hide().attr({"aria-hidden":"true"});this.active.length?(this.active.addClass("ui-tabs-active ui-state-active").attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}),this._getPanelForTab(this.active).show().attr({"aria-hidden":"false"})):this.tabs.eq(0).attr("tabIndex",0)},_processTabs:function(){var t=this,i=this.tabs,r=this.anchors,u=this.panels;this.tablist=this._getList().addClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").attr("role","tablist").delegate("> li","mousedown"+this.eventNamespace,function(t){n(this).is(".ui-state-disabled")&&t.preventDefault()}).delegate(".ui-tabs-anchor","focus"+this.eventNamespace,function(){n(this).closest("li").is(".ui-state-disabled")&&this.blur()});this.tabs=this.tablist.find("> li:has(a[href])").addClass("ui-state-default ui-corner-top").attr({role:"tab",tabIndex:-1});this.anchors=this.tabs.map(function(){return n("a",this)[0]}).addClass("ui-tabs-anchor").attr({role:"presentation",tabIndex:-1});this.panels=n();this.anchors.each(function(i,r){var f,u,e,s=n(r).uniqueId().attr("id"),o=n(r).closest("li"),h=o.attr("aria-controls");t._isLocal(r)?(f=r.hash,e=f.substring(1),u=t.element.find(t._sanitizeSelector(f))):(e=o.attr("aria-controls")||n({}).uniqueId()[0].id,f="#"+e,u=t.element.find(f),u.length||(u=t._createPanel(e),u.insertAfter(t.panels[i-1]||t.tablist)),u.attr("aria-live","polite"));u.length&&(t.panels=t.panels.add(u));h&&o.data("ui-tabs-aria-controls",h);o.attr({"aria-controls":e,"aria-labelledby":s});u.attr("aria-labelledby",s)});this.panels.addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").attr("role","tabpanel");i&&(this._off(i.not(this.tabs)),this._off(r.not(this.anchors)),this._off(u.not(this.panels)))},_getList:function(){return this.tablist||this.element.find("ol,ul").eq(0)},_createPanel:function(t){return n("<div>").attr("id",t).addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").data("ui-tabs-destroy",!0)},_setupDisabled:function(t){n.isArray(t)&&(t.length?t.length===this.anchors.length&&(t=!0):t=!1);for(var i,r=0;i=this.tabs[r];r++)t===!0||-1!==n.inArray(r,t)?n(i).addClass("ui-state-disabled").attr("aria-disabled","true"):n(i).removeClass("ui-state-disabled").removeAttr("aria-disabled");this.options.disabled=t},_setupEvents:function(t){var i={};t&&n.each(t.split(" "),function(n,t){i[t]="_eventHandler"});this._off(this.anchors.add(this.tabs).add(this.panels));this._on(!0,this.anchors,{click:function(n){n.preventDefault()}});this._on(this.anchors,i);this._on(this.tabs,{keydown:"_tabKeydown"});this._on(this.panels,{keydown:"_panelKeydown"});this._focusable(this.tabs);this._hoverable(this.tabs)},_setupHeightStyle:function(t){var i,r=this.element.parent();"fill"===t?(i=r.height(),i-=this.element.outerHeight()-this.element.height(),this.element.siblings(":visible").each(function(){var t=n(this),r=t.css("position");"absolute"!==r&&"fixed"!==r&&(i-=t.outerHeight(!0))}),this.element.children().not(this.panels).each(function(){i-=n(this).outerHeight(!0)}),this.panels.each(function(){n(this).height(Math.max(0,i-n(this).innerHeight()+n(this).height()))}).css("overflow","auto")):"auto"===t&&(i=0,this.panels.each(function(){i=Math.max(i,n(this).height("").height())}).height(i))},_eventHandler:function(t){var u=this.options,r=this.active,c=n(t.currentTarget),i=c.closest("li"),f=i[0]===r[0],e=f&&u.collapsible,o=e?n():this._getPanelForTab(i),s=r.length?this._getPanelForTab(r):n(),h={oldTab:r,oldPanel:s,newTab:e?n():i,newPanel:o};t.preventDefault();i.hasClass("ui-state-disabled")||i.hasClass("ui-tabs-loading")||this.running||f&&!u.collapsible||this._trigger("beforeActivate",t,h)===!1||(u.active=e?!1:this.tabs.index(i),this.active=f?n():i,this.xhr&&this.xhr.abort(),s.length||o.length||n.error("jQuery UI Tabs: Mismatching fragment identifier."),o.length&&this.load(this.tabs.index(i),t),this._toggle(t,h))},_toggle:function(t,i){function e(){u.running=!1;u._trigger("activate",t,i)}function o(){i.newTab.closest("li").addClass("ui-tabs-active ui-state-active");r.length&&u.options.show?u._show(r,u.options.show,e):(r.show(),e())}var u=this,r=i.newPanel,f=i.oldPanel;this.running=!0;f.length&&this.options.hide?this._hide(f,this.options.hide,function(){i.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active");o()}):(i.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active"),f.hide(),o());f.attr("aria-hidden","true");i.oldTab.attr({"aria-selected":"false","aria-expanded":"false"});r.length&&f.length?i.oldTab.attr("tabIndex",-1):r.length&&this.tabs.filter(function(){return 0===n(this).attr("tabIndex")}).attr("tabIndex",-1);r.attr("aria-hidden","false");i.newTab.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_activate:function(t){var r,i=this._findActive(t);i[0]!==this.active[0]&&(i.length||(i=this.active),r=i.find(".ui-tabs-anchor")[0],this._eventHandler({target:r,currentTarget:r,preventDefault:n.noop}))},_findActive:function(t){return t===!1?n():this.tabs.eq(t)},_getIndex:function(n){return"string"==typeof n&&(n=this.anchors.index(this.anchors.filter("[href$='"+n+"']"))),n},_destroy:function(){this.xhr&&this.xhr.abort();this.element.removeClass("ui-tabs ui-widget ui-widget-content ui-corner-all ui-tabs-collapsible");this.tablist.removeClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").removeAttr("role");this.anchors.removeClass("ui-tabs-anchor").removeAttr("role").removeAttr("tabIndex").removeUniqueId();this.tablist.unbind(this.eventNamespace);this.tabs.add(this.panels).each(function(){n.data(this,"ui-tabs-destroy")?n(this).remove():n(this).removeClass("ui-state-default ui-state-active ui-state-disabled ui-corner-top ui-corner-bottom ui-widget-content ui-tabs-active ui-tabs-panel").removeAttr("tabIndex").removeAttr("aria-live").removeAttr("aria-busy").removeAttr("aria-selected").removeAttr("aria-labelledby").removeAttr("aria-hidden").removeAttr("aria-expanded").removeAttr("role")});this.tabs.each(function(){var t=n(this),i=t.data("ui-tabs-aria-controls");i?t.attr("aria-controls",i).removeData("ui-tabs-aria-controls"):t.removeAttr("aria-controls")});this.panels.show();"content"!==this.options.heightStyle&&this.panels.css("height","")},enable:function(t){var i=this.options.disabled;i!==!1&&(void 0===t?i=!1:(t=this._getIndex(t),i=n.isArray(i)?n.map(i,function(n){return n!==t?n:null}):n.map(this.tabs,function(n,i){return i!==t?i:null})),this._setupDisabled(i))},disable:function(t){var i=this.options.disabled;if(i!==!0){if(void 0===t)i=!0;else{if(t=this._getIndex(t),-1!==n.inArray(t,i))return;i=n.isArray(i)?n.merge([t],i).sort():[t]}this._setupDisabled(i)}},load:function(t,i){t=this._getIndex(t);var u=this,r=this.tabs.eq(t),e=r.find(".ui-tabs-anchor"),f=this._getPanelForTab(r),o={tab:r,panel:f},s=function(n,t){"abort"===t&&u.panels.stop(!1,!0);r.removeClass("ui-tabs-loading");f.removeAttr("aria-busy");n===u.xhr&&delete u.xhr};this._isLocal(e[0])||(this.xhr=n.ajax(this._ajaxSettings(e,i,o)),this.xhr&&"canceled"!==this.xhr.statusText&&(r.addClass("ui-tabs-loading"),f.attr("aria-busy","true"),this.xhr.done(function(n,t,r){setTimeout(function(){f.html(n);u._trigger("load",i,o);s(r,t)},1)}).fail(function(n,t){setTimeout(function(){s(n,t)},1)})))},_ajaxSettings:function(t,i,r){var u=this;return{url:t.attr("href"),beforeSend:function(t,f){return u._trigger("beforeLoad",i,n.extend({jqXHR:t,ajaxSettings:f},r))}}},_getPanelForTab:function(t){var i=n(t).attr("aria-controls");return this.element.find(this._sanitizeSelector("#"+i))}});n.widget("ui.tooltip",{version:"1.11.4",options:{content:function(){var t=n(this).attr("title")||"";return n("<a>").text(t).html()},hide:!0,items:"[title]:not([disabled])",position:{my:"left top+15",at:"left bottom",collision:"flipfit flip"},show:!0,tooltipClass:null,track:!1,close:null,open:null},_addDescribedBy:function(t,i){var r=(t.attr("aria-describedby")||"").split(/\s+/);r.push(i);t.data("ui-tooltip-id",i).attr("aria-describedby",n.trim(r.join(" ")))},_removeDescribedBy:function(t){var u=t.data("ui-tooltip-id"),i=(t.attr("aria-describedby")||"").split(/\s+/),r=n.inArray(u,i);-1!==r&&i.splice(r,1);t.removeData("ui-tooltip-id");i=n.trim(i.join(" "));i?t.attr("aria-describedby",i):t.removeAttr("aria-describedby")},_create:function(){this._on({mouseover:"open",focusin:"open"});this.tooltips={};this.parents={};this.options.disabled&&this._disable();this.liveRegion=n("<div>").attr({role:"log","aria-live":"assertive","aria-relevant":"additions"}).addClass("ui-helper-hidden-accessible").appendTo(this.document[0].body)},_setOption:function(t,i){var r=this;return"disabled"===t?(this[i?"_disable":"_enable"](),this.options[t]=i,void 0):(this._super(t,i),"content"===t&&n.each(this.tooltips,function(n,t){r._updateContent(t.element)}),void 0)},_disable:function(){var t=this;n.each(this.tooltips,function(i,r){var u=n.Event("blur");u.target=u.currentTarget=r.element[0];t.close(u,!0)});this.element.find(this.options.items).addBack().each(function(){var t=n(this);t.is("[title]")&&t.data("ui-tooltip-title",t.attr("title")).removeAttr("title")})},_enable:function(){this.element.find(this.options.items).addBack().each(function(){var t=n(this);t.data("ui-tooltip-title")&&t.attr("title",t.data("ui-tooltip-title"))})},open:function(t){var r=this,i=n(t?t.target:this.element).closest(this.options.items);i.length&&!i.data("ui-tooltip-id")&&(i.attr("title")&&i.data("ui-tooltip-title",i.attr("title")),i.data("ui-tooltip-open",!0),t&&"mouseover"===t.type&&i.parents().each(function(){var i,t=n(this);t.data("ui-tooltip-open")&&(i=n.Event("blur"),i.target=i.currentTarget=this,r.close(i,!0));t.attr("title")&&(t.uniqueId(),r.parents[this.id]={element:this,title:t.attr("title")},t.attr("title",""))}),this._registerCloseHandlers(t,i),this._updateContent(i,t))},_updateContent:function(n,t){var i,r=this.options.content,u=this,f=t?t.type:null;return"string"==typeof r?this._open(t,n,r):(i=r.call(n[0],function(i){u._delay(function(){n.data("ui-tooltip-open")&&(t&&(t.type=f),this._open(t,n,i))})}),i&&this._open(t,n,i),void 0)},_open:function(t,i,r){function o(n){s.of=n;u.is(":hidden")||u.position(s)}var f,u,h,e,s=n.extend({},this.options.position);if(r){if(f=this._find(i))return f.tooltip.find(".ui-tooltip-content").html(r),void 0;i.is("[title]")&&(t&&"mouseover"===t.type?i.attr("title",""):i.removeAttr("title"));f=this._tooltip(i);u=f.tooltip;this._addDescribedBy(i,u.attr("id"));u.find(".ui-tooltip-content").html(r);this.liveRegion.children().hide();r.clone?(e=r.clone(),e.removeAttr("id").find("[id]").removeAttr("id")):e=r;n("<div>").html(e).appendTo(this.liveRegion);this.options.track&&t&&/^mouse/.test(t.type)?(this._on(this.document,{mousemove:o}),o(t)):u.position(n.extend({of:i},this.options.position));u.hide();this._show(u,this.options.show);this.options.show&&this.options.show.delay&&(h=this.delayedShow=setInterval(function(){u.is(":visible")&&(o(s.of),clearInterval(h))},n.fx.interval));this._trigger("open",t,{tooltip:u})}},_registerCloseHandlers:function(t,i){var r={keyup:function(t){if(t.keyCode===n.ui.keyCode.ESCAPE){var r=n.Event(t);r.currentTarget=i[0];this.close(r,!0)}}};i[0]!==this.element[0]&&(r.remove=function(){this._removeTooltip(this._find(i).tooltip)});t&&"mouseover"!==t.type||(r.mouseleave="close");t&&"focusin"!==t.type||(r.focusout="close");this._on(!0,i,r)},close:function(t){var u,f=this,i=n(t?t.currentTarget:this.element),r=this._find(i);return r?(u=r.tooltip,r.closing||(clearInterval(this.delayedShow),i.data("ui-tooltip-title")&&!i.attr("title")&&i.attr("title",i.data("ui-tooltip-title")),this._removeDescribedBy(i),r.hiding=!0,u.stop(!0),this._hide(u,this.options.hide,function(){f._removeTooltip(n(this))}),i.removeData("ui-tooltip-open"),this._off(i,"mouseleave focusout keyup"),i[0]!==this.element[0]&&this._off(i,"remove"),this._off(this.document,"mousemove"),t&&"mouseleave"===t.type&&n.each(this.parents,function(t,i){n(i.element).attr("title",i.title);delete f.parents[t]}),r.closing=!0,this._trigger("close",t,{tooltip:u}),r.hiding||(r.closing=!1)),void 0):(i.removeData("ui-tooltip-open"),void 0)},_tooltip:function(t){var i=n("<div>").attr("role","tooltip").addClass("ui-tooltip ui-widget ui-corner-all ui-widget-content "+(this.options.tooltipClass||"")),r=i.uniqueId().attr("id");return n("<div>").addClass("ui-tooltip-content").appendTo(i),i.appendTo(this.document[0].body),this.tooltips[r]={element:t,tooltip:i}},_find:function(n){var t=n.data("ui-tooltip-id");return t?this.tooltips[t]:null},_removeTooltip:function(n){n.remove();delete this.tooltips[n.attr("id")]},_destroy:function(){var t=this;n.each(this.tooltips,function(i,r){var f=n.Event("blur"),u=r.element;f.target=f.currentTarget=u[0];t.close(f,!0);n("#"+i).remove();u.data("ui-tooltip-title")&&(u.attr("title")||u.attr("title",u.data("ui-tooltip-title")),u.removeData("ui-tooltip-title"))});this.liveRegion.remove()}})});