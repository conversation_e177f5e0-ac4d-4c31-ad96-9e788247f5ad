.multiselect-container{position:absolute;list-style-type:none;margin:0;padding:0}.multiselect-container .input-group{margin:5px}.multiselect-container>li{padding:0}.multiselect-container>li>a.multiselect-all label{font-weight:700}.multiselect-container>li.multiselect-group label{margin:0;padding:3px 20px 3px 20px;height:100%;font-weight:700}.multiselect-container>li.multiselect-group-clickable label{cursor:pointer}.multiselect-container>li>a{padding:0}.multiselect-container>li>a>label{margin:0;height:100%;cursor:pointer;font-weight:400;padding:3px 20px 3px 40px}.multiselect-container>li>a>label.radio,.multiselect-container>li>a>label.checkbox{margin:0}.multiselect-container>li>a>label>input[type=checkbox]{margin-bottom:5px}.btn-group>.btn-group:nth-child(2)>.multiselect.btn{border-top-left-radius:4px;border-bottom-left-radius:4px}.form-inline .multiselect-container label.checkbox,.form-inline .multiselect-container label.radio{padding:3px 20px 3px 40px}.form-inline .multiselect-container li a label.checkbox input[type=checkbox],.form-inline .multiselect-container li a label.radio input[type=radio]{margin-left:-20px;margin-right:0}.btn-group button.multiselect{-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-ms-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3);background:#fff;font-size:13px;margin-right:0;position:relative;padding:6px 10px}.btn-group button.multiselect:hover{color:inherit}.btn-group button.multiselect:focus,.btn-group button.multiselect:visited{-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-ms-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3)}.btn-group button.multiselect .caret{position:absolute;right:10px;top:12px}.btn-group.open>.dropdown-toggle.btn.btn-default{background:#fff;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-ms-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3)}.btn:not(.btn-raised):not(.btn-link):hover,.btn:not(.btn-raised):not(.btn-link):focus,.input-group-btn .btn:not(.btn-raised):not(.btn-link):hover,.input-group-btn .btn:not(.btn-raised):not(.btn-link):focus{background:#fff}.multiselect.btn-default.active,.multiselect.btn-default:active{background:#fff}.multiselect .multiselect-selected-text{width:170px;margin-right:10px;display:inline-block;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;text-align:left}.multiselect-container.dropdown-menu{border:1px solid #dfdfdf;font-size:13px;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-khtml-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-moz-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-ms-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-o-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23)}.multiselect-container.dropdown-menu>li:hover>a{background-color:#ddd}.multiselect-container.dropdown-menu>li>a>label{line-height:28px;padding:0 20px 0 35px}.multiselect-container.dropdown-menu label.checkbox{display:block}.multiselect-container.dropdown-menu label.radio{padding-left:8px}.multiselect-container.dropdown-menu label.radio input[type=radio]{display:none}.multiselect-container.dropdown-menu .filter{padding:0}.multiselect-container.dropdown-menu .filter:hover{background:#fff}.multiselect-container.dropdown-menu .filter .input-group{width:91%}.multiselect-container.dropdown-menu .filter .input-group .input-group-addon,.multiselect-container.dropdown-menu .filter .input-group .input-group-btn{display:none}.multiselect-container.dropdown-menu .filter .input-group .form-control.multiselect-search{width:100%;margin-left:8px;border-radius:2px;box-shadow:none;border:1px solid #aaa;line-height:18px}.multiselect-container.dropdown-menu .filter .input-group .form-control.multiselect-search:hover{background:#fff}.multiselect-container.dropdown-menu .checkbox input[type="checkbox"],.multiselect-container.dropdown-menu .checkbox-inline input[type="checkbox"]{margin-left:-22px;top:2px}.multiselect-container.dropdown-menu .multiselect-all a{border-bottom:1px solid #cecece}.multiselect-container.dropdown-menu .multiselect-item.multiselect-group a{padding:6px 0 6px 36px !important;display:flex;align-items:center}.multiselect-container.dropdown-menu .multiselect-item.multiselect-group a input[type="checkbox"]{top:0}.multiselect-container.dropdown-menu .multiselect-item.multiselect-group a b:not(.caret){width:100%;display:flex;align-items:center;justify-content:space-between}.multiselect-container.dropdown-menu .multiselect-item.multiselect-group a input[type="checkbox"]{margin-right:3px !important}.multiselect-container.dropdown-menu .multiselect-item.multiselect-group-clickable a{padding:8px 0 8px 8px !important}.multiselect-container.dropdown-menu .multiselect-item.multiselect-group-clickable a:hover{color:inherit}.multiselect-container.dropdown-menu .multiselect-nested-item a{padding-left:16px}.form-inline .multiselect-container label.checkbox{padding:2px 20px 2px 36px}.form-inline .multiselect-container .active>a label,.form-inline .multiselect-container .active>a:hover label,.form-inline .multiselect-container .active>a:focus label{color:#333}.dropdown-menu>.active>a,.dropdown-menu>.active>a:hover,.dropdown-menu>.active>a:focus{background:none;color:#333}
/*!
 * Datetimepicker for Bootstrap 3
 * version : 4.17.42
 * https://github.com/Eonasdan/bootstrap-datetimepicker/
 */
.bootstrap-datetimepicker-widget{list-style:none}.bootstrap-datetimepicker-widget.dropdown-menu{margin:2px 0;padding:4px;width:19em}@media(min-width:768px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width:38em}}@media(min-width:992px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width:38em}}@media(min-width:1200px){.bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs{width:38em}}.bootstrap-datetimepicker-widget.dropdown-menu:before,.bootstrap-datetimepicker-widget.dropdown-menu:after{content:'';display:inline-block;position:absolute}.bootstrap-datetimepicker-widget.dropdown-menu.bottom:before{border-left:7px solid transparent;border-right:7px solid transparent;border-bottom:7px solid #ccc;border-bottom-color:rgba(0,0,0,.2);top:-7px;left:7px}.bootstrap-datetimepicker-widget.dropdown-menu.bottom:after{border-left:6px solid transparent;border-right:6px solid transparent;border-bottom:6px solid #fff;top:-6px;left:8px}.bootstrap-datetimepicker-widget.dropdown-menu.top:before{border-left:7px solid transparent;border-right:7px solid transparent;border-top:7px solid #ccc;border-top-color:rgba(0,0,0,.2);bottom:-7px;left:6px}.bootstrap-datetimepicker-widget.dropdown-menu.top:after{border-left:6px solid transparent;border-right:6px solid transparent;border-top:6px solid #fff;bottom:-6px;left:7px}.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:before{left:auto;right:6px}.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:after{left:auto;right:7px}.bootstrap-datetimepicker-widget .list-unstyled{margin:0}.bootstrap-datetimepicker-widget a[data-action]{padding:6px 0}.bootstrap-datetimepicker-widget a[data-action]:active{box-shadow:none}.bootstrap-datetimepicker-widget .timepicker-hour,.bootstrap-datetimepicker-widget .timepicker-minute,.bootstrap-datetimepicker-widget .timepicker-second{width:54px;font-weight:bold;font-size:1.2em;margin:0}.bootstrap-datetimepicker-widget button[data-action]{padding:6px}.bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Increment Hours"}.bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Increment Minutes"}.bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Decrement Hours"}.bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Decrement Minutes"}.bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Show Hours"}.bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Show Minutes"}.bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Toggle AM/PM"}.bootstrap-datetimepicker-widget .btn[data-action="clear"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Clear the picker"}.bootstrap-datetimepicker-widget .btn[data-action="today"]::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Set the date to today"}.bootstrap-datetimepicker-widget .picker-switch{text-align:center}.bootstrap-datetimepicker-widget .picker-switch::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Toggle Date and Time Screens"}.bootstrap-datetimepicker-widget .picker-switch td{padding:0;margin:0;height:auto;width:auto;line-height:inherit}.bootstrap-datetimepicker-widget .picker-switch td span{line-height:2.5;height:2.5em;width:100%}.bootstrap-datetimepicker-widget table{width:100%;margin:0}.bootstrap-datetimepicker-widget table td,.bootstrap-datetimepicker-widget table th{text-align:center;border-radius:4px}.bootstrap-datetimepicker-widget table th{height:20px;line-height:20px;width:20px}.bootstrap-datetimepicker-widget table th.picker-switch{width:145px}.bootstrap-datetimepicker-widget table th.disabled,.bootstrap-datetimepicker-widget table th.disabled:hover{background:none;color:#777;cursor:not-allowed}.bootstrap-datetimepicker-widget table th.prev::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Previous Month"}.bootstrap-datetimepicker-widget table th.next::after{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0;content:"Next Month"}.bootstrap-datetimepicker-widget table thead tr:first-child th{cursor:pointer}.bootstrap-datetimepicker-widget table thead tr:first-child th:hover{background:#eee}.bootstrap-datetimepicker-widget table td{height:54px;line-height:54px;width:54px}.bootstrap-datetimepicker-widget table td.cw{font-size:.8em;height:20px;line-height:20px;color:#777}.bootstrap-datetimepicker-widget table td.day{height:20px;line-height:20px;width:20px}.bootstrap-datetimepicker-widget table td.day:hover,.bootstrap-datetimepicker-widget table td.hour:hover,.bootstrap-datetimepicker-widget table td.minute:hover,.bootstrap-datetimepicker-widget table td.second:hover{background:#eee;cursor:pointer}.bootstrap-datetimepicker-widget table td.old,.bootstrap-datetimepicker-widget table td.new{color:#777}.bootstrap-datetimepicker-widget table td.today{position:relative}.bootstrap-datetimepicker-widget table td.today:before{content:'';display:inline-block;border:solid transparent;border-width:0 0 7px 7px;border-bottom-color:#337ab7;border-top-color:rgba(0,0,0,.2);position:absolute;bottom:4px;right:4px}.bootstrap-datetimepicker-widget table td.active,.bootstrap-datetimepicker-widget table td.active:hover{background-color:#337ab7;color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25)}.bootstrap-datetimepicker-widget table td.active.today:before{border-bottom-color:#fff}.bootstrap-datetimepicker-widget table td.disabled,.bootstrap-datetimepicker-widget table td.disabled:hover{background:none;color:#777;cursor:not-allowed}.bootstrap-datetimepicker-widget table td span{display:inline-block;width:54px;height:54px;line-height:54px;margin:2px 1.5px;cursor:pointer;border-radius:4px}.bootstrap-datetimepicker-widget table td span:hover{background:#eee}.bootstrap-datetimepicker-widget table td span.active{background-color:#337ab7;color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25)}.bootstrap-datetimepicker-widget table td span.old{color:#777}.bootstrap-datetimepicker-widget table td span.disabled,.bootstrap-datetimepicker-widget table td span.disabled:hover{background:none;color:#777;cursor:not-allowed}.bootstrap-datetimepicker-widget.usetwentyfour td.hour{height:27px;line-height:27px}.bootstrap-datetimepicker-widget.wider{width:21em}.bootstrap-datetimepicker-widget .datepicker-decades .decade{line-height:1.8em !important}.input-group.date .input-group-addon{cursor:pointer}.sr-only{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0}.control{display:inline-block;width:200px;vertical-align:middle;margin:0 8px 0 5px}.control.date{width:130px}.input-group.date{position:initial}.input-group.date .icon-calendar{font-size:24px;color:#7f7f7f;display:inline-block;position:relative}.input-group.date .icon-calendar:after{position:relative;content:'';display:block}.input-group.date .icon-calendar:hover:after{position:absolute;left:0;top:-2px;width:36px;height:36px;margin-left:-6px;margin-top:-4px;background:#b1aea3;border-radius:100%;opacity:1;-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0);-moz-animation:ripple 1s ease-out;-o-animation:ripple 1s ease-out;-webkit-animation:ripple 1s ease-out;animation:ripple 1s ease-out}.input-group.date .input-group-addon{background:transparent;border:none;padding:0 8px;position:relative;top:3px}.input-group.date .form-control{border:none;font-size:12px;height:28px;background:#fff;margin-left:0;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-ms-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3)}.bootstrap-datetimepicker-widget{border:1px solid #dfdfdf;font-family:Roboto,Arial,Tahoma,sans-serif;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-ms-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-khtml-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-moz-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-ms-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);-o-box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23);box-shadow:0 3px 6px rgba(0,0,0,.16),0 3px 6px rgba(0,0,0,.23)}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td,.bootstrap-datetimepicker-widget table td span.month,.bootstrap-datetimepicker-widget table td span.year,.bootstrap-datetimepicker-widget table td span.decade{position:relative;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active,.bootstrap-datetimepicker-widget table td span.active.month,.bootstrap-datetimepicker-widget table td span.active.year,.bootstrap-datetimepicker-widget table td span.active.decade{background-color:#337ab7;color:#fff;text-shadow:none;padding:3px 1px;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active:before,.bootstrap-datetimepicker-widget table td span.active.month:before,.bootstrap-datetimepicker-widget table td span.active.year:before,.bootstrap-datetimepicker-widget table td span.active.decade:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active:hover:after,.bootstrap-datetimepicker-widget table td span.active.month:hover:after,.bootstrap-datetimepicker-widget table td span.active.year:hover:after,.bootstrap-datetimepicker-widget table td span.active.decade:hover:after{animation:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active.today,.bootstrap-datetimepicker-widget table td span.active.today.month,.bootstrap-datetimepicker-widget table td span.active.today.year,.bootstrap-datetimepicker-widget table td span.active.today.decade{color:#fff}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.active.today:hover,.bootstrap-datetimepicker-widget table td span.active.today.month:hover,.bootstrap-datetimepicker-widget table td span.active.today.year:hover,.bootstrap-datetimepicker-widget table td span.active.today.decade:hover{background-color:#337ab7;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled:before,.bootstrap-datetimepicker-widget table td span.disabled.month:before,.bootstrap-datetimepicker-widget table td span.disabled.year:before,.bootstrap-datetimepicker-widget table td span.disabled.decade:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled:hover:after,.bootstrap-datetimepicker-widget table td span.disabled.month:hover:after,.bootstrap-datetimepicker-widget table td span.disabled.year:hover:after,.bootstrap-datetimepicker-widget table td span.disabled.decade:hover:after{animation:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td:after,.bootstrap-datetimepicker-widget table td span.month:after,.bootstrap-datetimepicker-widget table td span.year:after,.bootstrap-datetimepicker-widget table td span.decade:after{content:"";display:block;position:relative}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td:hover:after,.bootstrap-datetimepicker-widget table td span.month:hover:after,.bootstrap-datetimepicker-widget table td span.year:hover:after,.bootstrap-datetimepicker-widget table td span.decade:hover:after{animation:1s ease-out 0s normal 1 ripple;background:#337ab7;left:0;opacity:1;position:absolute;transform:scale(0);top:0;right:0;bottom:0;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days table thead tr:first-child th:hover{background:none}.bootstrap-datetimepicker-widget .datepicker-days table th.picker-switch{height:36px;line-height:36px;color:#3e3e3e}.bootstrap-datetimepicker-widget .datepicker-days table th.dow{color:#9e9e9e;font-weight:normal}.bootstrap-datetimepicker-widget .datepicker-days table th .glyphicon{font-family:'Iconalpha' !important;font-size:24px}.bootstrap-datetimepicker-widget .datepicker-days table th .glyphicon.glyphicon-chevron-left:before{content:'';line-height:36px}.bootstrap-datetimepicker-widget .datepicker-days table th .glyphicon.glyphicon-chevron-right:before{content:'';line-height:36px}.bootstrap-datetimepicker-widget .datepicker-days table th.prev{color:#757575}.bootstrap-datetimepicker-widget .datepicker-days table td.day{height:30px;line-height:30px;color:#000;width:30px;transition:all .3s ease;-webkit-border-radius:50%;-khtml-border-radius:50%;-moz-border-radius:50%;-ms-border-radius:50%;-o-border-radius:50%;border-radius:50%}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>thead>tr>th,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>th,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tfoot>tr>th,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>thead>tr>td,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td,.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tfoot>tr>td{padding:3px 1px}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed{border-collapse:separate}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td{color:#4c5246;font-size:13px}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled{color:#c7c7c7}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.disabled:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.today{color:#2491ef}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.today:before{display:none}.bootstrap-datetimepicker-widget .datepicker-days .table-condensed>tbody>tr>td.today:hover{background:none}.bootstrap-datetimepicker-widget table td.day:hover,.bootstrap-datetimepicker-widget table td.hour:hover,.bootstrap-datetimepicker-widget table td.minute:hover,.bootstrap-datetimepicker-widget table td.second:hover{background:none}.bootstrap-datetimepicker-widget table td span:hover{background:none}@keyframes ripple{0%{transform:scale(0)}20%{transform:scale(1)}100%{opacity:0;transform:scale(1)}}@media only screen and (max-device-width:743px){.input-group.date .form-control{height:36px;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 3px rgba(0,0,0,.3);-moz-box-shadow:0 1px 3px rgba(0,0,0,.3);-ms-box-shadow:0 1px 3px rgba(0,0,0,.3);-o-box-shadow:0 1px 3px rgba(0,0,0,.3);box-shadow:0 1px 3px rgba(0,0,0,.3)}.input-group.date .icon-calendar{font-size:26px;position:relative}.input-group.date .icon-calendar:after{position:relative;content:'';display:block}.input-group.date .icon-calendar:hover:after{position:absolute;left:0;top:-4px;width:40px;height:40px;margin-left:-7px;margin-top:-4px;background:#b1aea3;border-radius:100%;opacity:1;-webkit-transform:scale(0);-khtml-transform:scale(0);-moz-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0);-moz-animation:ripple 1s ease-out;-o-animation:ripple 1s ease-out;-webkit-animation:ripple 1s ease-out;animation:ripple 1s ease-out}}.input-group.date .input-group-addon{padding-right:0}.daterange-picker{display:none}