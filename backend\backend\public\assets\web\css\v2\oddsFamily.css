@charset "utf-8";
.HalfTime {
    font-size: 9px;
    font-weight: bold;
    color: #0000FF;
}

.IsLive,
.text_time font {
    font-size: 9px;
    color: #B50000;
}

.IsLiveLarge {
    font-size: 14px;
    color: #B50000;
}

.LiveTime {
    font-size: 9px;
    color: #B50000;
}

.text_time {
    font-size: 10px;
    color: #000000;
    text-align: center;
}

.text_time a {
    font-size: 10px;
    color: #000000;
    text-align: center;
}

.text_timeR {
    font-size: 11px;
    color: #FF0000;
    text-align: center;
}

.OddsDiv {
    min-width: 35%;
    text-align: right;
    white-space: nowrap;
    font-weight: normal;
}

.FavOddsClass,
.FavOddsClass a {
    color: #B50000!important;
    font-weight: bold;
}

.FavTeamClass,
.FavTeamClass a {
    color: #B50000;
    font-weight: bold;
}

.UdrDogOddsClass,
.UdrDogOddsClass a {
    color: #01122B;
    font-weight: bold;
}

.UdrDogOddsClass span {
    color: #01122B;
    font-weight: bold;
}

.UdrDogTeamClass,
.UdrDogTeamClass a {
    color: #01122B;
    font-weight: bold;
}

.HdpGoalClass,
.HdpGoalClass a {
    color: #606060;
    font-weight: bold;
    font-size: 10px;
}

.FavOddsClassBetProcess,
.FavOddsClassBetProcess a {
    color: #B50000;
    font-weight: bold;
    font-size: 15px;
}

.FavTeamClassBetProcess,
.FavTeamClassBetProcess a {
    color: #B50000;
    font-weight: bold;
    font-size: 15px;
}

.UdrDogOddsClassBetProcess,
.UdrDogOddsClassBetProcess a {
    color: #000000;
    font-weight: bold;
    font-size: 15px;
}

.UdrDogTeamClassBetProcess,
.UdrDogTeamClassBetProcess a {
    color: #000000;
    font-weight: bold;
    font-size: 15px;
}

.BetInfo .FavOddsClass,
.BetInfo .UdrDogOddsClass {
    font-size: 15px;
}


/*Add 0914 OddsBTN*/

a.FavOddsClass,
a.UdrDogOddsClass,
.FavOddsClass a,
.UdrDogOddsClass a {
    display: inline-block;
    padding: 0 2px;
    min-width: 33px;
    margin-top: 1px;
}

#oTableContainer_D .FavOddsClass a,
#oTableContainer_D .UdrDogOddsClass a,
#oTableContainer_L.FavOddsClass a,
#oTableContainer_L .UdrDogOddsClass a {
    min-width: 30px;
}

.line_divR.FavOddsClass a,
.line_divR.UdrDogOddsClass a {
    width: 44px;
}

.Odds_Upd a.FavOddsClass,
.Odds_Upd a.UdrDogOddsClass,
.Odds_Upd .FavOddsClass a,
.Odds_Upd .UdrDogOddsClass a,
.UdrDogOddsClass.Odds_Upd a,
.miniContent .Odds_Upd,
.MoreTable .FavOddsClass.Odds_Upd,
.MoreTable .UdrDogOddsClass.Odds_Upd,
.BetInfo.Odds_Upd,
.BetInfo .bet.Odds_Upd,
.text_time.Odds_Upd,
.euro .Odds_Upd .OddsDiv a,
.HdpGoalClass.Odds_Upd,
.numberGroup .Odds_Upd,
#parlay_bet_info .Odds_Upd,
.lottoBtn.Odds_Upd, .score_box .box01 .border.Odds_Upd, .score_box .box02 .border.Odds_Upd, .score_box .box03 .border.Odds_Upd {
    background-image: url(../images/layout/update_odds20090910.gif?v=20151216001);
    border-radius: 2px;
    min-height: 16px;
}

.Odds_Upd a:empty:hover {
    background-image: url(../images/layout/update_odds20090910.gif?v=20151216001);
    cursor: default;
    background-color: transparent !important;
}

.BetInfo .bet.Odds_Upd,
.text_time.Odds_Upd,
.numberGroup .Odds_Upd,
#parlay_bet_info .Odds_Upd, .score_box .box01 .border.Odds_Upd, .score_box .box02 .border.Odds_Upd, .score_box .box03 .border.Odds_Upd {
    border-radius: 0px;
}

a:hover.FavOddsClass,
a:hover.UdrDogOddsClass,
.FavOddsClass a:hover,
.UdrDogOddsClass a:hover,
.miniContent td[onclick]:hover,
.numberGroup .trbgov[style*="pointer"]:hover,
.MoreTable .UdrDogOddsClass[style*="pointer"]:hover,
.trbgov td[style*="pointer"]:hover,
.scoremapList .BetInfo:hover {
    background-image: none;
    background-color: #ffe00e!important;
    border-radius: 2px;
}


/*Modify*/

.score_box .border {
    margin-left: 2px;
}

.line_divR {
    padding-right: 0px;
}

.line_divL {
    padding-left: 1px;
}

.score_box .line_divL {
    padding-left: 3px;
}


/*end*/


/*Add 20151007 OddsBTN-Fix*/

.line_divR.OddsDiv a {
    display: block;
}

.line_divR.OddsDiv a+br {
    display: none;
}

.line_divL.HdpGoalClass {
    padding-top: 1px;
}

#div_BetListMini .UdrDogOddsClass,
#div_BetListMini .FavOddsClass,
#div_WaitingBets .UdrDogOddsClass,
#div_WaitingBets .FavOddsClass,
#div_VoidTicket .UdrDogOddsClass,
#div_VoidTicket .FavOddsClass {
    font-size: 11px;
}

.ScoreClass {
    font-size: 10px;
    color: #000000;
    font-weight: bold;
    font-family: Tahoma;
}

.styleblack {
    font-size: 12px;
}


/* all icon*/

.tvicon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -8px -95px;
    height: 15px;
    width: 15px;
}

.tv_gicon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -8px -78px;
    height: 15px;
    width: 15px;
}

.plusicon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -31px -51px;
    height: 15px;
    width: 15px;
}

.graphicon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -52px -51px;
    height: 15px;
    width: 15px;
}

.redcard {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -31px -34px;
    height: 15px;
    width: 13px;
}

.L_icon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -31px -71px;
    height: 15px;
    width: 13px;
}

.F_icon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -53px -71px;
    height: 15px;
    width: 13px;
}

.calendar_icon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -33px -90px;
    height: 18px;
    width: 15px;
}

.arrowR_icon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -7px -34px;
    height: 13px;
    width: 13px;
}

.arrowL_icon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -7px -45px;
    height: 13px;
    width: 13px;
}

.arrowRB_icon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -10px -123px;
    height: 20px;
    width: 20px;
}

.close_icon {
    background-image: url(../images/layout/all_menu.gif?v=20151216001);
    background-position: -10px -147px;
    height: 15px;
    width: 15px;
}


/* sport icon*/

.w1 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -20px 0px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w2 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -40px 0px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w3 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -60px 0px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w4 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -80px 0px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w5 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: 0px -20px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w6 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -20px -20px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w7 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -40px -20px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w8 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -60px -20px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w9 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -80px -20px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w10 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: 0px -40px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w11 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -20px -40px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w12 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -40px -40px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w13 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -60px -40px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w14 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -80px -40px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w15 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: 0px -60px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w16 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -20px -60px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w17 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -40px -60px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w18 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -60px -60px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w19 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -80px -60px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w20 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: 0px -80px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w21 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -20px -80px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w22 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -40px -80px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w23 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -60px -80px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w24 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: -80px -80px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}

.w99 {
    background-image: url(../images/login_page/sport_icon.gif?v=20151216001);
    background-position: 0px -100px;
    height: 15px;
    width: 15px;
    float: left;
    display: block;
}


/*20091028 Europe*/

.odds {
    margin: 3px;
    *margin: 5px;
}

.odds td {
    border-right-width: 4px;
    border-right-style: solid;
    border-right-color: #FFFFFF;
    border-bottom-width: 4px;
    border-bottom-style: solid;
    border-bottom-color: #FFFFFF;
}

.odds FavOddsClass,
.odds FavOddsClass a {
    color: #000000;
    font-weight: bold;
}

.odds FavOddsClass span {
    color: #000000;
    font-weight: bold;
}

.odds FavTeamClass,
.odds FavTeamClass a {
    color: #000000;
    font-weight: bold;
}

.odds FavTeamClass span {
    color: #000000;
    font-weight: bold;
}

.odds1x2Team,
.odds1x2Team a {
    color: #000000;
    font-weight: bold!important;
}

.odds1x2Team span {
    color: #000000;
    font-weight: normal!important;
}

.more,
.more a {
    color: #ff9a00!important;
    font-weight: bold;
}

.more span {
    color: #ff9a00!important;
    font-weight: normal!important;
}


/**1005*/

.Q_live {
    color: #FF0000!important;
    font-weight: bold;
    font-size: 10px;
}

.Q_live .line_divR {
    color: #000000;
    background-color: #FFCCBC;
    font-weight: bold;
}

.Q_start {
    color: #999999;
    font-weight: bold;
    font-size: 10px;
}

.Q_start .line_divR {
    color: #CCCCCC;
    background-color: #999999;
    font-weight: bold;
}

.Q_end {
    color: #999999;
    font-weight: bold;
    font-size: 10px;
}

.Q_end .line_divR {
    color: #000000;
    background-color: #66CC66;
    font-weight: bold;
}

.Q_TG {
    color: #000000;
    font-weight: bold;
    font-size: 10px;
}

.Q_TG .line_divR {
    color: #000000;
    background-color: #FFCCBC;
    font-weight: bold;
}

.left {
    float: left;
}

.right {
    float: right;
}


/* Icon Odds */

.iconOdds,
.iconfast::before {
    background-image: url(../images/layout/icon_odds.png?v=20170615);
    background-repeat: no-repeat;
    background-position: left top;
    width: 13px;
    height: 13px;
    display: inline-block;
    cursor: pointer;
    line-height: 11px;
    vertical-align: middle;
    margin: 1px;
}

a:hover .iconOdds {
    cursor: pointer;
}

.iconOdds.off,
.iconOdds.more.off {
    cursor: default;
}

.iconOdds.favorite {
    background-position: 0 0;
}

.iconOdds.favorite:hover {
    background-position: 0 -15px;
}

.iconOdds.favoriteAdd {
    background-position: -15px 0;
}

.iconOdds.favoriteAdd:hover {
    background-position: -15px -15px;
}

.iconOdds.tv {
    background-position: -30px 0;
}

.iconOdds.tv:hover,
a:hover .iconOdds.tv {
    background-position: -30px -15px;
}

.iconOdds.tv.off {
    background-position: -30px -30px;
}

.iconOdds.scoreMap {
    background-position: -45px 0px;
}

.iconOdds.scoreMap:hover {
    background-position: -45px -15px;
}

.iconOdds.stats {
    background-position: -60px 0px;
}

.iconOdds.stats:hover,
a:hover .iconOdds.stats {
    background-position: -60px -15px;
}

.iconOdds.nWheel {
    background-position: -75px 0px;
}

.iconOdds.nWheel:hover {
    background-position: -75px -15px;
}

.iconOdds.info {
    background-position: -90px 0px;
}

.iconOdds.info:hover,
a:hover .iconOdds.info {
    background-position: -90px -15px;
}

.iconOdds.info.off {
    background-position: -90px -30px;
}

.iconOdds.liveInfo {
    background-position: -105px 0px;
}

.iconOdds.liveInfo:hover {
    background-position: -105px -15px;
}

.iconOdds.poker {
    background-position: -120px 0px;
}

.iconOdds.poker:hover {
    background-position: -120px -15px;
}

.iconOdds.help {
    background-position: -135px 0px;
}

.iconOdds.help:hover {
    background-position: -135px -15px;
}

.iconOdds.help-dark {
    background-position: -135px -30px;
}

.iconOdds.help-dark:hover {
    background-position: -135px -15px;
}

.iconOdds.more {
    background-position: -150px 0px;
}

.iconOdds.more:hover {
    background-position: -150px -15px;
}

.iconOdds.more.off {
    background-position: -150px -30px;
}

.iconOdds.moreAll {
    background-position: right 0px;
    width: 30px;
    color: #FFFFFF;
    font-size: 11px;
    line-height: 11px;
}

.iconOdds.moreAll:hover {
    background-position: right -15px;
}

.iconOdds.bFront,
.iconOdds.bFront.info:hover {
    background-position: 0 -60px;
}

.iconOdds.bFront:hover {
    background-position: 0 -75px;
}

.iconOdds.bForward,
.iconOdds.bForward.info:hover {
    background-position: -15px -60px;
}

.iconOdds.bForward:hover {
    background-position: -15px -75px;
}

.iconOdds.sBackward,
.iconOdds.sBackward.info:hover {
    background-position: -30px -60px;
}

.iconOdds.sBackward:hover {
    background-position: -30px -75px;
}

.iconOdds.sBack,
.iconOdds.sBack.info:hover {
    background-position: -45px -60px;
}

.iconOdds.sBack:hover {
    background-position: -45px -75px;
}

.iconOdds.liveChart {
    background-position: -60px -60px;
}

.iconOdds.liveChart:hover {
    background-position: -60px -75px;
}

.iconOdds.corner1F,
.iconOdds.corner1L,
.iconOdds.cornerF,
.iconOdds.cornerL {
    cursor: default;
    margin: -2px -2px 0 0;
}

.iconOdds.corner1F {
    background-position: 0px -120px;
}

.iconOdds.corner1L {
    background-position: -15px -120px;
}

.iconOdds.cornerF {
    background-position: -30px -120px;
}

.iconOdds.cornerL {
    background-position: -45px -120px;
}

.iconOdds.fastMarket {
    background-position: -75px -60px;
}

.iconOdds.fastMarket:hover {
    background-position: -75px -75px;
}

.iconOdds.badminton {
    background-position: -90px -60px;
}

.iconOdds.badminton:hover {
    background-position: -90px -75px;
}

.iconOdds.baseball {
    background-position: -105px -60px;
}

.iconOdds.baseball:hover {
    background-position: -105px -75px;
}

.iconOdds.basketball {
    background-position: -120px -60px;
}

.iconOdds.basketball:hover {
    background-position: -120px -75px;
}

.iconOdds.tennis {
    background-position: -135px -60px;
}

.iconOdds.tennis:hover {
    background-position: -135px -75px;
}

.iconOdds.soccer {
    background-position: -150px -60px;
}

.iconOdds.soccer:hover {
    background-position: -150px -75px;
}

.iconfast::before {
    content: "";
    background-position: 0px -95px;
    vertical-align: bottom;
    margin: 0 2px 0 0;
}


/* Icon Info */

.iconInfo, .icon-coffee, .icon-rain {
    background-image: url(../images/layout/icon_info.png?v=20151216001);
    background-repeat: no-repeat;
    background-position: left top;
    width: 13px;
    height: 13px;
    display: inline-block;
    vertical-align: middle;
}

.iconInfo.clock {
    background-position: left top;
}

.iconInfo.baseball {
    background-position: -15px top;
}

.iconInfo.football {
    background-position: -30px top;
}

.iconInfo.hockey {
    background-position: -45px top;
}

.iconInfo.hockeyPP {
    background-position: -60px top;
}

.iconInfo.injury {
    background-position: -75px top;
}

.iconInfo.point {
    background-position: -90px top;
}

.iconInfo.rain, .icon-rain {
    background-position: -105px top;
}

.iconInfo.break, .icon-coffee{
    background-position: -120px top;
}

.infoBar .iconOdds,
.infoBar .iconInfo {
    margin-right: 5px;
    float: left;
}


/*Bet info*/

.TextStyle01 {
    color: #000000;
    font-weight: bold;
}

.TextStyle02 {
    color: #b50000;
    font-weight: bold;
}

.TextStyle03 {
    color: #606060;
    font-weight: bold;
}

.TextStyle04 {
    color: #000000;
    font-size: 10px;
}

.TextStyle05 {
    color: #e94201;
}

.TextStyle06 {
    color: #000000;
}

.TextStyle07 {
    color: #003399;
}

.TextStyle08 { /*add*/
    color: #8b8b8b;
} 

.MinBet,
.BetprocessMinBet {
    color: #003399;
}

.MaxBet,
.BetprocessMaxBet {
    color: #FF0000;
}

.Currency {
    color: #003399;
    font-weight: bold;
    position: relative;
}

#div_MixParlay .TextStyle04,
#divSingleTickets .TextStyle04,
.oddsTable.info .TextStyle04 {
    font-size: 11px;
}

#SuccessBetContainer .TextStyle07 {
    font-weight: bold;
}

#SuccessBetContainer .UdrDogTeamClass,
#SuccessBetContainer .FavTeamClass {
    font-size: 12px;
}

#SuccessBetContainer .UdrDogTeamClass {
    color: #003399;
}

#SuccessBetContainer .FavTeamClass {
    color: #FF0000;
}

.ipColor {
    color: #6076B5;
    font-family: Tahoma;
    font-size: 10px;
    font-weight: bold;
}

.pad {
    padding-left: 3px;
}

.mag {
    margin: 3px 0px;
}

.Void .TextStyle01,
.Void .TextStyle02,
.Void .TextStyle03,
.Void .TextStyle04,
.Void .TextStyle05,
.Void .FavOddsClass,
.Void .UdrDogOddsClass,
.Void .FavTeamClass,
.Void .UdrDogTeamClass {
    color: #8a8a8a !important;
}

.Void {
    background-color: #c7c7c7;
}

.linethrough,
.linethrough * {
    color: #8b8b8b !important;
    text-decoration: line-through;
	opacity: 1 !important;
}

.linethrough .TextStyle08.singleTicketStatus,
.linethrough.TextStyle08.singleTicketStatus{
    text-decoration: blink!important;
}

.Total {
    background-color: #c8c8c8;
    color: #000;
    font-weight: bold;
    line-height: 20px;
}

.Total td {
    padding: 0px;
}

.Total div {
    padding-right: 5px;
    border-top: 1px solid #c8c8c8;
}

.none_Bline {
    border-bottom-style: none !important;
}

.trbgov {
    background-color: #f5eeb8;
}

.multiple+.multiple {
    border-top: #FFFFFF 1px solid;
    display: block;
    padding: 4px 0px;
}

.text-ellipsis {
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap!important;
}

.bet {
    border: #B3BCD2 solid 1px;
    text-align: right;
}

.bet .mag {
    margin: 0px;
}

.liveligh .bet {
    background-color: #FFCCBC;
}

.scoremapInfo .button {
    margin-right: 0;
    margin-top: -1px;
}

.scoremapList .btnIcon {
    display: inline-block;
}

.scoremapList input {
    margin-top: 0;
}

.scoremapList .stake {
    position: relative;
    top: -4px;
    margin-right: 5px;
    margin-left: 5px;
}

.hint {
    position: relative;
    top: -9px;
    left: 24px;
    box-shadow: 2px 4px 5px rgba( 0, 0, 0, 0.15);
    width: 210px;
    padding: 6px 8px;
    line-height: 15px;
    background: #ffffff;
    border: 1px solid #FF6A00;
}

.hint:after,
.hint:before {
    border-style: solid;
    border-color: transparent;
    content: "";
    position: absolute;
    top: 13px;
    left: 0;
}

.hint:after {
    border-width: 6px;
    border-right-color: #ffffff;
    margin: -6px 0 0 -12px;
}

.hint:before {
    border-width: 7px;
    border-right-color: #FF6A00;
    margin: -7px 0 0 -14px;
}

.displayOff {
    display: none!important;
}


/* Search Team Name */

.nameResult {
    background: #ffba00;
}


/* Muay Thai Player Coloer */

.player-red {
    color: #B50000!important;
}

.player-blue {
    color: #0004DB!important;
}


/* AOS */

.AOSbox {
    border-top: #B3BCD2 solid 1px;
    padding: 0 2px;
}

#ParlayDetail .AOSbox {
    border-top: #B4B7BD solid 1px;
    padding: 2px 0;
}


/*combo parlay*/

.combo-list ul,
.combo-list li {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.combo-item .checkbox {
    background-color: #cfcfcf;
    padding: 4px 2px;
    font-weight: normal;
}

.combo-list .list,
.helpbox {
    padding: 4px 5px;
}

.combo-list .list,
.ComboParlay>div:first-child {
    display: block;
    background-image: url(../images/layout/L_menuBg.png?v=20160425);
    background-position: 3px 1px;
    background-repeat: no-repeat;
    line-height: 18px;
    padding-left: 24px;
}

.combo-list .list:hover,
.ComboParlay>div:first-child:hover,
.ComboParlay.on>div:first-child {
    cursor: pointer;
    color: #ff4e00;
}

.combo-list .on .list {
    background-position: 3px -152px;
}

.combo-list i,
.combo-list-item .TextStyle02 i,
.Game-3rd i {
    font-style: normal;
    font-weight: normal;
    padding-left: 3px;
}

.combo-list li+li {
    border-top: #cccccc 1px solid;
}

.combo-list .bet-range {
    font-weight: normal;
    text-align: right;
}

.n2 {
    text-align: right;
    padding: 0px 5px 4px 5px;
    display: none;
}

.combo-list .on .n2 {
    display: block;
}

.combo-list .on .Odds {
    margin-left: -15px;
}

.combo-list .on .UdrDogOddsClass {
    font-size: 15px;
}

.combo-list .Ltrbgov {
    background-color: #f5eeb8;
}

.multiple .tabstyle02 .Currency,
.txt-r {
    text-align: right;
}

.multiple .tabstyle02 th:first-child {
    font-size: 11px;
}

.multiple .tabstyle02 tr.stakePerBet th {
    font-size: 11px;
    background-image: url(../images/layout/L_menuBg.png?v=20160425);
    background-position: 3px -1px;
    background-repeat: no-repeat;
    line-height: 21px;
    cursor: pointer;
    padding: 1px 0;
}

.multiple .tabstyle02 tr.stakePerBet.Ltrbgov th {
    background-color: #f5eeb8;
}

.multiple .tabstyle02 .stakePerBetTitle {
    padding-left: 24px;
}

.multiple .tabstyle02 tr.stakePerBet.on th {
    background-position: 3px -154px;
}

.multiple .tabstyle02 tr.stakePerBet .Currency {
    display: none;
}

.multiple .tabstyle02 tr.stakePerBet.on .Currency {
    display: block;
    padding: 0px 5px 4px 5px;
}

.numberPad-wrap li+li {
    border-top: none;
}


/*Combo betlist*/

.multiple-box,
.combo-list-item {
    display: none;
}

.ComboParlay.on .multiple-box {
    display: block;
    border-top: #b1b1b1 1px solid;
    padding-top: 5px;
}

.combo-list-item {
    display: table;
    width: 100%;
    margin-bottom: 5px;
    border-collapse: collapse;
}

.combo-list-item .tab-tr {
    display: table-row;
}

.combo-list-item .tab-tr div {
    display: table-cell;
    vertical-align: top;
}

.ComboParlay>div:first-child {
    background-position: -3px -3px;
    padding: 0 0 3px 16px;
}

.ComboParlay.on>div:first-child {
    background-position: -2px -155px;
}


/*Game-Lotto*/

.Game-3rd .combo-list-item {
    margin-bottom: 0px;
}

.Game-3rd i {
    color: #000000;
}

.Game-3rd .combo-text span:before {
    content: ",";
    display: inline-block;
    margin-right: 4px;
    font-weight: normal;
    position: relative;
    right: 0;
}

.Game-3rd .combo-text span:first-child:before {
    display: none;
}


/*Fast Market*/

.MoreTable.fastMarket td+td {
    padding-left: 3px!important;
}

.titleArea {
    background-color: #5574A7;
    color: #fff;
    padding: 5px 2px 5px 5px;
    margin-bottom: 3px;
    position: relative;
    font-weight: bold;
}

.titleArea .restyle:after {
    clear: both;
    content: "";
    display: block;
}

.titleArea .restyle div+div+div {
    margin-left: 0;
}

.titleArea .restyle .button {
    margin-top: -1px;
    font-weight: lighter;
}

.titleArea .restyle .button .submenu {
    left: 4px;
    right: inherit;
}

.oddsTable .fastMarket thead>tr>td {
    padding: 3px 0;
    border: none;
}

.fastMarket td {
    padding: 3px 5px;
}

.fastMarket th {
    height: 16px;
}

.fastMarket .col-2 th {
    width: 50%;
    text-align: left;
}

.fastMarket .col-3 th {
    width: 33.333%;
    text-align: left;
}

.fastMarket .col-3 td {
    width: 33.333%;
}

.fastMarket .oddsTable {
    table-layout: fixed;
}

.fastMarket .left {
    max-width: 75%;
    line-height: 16px;
    text-align: left;
}
.fastMarket .left.big {
    max-width: 88%;
}

.fastMarket .tabtitle>.left { /*ariel-0201add*/
    max-width: 95%;
}

.fastMarket th .text-ellipsis {
    width: 95%;
    line-height: 16px;
}

.fastMarket .stress {
    color: #B50000;
}

.fastMarket .hint {
    font-weight: lighter;
    left: -209px;
    top: 18px;
}

.fastMarket .hint:before,
.fastMarket .hint:after {
    top: -7px;
    left: 222px;
}

.fastMarket .hint:before {
    border-color: transparent transparent #ff6a00 transparent;
}

.fastMarket .hint:after {
    border-color: transparent transparent #fff transparent;
    margin: -5px 0 0 -13px;
}


/* Live Page - Number Game */

[id="oTableContainer_161"] th font {
    display: block;
}


/* 1.0 - [MBC][789y][y987] */
/* ---------------------------------------------------- 
   Bet Slip 的 <span id="spChoiceClass">...</span> 之內
   於特定 Bet Type 需要時，程式會置入以下 html 
   
   置入的內容: 
   <span class="js-bet-name">A <em>&</em> B</span>

/* 繼承父層強弱隊配色 */
.js-bet-name {
	color: currentColor; 
}
/* 分隔符號 "&" 使用基本文字顏色 */
.js-bet-name em {
	font-style: normal; 
	color: #000;
	opacity: .75;
}
/* 繼承 Void 的顏色 */
.Void .js-bet-name em {
	color: inherit;
	opacity: 1;
}


/* 2019 Bet_Builder */
.betBuilder .multiple-box{
    padding: 0 4px;
}
.betBuilder .multiple-box .multiple{
    padding: 4px 0px;
}
.betBuilder .multiple-box .multiple:first-child {
    padding-top: 0;
}
.betBuilder .multiple-box .multiple .TextStyle08 {
    padding: 2px 0;
}
.betBuilder .otherBetDetial .comboListName:last-child {
    padding-bottom: 0;
}

/* Single */
.otherBetDetial {
    border: none;
    padding: 13px 0 0 0;
    margin: 0 0 0px 8px;
}
.otherBetDetial .comboListName {
    width: 100%;
    text-align: left;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
    word-wrap: break-word;
    word-break: normal;
    padding-bottom: 16px;
    padding-left: 9px;
    border-left: 2px solid #879dc2;
}
.otherBetDetial .comboListName:before {
    content: "";
    display: block;
    position: absolute;
    top: -0.3em;
    left: -4px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
    box-sizing: border-box;
    z-index: 3;
    border: 1px solid #879dc2;
    background-color: #ffffff;
}
.bgcpe .otherBetDetial .comboListName:before {
    background-color: #c6d4f1;
}
.bgcpelight .otherBetDetial .comboListName:before {
    background-color: #e4e4e4;
}
.otherBetDetial .comboListName:last-child {
    border-left-color: rgba(108, 165, 208, 0);
    padding-bottom: 0.5em;
}
.otherBetDetial .comboListName .description {
    margin-top: -9px;
    line-height: 16px;
    display: block;
    word-wrap: break-word;
    word-break: normal;
}
.otherBetDetial .comboListName .stacks {
    font-weight: normal;
    margin-left: 0.5em;
    position: relative;
    color: black;

}
.oddsDetail{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    padding-left: 18px;
    margin-bottom: 5px;
}
.oddsTable .defaultBtn{
    background: url(../images/layout/oddsTable_tag.png?v=20161107001) repeat-x 0 -40px;
}
.oddsTable .defaultBtn:hover{
    background: url(../images/layout/oddsTable_tag.png?v=20161107001) repeat-x 0 -60px;
}

.singleTicketStatus{
    border: 1px solid #8b8b8b;
    color: #8b8b8b;
    border-radius: 50px;
    padding: 0px 8px!important;
    vertical-align: middle;
    display: inline-block;
    line-height: 15px;
}


/* Bet List */
.multipleInfo .otherBetDetial {
    margin-left: 2px;
}


/* New Parlay */
.multipleInfo .multiple:last-child {
    border-bottom: #FFFFFF 1px solid;
    display: block;
}
.multiple.multipleInfo .TextStyle08 {
    padding: 2px 0;
}

.TextStyle08.singleTicketStatus{ margin: 5px 0 3px;}