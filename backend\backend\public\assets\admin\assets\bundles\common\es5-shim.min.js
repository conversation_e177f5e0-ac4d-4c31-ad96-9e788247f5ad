/*!
 * https://github.com/es-shims/es5-shim
 * @license es5-shim Copyright 2009-2015 by contributors, MIT License
 * see https://github.com/es-shims/es5-shim/blob/master/LICENSE
 */
(function(n,t){"use strict";typeof define=="function"&&define.amd?define(t):typeof exports=="object"?module.exports=t():n.returnExports=t()})(this,function(){var d=Array,n=d.prototype,f=Object,g=f.prototype,di=Function,pt=di.prototype,e=String,o=e.prototype,s=Number,ft=s.prototype,nt=n.slice,gi=n.splice,et=n.push,pu=n.unshift,nr=n.concat,wu=n.join,u=pt.call,wt=pt.apply,w=Math.max,ot=Math.min,bt=g.toString,kt=typeof Symbol=="function"&&typeof Symbol.toStringTag=="symbol",tr=Function.prototype.toString,bu=/^\s*class /,ir=function(n){try{var t=tr.call(n),i=t.replace(/\/\/.*\n/g,""),r=i.replace(/\/\*[.\s\S]*\*\//g,""),u=r.replace(/\n/mg," ").replace(/ {2}/g," ");return bu.test(u)}catch(f){return!1}},ku=function(n){try{return ir(n)?!1:(tr.call(n),!0)}catch(t){return!1}},du="[object Function]",gu="[object GeneratorFunction]",h=function(n){if(!n||typeof n!="function"&&typeof n!="object")return!1;if(kt)return ku(n);if(ir(n))return!1;var t=bt.call(n);return t===du||t===gu},dt,nf=RegExp.prototype.exec,tf=function(n){try{return nf.call(n),!0}catch(t){return!1}},rf="[object RegExp]",ti,ii,er,or,sr,hr,cr,lr,ri,ct,ui,fi,ar,vr,fu,b,yi,ou,pi,wi,su,hu,cu,bi,lu,au,vu,ki,yu;dt=function(n){return typeof n!="object"?!1:kt?tf(n):bt.call(n)===rf};var c,uf=String.prototype.valueOf,ff=function(n){try{return uf.call(n),!0}catch(t){return!1}},ef="[object String]";c=function(n){return typeof n=="string"?!0:typeof n!="object"?!1:kt?ff(n):bt.call(n)===ef};var gt=f.defineProperty&&function(){var n,t;try{n={};f.defineProperty(n,"x",{enumerable:!1,value:n});for(t in n)return!1;return n.x===n}catch(i){return!1}}(),t=function(n){var t;return t=gt?function(n,t,i,r){!r&&t in n||f.defineProperty(n,t,{configurable:!0,enumerable:!1,writable:!0,value:i})}:function(n,t,i,r){!r&&t in n||(n[t]=i)},function(i,r,u){for(var f in r)n.call(r,f)&&t(i,f,r[f],u)}}(g.hasOwnProperty),st=function(n){var t=typeof n;return n===null||t!=="object"&&t!=="function"},tt=s.isNaN||function(n){return n!==n},i={ToInteger:function(n){var t=+n;return tt(t)?t=0:t!==0&&t!==1/0&&t!==-(1/0)&&(t=(t>0||-1)*Math.floor(Math.abs(t))),t},ToPrimitive:function(n){var t,i,r;if(st(n))return n;if((i=n.valueOf,h(i)&&(t=i.call(n),st(t)))||(r=n.toString,h(r)&&(t=r.call(n),st(t))))return t;throw new TypeError;},ToObject:function(n){if(n==null)throw new TypeError("can't convert "+n+" to object");return f(n)},ToUint32:function(n){return n>>>0}},ni=function(){};t(pt,{bind:function(n){var t=this,r;if(!h(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);var u=nt.call(arguments,1),i,o=function(){if(this instanceof i){var r=wt.call(t,this,nr.call(u,nt.call(arguments)));return f(r)===r?r:this}return wt.call(t,n,nr.call(u,nt.call(arguments)))},s=w(0,t.length-u.length),e=[];for(r=0;r<s;r++)et.call(e,"$"+r);return i=di("binder","return function ("+wu.call(e,",")+"){ return binder.apply(this, arguments); }")(o),t.prototype&&(ni.prototype=t.prototype,i.prototype=new ni,ni.prototype=null),i}});var v=u.bind(g.hasOwnProperty),rr=u.bind(g.toString),k=u.bind(nt),of=wt.bind(nt),a=u.bind(o.slice),l=u.bind(o.split),sf=u.bind(o.indexOf),y=u.bind(et),hf=u.bind(g.propertyIsEnumerable),ur=u.bind(n.sort),ht=d.isArray||function(n){return rr(n)==="[object Array]"},cf=[].unshift(0)!==1;t(n,{unshift:function(){return pu.apply(this,arguments),this.length}},cf);t(d,{isArray:ht});var fr=f("a"),p=fr[0]!=="a"||!(0 in fr),it=function(n){var t=!0,i=!0,r=!1;if(n)try{n.call("foo",function(n,i,r){typeof r!="object"&&(t=!1)});n.call([1],function(){"use strict";i=typeof this=="string"},"x")}catch(u){r=!0}return!!n&&!r&&t&&i};t(n,{forEach:function(n){var u=i.ToObject(this),r=p&&c(this)?l(this,""):u,t=-1,e=i.ToUint32(r.length),f;if(arguments.length>1&&(f=arguments[1]),!h(n))throw new TypeError("Array.prototype.forEach callback must be a function");while(++t<e)t in r&&(typeof f=="undefined"?n(r[t],t,u):n.call(f,r[t],t,u))}},!it(n.forEach));t(n,{map:function(n){var u=i.ToObject(this),r=p&&c(this)?l(this,""):u,e=i.ToUint32(r.length),o=d(e),f,t;if(arguments.length>1&&(f=arguments[1]),!h(n))throw new TypeError("Array.prototype.map callback must be a function");for(t=0;t<e;t++)t in r&&(o[t]=typeof f=="undefined"?n(r[t],t,u):n.call(f,r[t],t,u));return o}},!it(n.map));t(n,{filter:function(n){var u=i.ToObject(this),f=p&&c(this)?l(this,""):u,s=i.ToUint32(f.length),o=[],r,e,t;if(arguments.length>1&&(e=arguments[1]),!h(n))throw new TypeError("Array.prototype.filter callback must be a function");for(t=0;t<s;t++)t in f&&(r=f[t],(typeof e=="undefined"?n(r,t,u):n.call(e,r,t,u))&&y(o,r));return o}},!it(n.filter));t(n,{every:function(n){var u=i.ToObject(this),r=p&&c(this)?l(this,""):u,e=i.ToUint32(r.length),f,t;if(arguments.length>1&&(f=arguments[1]),!h(n))throw new TypeError("Array.prototype.every callback must be a function");for(t=0;t<e;t++)if(t in r&&!(typeof f=="undefined"?n(r[t],t,u):n.call(f,r[t],t,u)))return!1;return!0}},!it(n.every));t(n,{some:function(n){var u=i.ToObject(this),r=p&&c(this)?l(this,""):u,e=i.ToUint32(r.length),f,t;if(arguments.length>1&&(f=arguments[1]),!h(n))throw new TypeError("Array.prototype.some callback must be a function");for(t=0;t<e;t++)if(t in r&&(typeof f=="undefined"?n(r[t],t,u):n.call(f,r[t],t,u)))return!0;return!1}},!it(n.some));ti=!1;n.reduce&&(ti=typeof n.reduce.call("es5",function(n,t,i,r){return r})=="object");t(n,{reduce:function(n){var e=i.ToObject(this),r=p&&c(this)?l(this,""):e,f=i.ToUint32(r.length),t,u;if(!h(n))throw new TypeError("Array.prototype.reduce callback must be a function");if(f===0&&arguments.length===1)throw new TypeError("reduce of empty array with no initial value");if(t=0,arguments.length>=2)u=arguments[1];else do{if(t in r){u=r[t++];break}if(++t>=f)throw new TypeError("reduce of empty array with no initial value");}while(1);for(;t<f;t++)t in r&&(u=n(u,r[t],t,e));return u}},!ti);ii=!1;n.reduceRight&&(ii=typeof n.reduceRight.call("es5",function(n,t,i,r){return r})=="object");t(n,{reduceRight:function(n){var f=i.ToObject(this),u=p&&c(this)?l(this,""):f,e=i.ToUint32(u.length),r,t;if(!h(n))throw new TypeError("Array.prototype.reduceRight callback must be a function");if(e===0&&arguments.length===1)throw new TypeError("reduceRight of empty array with no initial value");if(t=e-1,arguments.length>=2)r=arguments[1];else do{if(t in u){r=u[t--];break}if(--t<0)throw new TypeError("reduceRight of empty array with no initial value");}while(1);if(t<0)return r;do t in u&&(r=n(r,u[t],t,f));while(t--);return r}},!ii);er=n.indexOf&&[0,1].indexOf(1,2)!==-1;t(n,{indexOf:function(n){var r=p&&c(this)?l(this,""):i.ToObject(this),u=i.ToUint32(r.length),t;if(u===0)return-1;for(t=0,arguments.length>1&&(t=i.ToInteger(arguments[1])),t=t>=0?t:w(0,u+t);t<u;t++)if(t in r&&r[t]===n)return t;return-1}},er);or=n.lastIndexOf&&[0,1].lastIndexOf(0,-3)!==-1;t(n,{lastIndexOf:function(n){var r=p&&c(this)?l(this,""):i.ToObject(this),u=i.ToUint32(r.length),t;if(u===0)return-1;for(t=u-1,arguments.length>1&&(t=ot(t,i.ToInteger(arguments[1]))),t=t>=0?t:u-Math.abs(t);t>=0;t--)if(t in r&&n===r[t])return t;return-1}},or);sr=function(){var n=[1,2],t=n.splice();return n.length===2&&ht(t)&&t.length===0}();t(n,{splice:function(){return arguments.length===0?[]:gi.apply(this,arguments)}},!sr);hr=function(){var t={};return n.splice.call(t,0,0,1),t.length===1}();t(n,{splice:function(n,t){if(arguments.length===0)return[];var r=arguments;return this.length=w(i.ToInteger(this.length),0),arguments.length>0&&typeof t!="number"&&(r=k(arguments),r.length<2?y(r,this.length-n):r[1]=i.ToInteger(t)),gi.apply(this,r)}},!hr);cr=function(){var n=new d(1e5);return n[8]="x",n.splice(1,1),n.indexOf("x")===7}();lr=function(){var n=256,t=[];return t[n]="a",t.splice(n+1,0,"b"),t[n]==="a"}();t(n,{splice:function(n,t){for(var u=i.ToObject(this),b=[],s=i.ToUint32(u.length),p=i.ToInteger(n),l=p<0?w(s+p,0):ot(p,s),f=ot(w(i.ToInteger(t),0),s-l),r=0,o,a,h,c,d,g,y;r<f;)o=e(l+r),v(u,o)&&(b[r]=u[o]),r+=1;if(a=k(arguments,2),h=a.length,h<f){for(r=l,d=s-f;r<d;)o=e(r+f),c=e(r+h),v(u,o)?u[c]=u[o]:delete u[c],r+=1;for(r=s,g=s-f+h;r>g;)delete u[r-1],r-=1}else if(h>f)for(r=s-f;r>l;)o=e(r+f-1),c=e(r+h-1),v(u,o)?u[c]=u[o]:delete u[c],r-=1;for(r=l,y=0;y<a.length;++y)u[r]=a[y],r+=1;return u.length=s-f+h,b}},!cr||!lr);ri=n.join;try{ct=Array.prototype.join.call("123",",")!=="1,2,3"}catch(ao){ct=!0}ct&&t(n,{join:function(n){var t=typeof n=="undefined"?",":n;return ri.call(c(this)?l(this,""):this,t)}},ct);ui=[1,2].join(undefined)!=="1,2";ui&&t(n,{join:function(n){var t=typeof n=="undefined"?",":n;return ri.call(this,t)}},ui);fi=function(){for(var t=i.ToObject(this),r=i.ToUint32(t.length),n=0;n<arguments.length;)t[r+n]=arguments[n],n+=1;return t.length=r+n,r+n};ar=function(){var n={},t=Array.prototype.push.call(n,undefined);return t!==1||n.length!==1||typeof n[0]!="undefined"||!v(n,0)}();t(n,{push:function(){return ht(this)?et.apply(this,arguments):fi.apply(this,arguments)}},ar);vr=function(){var n=[],t=n.push(undefined);return t!==1||n.length!==1||typeof n[0]!="undefined"||!v(n,0)}();t(n,{push:fi},vr);t(n,{slice:function(){var n=c(this)?l(this,""):this;return of(n,arguments)}},p);var lf=function(){try{return[1,2].sort(null),[1,2].sort({}),!0}catch(n){}return!1}(),af=function(){try{return[1,2].sort(/a/),!1}catch(n){}return!0}(),vf=function(){try{return[1,2].sort(undefined),!0}catch(n){}return!1}();t(n,{sort:function(n){if(typeof n=="undefined")return ur(this);if(!h(n))throw new TypeError("Array.prototype.sort callback must be a function");return ur(this,n)}},lf||!vf||!af);var yf=!{toString:null}.propertyIsEnumerable("toString"),pf=function(){}.propertyIsEnumerable("prototype"),wf=!v("x","0"),ei=function(n){var t=n.constructor;return t&&t.prototype===n},bf={$window:!0,$console:!0,$parent:!0,$self:!0,$frame:!0,$frames:!0,$frameElement:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$external:!0},kf=function(){if(typeof window=="undefined")return!1;for(var n in window)try{!bf["$"+n]&&v(window,n)&&window[n]!==null&&typeof window[n]=="object"&&ei(window[n])}catch(t){return!0}return!1}(),df=function(n){if(typeof window=="undefined"||!kf)return ei(n);try{return ei(n)}catch(t){return!1}},yr=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],gf=yr.length,pr=function(n){return rr(n)==="[object Arguments]"},ne=function(n){return n!==null&&typeof n=="object"&&typeof n.length=="number"&&n.length>=0&&!ht(n)&&h(n.callee)},wr=pr(arguments)?pr:ne;t(f,{keys:function(n){var s=h(n),o=wr(n),l=n!==null&&typeof n=="object",w=l&&c(n),t,a,i,r,p,u,f;if(!l&&!s&&!o)throw new TypeError("Object.keys called on a non-object");if(t=[],a=pf&&s,w&&wf||o)for(i=0;i<n.length;++i)y(t,e(i));if(!o)for(r in n)a&&r==="prototype"||!v(n,r)||y(t,e(r));if(yf)for(p=df(n),u=0;u<gf;u++)f=yr[u],p&&f==="constructor"||!v(n,f)||y(t,f);return t}});var te=f.keys&&function(){return f.keys(arguments).length===2}(1,2),ie=f.keys&&function(){var n=f.keys(arguments);return arguments.length!==1||n.length!==1||n[0]!==1}(1),br=f.keys;t(f,{keys:function(n){return wr(n)?br(k(n)):br(n)}},!te||ie);var lt=new Date(-0xc782b5b342b24).getUTCMonth()!==0,at=new Date(-0x55d318d56a724),kr=new Date(14496624e5),re=at.toUTCString()!=="Mon, 01 Jan -45875 11:59:59 GMT",oi,si,ue=at.getTimezoneOffset();ue<-720?(oi=at.toDateString()!=="Tue Jan 02 -45875",si=!/^Thu Dec 10 2015 \d\d:\d\d:\d\d GMT[-\+]\d\d\d\d(?: |$)/.test(kr.toString())):(oi=at.toDateString()!=="Mon Jan 01 -45875",si=!/^Wed Dec 09 2015 \d\d:\d\d:\d\d GMT[-\+]\d\d\d\d(?: |$)/.test(kr.toString()));var hi=u.bind(Date.prototype.getFullYear),ci=u.bind(Date.prototype.getMonth),dr=u.bind(Date.prototype.getDate),rt=u.bind(Date.prototype.getUTCFullYear),ut=u.bind(Date.prototype.getUTCMonth),li=u.bind(Date.prototype.getUTCDate),fe=u.bind(Date.prototype.getUTCDay),gr=u.bind(Date.prototype.getUTCHours),nu=u.bind(Date.prototype.getUTCMinutes),tu=u.bind(Date.prototype.getUTCSeconds),ee=u.bind(Date.prototype.getUTCMilliseconds),ai=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],vi=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],iu=function(n,t){return dr(new Date(t,n,0))};t(Date.prototype,{getFullYear:function(){if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var n=hi(this);return n<0&&ci(this)>11?n+1:n},getMonth:function(){if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var t=hi(this),n=ci(this);return t<0&&n>11?0:n},getDate:function(){var r;if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var t=hi(this),i=ci(this),n=dr(this);return t<0&&i>11?i===12?n:(r=iu(0,t+1),r-n+1):n},getUTCFullYear:function(){if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var n=rt(this);return n<0&&ut(this)>11?n+1:n},getUTCMonth:function(){if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var t=rt(this),n=ut(this);return t<0&&n>11?0:n},getUTCDate:function(){var r;if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var t=rt(this),i=ut(this),n=li(this);return t<0&&i>11?i===12?n:(r=iu(0,t+1),r-n+1):n}},lt);t(Date.prototype,{toUTCString:function(){if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var u=fe(this),n=li(this),f=ut(this),e=rt(this),t=gr(this),i=nu(this),r=tu(this);return ai[u]+", "+(n<10?"0"+n:n)+" "+vi[f]+" "+e+" "+(t<10?"0"+t:t)+":"+(i<10?"0"+i:i)+":"+(r<10?"0"+r:r)+" GMT"}},lt||re);t(Date.prototype,{toDateString:function(){if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var t=this.getDay(),n=this.getDate(),i=this.getMonth(),r=this.getFullYear();return ai[t]+" "+vi[i]+" "+(n<10?"0"+n:n)+" "+r}},lt||oi);(lt||si)&&(Date.prototype.toString=function(){if(!this||!(this instanceof Date))throw new TypeError("this is not a Date object.");var o=this.getDay(),n=this.getDate(),s=this.getMonth(),h=this.getFullYear(),t=this.getHours(),i=this.getMinutes(),r=this.getSeconds(),u=this.getTimezoneOffset(),f=Math.floor(Math.abs(u)/60),e=Math.floor(Math.abs(u)%60);return ai[o]+" "+vi[s]+" "+(n<10?"0"+n:n)+" "+h+" "+(t<10?"0"+t:t)+":"+(i<10?"0"+i:i)+":"+(r<10?"0"+r:r)+" GMT"+(u>0?"-":"+")+(f<10?"0"+f:f)+(e<10?"0"+e:e)},gt&&f.defineProperty(Date.prototype,"toString",{configurable:!0,enumerable:!1,writable:!0}));var ru=-621987552e5,uu="-000001",oe=Date.prototype.toISOString&&new Date(ru).toISOString().indexOf(uu)===-1,se=Date.prototype.toISOString&&new Date(-1).toISOString()!=="1969-12-31T23:59:59.999Z",he=u.bind(Date.prototype.getTime);t(Date.prototype,{toISOString:function(){var n,i,t,r;if(!isFinite(this)||!isFinite(he(this)))throw new RangeError("Date.prototype.toISOString called on non-finite value.");for(n=rt(this),i=ut(this),n+=Math.floor(i/12),i=(i%12+12)%12,t=[i+1,li(this),gr(this),nu(this),tu(this)],n=(n<0?"-":n>9999?"+":"")+a("00000"+Math.abs(n),0<=n&&n<=9999?-4:-6),r=0;r<t.length;++r)t[r]=a("00"+t[r],-2);return n+"-"+k(t,0,2).join("-")+"T"+k(t,2).join(":")+"."+a("000"+ee(this),-3)+"Z"}},oe||se);fu=function(){try{return Date.prototype.toJSON&&new Date(NaN).toJSON()===null&&new Date(ru).toJSON().indexOf(uu)!==-1&&Date.prototype.toJSON.call({toISOString:function(){return!0}})}catch(n){return!1}}();fu||(Date.prototype.toJSON=function(){var n=f(this),r=i.ToPrimitive(n),t;if(typeof r=="number"&&!isFinite(r))return null;if(t=n.toISOString,!h(t))throw new TypeError("toISOString property is not callable");return t.call(n)});var ce=Date.parse("+033658-09-27T01:46:40.000Z")===1e15,le=!isNaN(Date.parse("2012-04-04T24:00:00.500Z"))||!isNaN(Date.parse("2012-11-31T23:59:59.000Z"))||!isNaN(Date.parse("2012-12-31T23:59:60.000Z")),ae=isNaN(Date.parse("2000-01-01T00:00:00.000Z"));(ae||le||!ce)&&(b=Math.pow(2,31)-1,yi=tt(new Date(1970,0,1,0,0,0,b+1).getTime()),Date=function(n){var i=function(r,u,f,o,s,h,c){var l=arguments.length,a,v,y,w,p;return this instanceof n?(v=h,y=c,yi&&l>=7&&c>b&&(w=Math.floor(c/b)*b,p=Math.floor(w/1e3),v+=p,y-=p*1e3),a=l===1&&e(r)===r?new n(i.parse(r)):l>=7?new n(r,u,f,o,s,v,y):l>=6?new n(r,u,f,o,s,v):l>=5?new n(r,u,f,o,s):l>=4?new n(r,u,f,o):l>=3?new n(r,u,f):l>=2?new n(r,u):l>=1?new n(r instanceof n?+r:r):new n):a=n.apply(this,arguments),st(a)||t(a,{constructor:i},!0),a},o=new RegExp("^(\\d{4}|[+-]\\d{6})(?:-(\\d{2})(?:-(\\d{2})(?:T(\\d{2}):(\\d{2})(?::(\\d{2})(?:(\\.\\d{1,}))?)?(Z|(?:([-+])(\\d{2}):(\\d{2})))?)?)?)?$"),h=[0,31,59,90,120,151,181,212,243,273,304,334,365],u=function(n,t){var i=t>1?1:0;return h[t]+Math.floor((n-1969+i)/4)-Math.floor((n-1901+i)/100)+Math.floor((n-1601+i)/400)+365*(n-1970)},c=function(t){var u=0,i=t,f,r;return yi&&i>b&&(f=Math.floor(i/b)*b,r=Math.floor(f/1e3),u+=r,i-=r*1e3),s(new n(1970,0,1,0,0,u,i))},r,f;for(r in n)v(n,r)&&(i[r]=n[r]);return t(i,{now:n.now,UTC:n.UTC},!0),i.prototype=n.prototype,t(i.prototype,{constructor:i},!0),f=function(t){var i=o.exec(t);if(i){var e=s(i[1]),f=s(i[2]||1)-1,h=s(i[3]||1)-1,y=s(i[4]||0),l=s(i[5]||0),a=s(i[6]||0),v=Math.floor(s(i[7]||0)*1e3),k=Boolean(i[4]&&!i[8]),p=i[9]==="-"?1:-1,w=s(i[10]||0),b=s(i[11]||0),r,d=l>0||a>0||v>0;return y<(d?24:25)&&l<60&&a<60&&v<1e3&&f>-1&&f<12&&w<24&&b<60&&h>-1&&h<u(e,f+1)-u(e,f)&&(r=((u(e,f)+h)*24+y+w*p)*60,r=((r+l+b*p)*60+a)*1e3+v,k&&(r=c(r)),-864e13<=r&&r<=864e13)?r:NaN}return n.parse.apply(this,arguments)},t(i,{parse:f}),i}(Date));Date.now||(Date.now=function(){return(new Date).getTime()});var ve=ft.toFixed&&(8e-5.toFixed(3)!=="0.000"||.9.toFixed(0)!=="1"||1.255.toFixed(2)!=="1.25"||1000000000000000128..toFixed(0)!=="1000000000000000128"),r={base:1e7,size:6,data:[0,0,0,0,0,0],multiply:function(n,t){for(var u=-1,i=t;++u<r.size;)i+=n*r.data[u],r.data[u]=i%r.base,i=Math.floor(i/r.base)},divide:function(n){for(var i=r.size,t=0;--i>=0;)t+=r.data[i],r.data[i]=Math.floor(t/n),t=t%n*r.base},numToString:function(){for(var t=r.size,n="",i;--t>=0;)(n!==""||t===0||r.data[t]!==0)&&(i=e(r.data[t]),n===""?n=i:n+=a("0000000",0,7-i.length)+i);return n},pow:function eu(n,t,i){return t===0?i:t%2==1?eu(n,t-1,i*n):eu(n*n,t/2,i)},log:function(n){for(var i=0,t=n;t>=4096;)i+=12,t/=4096;while(t>=2)i+=1,t/=2;return i}},ye=function(n){var t,i,h,u,f,l,o,c;if(t=s(n),t=tt(t)?0:Math.floor(t),t<0||t>20)throw new RangeError("Number.toFixed called with invalid number of decimals");if(i=s(this),tt(i))return"NaN";if(i<=-1e21||i>=1e21)return e(i);if(h="",i<0&&(h="-",i=-i),u="0",i>1e-21)if(f=r.log(i*r.pow(2,69,1))-69,l=f<0?i*r.pow(2,-f,1):i/r.pow(2,f,1),l*=4503599627370496,f=52-f,f>0){for(r.multiply(0,l),o=t;o>=7;)r.multiply(1e7,0),o-=7;for(r.multiply(r.pow(10,o,1),0),o=f-1;o>=23;)r.divide(8388608),o-=23;r.divide(1<<o);r.multiply(1,1);r.divide(2);u=r.numToString()}else r.multiply(0,l),r.multiply(1<<-f,0),u=r.numToString()+a("0.00000000000000000000",2,2+t);return t>0?(c=u.length,u=c<=t?h+a("0.0000000000000000000",0,t-c+2)+u:h+a(u,0,c-t)+"."+a(u,c-t)):u=h+u,u};t(ft,{toFixed:ye},ve);ou=function(){try{return 1..toPrecision(undefined)==="1"}catch(n){return!0}}();pi=ft.toPrecision;t(ft,{toPrecision:function(n){return typeof n=="undefined"?pi.call(this):pi.call(this,n)}},ou);"ab".split(/(?:ab)*/).length!==2||".".split(/(.?)(.?)/).length!==4||"tesst".split(/(s)*/)[1]==="t"||"test".split(/(?:)/,-1).length!==4||"".split(/.?/).length||".".split(/()()/).length>1?function(){var n=typeof/()??/.exec("")[1]=="undefined",t=Math.pow(2,32)-1;o.split=function(r,u){var o=String(this),c;if(typeof r=="undefined"&&u===0)return[];if(!dt(r))return l(this,r,u);var e=[],p=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(r.sticky?"y":""),h=0,w,f,v,b,s=new RegExp(r.source,p+"g");for(n||(w=new RegExp("^"+s.source+"$(?!\\s)",p)),c=typeof u=="undefined"?t:i.ToUint32(u),f=s.exec(o);f;){if(v=f.index+f[0].length,v>h&&(y(e,a(o,h,f.index)),!n&&f.length>1&&f[0].replace(w,function(){for(var n=1;n<arguments.length-2;n++)typeof arguments[n]=="undefined"&&(f[n]=void 0)}),f.length>1&&f.index<o.length&&et.apply(e,k(f,1)),b=f[0].length,h=v,e.length>=c))break;s.lastIndex===f.index&&s.lastIndex++;f=s.exec(o)}return h===o.length?(b||!s.test(""))&&y(e,""):y(e,a(o,h)),e.length>c?k(e,0,c):e}}():"0".split(void 0,0).length&&(o.split=function(n,t){return typeof n=="undefined"&&t===0?[]:l(this,n,t)});wi=o.replace;su=function(){var n=[];return"x".replace(/x(.)?/g,function(t,i){y(n,i)}),n.length===1&&typeof n[0]=="undefined"}();su||(o.replace=function(n,t){var r=h(t),u=dt(n)&&/\)[*?]/.test(n.source),i;return r&&u?(i=function(i){var u=arguments.length,f=n.lastIndex,r;return n.lastIndex=0,r=n.exec(i)||[],n.lastIndex=f,y(r,arguments[u-2],arguments[u-1]),t.apply(this,r)},wi.call(this,n,i)):wi.call(this,n,t)});hu=o.substr;cu="".substr&&"0b".substr(-1)!=="b";t(o,{substr:function(n,t){var i=n;return n<0&&(i=w(this.length+n,0)),hu.call(this,i,t)}},cu);var vt="\t\n\x0b\f\r \u00a0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029\ufeff",yt="["+vt+"]",pe=new RegExp("^"+yt+yt+"*"),we=new RegExp(yt+yt+"*$"),be=o.trim&&(vt.trim()||!"\u200b".trim());t(o,{trim:function(){if(typeof this=="undefined"||this===null)throw new TypeError("can't convert "+this+" to object");return e(this).replace(pe,"").replace(we,"")}},be);bi=u.bind(String.prototype.trim);lu=o.lastIndexOf&&"abc\u3042\u3044".lastIndexOf("\u3042\u3044",2)!==-1;t(o,{lastIndexOf:function(n){var u;if(typeof this=="undefined"||this===null)throw new TypeError("can't convert "+this+" to object");for(var f=e(this),o=e(n),h=arguments.length>1?s(arguments[1]):NaN,l=tt(h)?Infinity:i.ToInteger(h),c=ot(w(l,0),f.length),r=o.length,t=c+r;t>0;)if(t=w(0,t-r),u=sf(a(f,t,c+r),o),u!==-1)return t+u;return-1}},lu);au=o.lastIndexOf;t(o,{lastIndexOf:function(){return au.apply(this,arguments)}},o.lastIndexOf.length!==1);(parseInt(vt+"08")!==8||parseInt(vt+"0x16")!==22)&&(parseInt=function(n){var t=/^[\-+]?0[xX]/;return function(i,r){var u=bi(i),f=s(r)||(t.test(u)?16:10);return n(u,f)}}(parseInt));1/parseFloat("-0")!=-Infinity&&(parseFloat=function(n){return function(t){var i=bi(t),r=n(i);return r===0&&a(i,0,1)==="-"?-0:r}}(parseFloat));String(new RangeError("test"))!=="RangeError: test"&&(vu=function(){var n,t;if(typeof this=="undefined"||this===null)throw new TypeError("can't convert "+this+" to object");return(n=this.name,typeof n=="undefined"?n="Error":typeof n!="string"&&(n=e(n)),t=this.message,typeof t=="undefined"?t="":typeof t!="string"&&(t=e(t)),!n)?t:t?n+": "+t:n},Error.prototype.toString=vu);gt&&(ki=function(n,t){if(hf(n,t)){var i=Object.getOwnPropertyDescriptor(n,t);i.enumerable=!1;Object.defineProperty(n,t,i)}},ki(Error.prototype,"message"),Error.prototype.message!==""&&(Error.prototype.message=""),ki(Error.prototype,"name"));String(/a/mig)!=="/a/gim"&&(yu=function(){var n="/"+this.source+"/";return this.global&&(n+="g"),this.ignoreCase&&(n+="i"),this.multiline&&(n+="m"),n},RegExp.prototype.toString=yu)});
/*!
 * https://github.com/es-shims/es5-shim
 * @license es5-shim Copyright 2009-2015 by contributors, MIT License
 * see https://github.com/es-shims/es5-shim/blob/master/LICENSE
 */
(function(n,t){"use strict";typeof define=="function"&&define.amd?define(t):typeof exports=="object"?module.exports=t():n.returnExports=t()})(this,function(){var t=Function.call,n=Object.prototype,i=t.bind(n.hasOwnProperty),d=t.bind(n.propertyIsEnumerable),g=t.bind(n.toString),a,v,o,s,r=i(n,"__defineGetter__"),h,y,p,u,w,l,b,k,f,e;if(r&&(a=t.bind(n.__defineGetter__),v=t.bind(n.__defineSetter__),o=t.bind(n.__lookupGetter__),s=t.bind(n.__lookupSetter__)),Object.getPrototypeOf||(Object.getPrototypeOf=function(t){var i=t.__proto__;return i||i===null?i:g(t.constructor)==="[object Function]"?t.constructor.prototype:t instanceof Object?n:null}),h=function(n){try{return n.sentinel=0,Object.getOwnPropertyDescriptor(n,"sentinel").value===0}catch(t){return!1}},Object.defineProperty&&(y=h({}),p=typeof document=="undefined"||h(document.createElement("div")),p&&y||(u=Object.getOwnPropertyDescriptor)),(!Object.getOwnPropertyDescriptor||u)&&(w="Object.getOwnPropertyDescriptor called on a non-object: ",Object.getOwnPropertyDescriptor=function(t,f){var e,a,l,h,c;if(typeof t!="object"&&typeof t!="function"||t===null)throw new TypeError(w+t);if(u)try{return u.call(Object,t,f)}catch(v){}return i(t,f)?(e={enumerable:d(t,f),configurable:!0},r&&(a=t.__proto__,l=t!==n,l&&(t.__proto__=n),h=o(t,f),c=s(t,f),l&&(t.__proto__=a),h||c))?(h&&(e.get=h),c&&(e.set=c),e):(e.value=t[f],e.writable=!0,e):e}),Object.getOwnPropertyNames||(Object.getOwnPropertyNames=function(n){return Object.keys(n)}),!Object.create){var c,nt=!({__proto__:null}instanceof Object),tt=function(){if(!document.domain)return!1;try{return!!new ActiveXObject("htmlfile")}catch(n){return!1}},it=function(){var t,n;return n=new ActiveXObject("htmlfile"),n.write("<script><\/script>"),n.close(),t=n.parentWindow.Object.prototype,n=null,t},rt=function(){var n=document.createElement("iframe"),t=document.body||document.documentElement,i;return n.style.display="none",t.appendChild(n),n.src="javascript:",i=n.contentWindow.Object.prototype,t.removeChild(n),n=null,i};c=nt||typeof document=="undefined"?function(){return{__proto__:null}}:function(){var n=tt()?it():rt(),t;return delete n.constructor,delete n.hasOwnProperty,delete n.propertyIsEnumerable,delete n.isPrototypeOf,delete n.toLocaleString,delete n.toString,delete n.valueOf,t=function(){},t.prototype=n,c=function(){return new t},new t};Object.create=function(n,t){var i,r=function(){};if(n===null)i=c();else{if(typeof n!="object"&&typeof n!="function")throw new TypeError("Object prototype may only be an Object or null");r.prototype=n;i=new r;i.__proto__=n}return t!==void 0&&Object.defineProperties(i,t),i}}if(l=function(n){try{return Object.defineProperty(n,"sentinel",{}),"sentinel"in n}catch(t){return!1}},Object.defineProperty&&(b=l({}),k=typeof document=="undefined"||l(document.createElement("div")),b&&k||(f=Object.defineProperty,e=Object.defineProperties)),!Object.defineProperty||f){var ut="Property description must be an object: ",ft="Object.defineProperty called on non-object: ",et="getters & setters can not be defined on this javascript engine";Object.defineProperty=function(t,i,u){if(typeof t!="object"&&typeof t!="function"||t===null)throw new TypeError(ft+t);if(typeof u!="object"&&typeof u!="function"||u===null)throw new TypeError(ut+u);if(f)try{return f.call(Object,t,i,u)}catch(h){}if("value"in u)if(r&&(o(t,i)||s(t,i))){var e=t.__proto__;t.__proto__=n;delete t[i];t[i]=u.value;t.__proto__=e}else t[i]=u.value;else{if(!r&&("get"in u||"set"in u))throw new TypeError(et);"get"in u&&a(t,i,u.get);"set"in u&&v(t,i,u.set)}return t}}(!Object.defineProperties||e)&&(Object.defineProperties=function(n,t){if(e)try{return e.call(Object,n,t)}catch(i){}return Object.keys(t).forEach(function(i){i!=="__proto__"&&Object.defineProperty(n,i,t[i])}),n});Object.seal||(Object.seal=function(n){if(Object(n)!==n)throw new TypeError("Object.seal can only be called on Objects.");return n});Object.freeze||(Object.freeze=function(n){if(Object(n)!==n)throw new TypeError("Object.freeze can only be called on Objects.");return n});try{Object.freeze(function(){})}catch(pt){Object.freeze=function(n){return function(t){return typeof t=="function"?t:n(t)}}(Object.freeze)}Object.preventExtensions||(Object.preventExtensions=function(n){if(Object(n)!==n)throw new TypeError("Object.preventExtensions can only be called on Objects.");return n});Object.isSealed||(Object.isSealed=function(n){if(Object(n)!==n)throw new TypeError("Object.isSealed can only be called on Objects.");return!1});Object.isFrozen||(Object.isFrozen=function(n){if(Object(n)!==n)throw new TypeError("Object.isFrozen can only be called on Objects.");return!1});Object.isExtensible||(Object.isExtensible=function(n){var t,r;if(Object(n)!==n)throw new TypeError("Object.isExtensible can only be called on Objects.");for(t="";i(n,t);)t+="?";return n[t]=!0,r=i(n,t),delete n[t],r})});