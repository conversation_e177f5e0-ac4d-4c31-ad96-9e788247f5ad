﻿@import url("/assets/styles/base/roboto-fonts.css");html{touch-action:manipulation;}body{font-family:Roboto,Arial,Tahoma,helvetica,sans-serif;font-size:13px;color:#333;margin:0;}input[type=text]{border:1px solid #cecece;font-size:11px;height:22px;line-height:22px;text-align:left;width:127px;border-radius:1px;padding:0 8px;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.3);-khtml-box-shadow:0 1px 1px rgba(0,0,0,.3);-moz-box-shadow:0 1px 1px rgba(0,0,0,.3);-o-box-shadow:0 1px 1px rgba(0,0,0,.3);box-shadow:0 1px 1px rgba(0,0,0,.3);-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}input[type=button],.btn,input[type=button]#dSubmit,input[type=button].btnSubmit,input[type=submit]#dSubmit,input[type=submit].btnSubmit,.buttonSubmit,.btn.btnMain{border:none;cursor:pointer;height:28px;outline:none;margin-right:8px;padding-left:16px;padding-right:16px;-webkit-appearance:none;-webkit-border-radius:1px;-khtml-border-radius:1px;-moz-border-radius:1px;-o-border-radius:1px;border-radius:1px;-webkit-box-shadow:0 2px 2px rgba(0,0,0,.3);-khtml-box-shadow:0 2px 2px rgba(0,0,0,.3);-moz-box-shadow:0 2px 2px rgba(0,0,0,.3);-o-box-shadow:0 2px 2px rgba(0,0,0,.3);box-shadow:0 2px 2px rgba(0,0,0,.3);-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}input[disabled][type=button],[disabled].btn,input[disabled][type=button]#dSubmit,input[disabled][type=submit]#dSubmit,input[disabled][type=submit].btnSubmit,[disabled].buttonSubmit{opacity:.4;cursor:default;}input[type=button],.btn{background-color:#e1e1e1;color:#212121;}input[type=button]:hover,.btn:hover{color:#212121;background-color:#d4d4d4;}input[type=button]#dSubmit,input[type=button].btnSubmit,input[type=submit]#dSubmit,input[type=submit].btnSubmit,.buttonSubmit{background:#0b599c;color:#fff;}input[type=button]#dSubmit:hover,input[type=button]#dSubmit:active,input[type=button]#dSubmit:focus,input[type=button].btnSubmit:hover,input[type=button].btnSubmit:active,input[type=button].btnSubmit:focus,input[type=submit]#dSubmit:hover,input[type=submit]#dSubmit:active,input[type=submit]#dSubmit:focus,input[type=submit].btnSubmit:hover,input[type=submit].btnSubmit:active,input[type=submit].btnSubmit:focus,.buttonSubmit:hover,.buttonSubmit:active,.buttonSubmit:focus{background-color:#094b84;}input[type=reset],input[type=button].btn-reset{background-color:#f6731b;color:#fff;outline:none;}input[type=reset]:hover,input[type=button].btn-reset:hover{color:#fff;background-color:#ee6509;}.btn.btnMain{background-color:#1bac69;color:#fff;}.btn.btnMain:hover,.btn.btnMain:active,.btn.btnMain:focus{background-color:#18965c;color:#fff;}#diverrmsg{width:680px !important;}.tblRpt{width:680px;background-color:#f8f0e5;border-collapse:collapse;margin-bottom:7px;font-size:13px;margin-top:12px;}.tblRpt td{border:1px solid #cecece;line-height:26px;height:30px;padding:2px 4px;-webkit-box-sizing:border-box;-khtml-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;}.RptHeader{background:#666;color:#fff;font-weight:bold;}.RptHeader td{padding:0 8px;}.l{text-align:left;}.r{text-align:right;}.c{text-align:center;}.b{font-weight:bold;}.bg_eb2 td{width:25%;}.MaskLoadingDiv{background:url(../Images/loader3.gif);}#title_header{color:#9d1c1c;font-weight:bold;height:39px;line-height:40px;text-transform:uppercase;min-width:650px;border-bottom:1px solid #cecece;}.hidden{display:none;}