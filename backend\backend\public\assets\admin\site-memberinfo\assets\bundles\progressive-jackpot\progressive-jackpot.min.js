"use strict";var ProductMultiSelectionOption=function(){function n(n,t,i,r){n===void 0&&(n="dropdown-product");t===void 0&&(t=!0);i===void 0&&(i="product-ids");r===void 0&&(r=null);this.dropdownId=n;this.validateBeforeSubmit=t;this.productsInputId=i;this.onChange=r}return n}(),ProductMultiSelectionComponent=function(){function n(n){var t=this;this.options=new ProductMultiSelectionOption;this.render=function(n){n===void 0&&(n=new ProductMultiSelectionOption);t.options=t.$.extend({},t.options,n);t.$dropdownProduct=t.$("#"+t.options.dropdownId);var i=t.convertAttributeToBool(t.$dropdownProduct.data("has-any-product"));return t.$hiddenInput=t.$("#"+t.options.productsInputId),t.$dropdownProduct.multiselect({maxHeight:300,numberDisplayed:1,nonSelectedText:i?t.$dropdownProduct.data("label-select-products"):t.$dropdownProduct.data("label-there-is-no-product"),nSelectedText:" "+t.$dropdownProduct.data("label-selected"),allSelectedText:t.$dropdownProduct.data("label-all-products"),includeSelectAllOption:i,onChange:t.onChange,onSelectAll:t.onChange,language:t.$dropdownProduct.data("language")}),t.$multiSelectButton=t.$dropdownProduct.parent().find(".multiselect.btn"),t.$dropdownProduct};this.onChange=function(){t.validate();t.$hiddenInput.val(t.getSelectedItems().join(","));t.options.onChange&&t.options.onChange()};this.validate=function(){t.options.validateBeforeSubmit&&t.$multiSelectButton&&t.$dropdownProduct.find("option:selected").length&&t.$multiSelectButton.hasClass("error")&&t.$multiSelectButton.removeClass("error")};this.getSelectedItems=function(){var n=[],i=t.$dropdownProduct.find("option:selected"),r=t.$;return i.each(function(){n.push([r(this).val()])}),n};this.convertAttributeToBool=function(n){return!!n&&n.toString().toLowerCase()==="true"};this.$=n}return n}(),ProgressiveJackpot,progressiveJackpot;ProgressiveJackpot=function(){function n(n,t,i){var r=this;this.init=function(){r.cacheDOMElements();r.productMultiSelectionComponent.render();r.$iconInformation.click(r.showHandbook)};this.cacheDOMElements=function(){r.$dropdownProduct=r.$("#dropdown-product");r.$iconInformation=r.$("#icon-information");r.handbookUrl=r.$("#HandbookUrl").val();r.handbookPopupTitle=r.$("#HandbookPopupTitle").val()};this.showHandbook=function(){r.topWindow.popupManager.open(r.handbookUrl,r.handbookPopupTitle,900,600)};this.$=n;this.topWindow=t;this.productMultiSelectionComponent=i}return n}();progressiveJackpot=new ProgressiveJackpot($,window.top,new ProductMultiSelectionComponent($));$(function(){progressiveJackpot.init()});