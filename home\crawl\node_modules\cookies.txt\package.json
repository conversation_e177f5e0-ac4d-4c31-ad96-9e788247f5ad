{"name": "cookies.txt", "description": "This is a wget cookies.txt formart parser for nodejs and request.", "tags": ["wget", "cookie", "cookies.txt", "http", "request"], "scripts": {"test": "node test/test.js"}, "version": "0.1.2", "author": "Inaction <<EMAIL>> (http://inaction.me)", "repository": {"type": "git", "url": "git://github.com/mxfli/node-cookies.txt.git"}, "bugs": {"url": "https://github.com/mxfli/node-cookies.txt/issues"}, "engines": {"node": ">= 0.4.1"}, "main": "index.js", "dependencies": {}, "devDependencies": {}}