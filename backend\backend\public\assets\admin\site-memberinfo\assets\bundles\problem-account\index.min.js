"use strict";var __extends=this&&this.__extends||function(){var n=function(t,i){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i])},n(t,i)};return function(t,i){function r(){this.constructor=t}n(t,i);t.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}(),Attribute=function(){function n(n,t){this.Name=n;this.Value=t}return n}(),ElementWrapper=function(){function n(n,t,i){i===void 0&&(i=[]);var r=this;this.ToString=function(){var n="";return r.Attributes.forEach(function(t){var i=t.Name,r=t.Value;n+=i+"='"+r+"' "}),"<"+r.Tag+" "+n+">"+r.Content+"<\/"+r.Tag+">"};this.Tag=n;this.Attributes=i;this.Content=t}return n}(),SpanWrapper=function(n){function t(t,i){return t===void 0&&(t=""),i===void 0&&(i=[]),n.call(this,"span",t,i)||this}return __extends(t,n),t}(ElementWrapper),DivWrapper=function(n){function t(t,i){return i===void 0&&(i=[]),n.call(this,"div",t,i)||this}return __extends(t,n),t}(ElementWrapper),UnoderListWrapper=function(n){function t(t,i){return i===void 0&&(i=[]),n.call(this,"ul",t,i)||this}return __extends(t,n),t}(ElementWrapper),ListItemWrapper=function(n){function t(t,i){return i===void 0&&(i=[]),n.call(this,"li",t,i)||this}return __extends(t,n),t}(ElementWrapper),AnchorWrapper=function(n){function t(t,i){return i===void 0&&(i=[]),n.call(this,"a",t,i)||this}return __extends(t,n),t}(ElementWrapper),ColumnWrapper=function(n){function t(t,i){return i===void 0&&(i=[]),n.call(this,"td",t,i)||this}return __extends(t,n),t}(ElementWrapper),RowWrapper=function(n){function t(t,i){return i===void 0&&(i=[]),n.call(this,"tr",t,i)||this}return __extends(t,n),t}(ElementWrapper),SingleSelectionDropdownOption,SingleSelectionDropdownComponent,ProblemAccount,problemAccount;SingleSelectionDropdownOption=function(){function n(n,t,i,r,u){n===void 0&&(n="dropdown-single");t===void 0&&(t="UserSelectedId");i===void 0&&(i=null);r===void 0&&(r=!1);u===void 0&&(u=!1);this.dropdownId=n;this.inputId=t;this.onChange=i;this.enableClickableOptGroups=r;this.enableCollapsibleOptGroups=u}return n}();SingleSelectionDropdownComponent=function(){function n(n){var t=this;this.options=new SingleSelectionDropdownOption;this.render=function(n){var i,r,u;n===void 0&&(n=new SingleSelectionDropdownOption);t.options=t.$.extend({},t.options,n);i=t.$("#"+t.options.dropdownId);t.$dropdown=i;r=t.$("#"+t.options.inputId);t.$hiddenInput=r;u=t;t.selectedValue=u.getSelectedItem();t.$dropdown.multiselect({multiple:!1,maxHeight:300,numberDisplayed:1,includeSelectAllOption:!1,onChange:t.onchange,enableClickableOptGroups:t.options.enableClickableOptGroups,enableCollapsibleOptGroups:t.options.enableCollapsibleOptGroups,optionClass:t.optionClass});t.options.enableClickableOptGroups&&t.options.enableCollapsibleOptGroups&&t.$dropdown.next().find(".multiselect-container li").removeClass("hidden")};this.getSelectedItem=function(){var n=[],i=t.$dropdown.find("option:selected"),r=t.$;return i.each(function(){n.push([r(this).val()])}),n.length?n[0].toString():""};this.updateSelectedValue=function(){t.$hiddenInput.val(t.selectedValue)};this.onchange=function(){t.selectedValue=t.getSelectedItem();t.options.onChange&&t.options.onChange()};this.optionClass=function(n){if(t.$(n).data("isnested")===1)return"multiselect-nested-item"};this.changeSelection=function(n){t.$dropdown.val(n);t.$hiddenInput.val(n);t.$dropdown.multiselect("select",[n]);t.$dropdown.multiselect("refresh")};this.$=n}return n}();"use strict";var DateRangePickerOption=function(){function n(n,t,i){n===void 0&&(n="daterange-picker");t===void 0&&(t="report-form");i===void 0&&(i=null);this.selectorId=n;this.reportFormId=t;this.onChange=i;this.selectorId=n;this.reportFormId=t;this.onChange=i}return n}(),DateRangePickerComponent=function(){function n(n){var t=this;this.options=new DateRangePickerOption;this.quickChooseType={specificDates:7};this.isCompleteInitDateRangePicker=!1;this.render=function(n){n===void 0&&(n=new DateRangePickerOption);t.options=t.$.extend({},t.options,n);var i=t.$("#"+t.options.selectorId),u=i.data("currentdate"),f=i.data("mindate"),r=i.data("maxdate");return t.$reportForm=t.$("#"+t.options.reportFormId),i.nexDatePicker({datePickerFormat:{dateFormat:"MM/DD/YYYY"},selectedItem:i.data("selecteddaterange"),minDate:new Date(f),maxDate:r===""?null:r,currentDate:new Date(u),onSelectDatePicker:function(){t.options.onChange&&t.options.onChange()},onQuickChooseChange:function(n){t.isCompleteInitDateRangePicker&&n!==t.quickChooseType.specificDates&&t.$reportForm.submit()}}),t.isCompleteInitDateRangePicker=!0,i};this.$=n}return n}(),BaseList=function(){function n(n,t,i,r,u,f){f===void 0&&(f=null);var e=this;this.displayNoneClass="display-none";this.export=function(){var n=e.isIndirectDownlineOfActor?e.$viewedNameDirectUpline.val():null;e.$("#FromDate").val(e.$fromDate.val());e.$("#ToDate").val(e.$toDate.val());e.$("#UserSelectedReviewedStatusId").val(parseInt(e.$dropdownStatus.find("option:selected").val()));e.$("#UplineName").val(n);e.$("#SearchRoleId").val(e.levelId);e.$("#UserName").val(e.$username.val());e.$("#export-excel-form").submit()};this.clickCheckboxAll=function(n){var i=e.$tblReport.find(".chk"),t;if(n){for(t=0;t<i.length;t++)e.$(i[t]).removeClass("icon-uncheck"),e.$(i[t]).addClass("icon-checkbox");return}for(t=0;t<i.length;t++)e.$(i[t]).removeClass("icon-checkbox"),e.$(i[t]).addClass("icon-uncheck")};this.loadData=function(){var n=e.isIndirectDownlineOfActor?e.$viewedNameDirectUpline.val():null;e.reportParam.UplineName=n;e.reportParam.SearchRoleId=e.levelId;e.reportParam.UserSelectedReviewedStatusId=parseInt(e.$dropdownStatus.find("option:selected").val());e.reportParam.UserName=e.$username.val();e.reportParam.FromDate=e.$fromDate.val();e.reportParam.ToDate=e.$toDate.val();e.$.ajax({method:"GET",data:e.reportParam,url:e.getProblemAccountUrl,cache:!1,success:function(n){e.renderCustomerList(n)},complete:e.$.unblockUI})};this.$=n;this.topWindow=t;this.reviewStatusComponent=f===null?new SingleSelectionDropdownComponent(n):f;this.levelName=r;this.levelNameLowerCase=r.toLowerCase();this.levelId=i;this.dateRangePickerComponent=u}return n.prototype.init=function(n,t){var i=this;this.indexManager=n;this.isIndirectDownlineOfActor=this.levelId<this.indexManager.ActorLevel-1;this.directDownlineRoleId=this.levelId-1;this.cacheDOMElements();this.customerListHelper=new CustomerListHelper(n.viewResources);this.$downlineObject=t;this.reviewStatusComponent.render(new SingleSelectionDropdownOption("dropdown-reviewstatus-"+this.levelNameLowerCase,"UserSelectedReviewedStatusId-"+this.levelName));this.dateRangePickerComponent.render(new DateRangePickerOption("daterange-picker-"+this.levelNameLowerCase,""));this.$tblReport.stickyTableHeaders();this.$("#btn-submit-"+this.levelNameLowerCase).off("click").on("click",function(){i.viewList()});this.$("#icon-excel-"+this.levelNameLowerCase).off("click").on("click",function(){i.export()});this.$username.keypress(function(n){n.which==13&&(i.viewList(),n.preventDefault())});this.$viewedNameDirectUpline.keypress(function(n){n.which==13&&(i.viewList(),n.preventDefault())})},n.prototype.cacheDOMElements=function(){var n=this;this.$updateButton=this.$(".update-"+this.levelNameLowerCase);this.$usernameAutoSuggestionPath=this.$("#usernameAutoSuggestionPath");this.$username=this.$("#txt-username-"+this.levelNameLowerCase);this.$uplineName=this.$("#txt-uplinename-"+this.levelNameLowerCase);this.$dropdownStatus=this.$("#dropdown-reviewstatus-"+this.levelNameLowerCase);this.getProblemAccountUrl=this.$("#getProblemAccountUrl").val();this.$tblReport=this.$("#tbl-"+this.levelNameLowerCase);this.$fromDate=this.$("#from-date-"+this.levelNameLowerCase);this.$toDate=this.$("#to-date-"+this.levelNameLowerCase);this.$viewedNameDirectUpline=this.$("#txt-uplinename-"+(this.levelId===siteInfrastructure.Role.Agent.Id?"master":"agent"));this.reportParam=this.isIndirectDownlineOfActor?new ReportParam:{UserName:this.$username.val(),SearchRoleId:this.levelId,UplineName:"",UserSelectedReviewedStatusId:parseInt(this.$("#UserSelectedReviewedStatusId").val()),FromDate:new Date,ToDate:new Date};this.$updateButton.on("click",function(){n.update()});this.$("#chkAll-"+this.levelNameLowerCase).on("click touchend",function(){n.clickCheckboxAll(!0)});this.$("#chkNone-"+this.levelNameLowerCase).on("click touchend",function(){n.clickCheckboxAll(!1)})},n.prototype.viewDownlines=function(n){var t=this.$(n.currentTarget),r=parseInt(t.attr("cust-id")),i=t.html();this.$("#tab"+this.directDownlineRoleId).click();this.$downlineObject.$fromDate.val(this.$fromDate.val());this.$downlineObject.$toDate.val(this.$toDate.val());this.$downlineObject.dateRangePickerComponent.render();this.$downlineObject.$viewedNameDirectUpline.val(i);this.$downlineObject.$username.val("");this.$downlineObject.reviewStatusComponent.changeSelection(this.reportParam.UserSelectedReviewedStatusId);this.$downlineObject.viewList()},n.prototype.bindReportEvents=function(){var n=this;this.$tblReport.find(".view-downline").click(function(t){return n.viewDownlines(t)});this.$tblReport.find(".chk").click(function(t){return n.checkbox(t)})},n.prototype.checkbox=function(n){var t=this.$(n.currentTarget),i=t.hasClass("icon-uncheck");i?(t.removeClass("icon-uncheck"),t.addClass("icon-checkbox")):(t.removeClass("icon-checkbox"),t.addClass("icon-uncheck"))},n.prototype.update=function(){for(var t=this.$tblReport.find(".icon-checkbox"),r=[],u=[],i="",n=0;n<t.length;n++)r.push(this.$(t[n]).attr("cust-id")),u.push(this.$(t[n]).attr("username")),i=this.$(t[n]).attr("cust-role");if(u.length!==0&&r.length!==0&&i!==""){this.$("#Ids").val(r);this.$("#UserNames").val(u);this.$("#RoleId").val(i);var f=this.indexManager.viewResources.UpdatePopupTitle,e=parseInt(i)==siteInfrastructure.Role.Member.Id?250:300;this.openPopup(f,"",500,e)}},n.prototype.openPopup=function(n,t,i,r){var f=this,u;i===void 0&&(i=480);r===void 0&&(r=300);u=this.$("target").attr("action");this.topWindow.indexManager.reloadProblemAccount=function(){f.viewList()};this.topWindow.popupManager.open("",n,i,r,t,null,null,null,null,{preUrl:u})},n.prototype.renderCustomerList=function(n){n&&(this.$tblReport.removeClass(this.displayNoneClass),this.$tblReport.find("> tbody").empty().html(this.customerListHelper.buildBody(n,this.levelId,this.indexManager.ActorLevel,this.reportParam)),this.$(window).trigger("resize.stickyTableHeaders"),this.bindReportEvents())},n.prototype.reloadPage=function(){var n=this.reportParam.UserName;this.$username.val(n);this.isIndirectDownlineOfActor&&this.$viewedNameDirectUpline.val(n);this.viewList()},n.prototype.viewList=function(){this.$.blockUI();this.loadData()},n}(),ViewResources=function(){function n(n){this.$=n;this.NoInformationIsAvailable=this.$("#NoInformationIsAvailable").val();this.UpdatePopupTitle=this.$("#UpdatePopupTitle").val();this.TotalColumnMaster=parseInt(this.$("#TotalColumnMaster").val());this.TotalColumnAgent=parseInt(this.$("#TotalColumnAgent").val());this.TotalColumnMember=parseInt(this.$("#TotalColumnMember").val())}return n}(),ReportParam=function(){function n(){this.UserName="";this.SearchRoleId=0;this.UplineName="";this.UserSelectedReviewedStatusId=0}return n}(),SiteInfrastructure=function(){function n(){this.Role={Super:{Id:4,Name:"Super"},Master:{Id:3,Name:"Master"},Agent:{Id:2,Name:"Agent"},Member:{Id:1,Name:"Member"}}}return n}(),siteInfrastructure=new SiteInfrastructure,CustomerListHelper=function(){function n(n){this.viewResources=n}return n.prototype.buildCommonAttributesJson=function(n,t,i){return[{Name:"cust-id",Value:n},{Name:"username",Value:t},{Name:"cust-role",Value:i}]},n.prototype.buildNoInfoContent=function(n){return"<tr><td colspan='"+n+"' align='center' class='no-info'>"+this.viewResources.NoInformationIsAvailable+"<\/td><\/tr>"},n.prototype.buildColumnAttributesJson=function(n,t,i,r){var e=siteInfrastructure.Role.Member.Id,u=t===e,o=i-1==t,s=n.CustId,f=n.UserName,h=n.StatusName,c=n.NickName,l=n.FirstName,a=n.LastName,v=n.CreatedDateStr,y=n.ReviewStatus,p=n.Count,w=n.CanEdit,b=this.buildCommonAttributesJson(s,f,t);return[{Content:r.toString(),Attributes:[{Name:"class",Value:"text-center"}],Visibile:!0},{Content:f,Attributes:[{Name:"class",Value:u?"":" link view-downline"}].concat(b),Visibile:!0},{Content:c,Attributes:[],Visibile:o},{Content:l,Attributes:[],Visibile:!0},{Content:a,Attributes:[],Visibile:!0},{Content:h,Attributes:[{Name:"class",Value:"text-center"}],Visibile:!0},{Content:v,Attributes:[{Name:"class",Value:"text-center"}],Visibile:!0},{Content:p,Attributes:[{Name:"class",Value:"text-right"}],Visibile:!u},{Content:y,Attributes:[{Name:"class",Value:"text-center"}],Visibile:!0},{Content:this.buildActionColumnOnDesktop(n,t,w),Attributes:[{Name:"class",Value:"text-center"}],Visibile:!0}]},n.prototype.buildActionColumnOnDesktop=function(n,t,i){var r={Attributes:[{Name:"class",Value:"chk icon-uncheck"}],Visibile:i},f=[{Content:"",Attributes:[{Name:"title",Value:"Check"}].concat(r.Attributes),Visibile:r.Visibile}],e=[{Name:"data-toggle",Value:"tooltip"},{Name:"data-placement",Value:"top"}].concat(this.buildCommonAttributesJson(n.CustId,n.UserName,t)),u="";return f.forEach(function(n){n.Visibile&&(u+=new SpanWrapper(n.Content,n.Attributes.concat(e).map(function(n){return new Attribute(n.Name,n.Value)})).ToString())}),u},n.prototype.buildAccountStatusCssClass=function(n){var t="";switch(n.Status){case 2:case 4:t=n.StatusNameEN;break;case 3:t=n.StatusNameEN+n.ClosedReasonName;break;default:t=n.Locked===0?n.StatusNameEN:this.buildLockedAccessCssClass(n.Locked)}return"row-customer RowBg"+t},n.prototype.buildLockedAccessCssClass=function(n){switch(n){case 1:return"LockedFalsedOTPSC";case 2:return"LockedFalsePassword";default:return""}},n.prototype.buildBody=function(n,t,i){var r;if(n.length===0)return t===siteInfrastructure.Role.Member.Id?this.buildNoInfoContent(this.viewResources.TotalColumnMember):t===siteInfrastructure.Role.Agent.Id?this.buildNoInfoContent(this.viewResources.TotalColumnAgent):this.buildNoInfoContent(this.viewResources.TotalColumnMaster);var u=1,f="",o=function(r){var o="";e.buildColumnAttributesJson(n[r],t,i,u).forEach(function(n){n.Visibile&&(o+=new ColumnWrapper(n.Content,n.Attributes.map(function(n){return new Attribute(n.Name,n.Value)})).ToString())});f+=new RowWrapper(o,[new Attribute("class",e.buildAccountStatusCssClass(n[r])),new Attribute("cust-id",n[r].CustId)]).ToString()},e=this;for(r=0;r<n.length;r++,u++)o(r);return f},n}();ProblemAccount=function(){function n(n,t,i){i===void 0&&(i=null);var r=this;this.init=function(){r.ActorLevel=parseInt(r.$("#ActorLevel").val());r.render()};this.render=function(){r.initDownlineLists();var n=parseInt(r.$("#SearchRoleId").val());if(n>0){r.$("#tab"+n).click();switch(n){case siteInfrastructure.Role.Master.Id:r.masterList.reloadPage();break;case siteInfrastructure.Role.Agent.Id:r.masterList&&r.masterList.reloadPage();r.agentList.reloadPage();break;case siteInfrastructure.Role.Member.Id:r.masterList&&r.masterList.reloadPage();r.agentList&&r.agentList.reloadPage();r.memberList.reloadPage()}}};this.$=n;this.viewResources=i===null?new ViewResources(n):i;this.topWindow=t}return n.prototype.initDownlineLists=function(){this.memberList=new BaseList(this.$,this.topWindow,siteInfrastructure.Role.Member.Id,siteInfrastructure.Role.Member.Name,new DateRangePickerComponent($));this.memberList.init(this,null);this.ActorLevel>=siteInfrastructure.Role.Master.Id&&(this.agentList=new BaseList(this.$,this.topWindow,siteInfrastructure.Role.Agent.Id,siteInfrastructure.Role.Agent.Name,new DateRangePickerComponent($)),this.agentList.init(this,this.memberList));this.ActorLevel>=siteInfrastructure.Role.Super.Id&&(this.masterList=new BaseList(this.$,this.topWindow,siteInfrastructure.Role.Master.Id,siteInfrastructure.Role.Master.Name,new DateRangePickerComponent($)),this.masterList.init(this,this.agentList))},n}();problemAccount=new ProblemAccount($,window.top);$(function(){problemAccount.init()});