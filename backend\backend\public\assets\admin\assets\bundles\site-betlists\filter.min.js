!function(n){"use strict";function i(n,t){for(var i=0;i<n.length;++i)t(n[i],i)}function t(t,i){this.$select=n(t);this.$select.attr("data-placeholder")&&(i.nonSelectedText=this.$select.data("placeholder"));this.options=this.mergeOptions(n.extend({},i,this.$select.data()));this.originalOptions=this.$select.clone()[0].options;this.query="";this.searchTimeout=null;this.lastToggledInput=null;this.options.multiple=this.$select.attr("multiple")==="multiple";this.options.onChange=n.proxy(this.options.onChange,this);this.options.onDropdownShow=n.proxy(this.options.onDropdownShow,this);this.options.onDropdownHide=n.proxy(this.options.onDropdownHide,this);this.options.onDropdownShown=n.proxy(this.options.onDropdownShown,this);this.options.onDropdownHidden=n.proxy(this.options.onDropdownHidden,this);this.options.onInitialized=n.proxy(this.options.onInitialized,this);this.buildContainer();this.buildButton();this.buildDropdown();this.buildSelectAll();this.buildDropdownOptions();this.buildFilter();this.updateButtonText();this.updateSelectAll(!0);this.options.disableIfEmpty&&n("option",this.$select).length<=0&&this.disable();this.$select.addClass("hide").after(this.$container);this.options.onInitialized(this.$select,this.$container)}typeof ko!="undefined"&&ko.bindingHandlers&&!ko.bindingHandlers.multiselect&&(ko.bindingHandlers.multiselect={after:["options","value","selectedOptions","enable","disable"],init:function(t,i,r){var u=n(t),l=ko.toJS(i()),s,h,c,f,e,o;u.multiselect(l);r.has("options")&&(s=r.get("options"),ko.isObservable(s)&&ko.computed({read:function(){s();setTimeout(function(){var n=u.data("multiselect");n&&n.updateOriginalOptions();u.multiselect("rebuild")},1)},disposeWhenNodeIsRemoved:t}));r.has("value")&&(h=r.get("value"),ko.isObservable(h)&&ko.computed({read:function(){h();setTimeout(function(){u.multiselect("refresh")},1)},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0}));r.has("selectedOptions")&&(c=r.get("selectedOptions"),ko.isObservable(c)&&ko.computed({read:function(){c();setTimeout(function(){u.multiselect("refresh")},1)},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0}));f=function(n){setTimeout(function(){n?u.multiselect("enable"):u.multiselect("disable")})};r.has("enable")&&(e=r.get("enable"),ko.isObservable(e)?ko.computed({read:function(){f(e())},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0}):f(e));r.has("disable")&&(o=r.get("disable"),ko.isObservable(o)?ko.computed({read:function(){f(!o())},disposeWhenNodeIsRemoved:t}).extend({rateLimit:100,notifyWhenChangesStop:!0}):f(!o));ko.utils.domNodeDisposal.addDisposeCallback(t,function(){u.multiselect("destroy")})},update:function(t,i){var r=n(t),u=ko.toJS(i());r.multiselect("setOptions",u);r.multiselect("rebuild")}});t.prototype={defaults:{buttonText:function(t,i){if(this.disabledText.length>0&&(this.disableIfEmpty||i.prop("disabled"))&&t.length==0)return this.disabledText;if(t.length===0)return this.nonSelectedText;if(this.allSelectedText&&t.length===n("option",n(i)).length&&n("option",n(i)).length!==1&&this.multiple)return this.selectAllNumber?this.allSelectedText+" ("+t.length+")":this.allSelectedText;if(t.length>this.numberDisplayed)return this.language&&this.language.toLowerCase()==="vi-vn"?this.nSelectedText+" "+t.length:t.length+" "+this.nSelectedText;var r="",u=this.delimiterText;return t.each(function(){var t=n(this).attr("label")!==undefined?n(this).attr("label"):n(this).text();r+=t+u}),r.substr(0,r.length-2)},buttonTitle:function(t){if(t.length===0)return this.nonSelectedText;var i="",r=this.delimiterText;return t.each(function(){var t=n(this).attr("label")!==undefined?n(this).attr("label"):n(this).text();i+=t+r}),i.substr(0,i.length-2)},optionLabel:function(t){return n(t).attr("label")||n(t).text()},optionClass:function(t){return n(t).attr("class")||""},onChange:function(){},onDropdownShow:function(){},onDropdownHide:function(){},onDropdownShown:function(){},onDropdownHidden:function(){},onSelectAll:function(){},onInitialized:function(){},enableHTML:!1,buttonClass:"btn btn-default",inheritClass:!1,buttonWidth:"auto",buttonContainer:'<div class="btn-group" />',dropRight:!1,dropUp:!1,selectedClass:"active",maxHeight:!1,checkboxName:!1,includeSelectAllOption:!1,includeSelectAllIfMoreThan:0,selectAllText:" Select all",selectAllValue:"multiselect-all",selectAllName:!1,selectAllNumber:!0,selectAllJustVisible:!0,enableFiltering:!1,enableCaseInsensitiveFiltering:!1,enableFullValueFiltering:!1,enableClickableOptGroups:!1,enableCollapsibelOptGroups:!1,filterPlaceholder:"Search",filterBehavior:"text",includeFilterClearBtn:!0,preventInputChangeEvent:!1,nonSelectedText:"None selected",nSelectedText:"selected",allSelectedText:"All selected",numberDisplayed:3,disableIfEmpty:!1,disabledText:"",delimiterText:", ",language:"en-US",templates:{button:'<button type="button" class="multiselect dropdown-toggle" data-toggle="dropdown"><span class="multiselect-selected-text"><\/span> <b class="caret"><\/b><\/button>',ul:'<ul class="multiselect-container dropdown-menu"><\/ul>',filter:'<li class="multiselect-item filter"><div class="input-group"><span class="input-group-addon"><i class="glyphicon glyphicon-search"><\/i><\/span><input class="form-control multiselect-search" type="text"><\/div><\/li>',filterClearBtn:'<span class="input-group-btn"><button class="btn btn-default multiselect-clear-filter" type="button"><i class="glyphicon glyphicon-remove-circle"><\/i><\/button><\/span>',li:'<li><a tabindex="0"><label><\/label><\/a><\/li>',divider:'<li class="multiselect-item divider"><\/li>',liGroup:'<li class="multiselect-item multiselect-group"><label><\/label><\/li>'}},constructor:t,buildContainer:function(){this.$container=n(this.options.buttonContainer);this.$container.on("show.bs.dropdown",this.options.onDropdownShow);this.$container.on("hide.bs.dropdown",this.options.onDropdownHide);this.$container.on("shown.bs.dropdown",this.options.onDropdownShown);this.$container.on("hidden.bs.dropdown",this.options.onDropdownHidden)},buildButton:function(){this.$button=n(this.options.templates.button).addClass(this.options.buttonClass);this.$select.attr("class")&&this.options.inheritClass&&this.$button.addClass(this.$select.attr("class"));this.$select.prop("disabled")?this.disable():this.enable();this.options.buttonWidth&&this.options.buttonWidth!=="auto"&&(this.$button.css({width:this.options.buttonWidth,overflow:"hidden","text-overflow":"ellipsis"}),this.$container.css({width:this.options.buttonWidth}));var t=this.$select.attr("tabindex");t&&this.$button.attr("tabindex",t);this.$container.prepend(this.$button)},buildDropdown:function(){if(this.$ul=n(this.options.templates.ul),this.options.dropRight&&this.$ul.addClass("pull-right"),this.options.maxHeight&&this.$ul.css({"max-height":this.options.maxHeight+"px","overflow-y":"auto","overflow-x":"hidden"}),this.options.dropUp){var t=Math.min(this.options.maxHeight,n('option[data-role!="divider"]',this.$select).length*26+n('option[data-role="divider"]',this.$select).length*19+(this.options.includeSelectAllOption?26:0)+(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering?44:0)),i=t+34;this.$ul.css({"max-height":t+"px","overflow-y":"auto","overflow-x":"hidden","margin-top":"-"+i+"px"})}this.$container.append(this.$ul)},buildDropdownOptions:function(){this.$select.children().each(n.proxy(function(t,i){var r=n(i),u=r.prop("tagName").toLowerCase();r.prop("value")!==this.options.selectAllValue&&(u==="optgroup"?this.createOptgroup(i):u==="option"&&(r.data("role")==="divider"?this.createDivider():this.createOptionValue(i)))},this));n("li input",this.$ul).on("change",n.proxy(function(t){var i=n(t.target),u=i.prop("checked")||!1,o=i.val()===this.options.selectAllValue;this.options.selectedClass&&(u?i.closest("li").addClass(this.options.selectedClass):i.closest("li").removeClass(this.options.selectedClass));var s=i.val(),r=this.getOptionByValue(s),f=n("option",this.$select).not(r),e=n("input",this.$container).not(i);if(o)u?this.selectAll(this.options.selectAllJustVisible):this.deselectAll(this.options.selectAllJustVisible);else{u?(r.prop("selected",!0),this.options.multiple?r.prop("selected",!0):(this.options.selectedClass&&n(e).closest("li").removeClass(this.options.selectedClass),n(e).prop("checked",!1),f.prop("selected",!1),n(e).removeProp("selected"),f.removeProp("selected"),this.$button.click()),this.options.selectedClass==="active"&&f.closest("a").css("outline","")):(r.prop("selected",!1),r.removeProp("selected"));this.options.onChange(r,u)}return this.$select.change(),this.updateButtonText(),this.updateSelectAll(),this.options.preventInputChangeEvent?!1:void 0},this));n("li a",this.$ul).on("mousedown",function(n){if(n.shiftKey)return!1});n("li a",this.$ul).on("touchstart click",n.proxy(function(t){var i,u,f,r,h,e,o,c,l,s;if(t.stopPropagation(),i=n(t.target),t.shiftKey&&this.options.multiple){if(i.is("label")&&(t.preventDefault(),i=i.find("input"),i.prop("checked",!i.prop("checked"))),u=i.prop("checked")||!1,this.lastToggledInput!==null&&this.lastToggledInput!==i)for(f=i.closest("li").index(),r=this.lastToggledInput.closest("li").index(),f>r&&(h=r,r=f,f=h),++r,e=this.$ul.find("li").slice(f,r).find("input"),e.prop("checked",u),this.options.selectedClass&&e.closest("li").toggleClass(this.options.selectedClass,u),o=0,c=e.length;o<c;o++)l=n(e[o]),s=this.getOptionByValue(l.val()),s.prop("selected",u),u||s.removeProp("selected");i.trigger("change")}i.is("input")&&!i.closest("li").is(".multiselect-item")&&(this.lastToggledInput=i);i.blur()},this));this.$container.off("keydown.multiselect").on("keydown.multiselect",n.proxy(function(t){var r,i,f,u;if(!n('input[type="text"]',this.$container).is(":focus"))if(t.keyCode===9&&this.$container.hasClass("open"))this.$button.click();else{if(r=n(this.$container).find("li:not(.divider):not(.disabled) a").filter(":visible"),!r.length)return;i=r.index(r.filter(":focus"));t.keyCode===38&&i>0?i--:t.keyCode===40&&i<r.length-1?i++:~i||(i=0);f=r.eq(i);f.focus();(t.keyCode===32||t.keyCode===13)&&(u=f.find("input"),u.prop("checked",!u.prop("checked")),u.change());t.stopPropagation();t.preventDefault()}},this));if(this.options.enableClickableOptGroups&&this.options.multiple)n("li.multiselect-group",this.$ul).on("click",n.proxy(function(t){t.stopPropagation();console.log("test");var f=n(t.target).parent(),e=f.nextUntil("li.multiselect-group"),o=e.filter(":visible:not(.disabled)"),i=!0,u=o.find("input"),r=[];u.each(function(){i=i&&n(this).prop("checked");r.push(n(this).val())});i?this.deselect(r,!1):this.select(r,!1);this.options.onChange(u,!i)},this));if(this.options.enableCollapsibleOptGroups){n("li.multiselect-group input",this.$ul).off();n("li.multiselect-group",this.$ul).siblings().not("li.multiselect-group, li.multiselect-all",this.$ul).each(function(){n(this).toggleClass("hidden",!0)});n("li.multiselect-group",this.$ul).on("click",n.proxy(function(n){n.stopPropagation()},this));n("li.multiselect-group > a > b",this.$ul).on("click",n.proxy(function(t){t.stopPropagation();var u=n(t.target).closest("li"),r=u.nextUntil("li.multiselect-group"),i=!0;r.each(function(){i=i&&n(this).hasClass("hidden")});r.toggleClass("hidden",!i)},this));n("li.multiselect-group > a > input",this.$ul).on("change",n.proxy(function(t){t.stopPropagation();var u=n(t.target).closest("li"),f=u.nextUntil("li.multiselect-group",":not(.disabled)"),r=f.find("input"),i=!0;r.each(function(){i=i&&n(this).prop("checked")});r.prop("checked",!i).trigger("change")},this));n("li.multiselect-group",this.$ul).each(function(){var i=n(this).nextUntil("li.multiselect-group",":not(.disabled)"),r=i.find("input"),t=!0;r.each(function(){t=t&&n(this).prop("checked")});n(this).find("input").prop("checked",t)});n("li:not(.multiselect-group) input",this.$ul).on("change",n.proxy(function(t){t.stopPropagation();var r=n(t.target).closest("li"),u=r.prevUntil("li.multiselect-group",":not(.disabled)"),f=r.nextUntil("li.multiselect-group",":not(.disabled)"),e=u.find("input"),o=f.find("input"),i=n(t.target).prop("checked");e.each(function(){i=i&&n(this).prop("checked")});o.each(function(){i=i&&n(this).prop("checked")});r.prevAll(".multiselect-group").first().find("input").prop("checked",i)},this));n("li.multiselect-all",this.$ul).css("background","#f3f3f3").css("border-bottom","1px solid #eaeaea");n("li.multiselect-group > a, li.multiselect-all > a > label.checkbox",this.$ul).css("padding","3px 20px 3px 35px");n("li.multiselect-group > a > input",this.$ul).css("margin","4px 0px 5px -20px")}},createOptionValue:function(t){var r=n(t),i,e;r.is(":selected")&&r.prop("selected",!0);var o=this.options.optionLabel(t),c=this.options.optionClass(t),s=r.val(),h=this.options.multiple?"checkbox":"radio",f=n(this.options.templates.li),u=n("label",f);u.addClass(h);f.addClass(c);this.options.enableHTML?u.html(" "+o):u.text(" "+o);i=n("<input/>").attr("type",h);this.options.checkboxName&&i.attr("name",this.options.checkboxName);u.prepend(i);e=r.prop("selected")||!1;i.val(s);s===this.options.selectAllValue&&(f.addClass("multiselect-item multiselect-all"),i.parent().parent().addClass("multiselect-all"));u.attr("title",r.attr("title"));this.$ul.append(f);r.is(":disabled")&&i.attr("disabled","disabled").prop("disabled",!0).closest("a").attr("tabindex","-1").closest("li").addClass("disabled");i.prop("checked",e);e&&this.options.selectedClass&&i.closest("li").addClass(this.options.selectedClass)},createDivider:function(){var t=n(this.options.templates.divider);this.$ul.append(t)},createOptgroup:function(t){var u,i;if(this.options.enableCollapsibleOptGroups){var f=n(t).attr("label"),e=n(t).attr("value"),r=this.options.multiple?n('<li class="multiselect-item multiselect-group"><a href="javascript:void(0);"><input type="checkbox" value="'+e+'"/><b> '+f+'<b class="caret"><\/b><\/b><\/a><\/li>'):n('<li class="multiselect-item multiselect-group"><a href="javascript:void(0);"><b> '+f+'<b class="caret"><\/b><\/b><\/a><\/li>');this.options.enableClickableOptGroups&&r.addClass("multiselect-group-clickable");this.$ul.append(r);n(t).is(":disabled")&&r.addClass("disabled");n("option",t).each(n.proxy(function(n,t){this.createOptionValue(t)},this))}else u=n(t).prop("label"),i=n(this.options.templates.liGroup),this.options.enableHTML?n("label",i).html(u):n("label",i).text(u),this.options.enableClickableOptGroups&&i.addClass("multiselect-group-clickable"),this.$ul.append(i),n(t).is(":disabled")&&i.addClass("disabled"),n("option",t).each(n.proxy(function(n,t){this.createOptionValue(t)},this))},buildSelectAll:function(){var r,t,i;typeof this.options.selectAllValue=="number"&&(this.options.selectAllValue=this.options.selectAllValue.toString());r=this.hasSelectAll();!r&&this.options.includeSelectAllOption&&this.options.multiple&&n("option",this.$select).length>this.options.includeSelectAllIfMoreThan&&(this.options.includeSelectAllDivider&&this.$ul.prepend(n(this.options.templates.divider)),t=n(this.options.templates.li),n("label",t).addClass("checkbox"),this.options.enableHTML?n("label",t).html(" "+this.options.selectAllText):n("label",t).text(" "+this.options.selectAllText),this.options.selectAllName?n("label",t).prepend('<input type="checkbox" name="'+this.options.selectAllName+'" />'):n("label",t).prepend('<input type="checkbox" />'),i=n("input",t),i.val(this.options.selectAllValue),t.addClass("multiselect-item multiselect-all"),i.parent().parent().addClass("multiselect-all"),this.$ul.prepend(t),i.prop("checked",!1))},buildFilter:function(){var i,t;if((this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering)&&(i=Math.max(this.options.enableFiltering,this.options.enableCaseInsensitiveFiltering),this.$select.find("option").length>=i)){if(this.$filter=n(this.options.templates.filter),n("input",this.$filter).attr("placeholder",this.options.filterPlaceholder),this.options.includeFilterClearBtn){t=n(this.options.templates.filterClearBtn);t.on("click",n.proxy(function(){clearTimeout(this.searchTimeout);this.$filter.find(".multiselect-search").val("");n("li",this.$ul).show().removeClass("filter-hidden");this.updateSelectAll()},this));this.$filter.find(".input-group").append(t)}this.$ul.prepend(this.$filter);this.$filter.val(this.query).on("click",function(n){n.stopPropagation()}).on("input keydown",n.proxy(function(t){t.which===13&&t.preventDefault();clearTimeout(this.searchTimeout);this.searchTimeout=this.asyncFunction(n.proxy(function(){if(this.query!==t.target.value){this.query=t.target.value;var i,r;n.each(n("li",this.$ul),n.proxy(function(t,u){var o=n("input",u).length>0?n("input",u).val():"",s=n("label",u).text(),e="",f,h;this.options.filterBehavior==="text"?e=s:this.options.filterBehavior==="value"?e=o:this.options.filterBehavior==="both"&&(e=s+"\n"+o);o!==this.options.selectAllValue&&s&&(f=!1,this.options.enableCaseInsensitiveFiltering&&(e=e.toLowerCase(),this.query=this.query.toLowerCase()),this.options.enableFullValueFiltering&&this.options.filterBehavior!=="both"?(h=e.trim().substring(0,this.query.length),this.query.indexOf(h)>-1&&(f=!0)):e.indexOf(this.query)>-1&&(f=!0),n(u).toggle(f).toggleClass("filter-hidden",!f),n(u).hasClass("multiselect-group")?(i=u,r=f):(f&&n(i).show().removeClass("filter-hidden"),!f&&r&&n(u).show().removeClass("filter-hidden")))},this))}this.updateSelectAll()},this),300,this)},this))}},destroy:function(){this.$container.remove();this.$select.show();this.$select.data("multiselect",null)},refresh:function(){var t=n.map(n("li input",this.$ul),n);n("option",this.$select).each(n.proxy(function(i,r){for(var f=n(r),o=f.val(),u,e=t.length;0<e--;)if(o===(u=t[e]).val()){f.is(":selected")?(u.prop("checked",!0),this.options.selectedClass&&u.closest("li").addClass(this.options.selectedClass)):(u.prop("checked",!1),this.options.selectedClass&&u.closest("li").removeClass(this.options.selectedClass));f.is(":disabled")?u.attr("disabled","disabled").prop("disabled",!0).closest("li").addClass("disabled"):u.prop("disabled",!1).closest("li").removeClass("disabled");break}},this));this.updateButtonText();this.updateSelectAll()},select:function(t,i){var u,r,f,e;for(n.isArray(t)||(t=[t]),u=0;u<t.length;u++)if((r=t[u],r!==null&&r!==undefined)&&(f=this.getOptionByValue(r),e=this.getInputByValue(r),f!==undefined&&e!==undefined)&&(this.options.multiple||this.deselectAll(!1),this.options.selectedClass&&e.closest("li").addClass(this.options.selectedClass),e.prop("checked",!0),f.prop("selected",!0),i))this.options.onChange(f,!0);this.updateButtonText();this.updateSelectAll()},clearSelection:function(){this.deselectAll(!1);this.updateButtonText();this.updateSelectAll()},deselect:function(t,i){var f,r,u,e;for(n.isArray(t)||(t=[t]),f=0;f<t.length;f++)if((r=t[f],r!==null&&r!==undefined)&&(u=this.getOptionByValue(r),e=this.getInputByValue(r),u!==undefined&&e!==undefined)&&(this.options.selectedClass&&e.closest("li").removeClass(this.options.selectedClass),e.prop("checked",!1),u.prop("selected",!1),u.removeProp("selected"),i))this.options.onChange(u,!1);this.updateButtonText();this.updateSelectAll()},selectAll:function(t,i){var f;t=this.options.enableCollapsibleOptGroups&&this.options.multiple?!1:t;var t=typeof t=="undefined"?!0:t,r=n("li input[type='checkbox']:enabled",this.$ul),u=r.filter(":visible"),e=r.length,o=u.length;t?(u.prop("checked",!0),n("li:not(.divider):not(.disabled)",this.$ul).filter(":visible").addClass(this.options.selectedClass)):(r.prop("checked",!0),n("li:not(.divider):not(.disabled)",this.$ul).addClass(this.options.selectedClass));e===o||t===!1?n("option:not([data-role='divider']):enabled",this.$select).prop("selected",!0):(f=u.map(function(){return n(this).val()}).get(),n("option:enabled",this.$select).filter(function(){return n.inArray(n(this).val(),f)!==-1}).prop("selected",!0));i&&this.options.onSelectAll()},deselectAll:function(t){var i,f,r,u;t=this.options.enableCollapsibleOptGroups&&this.options.multiple?!1:t;t=typeof t=="undefined"?!0:t;t?(i=n("li input[type='checkbox']:not(:disabled)",this.$ul).filter(":visible"),i.prop("checked",!1),f=i.map(function(){return n(this).val()}).get(),r=n("option:enabled",this.$select).filter(function(){return n.inArray(n(this).val(),f)!==-1}),r.prop("selected",!1),r.removeProp("selected"),this.options.selectedClass&&n("li:not(.divider):not(.disabled)",this.$ul).filter(":visible").removeClass(this.options.selectedClass)):(u=n("li input[type='checkbox']:enabled",this.$ul),u.prop("checked",!1),u.removeProp("selected"),n("option:enabled",this.$select).prop("selected",!1),this.options.selectedClass&&n("li:not(.divider):not(.disabled)",this.$ul).removeClass(this.options.selectedClass))},rebuild:function(){this.$ul.html("");this.options.multiple=this.$select.attr("multiple")==="multiple";this.buildSelectAll();this.buildDropdownOptions();this.buildFilter();this.updateButtonText();this.updateSelectAll(!0);this.options.disableIfEmpty&&n("option",this.$select).length<=0?this.disable():this.enable();this.options.dropRight&&this.$ul.addClass("pull-right")},dataprovider:function(t){var r=0,u=this.$select.empty();n.each(t,function(t,f){var e;n.isArray(f.children)?(r++,e=n("<optgroup/>").attr({label:f.label||"Group "+r,disabled:!!f.disabled}),i(f.children,function(t){e.append(n("<option/>").attr({value:t.value,label:t.label||t.value,title:t.title,selected:!!t.selected,disabled:!!t.disabled}))})):(e=n("<option/>").attr({value:f.value,label:f.label||f.value,title:f.title,"class":f.class,selected:!!f.selected,disabled:!!f.disabled}),e.text(f.label||f.value));u.append(e)});this.rebuild()},enable:function(){this.$select.prop("disabled",!1);this.$button.prop("disabled",!1).removeClass("disabled")},disable:function(){this.$select.prop("disabled",!0);this.$button.prop("disabled",!0).addClass("disabled")},setOptions:function(n){this.options=this.mergeOptions(n)},mergeOptions:function(t){return n.extend(!0,{},this.defaults,this.options,t)},hasSelectAll:function(){return n("li.multiselect-all",this.$ul).length>0},updateSelectAll:function(t){if(this.hasSelectAll()){var u=n("li:not(.multiselect-item):not(.filter-hidden) input:enabled",this.$ul),e=u.length,i=u.filter(":checked").length,r=n("li.multiselect-all",this.$ul),f=r.find("input");if(i>0&&i===e){f.prop("checked",!0);r.addClass(this.options.selectedClass);this.options.onSelectAll(!0)}else if(f.prop("checked",!1),r.removeClass(this.options.selectedClass),i===0&&!t)this.options.onSelectAll(!1)}},updateButtonText:function(){var t=this.getSelected();this.options.enableHTML?n(".multiselect .multiselect-selected-text",this.$container).html(this.options.buttonText(t,this.$select)):n(".multiselect .multiselect-selected-text",this.$container).text(this.options.buttonText(t,this.$select));n(".multiselect",this.$container).attr("title",this.options.buttonTitle(t,this.$select))},getSelected:function(){return n("option",this.$select).filter(":selected")},getOptionByValue:function(t){for(var r,u=n("option",this.$select),f=t.toString(),i=0;i<u.length;i=i+1)if(r=u[i],r.value===f)return n(r)},getInputByValue:function(t){for(var r,u=n("li input",this.$ul),f=t.toString(),i=0;i<u.length;i=i+1)if(r=u[i],r.value===f)return n(r)},updateOriginalOptions:function(){this.originalOptions=this.$select.clone()[0].options},asyncFunction:function(n,t,i){var r=Array.prototype.slice.call(arguments,3);return setTimeout(function(){n.apply(i||window,r)},t)},setAllSelectedText:function(n){this.options.allSelectedText=n;this.updateButtonText()}};n.fn.multiselect=function(i,r,u){return this.each(function(){var f=n(this).data("multiselect"),e=typeof i=="object"&&i;f||(f=new t(this,e),n(this).data("multiselect",f));typeof i=="string"&&(f[i](r,u),i==="destroy"&&n(this).data("multiselect",!1))})};n.fn.multiselect.Constructor=t;n(function(){n("select[data-role=multiselect]").multiselect()})}(window.jQuery);
//! moment.js
//! version : 2.10.6
//! authors : Tim Wood, Iskren Chernev, Moment.js contributors
//! license : MIT
//! momentjs.com
(function(n,t){typeof exports=="object"&&typeof module!="undefined"?module.exports=t():typeof define=="function"&&define.amd?define(t):n.moment=t()})(this,function(){"use strict";function i(){return ru.apply(null,arguments)}function eo(n){ru=n}function ui(n){return Object.prototype.toString.call(n)==="[object Array]"}function fi(n){return n instanceof Date||Object.prototype.toString.call(n)==="[object Date]"}function oo(n,t){for(var r=[],i=0;i<n.length;++i)r.push(t(n[i],i));return r}function ft(n,t){return Object.prototype.hasOwnProperty.call(n,t)}function ei(n,t){for(var i in t)ft(t,i)&&(n[i]=t[i]);return ft(t,"toString")&&(n.toString=t.toString),ft(t,"valueOf")&&(n.valueOf=t.valueOf),n}function kt(n,t,i,r){return sf(n,t,i,r,!0).utc()}function so(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1}}function e(n){return n._pf==null&&(n._pf=so()),n._pf}function uu(n){if(n._isValid==null){var t=e(n);n._isValid=!isNaN(n._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidMonth&&!t.invalidWeekday&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated;n._strict&&(n._isValid=n._isValid&&t.charsLeftOver===0&&t.unusedTokens.length===0&&t.bigHour===undefined)}return n._isValid}function fu(n){var t=kt(NaN);return n!=null?ei(e(t),n):e(t).userInvalidated=!0,t}function gi(n,t){var u,i,r;if(typeof t._isAMomentObject!="undefined"&&(n._isAMomentObject=t._isAMomentObject),typeof t._i!="undefined"&&(n._i=t._i),typeof t._f!="undefined"&&(n._f=t._f),typeof t._l!="undefined"&&(n._l=t._l),typeof t._strict!="undefined"&&(n._strict=t._strict),typeof t._tzm!="undefined"&&(n._tzm=t._tzm),typeof t._isUTC!="undefined"&&(n._isUTC=t._isUTC),typeof t._offset!="undefined"&&(n._offset=t._offset),typeof t._pf!="undefined"&&(n._pf=e(t)),typeof t._locale!="undefined"&&(n._locale=t._locale),di.length>0)for(u in di)i=di[u],r=t[i],typeof r!="undefined"&&(n[i]=r);return n}function dt(n){gi(this,n);this._d=new Date(n._d!=null?n._d.getTime():NaN);nr===!1&&(nr=!0,i.updateOffset(this),nr=!1)}function tt(n){return n instanceof dt||n!=null&&n._isAMomentObject!=null}function v(n){return n<0?Math.ceil(n):Math.floor(n)}function s(n){var t=+n,i=0;return t!==0&&isFinite(t)&&(i=v(t)),i}function eu(n,t,i){for(var f=Math.min(n.length,t.length),e=Math.abs(n.length-t.length),u=0,r=0;r<f;r++)(i&&n[r]!==t[r]||!i&&s(n[r])!==s(t[r]))&&u++;return u+e}function ou(){}function su(n){return n?n.toLowerCase().replace("_","-"):n}function ho(n){for(var r=0,i,t,f,u;r<n.length;){for(u=su(n[r]).split("-"),i=u.length,t=su(n[r+1]),t=t?t.split("-"):null;i>0;){if(f=hu(u.slice(0,i).join("-")),f)return f;if(t&&t.length>=i&&eu(u,t,!0)>=i-1)break;i--}r++}return null}function hu(n){var t=null;if(!et[n]&&typeof module!="undefined"&&module&&module.exports)try{t=oi._abbr;require("./locale/"+n);gt(t)}catch(i){}return et[n]}function gt(n,t){var i;return n&&(i=typeof t=="undefined"?ot(n):cu(n,t),i&&(oi=i)),oi._abbr}function cu(n,t){return t!==null?(t.abbr=n,et[n]=et[n]||new ou,et[n].set(t),gt(n),et[n]):(delete et[n],null)}function ot(n){var t;if(n&&n._locale&&n._locale._abbr&&(n=n._locale._abbr),!n)return oi;if(!ui(n)){if(t=hu(n),t)return t;n=[n]}return ho(n)}function l(n,t){var i=n.toLowerCase();ni[i]=ni[i+"s"]=ni[t]=n}function p(n){return typeof n=="string"?ni[n]||ni[n.toLowerCase()]:undefined}function lu(n){var r={},t;for(var i in n)ft(n,i)&&(t=p(i),t&&(r[t]=n[i]));return r}function pt(n,t){return function(r){return r!=null?(au(this,n,r),i.updateOffset(this,t),this):si(this,n)}}function si(n,t){return n._d["get"+(n._isUTC?"UTC":"")+t]()}function au(n,t,i){return n._d["set"+(n._isUTC?"UTC":"")+t](i)}function vu(n,t){var i;if(typeof n=="object")for(i in n)this.set(i,n[i]);else if(n=p(n),typeof this[n]=="function")return this[n](t);return this}function tr(n,t,i){var r=""+Math.abs(n),u=t-r.length,f=n>=0;return(f?i?"+":"":"-")+Math.pow(10,Math.max(0,u)).toString().substr(1)+r}function r(n,t,i,r){var u=r;typeof r=="string"&&(u=function(){return this[r]()});n&&(wt[n]=u);t&&(wt[t[0]]=function(){return tr(u.apply(this,arguments),t[1],t[2])});i&&(wt[i]=function(){return this.localeData().ordinal(u.apply(this,arguments),n)})}function co(n){return n.match(/\[[\s\S]/)?n.replace(/^\[|\]$/g,""):n.replace(/\\/g,"")}function lo(n){for(var i=n.match(yu),t=0,r=i.length;t<r;t++)i[t]=wt[i[t]]?wt[i[t]]:co(i[t]);return function(u){var f="";for(t=0;t<r;t++)f+=i[t]instanceof Function?i[t].call(u,n):i[t];return f}}function rr(n,t){return n.isValid()?(t=pu(t,n.localeData()),ir[t]=ir[t]||lo(t),ir[t](n)):n.localeData().invalidDate()}function pu(n,t){function r(n){return t.longDateFormat(n)||n}var i=5;for(hi.lastIndex=0;i>=0&&hi.test(n);)n=n.replace(hi,r),hi.lastIndex=0,i-=1;return n}function ao(n){return typeof n=="function"&&Object.prototype.toString.call(n)==="[object Function]"}function t(n,t,i){er[n]=ao(t)?t:function(n){return n&&i?i:t}}function vo(n,t){return ft(er,n)?er[n](t._strict,t._locale):new RegExp(yo(n))}function yo(n){return n.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(n,t,i,r,u){return t||i||r||u}).replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function c(n,t){var i,r=t;for(typeof n=="string"&&(n=[n]),typeof t=="number"&&(r=function(n,i){i[t]=s(n)}),i=0;i<n.length;i++)or[n[i]]=r}function ii(n,t){c(n,function(n,i,r,u){r._w=r._w||{};t(n,r._w,r,u)})}function po(n,t,i){t!=null&&ft(or,n)&&or[n](t,i._a,i,n)}function sr(n,t){return new Date(Date.UTC(n,t+1,0)).getUTCDate()}function wo(n){return this._months[n.month()]}function bo(n){return this._monthsShort[n.month()]}function ko(n,t,i){var r,u,f;for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++)if((u=kt([2e3,r]),i&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(u,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(u,"").replace(".","")+"$","i")),i||this._monthsParse[r]||(f="^"+this.months(u,"")+"|^"+this.monthsShort(u,""),this._monthsParse[r]=new RegExp(f.replace(".",""),"i")),i&&t==="MMMM"&&this._longMonthsParse[r].test(n))||i&&t==="MMM"&&this._shortMonthsParse[r].test(n)||!i&&this._monthsParse[r].test(n))return r}function gu(n,t){var i;return typeof t=="string"&&(t=n.localeData().monthsParse(t),typeof t!="number")?n:(i=Math.min(n.date(),sr(n.year(),t)),n._d["set"+(n._isUTC?"UTC":"")+"Month"](t,i),n)}function nf(n){return n!=null?(gu(this,n),i.updateOffset(this,!0),this):si(this,"Month")}function go(){return sr(this.year(),this.month())}function hr(n){var i,t=n._a;return t&&e(n).overflow===-2&&(i=t[g]<0||t[g]>11?g:t[k]<1||t[k]>sr(t[b],t[g])?k:t[a]<0||t[a]>24||t[a]===24&&(t[st]!==0||t[ht]!==0||t[ct]!==0)?a:t[st]<0||t[st]>59?st:t[ht]<0||t[ht]>59?ht:t[ct]<0||t[ct]>999?ct:-1,e(n)._overflowDayOfYear&&(i<b||i>k)&&(i=k),e(n).overflow=i),n}function tf(n){i.suppressDeprecationWarnings===!1&&typeof console!="undefined"&&console.warn&&console.warn("Deprecation warning: "+n)}function w(n,t){var i=!0;return ei(function(){return i&&(tf(n+"\n"+(new Error).stack),i=!1),t.apply(this,arguments)},t)}function ns(n,t){cr[n]||(tf(t),cr[n]=!0)}function rf(n){var t,i,r=n._i,u=ts.exec(r);if(u){for(e(n).iso=!0,t=0,i=lr.length;t<i;t++)if(lr[t][1].exec(r)){n._f=lr[t][0];break}for(t=0,i=ar.length;t<i;t++)if(ar[t][1].exec(r)){n._f+=(u[6]||" ")+ar[t][0];break}r.match(yi)&&(n._f+="Z");wr(n)}else n._isValid=!1}function rs(n){var t=is.exec(n._i);if(t!==null){n._d=new Date(+t[1]);return}rf(n);n._isValid===!1&&(delete n._isValid,i.createFromInputFallback(n))}function us(n,t,i,r,u,f,e){var o=new Date(n,t,i,r,u,f,e);return n<1970&&o.setFullYear(n),o}function vr(n){var t=new Date(Date.UTC.apply(null,arguments));return n<1970&&t.setUTCFullYear(n),t}function uf(n){return ff(n)?366:365}function ff(n){return n%4==0&&n%100!=0||n%400==0}function fs(){return ff(this.year())}function lt(n,t,i){var f=i-t,r=i-n.day(),u;return r>f&&(r-=7),r<f-7&&(r+=7),u=o(n).add(r,"d"),{week:Math.ceil(u.dayOfYear()/7),year:u.year()}}function es(n){return lt(n,this._week.dow,this._week.doy).week}function os(){return this._week.dow}function ss(){return this._week.doy}function hs(n){var t=this.localeData().week(this);return n==null?t:this.add((n-t)*7,"d")}function cs(n){var t=lt(this,1,4).week;return n==null?t:this.add((n-t)*7,"d")}function ls(n,t,i,r,u){var o=6+u-r,s=vr(n,0,1+o),e=s.getUTCDay(),f;return e<u&&(e+=7),i=i!=null?1*i:u,f=1+o+7*(t-1)-e+i,{year:f>0?n:n-1,dayOfYear:f>0?f:uf(n-1)+f}}function as(n){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return n==null?t:this.add(n-t,"d")}function bt(n,t,i){return n!=null?n:t!=null?t:i}function vs(n){var t=new Date;return n._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function pr(n){var t,i,r=[],u,f;if(!n._d){for(u=vs(n),n._w&&n._a[k]==null&&n._a[g]==null&&ys(n),n._dayOfYear&&(f=bt(n._a[b],u[b]),n._dayOfYear>uf(f)&&(e(n)._overflowDayOfYear=!0),i=vr(f,0,n._dayOfYear),n._a[g]=i.getUTCMonth(),n._a[k]=i.getUTCDate()),t=0;t<3&&n._a[t]==null;++t)n._a[t]=r[t]=u[t];for(;t<7;t++)n._a[t]=r[t]=n._a[t]==null?t===2?1:0:n._a[t];n._a[a]===24&&n._a[st]===0&&n._a[ht]===0&&n._a[ct]===0&&(n._nextDay=!0,n._a[a]=0);n._d=(n._useUTC?vr:us).apply(null,r);n._tzm!=null&&n._d.setUTCMinutes(n._d.getUTCMinutes()-n._tzm);n._nextDay&&(n._a[a]=24)}}function ys(n){var t,e,u,r,i,f,s;t=n._w;t.GG!=null||t.W!=null||t.E!=null?(i=1,f=4,e=bt(t.GG,n._a[b],lt(o(),1,4).year),u=bt(t.W,1),r=bt(t.E,1)):(i=n._locale._week.dow,f=n._locale._week.doy,e=bt(t.gg,n._a[b],lt(o(),i,f).year),u=bt(t.w,1),t.d!=null?(r=t.d,r<i&&++u):r=t.e!=null?t.e+i:i);s=ls(e,u,r,f,i);n._a[b]=s.year;n._dayOfYear=s.dayOfYear}function wr(n){if(n._f===i.ISO_8601){rf(n);return}n._a=[];e(n).empty=!0;for(var t=""+n._i,r,u,s,c=t.length,h=0,o=pu(n._f,n._locale).match(yu)||[],f=0;f<o.length;f++)u=o[f],r=(t.match(vo(u,n))||[])[0],r&&(s=t.substr(0,t.indexOf(r)),s.length>0&&e(n).unusedInput.push(s),t=t.slice(t.indexOf(r)+r.length),h+=r.length),wt[u]?(r?e(n).empty=!1:e(n).unusedTokens.push(u),po(u,r,n)):n._strict&&!r&&e(n).unusedTokens.push(u);e(n).charsLeftOver=c-h;t.length>0&&e(n).unusedInput.push(t);e(n).bigHour===!0&&n._a[a]<=12&&n._a[a]>0&&(e(n).bigHour=undefined);n._a[a]=ps(n._locale,n._a[a],n._meridiem);pr(n);hr(n)}function ps(n,t,i){var r;return i==null?t:n.meridiemHour!=null?n.meridiemHour(t,i):n.isPM!=null?(r=n.isPM(i),r&&t<12&&(t+=12),r||t!==12||(t=0),t):t}function ws(n){var t,f,u,r,i;if(n._f.length===0){e(n).invalidFormat=!0;n._d=new Date(NaN);return}for(r=0;r<n._f.length;r++)(i=0,t=gi({},n),n._useUTC!=null&&(t._useUTC=n._useUTC),t._f=n._f[r],wr(t),uu(t))&&(i+=e(t).charsLeftOver,i+=e(t).unusedTokens.length*10,e(t).score=i,(u==null||i<u)&&(u=i,f=t));ei(n,f||t)}function bs(n){if(!n._d){var t=lu(n._i);n._a=[t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond];pr(n)}}function ks(n){var t=new dt(hr(of(n)));return t._nextDay&&(t.add(1,"d"),t._nextDay=undefined),t}function of(n){var t=n._i,i=n._f;return(n._locale=n._locale||ot(n._l),t===null||i===undefined&&t==="")?fu({nullInput:!0}):(typeof t=="string"&&(n._i=t=n._locale.preparse(t)),tt(t))?new dt(hr(t)):(ui(i)?ws(n):i?wr(n):fi(t)?n._d=t:ds(n),n)}function ds(n){var t=n._i;t===undefined?n._d=new Date:fi(t)?n._d=new Date(+t):typeof t=="string"?rs(n):ui(t)?(n._a=oo(t.slice(0),function(n){return parseInt(n,10)}),pr(n)):typeof t=="object"?bs(n):typeof t=="number"?n._d=new Date(t):i.createFromInputFallback(n)}function sf(n,t,i,r,u){var f={};return typeof i=="boolean"&&(r=i,i=undefined),f._isAMomentObject=!0,f._useUTC=f._isUTC=u,f._l=i,f._i=n,f._f=t,f._strict=r,ks(f)}function o(n,t,i,r){return sf(n,t,i,r,!1)}function lf(n,t){var r,i;if(t.length===1&&ui(t[0])&&(t=t[0]),!t.length)return o();for(r=t[0],i=1;i<t.length;++i)(!t[i].isValid()||t[i][n](r))&&(r=t[i]);return r}function gs(){var n=[].slice.call(arguments,0);return lf("isBefore",n)}function nh(){var n=[].slice.call(arguments,0);return lf("isAfter",n)}function pi(n){var t=lu(n),i=t.year||0,r=t.quarter||0,u=t.month||0,f=t.week||0,e=t.day||0,o=t.hour||0,s=t.minute||0,h=t.second||0,c=t.millisecond||0;this._milliseconds=+c+h*1e3+s*6e4+o*36e5;this._days=+e+f*7;this._months=+u+r*3+i*12;this._data={};this._locale=ot();this._bubble()}function br(n){return n instanceof pi}function af(n,t){r(n,0,0,function(){var n=this.utcOffset(),i="+";return n<0&&(n=-n,i="-"),i+tr(~~(n/60),2)+t+tr(~~n%60,2)})}function kr(n){var i=(n||"").match(yi)||[],u=i[i.length-1]||[],t=(u+"").match(vf)||["-",0,0],r=+(t[1]*60)+s(t[2]);return t[0]==="+"?r:-r}function dr(n,t){var r,u;return t._isUTC?(r=t.clone(),u=(tt(n)||fi(n)?+n:+o(n))-+r,r._d.setTime(+r._d+u),i.updateOffset(r,!1),r):o(n).local()}function gr(n){return-Math.round(n._d.getTimezoneOffset()/15)*15}function th(n,t){var r=this._offset||0,u;return n!=null?(typeof n=="string"&&(n=kr(n)),Math.abs(n)<16&&(n=n*60),!this._isUTC&&t&&(u=gr(this)),this._offset=n,this._isUTC=!0,u!=null&&this.add(u,"m"),r!==n&&(!t||this._changeInProgress?df(this,it(n-r,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,i.updateOffset(this,!0),this._changeInProgress=null)),this):this._isUTC?r:gr(this)}function ih(n,t){return n!=null?(typeof n!="string"&&(n=-n),this.utcOffset(n,t),this):-this.utcOffset()}function rh(n){return this.utcOffset(0,n)}function uh(n){return this._isUTC&&(this.utcOffset(0,n),this._isUTC=!1,n&&this.subtract(gr(this),"m")),this}function fh(){return this._tzm?this.utcOffset(this._tzm):typeof this._i=="string"&&this.utcOffset(kr(this._i)),this}function eh(n){return n=n?o(n).utcOffset():0,(this.utcOffset()-n)%60==0}function oh(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function sh(){var n,t;return typeof this._isDSTShifted!="undefined"?this._isDSTShifted:(n={},gi(n,this),n=of(n),n._a?(t=n._isUTC?kt(n._a):o(n._a),this._isDSTShifted=this.isValid()&&eu(n._a,t.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted)}function hh(){return!this._isUTC}function ch(){return this._isUTC}function yf(){return this._isUTC&&this._offset===0}function it(n,t){var i=n,r=null,u,f,e;return br(n)?i={ms:n._milliseconds,d:n._days,M:n._months}:typeof n=="number"?(i={},t?i[t]=n:i.milliseconds=n):(r=pf.exec(n))?(u=r[1]==="-"?-1:1,i={y:0,d:s(r[k])*u,h:s(r[a])*u,m:s(r[st])*u,s:s(r[ht])*u,ms:s(r[ct])*u}):(r=wf.exec(n))?(u=r[1]==="-"?-1:1,i={y:at(r[2],u),M:at(r[3],u),d:at(r[4],u),h:at(r[5],u),m:at(r[6],u),s:at(r[7],u),w:at(r[8],u)}):i==null?i={}:typeof i=="object"&&("from"in i||"to"in i)&&(e=lh(o(i.from),o(i.to)),i={},i.ms=e.milliseconds,i.M=e.months),f=new pi(i),br(n)&&ft(n,"_locale")&&(f._locale=n._locale),f}function at(n,t){var i=n&&parseFloat(n.replace(",","."));return(isNaN(i)?0:i)*t}function bf(n,t){var i={milliseconds:0,months:0};return i.months=t.month()-n.month()+(t.year()-n.year())*12,n.clone().add(i.months,"M").isAfter(t)&&--i.months,i.milliseconds=+t-+n.clone().add(i.months,"M"),i}function lh(n,t){var i;return t=dr(t,n),n.isBefore(t)?i=bf(n,t):(i=bf(t,n),i.milliseconds=-i.milliseconds,i.months=-i.months),i}function kf(n,t){return function(i,r){var u,f;return r===null||isNaN(+r)||(ns(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period)."),f=i,i=r,r=f),i=typeof i=="string"?+i:i,u=it(i,r),df(this,u,n),this}}function df(n,t,r,u){var o=t._milliseconds,f=t._days,e=t._months;u=u==null?!0:u;o&&n._d.setTime(+n._d+o*r);f&&au(n,"Date",si(n,"Date")+f*r);e&&gu(n,si(n,"Month")+e*r);u&&i.updateOffset(n,f||e)}function ah(n,t){var r=n||o(),f=dr(r,this).startOf("day"),i=this.diff(f,"days",!0),u=i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse";return this.format(t&&t[u]||this.localeData().calendar(u,this,o(r)))}function vh(){return new dt(this)}function yh(n,t){var i;return t=p(typeof t!="undefined"?t:"millisecond"),t==="millisecond"?(n=tt(n)?n:o(n),+this>+n):(i=tt(n)?+n:+o(n),i<+this.clone().startOf(t))}function ph(n,t){var i;return t=p(typeof t!="undefined"?t:"millisecond"),t==="millisecond"?(n=tt(n)?n:o(n),+this<+n):(i=tt(n)?+n:+o(n),+this.clone().endOf(t)<i)}function wh(n,t,i){return this.isAfter(n,i)&&this.isBefore(t,i)}function bh(n,t){var i;return t=p(t||"millisecond"),t==="millisecond"?(n=tt(n)?n:o(n),+this==+n):(i=+o(n),+this.clone().startOf(t)<=i&&i<=+this.clone().endOf(t))}function kh(n,t,i){var f=dr(n,this),e=(f.utcOffset()-this.utcOffset())*6e4,u,r;return t=p(t),t==="year"||t==="month"||t==="quarter"?(r=dh(this,f),t==="quarter"?r=r/3:t==="year"&&(r=r/12)):(u=this-f,r=t==="second"?u/1e3:t==="minute"?u/6e4:t==="hour"?u/36e5:t==="day"?(u-e)/864e5:t==="week"?(u-e)/6048e5:u),i?r:v(r)}function dh(n,t){var r=(t.year()-n.year())*12+(t.month()-n.month()),i=n.clone().add(r,"months"),u,f;return t-i<0?(u=n.clone().add(r-1,"months"),f=(t-i)/(i-u)):(u=n.clone().add(r+1,"months"),f=(t-i)/(u-i)),-(r+f)}function gh(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function te(){var n=this.clone().utc();return 0<n.year()&&n.year()<=9999?"function"==typeof Date.prototype.toISOString?this.toDate().toISOString():rr(n,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):rr(n,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")}function nc(n){var t=rr(this,n||i.defaultFormat);return this.localeData().postformat(t)}function tc(n,t){return this.isValid()?it({to:this,from:n}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function ic(n){return this.from(o(),n)}function rc(n,t){return this.isValid()?it({from:this,to:n}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function uc(n){return this.to(o(),n)}function ie(n){var t;return n===undefined?this._locale._abbr:(t=ot(n),t!=null&&(this._locale=t),this)}function re(){return this._locale}function fc(n){n=p(n);switch(n){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return n==="week"&&this.weekday(0),n==="isoWeek"&&this.isoWeekday(1),n==="quarter"&&this.month(Math.floor(this.month()/3)*3),this}function ec(n){return(n=p(n),n===undefined||n==="millisecond")?this:this.startOf(n).add(1,n==="isoWeek"?"week":n).subtract(1,"ms")}function oc(){return+this._d-(this._offset||0)*6e4}function sc(){return Math.floor(+this/1e3)}function hc(){return this._offset?new Date(+this):this._d}function cc(){var n=this;return[n.year(),n.month(),n.date(),n.hour(),n.minute(),n.second(),n.millisecond()]}function lc(){var n=this;return{years:n.year(),months:n.month(),date:n.date(),hours:n.hours(),minutes:n.minutes(),seconds:n.seconds(),milliseconds:n.milliseconds()}}function ac(){return uu(this)}function vc(){return ei({},e(this))}function yc(){return e(this).overflow}function wi(n,t){r(0,[n,n.length],0,t)}function ue(n,t,i){return lt(o([n,11,31+t-i]),t,i).week}function pc(n){var t=lt(this,this.localeData()._week.dow,this.localeData()._week.doy).year;return n==null?t:this.add(n-t,"y")}function wc(n){var t=lt(this,1,4).year;return n==null?t:this.add(n-t,"y")}function bc(){return ue(this.year(),1,4)}function kc(){var n=this.localeData()._week;return ue(this.year(),n.dow,n.doy)}function dc(n){return n==null?Math.ceil((this.month()+1)/3):this.month((n-1)*3+this.month()%3)}function gc(n,t){return typeof n!="string"?n:isNaN(n)?(n=t.weekdaysParse(n),typeof n=="number")?n:null:parseInt(n,10)}function nl(n){return this._weekdays[n.day()]}function tl(n){return this._weekdaysShort[n.day()]}function il(n){return this._weekdaysMin[n.day()]}function rl(n){var t,i,r;for(this._weekdaysParse=this._weekdaysParse||[],t=0;t<7;t++)if(this._weekdaysParse[t]||(i=o([2e3,1]).day(t),r="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[t]=new RegExp(r.replace(".",""),"i")),this._weekdaysParse[t].test(n))return t}function ul(n){var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return n!=null?(n=gc(n,this.localeData()),this.add(n-t,"d")):t}function fl(n){var t=(this.day()+7-this.localeData()._week.dow)%7;return n==null?t:this.add(n-t,"d")}function el(n){return n==null?this.day()||7:this.day(this.day()%7?n:n-7)}function se(n,t){r(n,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function he(n,t){return t._meridiemParse}function ol(n){return(n+"").toLowerCase().charAt(0)==="p"}function sl(n,t,i){return n>11?i?"pm":"PM":i?"am":"AM"}function hl(n,t){t[ct]=s(("0."+n)*1e3)}function cl(){return this._isUTC?"UTC":""}function ll(){return this._isUTC?"Coordinated Universal Time":""}function al(n){return o(n*1e3)}function vl(){return o.apply(null,arguments).parseZone()}function yl(n,t,i){var r=this._calendar[n];return typeof r=="function"?r.call(t,i):r}function pl(n){var t=this._longDateFormat[n],i=this._longDateFormat[n.toUpperCase()];return t||!i?t:(this._longDateFormat[n]=i.replace(/MMMM|MM|DD|dddd/g,function(n){return n.slice(1)}),this._longDateFormat[n])}function wl(){return this._invalidDate}function bl(n){return this._ordinal.replace("%d",n)}function no(n){return n}function kl(n,t,i,r){var u=this._relativeTime[i];return typeof u=="function"?u(n,t,i,r):u.replace(/%d/i,n)}function dl(n,t){var i=this._relativeTime[n>0?"future":"past"];return typeof i=="function"?i(t):i.replace(/%s/i,t)}function gl(n){var t;for(var i in n)t=n[i],typeof t=="function"?this[i]=t:this["_"+i]=t;this._ordinalParseLenient=new RegExp(this._ordinalParse.source+"|"+/\d{1,2}/.source)}function io(n,t,i,r){var u=ot(),f=kt().set(r,t);return u[i](f,n)}function ri(n,t,i,r,u){if(typeof n=="number"&&(t=n,n=undefined),n=n||"",t!=null)return io(n,t,i,u);for(var e=[],f=0;f<r;f++)e[f]=io(n,f,i,u);return e}function na(n,t){return ri(n,t,"months",12,"month")}function ta(n,t){return ri(n,t,"monthsShort",12,"month")}function ia(n,t){return ri(n,t,"weekdays",7,"day")}function ra(n,t){return ri(n,t,"weekdaysShort",7,"day")}function ua(n,t){return ri(n,t,"weekdaysMin",7,"day")}function fa(){var n=this._data;return this._milliseconds=d(this._milliseconds),this._days=d(this._days),this._months=d(this._months),n.milliseconds=d(n.milliseconds),n.seconds=d(n.seconds),n.minutes=d(n.minutes),n.hours=d(n.hours),n.months=d(n.months),n.years=d(n.years),this}function ro(n,t,i,r){var u=it(t,i);return n._milliseconds+=r*u._milliseconds,n._days+=r*u._days,n._months+=r*u._months,n._bubble()}function ea(n,t){return ro(this,n,t,1)}function oa(n,t){return ro(this,n,t,-1)}function uo(n){return n<0?Math.floor(n):Math.ceil(n)}function sa(){var r=this._milliseconds,n=this._days,t=this._months,i=this._data,u,f,e,s,o;return r>=0&&n>=0&&t>=0||r<=0&&n<=0&&t<=0||(r+=uo(iu(t)+n)*864e5,n=0,t=0),i.milliseconds=r%1e3,u=v(r/1e3),i.seconds=u%60,f=v(u/60),i.minutes=f%60,e=v(f/60),i.hours=e%24,n+=v(e/24),o=v(fo(n)),t+=o,n-=uo(iu(o)),s=v(t/12),t%=12,i.days=n,i.months=t,i.years=s,this}function fo(n){return n*4800/146097}function iu(n){return n*146097/4800}function ha(n){var t,r,i=this._milliseconds;if(n=p(n),n==="month"||n==="year")return t=this._days+i/864e5,r=this._months+fo(t),n==="month"?r:r/12;t=this._days+Math.round(iu(this._months));switch(n){case"week":return t/7+i/6048e5;case"day":return t+i/864e5;case"hour":return t*24+i/36e5;case"minute":return t*1440+i/6e4;case"second":return t*86400+i/1e3;case"millisecond":return Math.floor(t*864e5)+i;default:throw new Error("Unknown unit "+n);}}function ca(){return this._milliseconds+this._days*864e5+this._months%12*2592e6+s(this._months/12)*31536e6}function ut(n){return function(){return this.as(n)}}function da(n){return n=p(n),this[n+"s"]()}function vt(n){return function(){return this._data[n]}}function ev(){return v(this.days()/7)}function ov(n,t,i,r,u){return u.relativeTime(t||1,!!i,n,r)}function sv(n,t,i){var r=it(n).abs(),h=yt(r.as("s")),f=yt(r.as("m")),e=yt(r.as("h")),o=yt(r.as("d")),s=yt(r.as("M")),c=yt(r.as("y")),u=h<nt.s&&["s",h]||f===1&&["m"]||f<nt.m&&["mm",f]||e===1&&["h"]||e<nt.h&&["hh",e]||o===1&&["d"]||o<nt.d&&["dd",o]||s===1&&["M"]||s<nt.M&&["MM",s]||c===1&&["y"]||["yy",c];return u[2]=t,u[3]=+n>0,u[4]=i,ov.apply(null,u)}function hv(n,t){return nt[n]===undefined?!1:t===undefined?nt[n]:(nt[n]=t,!0)}function cv(n){var t=this.localeData(),i=sv(this,!n,t);return n&&(i=t.pastFuture(+this,i)),t.postformat(i)}function ki(){var t=bi(this._milliseconds)/1e3,a=bi(this._days),i=bi(this._months),n,e,o;n=v(t/60);e=v(n/60);t%=60;n%=60;o=v(i/12);i%=12;var s=o,h=i,c=a,r=e,u=n,f=t,l=this.asSeconds();return l?(l<0?"-":"")+"P"+(s?s+"Y":"")+(h?h+"M":"")+(c?c+"D":"")+(r||u||f?"T":"")+(r?r+"H":"")+(u?u+"M":"")+(f?f+"S":""):"P0D"}var ru,di=i.momentProperties=[],nr=!1,et={},oi,ni={},yu=/(\[[^\[]*\])|(\\)?(Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Q|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,hi=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,ir={},wt={},wu=/\d/,y=/\d\d/,bu=/\d{3}/,ur=/\d{4}/,ci=/[+-]?\d{6}/,h=/\d\d?/,li=/\d{1,3}/,fr=/\d{1,4}/,ai=/[+-]?\d{1,6}/,vi=/[+-]?\d+/,yi=/Z|[+-]\d\d:?\d\d/gi,ti=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,er={},or={},b=0,g=1,k=2,a=3,st=4,ht=5,ct=6,ku,du,cr,yr,ef,hf,cf,vf,pf,wf,gf,ne,nu,tu,fe,ee,oe,ce,le,ae,ve,rt,ye,n,pe,we,be,ke,de,ge,to,u,d,yt,nt,bi,f;r("M",["MM",2],"Mo",function(){return this.month()+1});r("MMM",0,0,function(n){return this.localeData().monthsShort(this,n)});r("MMMM",0,0,function(n){return this.localeData().months(this,n)});l("month","M");t("M",h);t("MM",h,y);t("MMM",ti);t("MMMM",ti);c(["M","MM"],function(n,t){t[g]=s(n)-1});c(["MMM","MMMM"],function(n,t,i,r){var u=i._locale.monthsParse(n,r,i._strict);u!=null?t[g]=u:e(i).invalidMonth=n});ku="January_February_March_April_May_June_July_August_September_October_November_December".split("_");du="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");cr={};i.suppressDeprecationWarnings=!1;var ts=/^\s*(?:[+-]\d{6}|\d{4})-(?:(\d\d-\d\d)|(W\d\d$)|(W\d\d-\d)|(\d\d\d))((T| )(\d\d(:\d\d(:\d\d(\.\d+)?)?)?)?([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,lr=[["YYYYYY-MM-DD",/[+-]\d{6}-\d{2}-\d{2}/],["YYYY-MM-DD",/\d{4}-\d{2}-\d{2}/],["GGGG-[W]WW-E",/\d{4}-W\d{2}-\d/],["GGGG-[W]WW",/\d{4}-W\d{2}/],["YYYY-DDD",/\d{4}-\d{3}/]],ar=[["HH:mm:ss.SSSS",/(T| )\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss",/(T| )\d\d:\d\d:\d\d/],["HH:mm",/(T| )\d\d:\d\d/],["HH",/(T| )\d\d/]],is=/^\/?Date\((\-?\d+)/i;for(i.createFromInputFallback=w("moment construction falls back to js Date. This is discouraged and will be removed in upcoming major release. Please refer to https://github.com/moment/moment/issues/1407 for more info.",function(n){n._d=new Date(n._i+(n._useUTC?" UTC":""))}),r(0,["YY",2],0,function(){return this.year()%100}),r(0,["YYYY",4],0,"year"),r(0,["YYYYY",5],0,"year"),r(0,["YYYYYY",6,!0],0,"year"),l("year","y"),t("Y",vi),t("YY",h,y),t("YYYY",fr,ur),t("YYYYY",ai,ci),t("YYYYYY",ai,ci),c(["YYYYY","YYYYYY"],b),c("YYYY",function(n,t){t[b]=n.length===2?i.parseTwoDigitYear(n):s(n)}),c("YY",function(n,t){t[b]=i.parseTwoDigitYear(n)}),i.parseTwoDigitYear=function(n){return s(n)+(s(n)>68?1900:2e3)},yr=pt("FullYear",!1),r("w",["ww",2],"wo","week"),r("W",["WW",2],"Wo","isoWeek"),l("week","w"),l("isoWeek","W"),t("w",h),t("ww",h,y),t("W",h),t("WW",h,y),ii(["w","ww","W","WW"],function(n,t,i,r){t[r.substr(0,1)]=s(n)}),ef={dow:0,doy:6},r("DDD",["DDDD",3],"DDDo","dayOfYear"),l("dayOfYear","DDD"),t("DDD",li),t("DDDD",bu),c(["DDD","DDDD"],function(n,t,i){i._dayOfYear=s(n)}),i.ISO_8601=function(){},hf=w("moment().min is deprecated, use moment.min instead. https://github.com/moment/moment/issues/1548",function(){var n=o.apply(null,arguments);return n<this?this:n}),cf=w("moment().max is deprecated, use moment.max instead. https://github.com/moment/moment/issues/1548",function(){var n=o.apply(null,arguments);return n>this?this:n}),af("Z",":"),af("ZZ",""),t("Z",yi),t("ZZ",yi),c(["Z","ZZ"],function(n,t,i){i._useUTC=!0;i._tzm=kr(n)}),vf=/([\+\-]|\d\d)/gi,i.updateOffset=function(){},pf=/(\-)?(?:(\d*)\.)?(\d+)\:(\d+)(?:\:(\d+)\.?(\d{3})?)?/,wf=/^(-)?P(?:(?:([0-9,.]*)Y)?(?:([0-9,.]*)M)?(?:([0-9,.]*)D)?(?:T(?:([0-9,.]*)H)?(?:([0-9,.]*)M)?(?:([0-9,.]*)S)?)?|([0-9,.]*)W)$/,it.fn=pi.prototype,gf=kf(1,"add"),ne=kf(-1,"subtract"),i.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",nu=w("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(n){return n===undefined?this.localeData():this.locale(n)}),r(0,["gg",2],0,function(){return this.weekYear()%100}),r(0,["GG",2],0,function(){return this.isoWeekYear()%100}),wi("gggg","weekYear"),wi("ggggg","weekYear"),wi("GGGG","isoWeekYear"),wi("GGGGG","isoWeekYear"),l("weekYear","gg"),l("isoWeekYear","GG"),t("G",vi),t("g",vi),t("GG",h,y),t("gg",h,y),t("GGGG",fr,ur),t("gggg",fr,ur),t("GGGGG",ai,ci),t("ggggg",ai,ci),ii(["gggg","ggggg","GGGG","GGGGG"],function(n,t,i,r){t[r.substr(0,2)]=s(n)}),ii(["gg","GG"],function(n,t,r,u){t[u]=i.parseTwoDigitYear(n)}),r("Q",0,0,"quarter"),l("quarter","Q"),t("Q",wu),c("Q",function(n,t){t[g]=(s(n)-1)*3}),r("D",["DD",2],"Do","date"),l("date","D"),t("D",h),t("DD",h,y),t("Do",function(n,t){return n?t._ordinalParse:t._ordinalParseLenient}),c(["D","DD"],k),c("Do",function(n,t){t[k]=s(n.match(h)[0],10)}),tu=pt("Date",!0),r("d",0,"do","day"),r("dd",0,0,function(n){return this.localeData().weekdaysMin(this,n)}),r("ddd",0,0,function(n){return this.localeData().weekdaysShort(this,n)}),r("dddd",0,0,function(n){return this.localeData().weekdays(this,n)}),r("e",0,0,"weekday"),r("E",0,0,"isoWeekday"),l("day","d"),l("weekday","e"),l("isoWeekday","E"),t("d",h),t("e",h),t("E",h),t("dd",ti),t("ddd",ti),t("dddd",ti),ii(["dd","ddd","dddd"],function(n,t,i){var r=i._locale.weekdaysParse(n);r!=null?t.d=r:e(i).invalidWeekday=n}),ii(["d","e","E"],function(n,t,i,r){t[r]=s(n)}),fe="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),ee="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),oe="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),r("H",["HH",2],0,"hour"),r("h",["hh",2],0,function(){return this.hours()%12||12}),se("a",!0),se("A",!1),l("hour","h"),t("a",he),t("A",he),t("H",h),t("h",h),t("HH",h,y),t("hh",h,y),c(["H","HH"],a),c(["a","A"],function(n,t,i){i._isPm=i._locale.isPM(n);i._meridiem=n}),c(["h","hh"],function(n,t,i){t[a]=s(n);e(i).bigHour=!0}),ce=/[ap]\.?m?\.?/i,le=pt("Hours",!0),r("m",["mm",2],0,"minute"),l("minute","m"),t("m",h),t("mm",h,y),c(["m","mm"],st),ae=pt("Minutes",!1),r("s",["ss",2],0,"second"),l("second","s"),t("s",h),t("ss",h,y),c(["s","ss"],ht),ve=pt("Seconds",!1),r("S",0,0,function(){return~~(this.millisecond()/100)}),r(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),r(0,["SSS",3],0,"millisecond"),r(0,["SSSS",4],0,function(){return this.millisecond()*10}),r(0,["SSSSS",5],0,function(){return this.millisecond()*100}),r(0,["SSSSSS",6],0,function(){return this.millisecond()*1e3}),r(0,["SSSSSSS",7],0,function(){return this.millisecond()*1e4}),r(0,["SSSSSSSS",8],0,function(){return this.millisecond()*1e5}),r(0,["SSSSSSSSS",9],0,function(){return this.millisecond()*1e6}),l("millisecond","ms"),t("S",li,wu),t("SS",li,y),t("SSS",li,bu),rt="SSSS";rt.length<=9;rt+="S")t(rt,/\d+/);for(rt="S";rt.length<=9;rt+="S")c(rt,hl);ye=pt("Milliseconds",!1);r("z",0,0,"zoneAbbr");r("zz",0,0,"zoneName");n=dt.prototype;n.add=gf;n.calendar=ah;n.clone=vh;n.diff=kh;n.endOf=ec;n.format=nc;n.from=tc;n.fromNow=ic;n.to=rc;n.toNow=uc;n.get=vu;n.invalidAt=yc;n.isAfter=yh;n.isBefore=ph;n.isBetween=wh;n.isSame=bh;n.isValid=ac;n.lang=nu;n.locale=ie;n.localeData=re;n.max=cf;n.min=hf;n.parsingFlags=vc;n.set=vu;n.startOf=fc;n.subtract=ne;n.toArray=cc;n.toObject=lc;n.toDate=hc;n.toISOString=te;n.toJSON=te;n.toString=gh;n.unix=sc;n.valueOf=oc;n.year=yr;n.isLeapYear=fs;n.weekYear=pc;n.isoWeekYear=wc;n.quarter=n.quarters=dc;n.month=nf;n.daysInMonth=go;n.week=n.weeks=hs;n.isoWeek=n.isoWeeks=cs;n.weeksInYear=kc;n.isoWeeksInYear=bc;n.date=tu;n.day=n.days=ul;n.weekday=fl;n.isoWeekday=el;n.dayOfYear=as;n.hour=n.hours=le;n.minute=n.minutes=ae;n.second=n.seconds=ve;n.millisecond=n.milliseconds=ye;n.utcOffset=th;n.utc=rh;n.local=uh;n.parseZone=fh;n.hasAlignedHourOffset=eh;n.isDST=oh;n.isDSTShifted=sh;n.isLocal=hh;n.isUtcOffset=ch;n.isUtc=yf;n.isUTC=yf;n.zoneAbbr=cl;n.zoneName=ll;n.dates=w("dates accessor is deprecated. Use date instead.",tu);n.months=w("months accessor is deprecated. Use month instead",nf);n.years=w("years accessor is deprecated. Use year instead",yr);n.zone=w("moment().zone is deprecated, use moment().utcOffset instead. https://github.com/moment/moment/issues/1779",ih);pe=n;we={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};be={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};ke="Invalid date";de="%d";ge=/\d{1,2}/;to={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};u=ou.prototype;u._calendar=we;u.calendar=yl;u._longDateFormat=be;u.longDateFormat=pl;u._invalidDate=ke;u.invalidDate=wl;u._ordinal=de;u.ordinal=bl;u._ordinalParse=ge;u.preparse=no;u.postformat=no;u._relativeTime=to;u.relativeTime=kl;u.pastFuture=dl;u.set=gl;u.months=wo;u._months=ku;u.monthsShort=bo;u._monthsShort=du;u.monthsParse=ko;u.week=es;u._week=ef;u.firstDayOfYear=ss;u.firstDayOfWeek=os;u.weekdays=nl;u._weekdays=fe;u.weekdaysMin=il;u._weekdaysMin=oe;u.weekdaysShort=tl;u._weekdaysShort=ee;u.weekdaysParse=rl;u.isPM=ol;u._meridiemParse=ce;u.meridiem=sl;gt("en",{ordinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(n){var t=n%10,i=s(n%100/10)===1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i}});i.lang=w("moment.lang is deprecated. Use moment.locale instead.",gt);i.langData=w("moment.langData is deprecated. Use moment.localeData instead.",ot);d=Math.abs;var la=ut("ms"),aa=ut("s"),va=ut("m"),ya=ut("h"),pa=ut("d"),wa=ut("w"),ba=ut("M"),ka=ut("y");var ga=vt("milliseconds"),nv=vt("seconds"),tv=vt("minutes"),iv=vt("hours"),rv=vt("days"),uv=vt("months"),fv=vt("years");return yt=Math.round,nt={s:45,m:45,h:22,d:26,M:11},bi=Math.abs,f=pi.prototype,f.abs=fa,f.add=ea,f.subtract=oa,f.as=ha,f.asMilliseconds=la,f.asSeconds=aa,f.asMinutes=va,f.asHours=ya,f.asDays=pa,f.asWeeks=wa,f.asMonths=ba,f.asYears=ka,f.valueOf=ca,f._bubble=sa,f.get=da,f.milliseconds=ga,f.seconds=nv,f.minutes=tv,f.hours=iv,f.days=rv,f.weeks=ev,f.months=uv,f.years=fv,f.humanize=cv,f.toISOString=ki,f.toString=ki,f.toJSON=ki,f.locale=ie,f.localeData=re,f.toIsoString=w("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",ki),f.lang=nu,r("X",0,0,"unix"),r("x",0,0,"valueOf"),t("x",vi),t("X",/[+-]?\d+(\.\d{1,3})?/),c("X",function(n,t,i){i._d=new Date(parseFloat(n,10)*1e3)}),c("x",function(n,t,i){i._d=new Date(s(n))}),i.version="2.10.6",eo(o),i.fn=pe,i.min=gs,i.max=nh,i.utc=kt,i.unix=al,i.months=na,i.isDate=fi,i.locale=gt,i.invalid=fu,i.duration=it,i.isMoment=tt,i.weekdays=ia,i.parseZone=vl,i.localeData=ot,i.isDuration=br,i.monthsShort=ta,i.weekdaysMin=ua,i.defineLocale=cu,i.weekdaysShort=ra,i.normalizeUnits=p,i.relativeTimeThreshold=hv,i});Array.prototype.indexOf||(Array.prototype.indexOf=function(n,t){var r,f,u,i;if(this==null)throw new TypeError('"this" is null or not defined');if((f=Object(this),u=f.length>>>0,u===0)||(i=+t||0,Math.abs(i)===Infinity&&(i=0),i>=u))return-1;for(r=Math.max(i>=0?i:u-Math.abs(i),0);r<u;){if(r in f&&f[r]===n)return r;r++}return-1});Array.prototype.reduce||(Array.prototype.reduce=function(n){"use strict";if(this==null)throw new TypeError("Array.prototype.reduce called on null or undefined");if(typeof n!="function")throw new TypeError(n+" is not a function");var i=Object(this),u=i.length>>>0,t=0,r;if(arguments.length==2)r=arguments[1];else{while(t<u&&!(t in i))t++;if(t>=u)throw new TypeError("Reduce of empty array with no initial value");r=i[t++]}for(;t<u;t++)t in i&&(r=n(r,i[t],t,i));return r});String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}),function(n){n.fn.datepicker=function(t){function r(n,t){var i="IE",r=document.createElement("B"),u=document.documentElement,f;return n&&(i+=" "+n,t&&(i=t+" "+i)),r.innerHTML="<!--[if "+i+']><b id="iecctest"><\/b><![endif]-->',u.appendChild(r),f=!!document.getElementById("iecctest"),u.removeChild(r),f}var i={allowInputToggle:!1,calendarWeeks:!1,dayViewHeaderFormat:"MMM YYYY",format:"MM/DD/YYYY",showTodayButton:!1,ignoreReadonly:!0,debug:r(8)};return n.extend(i,t),this.datetimepicker(i)}}(jQuery);
/*! version : 4.17.42
 =========================================================
 bootstrap-datetimejs
 https://github.com/Eonasdan/bootstrap-datetimepicker
 Copyright (c) 2015 Jonathan Peterson
 =========================================================
 */
(function(n){"use strict";if(typeof define=="function"&&define.amd)define(["jquery","moment"],n);else if(typeof exports=="object")n(require("jquery"),require("moment"));else{if(typeof jQuery=="undefined")throw"bootstrap-datetimepicker requires jQuery to be loaded first";if(typeof moment=="undefined")throw"bootstrap-datetimepicker requires Moment.js to be loaded first";n(jQuery,moment)}})(function(n,t){"use strict";if(!t)throw new Error("bootstrap-datetimepicker requires Moment.js to be loaded first");var i=function(i,r){var u={},e,o,k=!0,s,l=!1,f=!1,d,nt=0,y,it,b,ot=[{clsName:"days",navFnc:"M",navStep:1},{clsName:"months",navFnc:"y",navStep:1},{clsName:"years",navFnc:"y",navStep:10},{clsName:"decades",navFnc:"y",navStep:100}],vt=["days","months","years","decades"],bt=["top","bottom","auto"],kt=["left","right","auto"],dt=["default","top","bottom"],gt={up:38,38:"up",down:40,40:"down",left:37,37:"left",right:39,39:"right",tab:9,9:"tab",escape:27,27:"escape",enter:13,13:"enter",pageUp:33,33:"pageUp",pageDown:34,34:"pageDown",shift:16,16:"shift",control:17,17:"control",space:32,32:"space",t:84,84:"t","delete":46,46:"delete"},st={},g=function(n){var i;return i=n===undefined||n===null?t():yt()?t.tz(n,it,r.useStrict,r.timeZone):t(n,it,r.useStrict),yt()&&i.tz(r.timeZone),i},p=function(n){if(typeof n!="string"||n.length>1)throw new TypeError("isEnabled expects a single character string parameter");switch(n){case"y":return y.indexOf("Y")!==-1;case"M":return y.indexOf("M")!==-1;case"d":return y.toLowerCase().indexOf("d")!==-1;case"h":case"H":return y.toLowerCase().indexOf("h")!==-1;case"m":return y.indexOf("m")!==-1;case"s":return y.indexOf("s")!==-1;default:return!1}},ht=function(){return p("h")||p("m")||p("s")},yt=function(){return t.tz!==undefined&&r.timeZone!==undefined&&r.timeZone!==null&&r.timeZone!==""},ct=function(){return p("y")||p("M")||p("d")},ei=function(){var t=n("<thead>").append(n("<tr>").append(n("<th>").addClass("prev").attr("data-action","previous").append(n("<span>").addClass(r.icons.previous))).append(n("<th>").addClass("picker-switch").attr("data-action","pickerSwitch").attr("colspan",r.calendarWeeks?"6":"5")).append(n("<th>").addClass("next").attr("data-action","next").append(n("<span>").addClass(r.icons.next)))),i=n("<tbody>").append(n("<tr>").append(n("<td>").attr("colspan",r.calendarWeeks?"8":"7")));return[n("<div>").addClass("datepicker-days").append(n("<table>").addClass("table-condensed").append(t).append(n("<tbody>"))),n("<div>").addClass("datepicker-months").append(n("<table>").addClass("table-condensed").append(t.clone()).append(i.clone())),n("<div>").addClass("datepicker-years").append(n("<table>").addClass("table-condensed").append(t.clone()).append(i.clone())),n("<div>").addClass("datepicker-decades").append(n("<table>").addClass("table-condensed").append(t.clone()).append(i.clone()))]},oi=function(){var t=n("<tr>"),i=n("<tr>"),u=n("<tr>");return p("h")&&(t.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.incrementHour}).addClass("btn").attr("data-action","incrementHours").append(n("<span>").addClass(r.icons.up)))),i.append(n("<td>").append(n("<span>").addClass("timepicker-hour").attr({"data-time-component":"hours",title:r.tooltips.pickHour}).attr("data-action","showHours"))),u.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.decrementHour}).addClass("btn").attr("data-action","decrementHours").append(n("<span>").addClass(r.icons.down))))),p("m")&&(p("h")&&(t.append(n("<td>").addClass("separator")),i.append(n("<td>").addClass("separator").html(":")),u.append(n("<td>").addClass("separator"))),t.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.incrementMinute}).addClass("btn").attr("data-action","incrementMinutes").append(n("<span>").addClass(r.icons.up)))),i.append(n("<td>").append(n("<span>").addClass("timepicker-minute").attr({"data-time-component":"minutes",title:r.tooltips.pickMinute}).attr("data-action","showMinutes"))),u.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.decrementMinute}).addClass("btn").attr("data-action","decrementMinutes").append(n("<span>").addClass(r.icons.down))))),p("s")&&(p("m")&&(t.append(n("<td>").addClass("separator")),i.append(n("<td>").addClass("separator").html(":")),u.append(n("<td>").addClass("separator"))),t.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.incrementSecond}).addClass("btn").attr("data-action","incrementSeconds").append(n("<span>").addClass(r.icons.up)))),i.append(n("<td>").append(n("<span>").addClass("timepicker-second").attr({"data-time-component":"seconds",title:r.tooltips.pickSecond}).attr("data-action","showSeconds"))),u.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.decrementSecond}).addClass("btn").attr("data-action","decrementSeconds").append(n("<span>").addClass(r.icons.down))))),d||(t.append(n("<td>").addClass("separator")),i.append(n("<td>").append(n("<button>").addClass("btn btn-primary").attr({"data-action":"togglePeriod",tabindex:"-1",title:r.tooltips.togglePeriod}))),u.append(n("<td>").addClass("separator"))),n("<div>").addClass("timepicker-picker").append(n("<table>").addClass("table-condensed").append([t,i,u]))},si=function(){var i=n("<div>").addClass("timepicker-hours").append(n("<table>").addClass("table-condensed")),r=n("<div>").addClass("timepicker-minutes").append(n("<table>").addClass("table-condensed")),u=n("<div>").addClass("timepicker-seconds").append(n("<table>").addClass("table-condensed")),t=[oi()];return p("h")&&t.push(i),p("m")&&t.push(r),p("s")&&t.push(u),t},hi=function(){var t=[];return r.showTodayButton&&t.push(n("<td>").append(n("<a>").attr({"data-action":"today",title:r.tooltips.today}).append(n("<span>").addClass(r.icons.today)))),!r.sideBySide&&ct()&&ht()&&t.push(n("<td>").append(n("<a>").attr({"data-action":"togglePicker",title:r.tooltips.selectTime}).append(n("<span>").addClass(r.icons.time)))),r.showClear&&t.push(n("<td>").append(n("<a>").attr({"data-action":"clear",title:r.tooltips.clear}).append(n("<span>").addClass(r.icons.clear)))),r.showClose&&t.push(n("<td>").append(n("<a>").attr({"data-action":"close",title:r.tooltips.close}).append(n("<span>").addClass(r.icons.close)))),n("<table>").addClass("table-condensed").append(n("<tbody>").append(n("<tr>").append(t)))},ci=function(){var t=n("<div>").addClass("bootstrap-datetimepicker-widget dropdown-menu"),f=n("<div>").addClass("datepicker").append(ei()),e=n("<div>").addClass("timepicker").append(si()),i=n("<ul>").addClass("list-unstyled"),u=n("<li>").addClass("picker-switch"+(r.collapse?" accordion-toggle":"")).append(hi());return(r.inline&&t.removeClass("dropdown-menu"),d&&t.addClass("usetwentyfour"),p("s")&&!d&&t.addClass("wider"),r.sideBySide&&ct()&&ht())?(t.addClass("timepicker-sbs"),r.toolbarPlacement==="top"&&t.append(u),t.append(n("<div>").addClass("row").append(f.addClass("col-md-6")).append(e.addClass("col-md-6"))),r.toolbarPlacement==="bottom"&&t.append(u),t):(r.toolbarPlacement==="top"&&i.append(u),ct()&&i.append(n("<li>").addClass(r.collapse&&ht()?"collapse in":"").append(f)),r.toolbarPlacement==="default"&&i.append(u),ht()&&i.append(n("<li>").addClass(r.collapse&&ct()?"collapse":"").append(e)),r.toolbarPlacement==="bottom"&&i.append(u),t.append(i))},li=function(){var t,u={};return t=i.is("input")||r.inline?i.data():i.find("input").data(),t.dateOptions&&t.dateOptions instanceof Object&&(u=n.extend(!0,u,t.dateOptions)),n.each(r,function(n){var i="date"+n.charAt(0).toUpperCase()+n.slice(1);t[i]!==undefined&&(u[n]=t[i])}),u},pt=function(){var o=(l||i).position(),s=(l||i).offset(),u=r.widgetPositioning.vertical,e=r.widgetPositioning.horizontal,t;if(r.widgetParent)t=r.widgetParent.append(f);else if(i.is("input"))t=i.after(f).parent();else{if(r.inline){t=i.append(f);return}t=i;i.children().first().after(f)}if(u==="auto"&&(u=s.top+f.height()*1.5>=n(window).height()+n(window).scrollTop()&&f.height()+i.outerHeight()<s.top?"top":"bottom"),e==="auto"&&(e=t.width()<s.left+f.outerWidth()/2&&s.left+f.outerWidth()>n(window).width()?"right":"left"),u==="top"?f.addClass("top").removeClass("bottom"):f.addClass("bottom").removeClass("top"),e==="right"?f.addClass("pull-right"):f.removeClass("pull-right"),t.css("position")!=="relative"&&(t=t.parents().filter(function(){return n(this).css("position")==="relative"}).first()),t.length===0)throw new Error("datetimepicker component should be placed within a relative positioned container");f.css({top:u==="top"?"auto":o.top+i.outerHeight(),bottom:u==="top"?t.outerHeight()-(t===i?0:o.top):"auto",left:e==="left"?t===i?0:o.left:"auto",right:e==="left"?"auto":t.outerWidth()-i.outerWidth()-(t===i?0:o.left)})},rt=function(n){(n.type!=="dp.change"||(!n.date||!n.date.isSame(n.oldDate))&&(n.date||n.oldDate))&&i.trigger(n)},ut=function(n){n==="y"&&(n="YYYY");rt({type:"dp.update",change:n,viewDate:o.clone()})},ft=function(n){f&&(b=n?Math.max(nt,Math.min(3,b+n)):0,f.find(".datepicker > div").hide().filter(".datepicker-"+ot[b].clsName).show())},ai=function(){var t=n("<tr>"),i=o.clone().startOf("w").startOf("d");for(r.calendarWeeks===!0&&t.append(n("<th>").addClass("cw").text("#"));i.isBefore(o.clone().endOf("w"));)t.append(n("<th>").addClass("dow").text(i.format("dd"))),i.add(1,"d");f.find(".datepicker-days thead").append(t)},vi=function(n){return r.disabledDates[n.format("YYYY-MM-DD")]===!0},yi=function(n){return r.enabledDates[n.format("YYYY-MM-DD")]===!0},pi=function(n){return r.disabledHours[n.format("H")]===!0},wi=function(n){return r.enabledHours[n.format("H")]===!0},c=function(t,i){if(!t.isValid()||r.disabledDates&&i==="d"&&vi(t)||r.enabledDates&&i==="d"&&!yi(t)||r.minDate&&t.isBefore(r.minDate,i)||r.maxDate&&t.isAfter(r.maxDate,i)||r.daysOfWeekDisabled&&i==="d"&&r.daysOfWeekDisabled.indexOf(t.day())!==-1||r.disabledHours&&(i==="h"||i==="m"||i==="s")&&pi(t)||r.enabledHours&&(i==="h"||i==="m"||i==="s")&&!wi(t))return!1;if(r.disabledTimeIntervals&&(i==="h"||i==="m"||i==="s")){var u=!1;if(n.each(r.disabledTimeIntervals,function(){if(t.isBetween(this[0],this[1]))return u=!0,!1}),u)return!1}return!0},bi=function(){for(var i=[],t=o.clone().startOf("y").startOf("d");t.isSame(o,"y");)i.push(n("<span>").attr("data-action","selectMonth").addClass("month").text(t.format("MMM"))),t.add(1,"M");f.find(".datepicker-months td").empty().append(i)},ki=function(){var i=f.find(".datepicker-months"),t=i.find("th"),u=i.find("tbody").find("span");t.eq(0).find("span").attr("title",r.tooltips.prevYear);t.eq(1).attr("title",r.tooltips.selectYear);t.eq(2).find("span").attr("title",r.tooltips.nextYear);i.find(".disabled").removeClass("disabled");c(o.clone().subtract(1,"y"),"y")||t.eq(0).addClass("disabled");t.eq(1).text(o.year());c(o.clone().add(1,"y"),"y")||t.eq(2).addClass("disabled");u.removeClass("active");e.isSame(o,"y")&&!k&&u.eq(e.month()).addClass("active");u.each(function(t){c(o.clone().month(t),"M")||n(this).addClass("disabled")})},di=function(){var i=f.find(".datepicker-years"),t=i.find("th"),n=o.clone().subtract(5,"y"),u=o.clone().add(6,"y"),s="";for(t.eq(0).find("span").attr("title",r.tooltips.prevDecade),t.eq(1).attr("title",r.tooltips.selectDecade),t.eq(2).find("span").attr("title",r.tooltips.nextDecade),i.find(".disabled").removeClass("disabled"),r.minDate&&r.minDate.isAfter(n,"y")&&t.eq(0).addClass("disabled"),t.eq(1).text(n.year()+"-"+u.year()),r.maxDate&&r.maxDate.isBefore(u,"y")&&t.eq(2).addClass("disabled");!n.isAfter(u,"y");)s+='<span data-action="selectYear" class="year'+(n.isSame(e,"y")&&!k?" active":"")+(c(n,"y")?"":" disabled")+'">'+n.year()+"<\/span>",n.add(1,"y");i.find("td").html(s)},gi=function(){var s=f.find(".datepicker-decades"),i=s.find("th"),n=t({y:o.year()-o.year()%100-1}),h=n.clone().add(100,"y"),y=n.clone(),a=!1,v=!1,u,l="";for(i.eq(0).find("span").attr("title",r.tooltips.prevCentury),i.eq(2).find("span").attr("title",r.tooltips.nextCentury),s.find(".disabled").removeClass("disabled"),(n.isSame(t({y:1900}))||r.minDate&&r.minDate.isAfter(n,"y"))&&i.eq(0).addClass("disabled"),i.eq(1).text(n.year()+"-"+h.year()),(n.isSame(t({y:2e3}))||r.maxDate&&r.maxDate.isBefore(h,"y"))&&i.eq(2).addClass("disabled");!n.isAfter(h,"y");)u=n.year()+12,a=r.minDate&&r.minDate.isAfter(n,"y")&&r.minDate.year()<=u,v=r.maxDate&&r.maxDate.isAfter(n,"y")&&r.maxDate.year()<=u,l+='<span data-action="selectDecade" class="decade'+(e.isAfter(n)&&e.year()<=u?" active":"")+(!c(n,"y")&&!a&&!v?" disabled":"")+'" data-selection="'+(n.year()+6)+'">'+(n.year()+1)+" - "+(n.year()+12)+"<\/span>",n.add(12,"y");l+="<span><\/span><span><\/span><span><\/span>";s.find("td").html(l);i.eq(1).text(y.year()+1+"-"+n.year())},et=function(){var h=f.find(".datepicker-days"),u=h.find("th"),t,a=[],s,i,l;if(ct()){for(u.eq(0).find("span").attr("title",r.tooltips.prevMonth),u.eq(1).attr("title",r.tooltips.selectMonth),u.eq(2).find("span").attr("title",r.tooltips.nextMonth),h.find(".disabled").removeClass("disabled"),u.eq(1).text(o.format(r.dayViewHeaderFormat)),c(o.clone().subtract(1,"M"),"M")||u.eq(0).addClass("disabled"),c(o.clone().add(1,"M"),"M")||u.eq(2).addClass("disabled"),t=o.clone().startOf("M").startOf("w").startOf("d"),l=0;l<42;l++)t.weekday()===0&&(s=n("<tr>"),r.calendarWeeks&&s.append('<td class="cw">'+t.week()+"<\/td>"),a.push(s)),i="",t.isBefore(o,"M")&&(i+=" old"),t.isAfter(o,"M")&&(i+=" new"),t.isSame(e,"d")&&!k&&(i+=" active"),c(t,"d")||(i+=" disabled"),t.isSame(g(),"d")&&(i+=" today"),(t.day()===0||t.day()===6)&&(i+=" weekend"),s.append('<td data-action="selectDay" data-day="'+t.format("L")+'" class="day'+i+'">'+t.date()+"<\/td>"),t.add(1,"d");h.find("tbody").empty().append(a);ki();di();gi()}},nr=function(){var u=f.find(".timepicker-hours table"),t=o.clone().startOf("d"),r=[],i=n("<tr>");for(o.hour()>11&&!d&&t.hour(12);t.isSame(o,"d")&&(d||o.hour()<12&&t.hour()<12||o.hour()>11);)t.hour()%4==0&&(i=n("<tr>"),r.push(i)),i.append('<td data-action="selectHour" class="hour'+(c(t,"h")?"":" disabled")+'">'+t.format(d?"HH":"hh")+"<\/td>"),t.add(1,"h");u.empty().append(r)},tr=function(){for(var s=f.find(".timepicker-minutes table"),t=o.clone().startOf("h"),u=[],i=n("<tr>"),e=r.stepping===1?5:r.stepping;o.isSame(t,"h");)t.minute()%(e*4)==0&&(i=n("<tr>"),u.push(i)),i.append('<td data-action="selectMinute" class="minute'+(c(t,"m")?"":" disabled")+'">'+t.format("mm")+"<\/td>"),t.add(e,"m");s.empty().append(u)},ir=function(){for(var u=f.find(".timepicker-seconds table"),t=o.clone().startOf("m"),r=[],i=n("<tr>");o.isSame(t,"m");)t.second()%20==0&&(i=n("<tr>"),r.push(i)),i.append('<td data-action="selectSecond" class="second'+(c(t,"s")?"":" disabled")+'">'+t.format("ss")+"<\/td>"),t.add(5,"s");u.empty().append(r)},rr=function(){var n,i,t=f.find(".timepicker span[data-time-component]");d||(n=f.find(".timepicker [data-action=togglePeriod]"),i=e.clone().add(e.hours()>=12?-12:12,"h"),n.text(e.format("A")),c(i,"h")?n.removeClass("disabled"):n.addClass("disabled"));t.filter("[data-time-component=hours]").text(e.format(d?"HH":"hh"));t.filter("[data-time-component=minutes]").text(e.format("mm"));t.filter("[data-time-component=seconds]").text(e.format("ss"));nr();tr();ir()},a=function(){f&&(et(),rr())},h=function(n){var t=k?null:e;if(!n){k=!0;s.val("");i.data("date","");rt({type:"dp.change",date:!1,oldDate:t});a();return}n=n.clone().locale(r.locale);yt()&&n.tz(r.timeZone);r.stepping!==1&&n.minutes(Math.round(n.minutes()/r.stepping)*r.stepping).seconds(0);c(n)?(e=n,o=e.clone(),s.val(e.format(y)),i.data("date",e.format(y)),k=!1,a(),rt({type:"dp.change",date:e.clone(),oldDate:t})):(r.keepInvalid||s.val(k?"":e.format(y)),rt({type:"dp.error",date:n}))},v=function(){var t=!1;return f?(f.find(".collapse").each(function(){var i=n(this).data("collapse");return i&&i.transitioning?(t=!0,!1):!0}),t)?u:(l&&l.hasClass("btn")&&l.toggleClass("active"),f.hide(),n(window).off("resize",pt),f.off("click","[data-action]"),f.off("mousedown",!1),f.remove(),f=!1,rt({type:"dp.hide",date:e.clone()}),s.blur(),u):u},ni=function(){h(null)},tt=function(n){return r.parseInputDate===undefined?t.isMoment(n)||(n=g(n)):n=r.parseInputDate(n),n},lt={next:function(){var n=ot[b].navFnc;o.add(ot[b].navStep,n);et();ut(n)},previous:function(){var n=ot[b].navFnc;o.subtract(ot[b].navStep,n);et();ut(n)},pickerSwitch:function(){ft(1)},selectMonth:function(t){var i=n(t.target).closest("tbody").find("span").index(n(t.target));o.month(i);b===nt?(h(e.clone().year(o.year()).month(o.month())),r.inline||v()):(ft(-1),et());ut("M")},selectYear:function(t){var i=parseInt(n(t.target).text(),10)||0;o.year(i);b===nt?(h(e.clone().year(o.year())),r.inline||v()):(ft(-1),et());ut("YYYY")},selectDecade:function(t){var i=parseInt(n(t.target).data("selection"),10)||0;o.year(i);b===nt?(h(e.clone().year(o.year())),r.inline||v()):(ft(-1),et());ut("YYYY")},selectDay:function(t){var i=o.clone();n(t.target).is(".old")&&i.subtract(1,"M");n(t.target).is(".new")&&i.add(1,"M");h(i.date(parseInt(n(t.target).text(),10)));ht()||r.keepOpen||r.inline||v()},incrementHours:function(){var n=e.clone().add(1,"h");c(n,"h")&&h(n)},incrementMinutes:function(){var n=e.clone().add(r.stepping,"m");c(n,"m")&&h(n)},incrementSeconds:function(){var n=e.clone().add(1,"s");c(n,"s")&&h(n)},decrementHours:function(){var n=e.clone().subtract(1,"h");c(n,"h")&&h(n)},decrementMinutes:function(){var n=e.clone().subtract(r.stepping,"m");c(n,"m")&&h(n)},decrementSeconds:function(){var n=e.clone().subtract(1,"s");c(n,"s")&&h(n)},togglePeriod:function(){h(e.clone().add(e.hours()>=12?-12:12,"h"))},togglePicker:function(t){var u=n(t.target),e=u.closest("ul"),i=e.find(".in"),o=e.find(".collapse:not(.in)"),f;if(i&&i.length){if(f=i.data("collapse"),f&&f.transitioning)return;i.collapse?(i.collapse("hide"),o.collapse("show")):(i.removeClass("in"),o.addClass("in"));u.is("span")?u.toggleClass(r.icons.time+" "+r.icons.date):u.find("span").toggleClass(r.icons.time+" "+r.icons.date)}},showPicker:function(){f.find(".timepicker > div:not(.timepicker-picker)").hide();f.find(".timepicker .timepicker-picker").show()},showHours:function(){f.find(".timepicker .timepicker-picker").hide();f.find(".timepicker .timepicker-hours").show()},showMinutes:function(){f.find(".timepicker .timepicker-picker").hide();f.find(".timepicker .timepicker-minutes").show()},showSeconds:function(){f.find(".timepicker .timepicker-picker").hide();f.find(".timepicker .timepicker-seconds").show()},selectHour:function(t){var i=parseInt(n(t.target).text(),10);d||(e.hours()>=12?i!==12&&(i+=12):i===12&&(i=0));h(e.clone().hours(i));lt.showPicker.call(u)},selectMinute:function(t){h(e.clone().minutes(parseInt(n(t.target).text(),10)));lt.showPicker.call(u)},selectSecond:function(t){h(e.clone().seconds(parseInt(n(t.target).text(),10)));lt.showPicker.call(u)},clear:ni,today:function(){var n=g();c(n,"d")&&h(n)},close:v},ur=function(t){return n(t.currentTarget).is(".disabled")?!1:(lt[n(t.currentTarget).data("action")].apply(u,arguments),!1)},w=function(){var t,i={year:function(n){return n.month(0).date(1).hours(0).seconds(0).minutes(0)},month:function(n){return n.date(1).hours(0).seconds(0).minutes(0)},day:function(n){return n.hours(0).seconds(0).minutes(0)},hour:function(n){return n.seconds(0).minutes(0)},minute:function(n){return n.seconds(0)}};if(s.prop("disabled")||!r.ignoreReadonly&&s.prop("readonly")||f)return u;s.val()!==undefined&&s.val().trim().length!==0?h(tt(s.val().trim())):r.useCurrent&&k&&(r.inline||s.is("input")&&s.val().trim().length===0)&&(t=g(),typeof r.useCurrent=="string"&&(t=i[r.useCurrent](t)),h(t));f=ci();ai();bi();f.find(".timepicker-hours").hide();f.find(".timepicker-minutes").hide();f.find(".timepicker-seconds").hide();a();ft();n(window).on("resize",pt);f.on("click","[data-action]",ur);f.on("mousedown",!1);return l&&l.hasClass("btn")&&l.toggleClass("active"),pt(),f.show(),r.focusOnShow&&!s.is(":focus")&&s.focus(),rt({type:"dp.show"}),u},wt=function(){return f?v():w()},ti=function(n){var o=null,t,e,c=[],l={},s=n.which,i,h,a="p";st[s]=a;for(t in st)st.hasOwnProperty(t)&&st[t]===a&&(c.push(t),parseInt(t,10)!==s&&(l[t]=!0));for(t in r.keyBinds)if(r.keyBinds.hasOwnProperty(t)&&typeof r.keyBinds[t]=="function"&&(i=t.split(" "),i.length===c.length&&gt[s]===i[i.length-1])){for(h=!0,e=i.length-2;e>=0;e--)if(!(gt[i[e]]in l)){h=!1;break}if(h){o=r.keyBinds[t];break}}o&&(o.call(u,f),n.stopPropagation(),n.preventDefault())},ii=function(n){st[n.which]="r";n.stopPropagation();n.preventDefault()},ri=function(t){var i=n(t.target).val().trim(),r=i?tt(i):null;return h(r),t.stopImmediatePropagation(),!1},fr=function(){s.on({change:ri,blur:r.debug?"":v,keydown:ti,keyup:ii,focus:r.allowInputToggle?w:""});if(i.is("input"))s.on({focus:w});else if(l){l.on("click",wt);l.on("mousedown",!1)}},er=function(){s.off({change:ri,blur:blur,keydown:ti,keyup:ii,focus:r.allowInputToggle?v:""});i.is("input")?s.off({focus:w}):l&&(l.off("click",wt),l.off("mousedown",!1))},ui=function(t){var i={};return n.each(t,function(){var n=tt(this);n.isValid()&&(i[n.format("YYYY-MM-DD")]=!0)}),Object.keys(i).length?i:!1},fi=function(t){var i={};return n.each(t,function(){i[this]=!0}),Object.keys(i).length?i:!1},at=function(){var n=r.format||"L LT";y=n.replace(/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,function(n){var t=e.localeData().longDateFormat(n)||n;return t.replace(/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,function(n){return e.localeData().longDateFormat(n)||n})});it=r.extraFormats?r.extraFormats.slice():[];it.indexOf(n)<0&&it.indexOf(y)<0&&it.push(y);d=y.toLowerCase().indexOf("a")<1&&y.replace(/\[.*?\]/g,"").indexOf("h")<1;p("y")&&(nt=2);p("M")&&(nt=1);p("d")&&(nt=0);b=Math.max(nt,b);k||h(e)};if(u.destroy=function(){v();er();i.removeData("DateTimePicker");i.removeData("date")},u.toggle=wt,u.show=w,u.hide=v,u.disable=function(){return v(),l&&l.hasClass("btn")&&l.addClass("disabled"),s.prop("disabled",!0),u},u.enable=function(){return l&&l.hasClass("btn")&&l.removeClass("disabled"),s.prop("disabled",!1),u},u.ignoreReadonly=function(n){if(arguments.length===0)return r.ignoreReadonly;if(typeof n!="boolean")throw new TypeError("ignoreReadonly () expects a boolean parameter");return r.ignoreReadonly=n,u},u.options=function(t){if(arguments.length===0)return n.extend(!0,{},r);if(!(t instanceof Object))throw new TypeError("options() options parameter should be an object");return n.extend(!0,r,t),n.each(r,function(n,t){if(u[n]!==undefined)u[n](t);else throw new TypeError("option "+n+" is not recognized!");}),u},u.date=function(n){if(arguments.length===0)return k?null:e.clone();if(n!==null&&typeof n!="string"&&!t.isMoment(n)&&!(n instanceof Date))throw new TypeError("date() parameter must be one of [null, string, moment or Date]");return h(n===null?null:tt(n)),u},u.format=function(n){if(arguments.length===0)return r.format;if(typeof n!="string"&&(typeof n!="boolean"||n!==!1))throw new TypeError("format() expects a sting or boolean:false parameter "+n);return r.format=n,y&&at(),u},u.timeZone=function(n){if(arguments.length===0)return r.timeZone;if(typeof n!="string")throw new TypeError("newZone() expects a string parameter");return r.timeZone=n,u},u.dayViewHeaderFormat=function(n){if(arguments.length===0)return r.dayViewHeaderFormat;if(typeof n!="string")throw new TypeError("dayViewHeaderFormat() expects a string parameter");return r.dayViewHeaderFormat=n,u},u.extraFormats=function(n){if(arguments.length===0)return r.extraFormats;if(n!==!1&&!(n instanceof Array))throw new TypeError("extraFormats() expects an array or false parameter");return r.extraFormats=n,it&&at(),u},u.disabledDates=function(t){if(arguments.length===0)return r.disabledDates?n.extend({},r.disabledDates):r.disabledDates;if(!t)return r.disabledDates=!1,a(),u;if(!(t instanceof Array))throw new TypeError("disabledDates() expects an array parameter");return r.disabledDates=ui(t),r.enabledDates=!1,a(),u},u.enabledDates=function(t){if(arguments.length===0)return r.enabledDates?n.extend({},r.enabledDates):r.enabledDates;if(!t)return r.enabledDates=!1,a(),u;if(!(t instanceof Array))throw new TypeError("enabledDates() expects an array parameter");return r.enabledDates=ui(t),r.disabledDates=!1,a(),u},u.daysOfWeekDisabled=function(n){if(arguments.length===0)return r.daysOfWeekDisabled.splice(0);if(typeof n=="boolean"&&!n)return r.daysOfWeekDisabled=!1,a(),u;if(!(n instanceof Array))throw new TypeError("daysOfWeekDisabled() expects an array parameter");if(r.daysOfWeekDisabled=n.reduce(function(n,t){return(t=parseInt(t,10),t>6||t<0||isNaN(t))?n:(n.indexOf(t)===-1&&n.push(t),n)},[]).sort(),r.useCurrent&&!r.keepInvalid){for(var t=0;!c(e,"d");){if(e.add(1,"d"),t===7)throw"Tried 7 times to find a valid date";t++}h(e)}return a(),u},u.maxDate=function(n){if(arguments.length===0)return r.maxDate?r.maxDate.clone():r.maxDate;if(typeof n=="boolean"&&n===!1)return r.maxDate=!1,a(),u;typeof n=="string"&&(n==="now"||n==="moment")&&(n=g());var t=tt(n);if(!t.isValid())throw new TypeError("maxDate() Could not parse date parameter: "+n);if(r.minDate&&t.isBefore(r.minDate))throw new TypeError("maxDate() date parameter is before options.minDate: "+t.format(y));return r.maxDate=t,r.useCurrent&&!r.keepInvalid&&e.isAfter(n)&&h(r.maxDate),o.isAfter(t)&&(o=t.clone().subtract(r.stepping,"m")),a(),u},u.minDate=function(n){if(arguments.length===0)return r.minDate?r.minDate.clone():r.minDate;if(typeof n=="boolean"&&n===!1)return r.minDate=!1,a(),u;typeof n=="string"&&(n==="now"||n==="moment")&&(n=g());var t=tt(n);if(!t.isValid())throw new TypeError("minDate() Could not parse date parameter: "+n);if(r.maxDate&&t.isAfter(r.maxDate))throw new TypeError("minDate() date parameter is after options.maxDate: "+t.format(y));return r.minDate=t,r.useCurrent&&!r.keepInvalid&&e.isBefore(n)&&h(r.minDate),o.isBefore(t)&&(o=t.clone().add(r.stepping,"m")),a(),u},u.defaultDate=function(n){if(arguments.length===0)return r.defaultDate?r.defaultDate.clone():r.defaultDate;if(!n)return r.defaultDate=!1,u;typeof n=="string"&&(n=n==="now"||n==="moment"?g():g(n));var t=tt(n);if(!t.isValid())throw new TypeError("defaultDate() Could not parse date parameter: "+n);if(!c(t))throw new TypeError("defaultDate() date passed is invalid according to component setup validations");return r.defaultDate=t,(r.defaultDate&&r.inline||s.val().trim()==="")&&h(r.defaultDate),u},u.locale=function(n){if(arguments.length===0)return r.locale;if(!t.localeData(n))throw new TypeError("locale() locale "+n+" is not loaded from moment locales!");return r.locale=n,e.locale(r.locale),o.locale(r.locale),y&&at(),f&&(v(),w()),u},u.stepping=function(n){return arguments.length===0?r.stepping:(n=parseInt(n,10),(isNaN(n)||n<1)&&(n=1),r.stepping=n,u)},u.useCurrent=function(n){var t=["year","month","day","hour","minute"];if(arguments.length===0)return r.useCurrent;if(typeof n!="boolean"&&typeof n!="string")throw new TypeError("useCurrent() expects a boolean or string parameter");if(typeof n=="string"&&t.indexOf(n.toLowerCase())===-1)throw new TypeError("useCurrent() expects a string parameter of "+t.join(", "));return r.useCurrent=n,u},u.collapse=function(n){if(arguments.length===0)return r.collapse;if(typeof n!="boolean")throw new TypeError("collapse() expects a boolean parameter");return r.collapse===n?u:(r.collapse=n,f&&(v(),w()),u)},u.icons=function(t){if(arguments.length===0)return n.extend({},r.icons);if(!(t instanceof Object))throw new TypeError("icons() expects parameter to be an Object");return n.extend(r.icons,t),f&&(v(),w()),u},u.tooltips=function(t){if(arguments.length===0)return n.extend({},r.tooltips);if(!(t instanceof Object))throw new TypeError("tooltips() expects parameter to be an Object");return n.extend(r.tooltips,t),f&&(v(),w()),u},u.useStrict=function(n){if(arguments.length===0)return r.useStrict;if(typeof n!="boolean")throw new TypeError("useStrict() expects a boolean parameter");return r.useStrict=n,u},u.sideBySide=function(n){if(arguments.length===0)return r.sideBySide;if(typeof n!="boolean")throw new TypeError("sideBySide() expects a boolean parameter");return r.sideBySide=n,f&&(v(),w()),u},u.viewMode=function(n){if(arguments.length===0)return r.viewMode;if(typeof n!="string")throw new TypeError("viewMode() expects a string parameter");if(vt.indexOf(n)===-1)throw new TypeError("viewMode() parameter must be one of ("+vt.join(", ")+") value");return r.viewMode=n,b=Math.max(vt.indexOf(n),nt),ft(),u},u.toolbarPlacement=function(n){if(arguments.length===0)return r.toolbarPlacement;if(typeof n!="string")throw new TypeError("toolbarPlacement() expects a string parameter");if(dt.indexOf(n)===-1)throw new TypeError("toolbarPlacement() parameter must be one of ("+dt.join(", ")+") value");return r.toolbarPlacement=n,f&&(v(),w()),u},u.widgetPositioning=function(t){if(arguments.length===0)return n.extend({},r.widgetPositioning);if({}.toString.call(t)!=="[object Object]")throw new TypeError("widgetPositioning() expects an object variable");if(t.horizontal){if(typeof t.horizontal!="string")throw new TypeError("widgetPositioning() horizontal variable must be a string");if(t.horizontal=t.horizontal.toLowerCase(),kt.indexOf(t.horizontal)===-1)throw new TypeError("widgetPositioning() expects horizontal parameter to be one of ("+kt.join(", ")+")");r.widgetPositioning.horizontal=t.horizontal}if(t.vertical){if(typeof t.vertical!="string")throw new TypeError("widgetPositioning() vertical variable must be a string");if(t.vertical=t.vertical.toLowerCase(),bt.indexOf(t.vertical)===-1)throw new TypeError("widgetPositioning() expects vertical parameter to be one of ("+bt.join(", ")+")");r.widgetPositioning.vertical=t.vertical}return a(),u},u.calendarWeeks=function(n){if(arguments.length===0)return r.calendarWeeks;if(typeof n!="boolean")throw new TypeError("calendarWeeks() expects parameter to be a boolean value");return r.calendarWeeks=n,a(),u},u.showTodayButton=function(n){if(arguments.length===0)return r.showTodayButton;if(typeof n!="boolean")throw new TypeError("showTodayButton() expects a boolean parameter");return r.showTodayButton=n,f&&(v(),w()),u},u.showClear=function(n){if(arguments.length===0)return r.showClear;if(typeof n!="boolean")throw new TypeError("showClear() expects a boolean parameter");return r.showClear=n,f&&(v(),w()),u},u.widgetParent=function(t){if(arguments.length===0)return r.widgetParent;if(typeof t=="string"&&(t=n(t)),t!==null&&typeof t!="string"&&!(t instanceof n))throw new TypeError("widgetParent() expects a string or a jQuery object parameter");return r.widgetParent=t,f&&(v(),w()),u},u.keepOpen=function(n){if(arguments.length===0)return r.keepOpen;if(typeof n!="boolean")throw new TypeError("keepOpen() expects a boolean parameter");return r.keepOpen=n,u},u.focusOnShow=function(n){if(arguments.length===0)return r.focusOnShow;if(typeof n!="boolean")throw new TypeError("focusOnShow() expects a boolean parameter");return r.focusOnShow=n,u},u.inline=function(n){if(arguments.length===0)return r.inline;if(typeof n!="boolean")throw new TypeError("inline() expects a boolean parameter");return r.inline=n,u},u.clear=function(){return ni(),u},u.keyBinds=function(n){return r.keyBinds=n,u},u.getMoment=function(n){return g(n)},u.debug=function(n){if(typeof n!="boolean")throw new TypeError("debug() expects a boolean parameter");return r.debug=n,u},u.allowInputToggle=function(n){if(arguments.length===0)return r.allowInputToggle;if(typeof n!="boolean")throw new TypeError("allowInputToggle() expects a boolean parameter");return r.allowInputToggle=n,u},u.showClose=function(n){if(arguments.length===0)return r.showClose;if(typeof n!="boolean")throw new TypeError("showClose() expects a boolean parameter");return r.showClose=n,u},u.keepInvalid=function(n){if(arguments.length===0)return r.keepInvalid;if(typeof n!="boolean")throw new TypeError("keepInvalid() expects a boolean parameter");return r.keepInvalid=n,u},u.datepickerInput=function(n){if(arguments.length===0)return r.datepickerInput;if(typeof n!="string")throw new TypeError("datepickerInput() expects a string parameter");return r.datepickerInput=n,u},u.parseInputDate=function(n){if(arguments.length===0)return r.parseInputDate;if(typeof n!="function")throw new TypeError("parseInputDate() sholud be as function");return r.parseInputDate=n,u},u.disabledTimeIntervals=function(t){if(arguments.length===0)return r.disabledTimeIntervals?n.extend({},r.disabledTimeIntervals):r.disabledTimeIntervals;if(!t)return r.disabledTimeIntervals=!1,a(),u;if(!(t instanceof Array))throw new TypeError("disabledTimeIntervals() expects an array parameter");return r.disabledTimeIntervals=t,a(),u},u.disabledHours=function(t){if(arguments.length===0)return r.disabledHours?n.extend({},r.disabledHours):r.disabledHours;if(!t)return r.disabledHours=!1,a(),u;if(!(t instanceof Array))throw new TypeError("disabledHours() expects an array parameter");if(r.disabledHours=fi(t),r.enabledHours=!1,r.useCurrent&&!r.keepInvalid){for(var i=0;!c(e,"h");){if(e.add(1,"h"),i===24)throw"Tried 24 times to find a valid date";i++}h(e)}return a(),u},u.enabledHours=function(t){if(arguments.length===0)return r.enabledHours?n.extend({},r.enabledHours):r.enabledHours;if(!t)return r.enabledHours=!1,a(),u;if(!(t instanceof Array))throw new TypeError("enabledHours() expects an array parameter");if(r.enabledHours=fi(t),r.disabledHours=!1,r.useCurrent&&!r.keepInvalid){for(var i=0;!c(e,"h");){if(e.add(1,"h"),i===24)throw"Tried 24 times to find a valid date";i++}h(e)}return a(),u},u.viewDate=function(n){if(arguments.length===0)return o.clone();if(!n)return o=e.clone(),u;if(typeof n!="string"&&!t.isMoment(n)&&!(n instanceof Date))throw new TypeError("viewDate() parameter must be one of [string, moment or Date]");return o=tt(n),ut(),u},i.is("input"))s=i;else if(s=i.find(r.datepickerInput),s.length===0)s=i.find("input");else if(!s.is("input"))throw new Error('CSS class "'+r.datepickerInput+'" cannot be applied to non input element');if(i.hasClass("input-group")&&(l=i.find(".datepickerbutton").length===0?i.find(".input-group-addon"):i.find(".datepickerbutton")),!r.inline&&!s.is("input"))throw new Error("Could not initialize DateTimePicker without an input element");return e=g(),o=e.clone(),n.extend(!0,r,li()),u.options(r),at(),fr(),s.prop("disabled")&&u.disable(),s.is("input")&&s.val().trim().length!==0?h(tt(s.val().trim())):r.defaultDate&&s.attr("placeholder")===undefined&&h(r.defaultDate),r.inline&&w(),u};n.fn.datetimepicker=function(t){return this.each(function(){var r=n(this);r.data("DateTimePicker")||(t=n.extend(!0,{},n.fn.datetimepicker.defaults,t),r.data("DateTimePicker",i(r,t)))})};n.fn.datetimepicker.defaults={timeZone:"",format:!1,dayViewHeaderFormat:"MMMM YYYY",extraFormats:!1,stepping:1,minDate:!1,maxDate:!1,useCurrent:!0,collapse:!0,locale:t.locale(),defaultDate:!1,disabledDates:!1,enabledDates:!1,icons:{time:"glyphicon glyphicon-time",date:"glyphicon glyphicon-calendar",up:"glyphicon glyphicon-chevron-up",down:"glyphicon glyphicon-chevron-down",previous:"glyphicon glyphicon-chevron-left",next:"glyphicon glyphicon-chevron-right",today:"glyphicon glyphicon-screenshot",clear:"glyphicon glyphicon-trash",close:"glyphicon glyphicon-remove"},tooltips:{today:"Go to today",clear:"Clear selection",close:"Close the picker",selectMonth:"Select Month",prevMonth:"Previous Month",nextMonth:"Next Month",selectYear:"Select Year",prevYear:"Previous Year",nextYear:"Next Year",selectDecade:"Select Decade",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevCentury:"Previous Century",nextCentury:"Next Century",pickHour:"Pick Hour",incrementHour:"Increment Hour",decrementHour:"Decrement Hour",pickMinute:"Pick Minute",incrementMinute:"Increment Minute",decrementMinute:"Decrement Minute",pickSecond:"Pick Second",incrementSecond:"Increment Second",decrementSecond:"Decrement Second",togglePeriod:"Toggle Period",selectTime:"Select Time"},useStrict:!1,sideBySide:!1,daysOfWeekDisabled:!1,calendarWeeks:!1,viewMode:"days",toolbarPlacement:"default",showTodayButton:!1,showClear:!1,showClose:!1,widgetPositioning:{horizontal:"auto",vertical:"auto"},widgetParent:null,ignoreReadonly:!1,keepOpen:!1,focusOnShow:!0,inline:!1,keepInvalid:!1,datepickerInput:".datepickerinput",keyBinds:{up:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().subtract(7,"d")):this.date(t.clone().add(this.stepping(),"m"))}},down:function(n){if(!n){this.show();return}var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().add(7,"d")):this.date(t.clone().subtract(this.stepping(),"m"))},"control up":function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().subtract(1,"y")):this.date(t.clone().add(1,"h"))}},"control down":function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().add(1,"y")):this.date(t.clone().subtract(1,"h"))}},left:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().subtract(1,"d"))}},right:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().add(1,"d"))}},pageUp:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().subtract(1,"M"))}},pageDown:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().add(1,"M"))}},enter:function(){this.hide()},escape:function(){this.hide()},"control space":function(n){n.find(".timepicker").is(":visible")&&n.find('.btn[data-action="togglePeriod"]').click()},t:function(){this.date(this.getMoment())},"delete":function(){this.clear()}},debug:!1,allowInputToggle:!1,disabledTimeIntervals:!1,disabledHours:!1,enabledHours:!1,viewDate:!1};typeof module!="undefined"&&(module.exports=n.fn.datetimepicker)});var NexDatePickerHelper=NexDatePickerHelper||{getShortDateString:function(n){var t=NexDatePickerHelper.getDateStr(n.getMonth()+1)+"/";return t+=NexDatePickerHelper.getDateStr(n.getDate())+"/",t+NexDatePickerHelper.getDateStr(n.getFullYear())},getDateStr:function(n){return n?n<10?"0"+n.toString():n.toString():""},getDateRangeOfCurrentWeek:function(n){var t=new Date(n),i=new Date(n),r=NexDatePickerHelper.getBaseDateOfWeek(t),u=new Date(t.setDate(r));return[u,i]},getDateRangeOfLastWeek:function(n){var u=NexDatePickerHelper.getBaseDateOfWeek(new Date(n)),f=u-7,t=new Date(n),i,r;return t.setDate(f),i=NexDatePickerHelper.getShortDateString(t),t.setDate(t.getDate()+6),r=NexDatePickerHelper.getShortDateString(t),[new Date(i),new Date(r)]},getDateRangeOfCurrentMonth:function(n){var t,i;return n=new Date(n),t=new Date(n),t.setDate(1),t.setMonth(n.getMonth()),i=new Date(n.setMonth(n.getMonth())),[t,i]},getDateRangeOfLastMonth:function(n){var i=new Date(n),e=i.getMonth(),r=i.getFullYear(),o=i.getFullYear()-1,u=new Date,f=new Date,t;return e==0?(u=new Date(o,11,1),f=new Date(o,11,31)):(t=e-1,u=new Date(r,t,1),f=new Date(r,t,NexDatePickerHelper.getTotalDayOfMonth(t+1,r))),[u,f]},getTotalDayOfMonth:function(n,t){var i=[31,28,31,30,31,30,31,31,30,31,30,31];return n!=2?i[n-1]:NexDatePickerHelper.isLeapYear(t)?i[1]+1:i[1]},correctDateInRange:function(n,t,i){return t&&n<t?t:i&&i<n?i:n},isLeapYear:function(n){return n%4==0&&n%100!=0||n%400==0},getBaseDateOfWeek:function(n){var t=n.getDay();return t===0?n.getDate()-6:n.getDate()-t+1},isAfterFromDateOfDateRange:function(n,t,i){var t=new Date(t),r=[t,t],u,f;switch(i){case NexDatePickerHelper.rangeOption.today:r=[t,t];break;case NexDatePickerHelper.rangeOption.yesterday:u=new Date(t);u.setDate(t.getDate()-1);r=[u,u];break;case NexDatePickerHelper.rangeOption.thisWeek:r=NexDatePickerHelper.getDateRangeOfCurrentWeek(t);break;case NexDatePickerHelper.rangeOption.lastWeek:r=NexDatePickerHelper.getDateRangeOfLastWeek(t);break;case NexDatePickerHelper.rangeOption.thisMonth:r=NexDatePickerHelper.getDateRangeOfCurrentMonth(t);break;case NexDatePickerHelper.rangeOption.lastMonth:r=NexDatePickerHelper.getDateRangeOfLastMonth(t);break;case NexDatePickerHelper.rangeOption.sinceLastMonth:f=new Date(t.getFullYear(),t.getMonth()-1,1);r=[f,t];break;default:return!1}return n>r[0]},rangeOption:{today:0,yesterday:1,thisWeek:2,lastWeek:3,thisMonth:4,lastMonth:5,sinceLastMonth:6,specificDate:7}};(function(){var n={createNexDatePicker:function(n){return n=$.extend({},$.fn.nexDatePicker.defaults,n),this.each(function(){return new t(this,n)})},getSingleDate:function(){return $(this).parent().find(".ui_nexdatepicker_fromdate").val()},getDateRange:function(){var n=$(this).parent();return{FromDate:n.find(".ui_nexdatepicker_fromdate").val(),ToDate:n.find(".ui_nexdatepicker_todate").val()}}},t;$.fn.nexDatePicker=function(t){return n[t]?n[t].apply(this,Array.prototype.slice.call(arguments,1)):typeof t=="object"||!t?n.createNexDatePicker.apply(this,arguments):void 0};$.fn.nexDatePicker.defaults={nexDatePickerType:"quickchoose",datePickerFormat:{dateFormat:"M dd, yy"},subDatePickerStyle:"",onSelectDatePicker:function(){},onQuickChooseChange:function(){},minDate:null,ConstraintFromTo:0};t=function(n,t){return this.Options=t,this.$select=$(n),this.$dropDownList=null,this.$firstDatePicker=null,this.$secondDatePicker=null,this.$datePickers=null,this.CurrentDate=t.currentDate,this.Yesterday=new Date(this.CurrentDate),this.Yesterday.setDate(this.CurrentDate.getDate()-1),this.Initialize(),this.$select};t.prototype={Initialize:function(){var n=this;n.$parent=n.$select.parent();n.$dropDownList=n.$parent.find("#dropdown-daterange");n.$firstDatePicker=n.$parent.find("#from-date-container");n.$secondDatePicker=n.$parent.find("#to-date-container");n.$userRangeLavel=n.$dropDownList.find("#range-label");n.$userRangeOptionHidden=n.$dropDownList.find("#hidden-range-option");n.FormatDatePicker();n.AddEvent();n.CorrectOptionWithMinDate()},FormatDatePicker:function(){var n=this,t=this.Options,u=t.nexDatePickerType==="single"?"#from-date-container":"#from-date-container, #to-date-container",i,r;this.$datePickers=n.$parent.find(u);moment.locale("en",{week:{dow:1}});this.$datePickers.datepicker({allowInputToggle:!0,format:t.datePickerFormat.dateFormat,parseInputDate:function(n){return typeof n=="string"?moment(new Date(n).toISOString()):moment(n.toISOString())}}).on("dp.change",function(i){var r,u,f,o,e,s;i.date.format("YYYY MM DD")!==i.oldDate.format("YYYY MM DD")&&(r=n.$dropDownList.find("a.range-option[data-value='7']"),n.$userRangeLavel.html(r.html()),n.$userRangeOptionHidden.val(r.attr("data-value")));this.id==="from-date-container"?n.$secondDatePicker.data("DateTimePicker").minDate(new Date(n.$firstDatePicker.find("input").val()).toISOString()):n.$firstDatePicker.data("DateTimePicker").maxDate(new Date(n.$secondDatePicker.find("input").val()).toISOString());t.onSelectDatePicker(i);t.ConstraintFromTo!=0&&(u=new Date(i.date),this.id==="from-date-container"?(f=moment(u).add(parseInt(t.ConstraintFromTo),"months"),o=new Date(n.$secondDatePicker.find("input").val()),f<o&&n.$secondDatePicker.data("DateTimePicker").date(f)):(e=moment(u).add(parseInt(-t.ConstraintFromTo),"months"),s=new Date(n.$firstDatePicker.find("input").val()),e>s&&n.$firstDatePicker.data("DateTimePicker").date(e)))});i=new Date(this.$firstDatePicker.find("input").val());r=new Date(this.$secondDatePicker.find("input").val());n.$firstDatePicker.data("DateTimePicker").date(i);t.nexDatePickerType!=="single"&&(n.$secondDatePicker.data("DateTimePicker").date(r),n.CheckSetMinDate(),n.CheckSetMaxDate())},CheckSetMinDate:function(){var n=this;n.Options.minDate!==null&&(n.$firstDatePicker.data("DateTimePicker").minDate(new Date(n.Options.minDate).toISOString()),n.$secondDatePicker.data("DateTimePicker").minDate(new Date(n.Options.minDate).toISOString()))},CheckSetMaxDate:function(){var n=this;n.Options.maxDate!==null&&(n.$firstDatePicker.data("DateTimePicker").maxDate(new Date(n.Options.maxDate).toISOString()),n.$secondDatePicker.data("DateTimePicker").maxDate(new Date(n.Options.maxDate).toISOString()))},AddEvent:function(){var n=this,t=this.Options;if(t.nexDatePickerType!=="single")n.$dropDownList.find(".dropdown-menu").on("click","a.range-option",function(t){var o;t.preventDefault();var s=$(this),e=parseInt(s.attr("data-value")),f=new Date(n.CurrentDate),i=new Date(n.CurrentDate),u=new Date(n.CurrentDate),r=[i,u];switch(e){case NexDatePickerHelper.rangeOption.today:r=[f,f];break;case NexDatePickerHelper.rangeOption.yesterday:r=[n.Yesterday,n.Yesterday];break;case NexDatePickerHelper.rangeOption.thisWeek:r=NexDatePickerHelper.getDateRangeOfCurrentWeek(i);break;case NexDatePickerHelper.rangeOption.lastWeek:r=NexDatePickerHelper.getDateRangeOfLastWeek(i);break;case NexDatePickerHelper.rangeOption.thisMonth:r=NexDatePickerHelper.getDateRangeOfCurrentMonth(i);break;case NexDatePickerHelper.rangeOption.lastMonth:r=NexDatePickerHelper.getDateRangeOfLastMonth(i);break;case NexDatePickerHelper.rangeOption.sinceLastMonth:i=new Date(f.getFullYear(),f.getMonth()-1,1);r=[i,f];break;case NexDatePickerHelper.rangeOption.specificDate:i=n.$firstDatePicker.data("DateTimePicker").date();u=n.$secondDatePicker.data("DateTimePicker").date();r=[i,u];break;default:o=$(this).find("option[value="+e+"]");i=new Date(o.attr("data-fDate"));u=new Date(o.attr("data-tDate"));i&&u&&(r=[i,u])}i=NexDatePickerHelper.correctDateInRange(r[0],n.Options.minDate,n.Options.maxDate);u=NexDatePickerHelper.correctDateInRange(r[1],n.Options.minDate,n.Options.maxDate);n.$firstDatePicker.data("DateTimePicker").maxDate(!1);n.$secondDatePicker.data("DateTimePicker").maxDate(!1);n.$firstDatePicker.data("DateTimePicker").date(i);n.$secondDatePicker.data("DateTimePicker").date(u);n.CheckSetMinDate();n.CheckSetMaxDate();n.$userRangeOptionHidden.val(e);n.$userRangeLavel.html(s.html());n.Options.onQuickChooseChange(e)})},CorrectOptionWithMinDate:function(){var n=this;n.Options.minDate&&n.$dropDownList.find(".dropdown-menu a.range-option").each(function(t,i){var r=$(i),u=parseInt(r.attr("data-value"));NexDatePickerHelper.isAfterFromDateOfDateRange(n.Options.minDate,n.Options.currentDate,u)&&r.remove()})}}})(jQuery);