(function(n,t){"use strict";typeof module!="undefined"&&module.exports?module.exports=t(require("jquery")):typeof define=="function"&&define.amd?define(["jquery"],function(n){return t(n)}):t(n.jQuery)})(this,function(n){"use strict";var t=function(i,r){this.$element=n(i);this.options=n.extend({},t.defaults,r);this.matcher=this.options.matcher||this.matcher;this.sorter=this.options.sorter||this.sorter;this.select=this.options.select||this.select;this.autoSelect=typeof this.options.autoSelect=="boolean"?this.options.autoSelect:!0;this.highlighter=this.options.highlighter||this.highlighter;this.render=this.options.render||this.render;this.updater=this.options.updater||this.updater;this.displayText=this.options.displayText||this.displayText;this.source=this.options.source;this.delay=this.options.delay;this.$menu=n(this.options.menu);this.$appendTo=this.options.appendTo?n(this.options.appendTo):null;this.fitToElement=typeof this.options.fitToElement=="boolean"?this.options.fitToElement:!1;this.shown=!1;this.listen();this.showHintOnFocus=typeof this.options.showHintOnFocus=="boolean"||this.options.showHintOnFocus==="all"?this.options.showHintOnFocus:!1;this.afterSelect=this.options.afterSelect;this.addItem=!1;this.value=this.$element.val()||this.$element.text()},i;t.prototype={constructor:t,select:function(){var t=this.$menu.find(".active").data("value"),n;return this.$element.data("active",t),(this.autoSelect||t)&&(n=this.updater(t),n||(n=""),this.$element.val(this.displayText(n)||n).text(this.displayText(n)||n).change(),this.afterSelect(n)),this.hide()},updater:function(n){return n},setSource:function(n){this.source=n},show:function(){var i=n.extend({},this.$element.position(),{height:this.$element[0].offsetHeight}),u=typeof this.options.scrollHeight=="function"?this.options.scrollHeight.call():this.options.scrollHeight,t,r;this.shown?t=this.$menu:this.$appendTo?(t=this.$menu.appendTo(this.$appendTo),this.hasSameParent=this.$appendTo.is(this.$element.parent())):(t=this.$menu.insertAfter(this.$element),this.hasSameParent=!0);this.hasSameParent||(t.css("position","fixed"),r=this.$element.offset(),i.top=r.top,i.left=r.left);var f=n(t).parent().hasClass("dropup"),e=f?"auto":i.top+i.height+u,o=n(t).hasClass("dropdown-menu-right"),s=o?"auto":i.left;return t.css({top:e,left:s}).show(),this.options.fitToElement===!0&&t.css("width",this.$element.outerWidth()+"px"),this.shown=!0,this},hide:function(){return this.$menu.hide(),this.shown=!1,this},lookup:function(t){var i;if(this.query=typeof t!="undefined"&&t!==null?t:this.$element.val()||this.$element.text()||"",this.query.length<this.options.minLength&&!this.options.showHintOnFocus)return this.shown?this.hide():this;i=n.proxy(function(){n.isFunction(this.source)?this.source(this.query,n.proxy(this.process,this)):this.source&&this.process(this.source)},this);clearTimeout(this.lookupWorker);this.lookupWorker=setTimeout(i,this.delay)},process:function(t){var i=this;return(t=n.grep(t,function(n){return i.matcher(n)}),t=this.sorter(t),!t.length&&!this.options.addItem)?this.shown?this.hide():this:(t.length>0?this.$element.data("active",t[0]):this.$element.data("active",null),this.options.addItem&&t.push(this.options.addItem),this.options.items=="all"?this.render(t).show():this.render(t.slice(0,this.options.items)).show())},matcher:function(n){var t=this.displayText(n);return~t.toLowerCase().indexOf(this.query.toLowerCase())},sorter:function(n){for(var r=[],u=[],f=[],t,i;t=n.shift();)i=this.displayText(t),i.toLowerCase().indexOf(this.query.toLowerCase())?~i.indexOf(this.query)?u.push(t):f.push(t):r.push(t);return r.concat(u,f)},highlighter:function(t){var r=n("<div><\/div>"),u=this.query,i=t.toLowerCase().indexOf(u.toLowerCase()),f=u.length,e,o,s,h;if(f===0)return r.text(t).html();while(i>-1)e=t.substr(0,i),o=t.substr(i,f),s=t.substr(i+f),h=n("<strong><\/strong>").text(o),r.append(document.createTextNode(e)).append(h),t=s,i=t.toLowerCase().indexOf(u.toLowerCase());return r.append(document.createTextNode(t)).html()},render:function(t){var r=this,f=this,e=!1,u=[],i=r.options.separator;return n.each(t,function(n,r){n>0&&r[i]!==t[n-1][i]&&u.push({__type:"divider"});r[i]&&(n===0||r[i]!==t[n-1][i])&&u.push({__type:"category",name:r[i]});u.push(r)}),t=n(u).map(function(t,i){if((i.__type||!1)=="category")return n(r.options.headerHtml).text(i.name)[0];if((i.__type||!1)=="divider")return n(r.options.headerDivider)[0];var u=f.displayText(i);return t=n(r.options.item).data("value",i),t.find("a").html(r.highlighter(u,i)),u==f.$element.val()&&(t.addClass("active"),f.$element.data("active",i),e=!0),t[0]}),this.autoSelect&&!e&&(t.filter(":not(.dropdown-header)").first().addClass("active"),this.$element.data("active",t.first().data("value"))),this.$menu.html(t),this},displayText:function(n){return typeof n!="undefined"&&typeof n.name!="undefined"&&n.name||n},next:function(){var i=this.$menu.find(".active").removeClass("active"),t=i.next();t.length||(t=n(this.$menu.find("li")[0]));t.addClass("active")},prev:function(){var t=this.$menu.find(".active").removeClass("active"),n=t.prev();n.length||(n=this.$menu.find("li").last());n.addClass("active")},listen:function(){this.$element.on("focus",n.proxy(this.focus,this)).on("blur",n.proxy(this.blur,this)).on("keypress",n.proxy(this.keypress,this)).on("input",n.proxy(this.input,this)).on("keyup",n.proxy(this.keyup,this));if(this.eventSupported("keydown"))this.$element.on("keydown",n.proxy(this.keydown,this));this.$menu.on("click",n.proxy(this.click,this)).on("mouseenter","li",n.proxy(this.mouseenter,this)).on("mouseleave","li",n.proxy(this.mouseleave,this)).on("mousedown",n.proxy(this.mousedown,this))},destroy:function(){this.$element.data("typeahead",null);this.$element.data("active",null);this.$element.off("focus").off("blur").off("keypress").off("input").off("keyup");this.eventSupported("keydown")&&this.$element.off("keydown");this.$menu.remove();this.destroyed=!0},eventSupported:function(n){var t=n in this.$element;return t||(this.$element.setAttribute(n,"return;"),t=typeof this.$element[n]=="function"),t},move:function(n){if(this.shown)switch(n.keyCode){case 9:case 13:case 27:n.preventDefault();break;case 38:if(n.shiftKey)return;n.preventDefault();this.prev();break;case 40:if(n.shiftKey)return;n.preventDefault();this.next()}},keydown:function(t){this.suppressKeyPressRepeat=~n.inArray(t.keyCode,[40,38,9,13,27]);this.shown||t.keyCode!=40?this.move(t):this.lookup()},keypress:function(n){this.suppressKeyPressRepeat||this.move(n)},input:function(){var n=this.$element.val()||this.$element.text();this.value!==n&&(this.value=n,this.lookup())},keyup:function(n){if(!this.destroyed)switch(n.keyCode){case 9:case 13:if(!this.shown)return;this.select();break;case 27:if(!this.shown)return;this.hide()}},focus:function(){this.focused||(this.focused=!0,this.options.showHintOnFocus&&this.skipShowHintOnFocus!==!0&&(this.options.showHintOnFocus==="all"?this.lookup(""):this.lookup()));this.skipShowHintOnFocus&&(this.skipShowHintOnFocus=!1)},blur:function(){this.mousedover||this.mouseddown||!this.shown?this.mouseddown&&(this.skipShowHintOnFocus=!0,this.$element.focus(),this.mouseddown=!1):(this.hide(),this.focused=!1)},click:function(n){n.preventDefault();this.skipShowHintOnFocus=!0;this.select();this.$element.focus();this.hide()},mouseenter:function(t){this.mousedover=!0;this.$menu.find(".active").removeClass("active");n(t.currentTarget).addClass("active")},mouseleave:function(){this.mousedover=!1;!this.focused&&this.shown&&this.hide()},mousedown:function(){this.mouseddown=!0;this.$menu.one("mouseup",function(){this.mouseddown=!1}.bind(this))}};i=n.fn.typeahead;n.fn.typeahead=function(i){var r=arguments;return typeof i=="string"&&i=="getActive"?this.data("active"):this.each(function(){var f=n(this),u=f.data("typeahead"),e=typeof i=="object"&&i;u||f.data("typeahead",u=new t(this,e));typeof i=="string"&&u[i]&&(r.length>1?u[i].apply(u,Array.prototype.slice.call(r,1)):u[i]())})};t.defaults={source:[],items:8,menu:'<ul class="typeahead dropdown-menu" role="listbox"><\/ul>',item:'<li><a class="dropdown-item" href="#" role="option"><\/a><\/li>',minLength:1,scrollHeight:0,autoSelect:!0,afterSelect:n.noop,addItem:!1,delay:0,separator:"category",headerHtml:'<li class="dropdown-header"><\/li>',headerDivider:'<li class="divider" role="separator"><\/li>'};n.fn.typeahead.Constructor=t;n.fn.typeahead.noConflict=function(){return n.fn.typeahead=i,this};n(document).on("focus.typeahead.data-api",'[data-provide="typeahead"]',function(){var t=n(this);t.data("typeahead")||t.typeahead(t.data())})});