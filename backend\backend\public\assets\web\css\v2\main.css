@charset "UTF-8";
@-webkit-keyframes oddsChangeColor-transparent {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: transparent;
    }
    100% {
        background: transparent;
    }
}

@keyframes oddsChangeColor-transparent {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: transparent;
    }
    100% {
        background: transparent;
    }
}

@-webkit-keyframes oddsChangeColor-quickBet {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: #ffffff;
    }
    100% {
        background: #ffffff;
    }
}

@keyframes oddsChangeColor-quickBet {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: #ffffff;
    }
    100% {
        background: #ffffff;
    }
}

@-webkit-keyframes oddsChangeColor-betSlip {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: #ececec;
    }
    100% {
        background: #ececec;
    }
}

@keyframes oddsChangeColor-betSlip {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: #ececec;
    }
    100% {
        background: #ececec;
    }
}

@-webkit-keyframes oddsChangeColor-betSlipLive {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: #ffddd2;
    }
    100% {
        background: #ffddd2;
    }
}

@keyframes oddsChangeColor-betSlipLive {
    0% {
        background: #ffaf96;
    }
    70% {
        background: #ffaf96;
    }
    71% {
        background: #ffddd2;
    }
    100% {
        background: #ffddd2;
    }
}

@-webkit-keyframes FadeInOut {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    5% {
        transform: translateY(0);
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes FadeInOut {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    5% {
        transform: translateY(0);
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@-webkit-keyframes rotate {
    to {
        transform: rotate(360deg);
    }
}

@keyframes rotate {
    to {
        transform: rotate(360deg);
    }
}

@-webkit-keyframes rotateScale {
    0% {
        transform: rotate(0deg) scale(1.3);
    }
    100% {
        transform: rotate(360deg) scale(1.3);
    }
}

@keyframes rotateScale {
    0% {
        transform: rotate(0deg) scale(1.3);
    }
    100% {
        transform: rotate(360deg) scale(1.3);
    }
}

@-webkit-keyframes rotateScale-large {
    0% {
        transform: rotate(0deg) scale(1.4);
    }
    100% {
        transform: rotate(360deg) scale(1.4);
    }
}

@keyframes rotateScale-large {
    0% {
        transform: rotate(0deg) scale(1.4);
    }
    100% {
        transform: rotate(360deg) scale(1.4);
    }
}

@-webkit-keyframes loading-a {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.3);
        opacity: .2;
    }
}

@keyframes loading-a {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.3);
        opacity: .2;
    }
}

@-moz-document url-prefix() {
    .backtoTop a::before,
    .liveStreaming-flashCrash .icon-flashCrash::before,
    .iconWithBg::before {
        line-height: 1.2 !important;
    }
}

@-moz-document url-prefix() {}

@font-face {
    font-family: "iconFont";
    src: url(data:font/ttf;base64,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) format("truetype");
}

body,
input,
textarea,
button,
table {
    color: #545454;
    line-height: 1.5;
    font-family: "Arial", "Tahoma", "pmingliu", "新細明體";
}

body {
    font-size: 0.688em;
    position: relative;
    background: #e8eff5;
    -webkit-text-size-adjust: none;
}

input::-ms-clear {
    display: none;
}

div:focus,
button:focus {
    outline: none;
}

.mini .widgetPanel.active .contentArea,
.popupPanel .contentArea-darkBlue,
.popupPanel-center .contentArea-darkBlue,
.popupPanel-smaller .contentArea-darkBlue,
.popupPanel-small .contentArea-darkBlue,
.popupPanel-smaller-center .contentArea-darkBlue,
.popupPanel-small-center .contentArea-darkBlue,
.popupPanel-large .contentArea-darkBlue,
.popupPanel-large-center .contentArea-darkBlue,
.popupPanel-larger .contentArea-darkBlue,
.popupPanel-larger-center .contentArea-darkBlue,
.oddsTable .time,
.oddsTable .event,
.oddsTitle>div,
.oddsTitle-accent>div,
.oddsTitleSub>div,
.oddsTotal>div,
.tabs-item,
.infoGroup .infoTitle,
.infoGroup .infoItem,
.baseList .category-sub li a,
.baseList .category-sub li.link-none a,
.baseArea .betGroup-b .betArea,
.baseArea .betGroup-c .betArea,
.baseArea .betGroup-d .betArea,
.heading,
.heading .text,
.heading .accountTable .text-auto,
.accountTable .heading .text-auto,
.heading .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .heading .text-auto,
.liveStreaming-flashCrash,
.liveStreaming-defaultImage,
.liveStreaming.is-new-window .liveStreaming-sidebar,
.otherTableRow>div,
.otherTableTotal .totalRow>div,
.otherTableHead>div,
.otherTableHead-sub>div,
.otherTableRow .otherTableGroup>div,
.gameResult .team,
footer {
    box-sizing: border-box;
}

.mini .widgetPanel.active .contentArea,
.popupPanel .contentArea-darkBlue,
.popupPanel-center .contentArea-darkBlue,
.popupPanel-smaller .contentArea-darkBlue,
.popupPanel-small .contentArea-darkBlue,
.popupPanel-smaller-center .contentArea-darkBlue,
.popupPanel-small-center .contentArea-darkBlue,
.popupPanel-large .contentArea-darkBlue,
.popupPanel-large-center .contentArea-darkBlue,
.popupPanel-larger .contentArea-darkBlue,
.popupPanel-larger-center .contentArea-darkBlue {
    background: #233d67;
    padding: 0.3em;
}

.collapsible>li>a::before {
    content: "";
    position: absolute;
    left: 0.2em;
    font-size: 1.5em;
    width: 1em;
    height: 1em;
    line-height: 1;
    text-align: center;
    font-weight: bold;
}

.collapsible.vertical-line>li::after,
.collapsible.vertical-line>li>a::after,
.bookingReminder::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0.2em;
    font-size: 1.5em;
    width: calc(1em / 2);
    height: 100%;
    border-right: 1px solid #5574a7;
}

.betDetial .photo img {
    border: 1px solid #adbed6;
    background: #ffffff;
    width: 2.6em;
    height: auto;
    padding: 0.18em 0.15em;
}

.quickBetPanel .betInfo .selectorOdds,
.betSlip .selectorOdds,
.betItem .text-large,
.betItem-closed .text-large {
    display: inline-block;
    font-size: 1.6em;
    line-height: 0;
}

a.icon-next::before,
div.icon-next::before,
span.icon-next::before,
form.icon-next::before,
label.icon-next::before,
button.icon-next::before,
h1.icon-next::before,
h2.icon-next::before,
h3.icon-next::before,
h4.icon-next::before,
h5.icon-next::before,
h6.icon-next::before,
p.icon-next::before,
li.icon-next::before,
ol.icon-next::before,
ul.icon-next::before,
a.icon-arrow-down::before,
div.icon-arrow-down::before,
span.icon-arrow-down::before,
form.icon-arrow-down::before,
label.icon-arrow-down::before,
button.icon-arrow-down::before,
h1.icon-arrow-down::before,
h2.icon-arrow-down::before,
h3.icon-arrow-down::before,
h4.icon-arrow-down::before,
h5.icon-arrow-down::before,
h6.icon-arrow-down::before,
p.icon-arrow-down::before,
li.icon-arrow-down::before,
ol.icon-arrow-down::before,
ul.icon-arrow-down::before,
a.icon-arrow-up::before,
div.icon-arrow-up::before,
span.icon-arrow-up::before,
form.icon-arrow-up::before,
label.icon-arrow-up::before,
button.icon-arrow-up::before,
h1.icon-arrow-up::before,
h2.icon-arrow-up::before,
h3.icon-arrow-up::before,
h4.icon-arrow-up::before,
h5.icon-arrow-up::before,
h6.icon-arrow-up::before,
p.icon-arrow-up::before,
li.icon-arrow-up::before,
ol.icon-arrow-up::before,
ul.icon-arrow-up::before,
a.icon-next::after,
div.icon-next::after,
span.icon-next::after,
form.icon-next::after,
label.icon-next::after,
button.icon-next::after,
h1.icon-next::after,
h2.icon-next::after,
h3.icon-next::after,
h4.icon-next::after,
h5.icon-next::after,
h6.icon-next::after,
p.icon-next::after,
li.icon-next::after,
ol.icon-next::after,
ul.icon-next::after,
a.icon-arrow-down::after,
div.icon-arrow-down::after,
span.icon-arrow-down::after,
form.icon-arrow-down::after,
label.icon-arrow-down::after,
button.icon-arrow-down::after,
h1.icon-arrow-down::after,
h2.icon-arrow-down::after,
h3.icon-arrow-down::after,
h4.icon-arrow-down::after,
h5.icon-arrow-down::after,
h6.icon-arrow-down::after,
p.icon-arrow-down::after,
li.icon-arrow-down::after,
ol.icon-arrow-down::after,
ul.icon-arrow-down::after,
a.icon-arrow-up::after,
div.icon-arrow-up::after,
span.icon-arrow-up::after,
form.icon-arrow-up::after,
label.icon-arrow-up::after,
button.icon-arrow-up::after,
h1.icon-arrow-up::after,
h2.icon-arrow-up::after,
h3.icon-arrow-up::after,
h4.icon-arrow-up::after,
h5.icon-arrow-up::after,
h6.icon-arrow-up::after,
p.icon-arrow-up::after,
li.icon-arrow-up::after,
ol.icon-arrow-up::after,
ul.icon-arrow-up::after {
    font-weight: bold;
}

.added.icon-favorite.smallBtn::before,
.added.icon-favorite.smallBtn-text::before,
.added.hotKey.icon-favorite::before,
.added.trigger.icon-favorite::before {
    color: #ffd330;
}

.smallBtn,
.smallBtn-text,
.largeBtn,
.setting,
.glyphIcon,
.glyphIcon-large,
.data,
.form,
.inOddsTable,
.trigger,
.largestBtn,
.largestBtn-lightBlue,
.middleBtn,
.circleBtn,
.tool,
.hotKey,
.filter,
.message,
.heading-default .glyphIcon-large,
.heading-noMoving .glyphIcon-large {
    position: relative;
    border: 0;
    background-color: transparent;
    cursor: pointer;
    display: inline-block;
}

.smallBtn,
.smallBtn-text,
.largeBtn,
.setting,
.glyphIcon,
.glyphIcon-large,
.data,
.form,
.inOddsTable,
.trigger {
    border-radius: 3px;
    padding: 0.15em 0.5em;
    float: left;
    margin-left: 0.25em;
    color: #bbbbbb;
}

.smallBtn:first-child,
.smallBtn-text:first-child,
.largeBtn:first-child,
.setting:first-child,
.glyphIcon:first-child,
.glyphIcon-large:first-child,
.data:first-child,
.form:first-child,
.inOddsTable:first-child,
.trigger:first-child {
    margin-left: 0;
}

.spin.smallBtn,
.spin.smallBtn-text,
.spin.largeBtn,
.spin.setting,
.spin.glyphIcon,
.spin.glyphIcon-large,
.spin.data,
.spin.form,
.spin.inOddsTable,
.spin.trigger {
    cursor: default;
}

.spin.smallBtn::before,
.spin.smallBtn-text::before,
.spin.largeBtn::before,
.spin.setting::before,
.spin.glyphIcon::before,
.spin.glyphIcon-large::before,
.spin.data::before,
.spin.form::before,
.spin.inOddsTable::before,
.spin.trigger::before {
    -webkit-animation: rotate 1.5s linear infinite;
    animation: rotate 1.5s linear infinite;
}

.focus.smallBtn,
.focus.smallBtn-text,
.focus.largeBtn,
.focus.setting,
.focus.glyphIcon,
.focus.glyphIcon-large,
.focus.data,
.focus.form,
.focus.inOddsTable,
.focus.trigger {
    box-shadow: 0 0 0 1px #ffffff, 0 0 1px 3px rgba(0, 0, 0, 0.25);
}

.smallBtn .preloader-third,
.smallBtn-text .preloader-third,
.largeBtn .preloader-third,
.setting .preloader-third,
.glyphIcon .preloader-third,
.glyphIcon-large .preloader-third,
.data .preloader-third,
.form .preloader-third,
.inOddsTable .preloader-third,
.trigger .preloader-third {
    border-radius: 3px;
}

.smallBtn .preloader-third .spin,
.smallBtn-text .preloader-third .spin,
.largeBtn .preloader-third .spin,
.setting .preloader-third .spin,
.glyphIcon .preloader-third .spin,
.glyphIcon-large .preloader-third .spin,
.data .preloader-third .spin,
.form .preloader-third .spin,
.inOddsTable .preloader-third .spin,
.trigger .preloader-third .spin {
    font-size: 1em;
}

.entryInfo>div .content.changing,
.confirmPanel .confirmInfo .choiseInfo .content {
    font-weight: bold;
    -ms-transform: translate(0.3em, -0.4em) scale(1.5);
    -webkit-transform: translate(0.3em, -0.4em) scale(1.5);
    transform: translate(0.3em, -0.4em) scale(1.5);
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
}

.oddsTitle,
.oddsTitle-accent,
.oddsTitleSub,
.live-a,
.live-b,
.normal-a,
.normal-b,
.mmr-a,
.mmr-b,
.oddsTotal {
    width: 100%;
    display: table;
    border-collapse: separate;
}

.oddsTable .time,
.oddsTable .event,
.oddsTitle>div,
.oddsTitle-accent>div,
.oddsTitleSub>div,
.oddsTotal>div {
    padding: 2px 4px;
    display: table-cell;
    vertical-align: middle;
}

.oddsBet {
    border-radius: 3px;
    display: inline-block;
    position: relative;
    padding: 0 1px;
    text-align: right;
    cursor: pointer;
}

.text-center.oddsBet {
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.oddsBet:hover {
    background: #ffffff;
    color: #01122b;
}

.underdog.oddsBet,
.underdog.oddsBet .oddsBet {
    color: #b53f39 !important;
}

.disable.oddsBet {
    cursor: default;
}

.selected.oddsBet {
    background: #ffffff;
    color: #01122b;
    color: #01122b;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    position: relative;
    z-index: 16;
}

.oddsBet.indicatorDown,
.oddsBet.indicatorUp-a,
.oddsBet.indicatorUp,
.oddsBet.indicatorDown-a,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorDown,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorUp-a,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorUp,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorDown-a {
    background: #ffaf96 !important;
}

.oddsBet.indicatorDown:hover,
.oddsBet.indicatorUp-a:hover,
.oddsBet.indicatorUp:hover,
.oddsBet.indicatorDown-a:hover,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorDown:hover,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorUp-a:hover,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorUp:hover,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorDown-a:hover {
    background: #ffc2b0 !important;
}

.widgetPanel.sportsMenu .fixed .category,
.showingNow .fixed,
.myScore .fixed {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

.collapsible.default>li>a,
.static.faq .collapsible>li>a {
    font-weight: bold;
    color: #5574a7;
    background: #d5e0f0;
    border-bottom: 1px solid #adbed6;
}

.collapsible.default>li .innerContent,
.static.faq .collapsible>li .innerContent {
    background: rgba(255, 255, 255, 0.2);
}

.articleFormat h1,
.static .panelContent h1 {
    margin: 1em 0;
    line-height: 1.5;
    font-size: 2em;
    font-weight: bold;
    color: #545454;
}

.articleFormat h2,
.static .panelContent h2,
.panelTitle {
    margin: 1em 0;
    line-height: 1.5;
    font-size: 1.5em;
    font-weight: bold;
    color: #545454;
}

.articleFormat h3,
.static .panelContent h3,
.panelItemTitle {
    margin: 0.5em 0;
    line-height: 1.25;
    font-size: 1.25em;
    font-weight: bold;
    color: #545454;
}

.articleFormat a,
.static .panelContent a {
    color: #7591c1;
    text-decoration: none;
}

.articleFormat p,
.static .panelContent p {
    margin: 1em 0;
}

.articleFormat ol,
.static .panelContent ol {
    padding-left: 2em;
}

.articleFormat ol>li,
.static .panelContent ol>li {
    list-style-type: disc;
}

.articleFormat .backtoTop a,
.static .panelContent .backtoTop a {
    color: #ffffff;
}

.articleFormat .point,
.static .panelContent .point {
    color: #9c0000;
}

.articleFormat .style2,
.static .panelContent .style2 {
    color: #b53f39;
}

fieldset .formInput>div,
.singlePage .formInput>div {
    color: #b53f39;
    margin-top: 0.5em;
    display: none;
}

font[color="red"] {
    color: #b53f39 !important;
}

.hightlight {
    color: #5574a7;
    font-weight: bold;
}

.hiddenElement {
    display: none !important;
}

.showElement {
    display: inline-block !important;
}

.absoluteElement {
    position: absolute !important;
}

.invisibleElement {
    visibility: hidden !important;
}

.fixedElement {
    position: fixed !important;
    -webkit-backface-visibility: hidden;
}

.fixed-viewport-bottom {
    position: fixed !important;
    -webkit-backface-visibility: hidden;
    bottom: 0;
}

.searchResult {
    background: #feec6e;
}

.strikeThrough {
    text-decoration: line-through;
}

label input[type="checkbox"]+.checkbox,
label input[type="checkbox"]:checked+.checkbox,
.checkbox {
    width: 1em;
    height: 1em;
    border: 1px solid #a3a3a3;
    background: #ffffff;
    cursor: pointer;
    display: inline-block;
    border-radius: 3px;
}

label input[type="checkbox"]:checked+.checkbox::before,
.checkbox-checked::before {
    content: "";
    font-weight: normal;
    line-height: 1;
    float: left;
}

label,
label.primary {
    display: block;
    cursor: pointer;
    position: relative;
    padding-left: 1.467em;
    min-height: 1.5em;
}

label .checkbox+span,
label .checkbox+div {
    pointer-events: none;
}

label input[type="checkbox"] {
    display: none;
    color: #545454;
}

label input[type="checkbox"]+.checkbox {
    position: absolute;
    left: .1em;
    top: .1em;
}

label input[type="checkbox"]+.checkbox::before {
    content: "";
}

.filter label input[type="checkbox"]+.checkbox::before {
    position: relative;
    z-index: 1;
    height: 100%;
    display: block;
}

.filter label input[type="checkbox"]:checked+.checkbox::before {
    -ms-transform: translate(-0.5em, -0.3em) scale(1.2);
    -webkit-transform: translate(-0.5em, -0.3em) scale(1.2);
    transform: translate(-0.5em, -0.3em) scale(1.2);
}

label.disable {
    color: #a3a3a3 !important;
    cursor: default;
}

label.disable input[type="checkbox"]+.checkbox {
    border-color: #bbbbbb !important;
    background: #cdcdcd !important;
    color: #7c7c7c !important;
}

label input[type="radio"] {
    display: none;
    color: #545454;
}

label input[type="radio"]+.radiobox {
    width: 1.167em;
    height: 1.167em;
    border: 1px solid #a3a3a3;
    background: #ffffff;
    position: absolute;
    left: 0;
    top: 0.15em;
    border-radius: 100%;
}

label input[type="radio"]:checked+.radiobox::before {
    content: "";
    width: 6px;
    height: 6px;
    border-radius: inherit;
    background: #545454;
    display: block;
    -ms-transform: translate(0.35em, 0.35em);
    -webkit-transform: translate(0.35em, 0.35em);
    transform: translate(0.35em, 0.35em);
}

label.primary {
    color: #5574a7;
}

label.primary input[type="checkbox"]+.checkbox {
    border-color: #adbed6;
    color: #435f8b;
}

.checkbox-checked::before {
    position: relative;
    top: -0.1em;
    font-size: 1.2em;
}

.note,
.note-list,
.note-dynamic {
    padding-top: 0.5em;
}

.note .title,
.note-list .title,
.note-dynamic .title {
    font-weight: bold;
    float: left;
}

.note .title::after,
.note-list .title::after,
.note-dynamic .title::after {
    content: ":";
    margin-right: 0.25em;
}

.note .txt,
.note-list .txt,
.note-dynamic .txt {
    margin-top: 0.1em;
}

.note-list .list {
    clear: both;
    padding-left: 2em;
    list-style: disc;
}

.note-dynamic .txt {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    position: relative;
    padding-right: 1.25em;
}

.note-dynamic .txt [class*="smallBtn"] {
    float: none;
    position: absolute;
    right: 0;
    top: 0.15em;
}

.note-dynamic .txt [class*="smallBtn"]:before {
    content: "";
}

.note-dynamic .txt.more {
    white-space: normal;
    overflow: visible;
    padding-right: 0;
}

.note-dynamic .txt.more [class*="smallBtn"] {
    position: relative;
}

.note-dynamic .txt.more [class*="smallBtn"]:before {
    content: "";
}

.numberBall,
.numberBall-primary,
.numberBall-disable {
    display: inline-block;
    border-radius: 100%;
    width: 2em;
    height: 2em;
    color: #01122b;
    font-weight: bold;
    line-height: 2em;
    text-align: center;
    font-size: 1.3em;
    position: relative;
    vertical-align: middle;
    font-family: "Arial", "Tahoma", "pmingliu", "新細明體";
}

.numberBall::before,
.numberBall-primary::before,
.numberBall-disable::before,
.numberBall::after,
.numberBall-primary::after,
.numberBall-disable::after {
    content: "";
    width: inherit;
    height: inherit;
    position: absolute;
    left: -3%;
    top: -3%;
    border-radius: inherit;
}

.numberBall::before,
.numberBall-primary::before,
.numberBall-disable::before {
    background: #ffffff;
    -ms-transform: scale(0.7);
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
}

.numberBall::after,
.numberBall-primary::after,
.numberBall-disable::after {
    -ms-transform: scale(0.85);
    -webkit-transform: scale(0.85);
    transform: scale(0.85);
    border: 0.1em solid #ffffff;
    content: "";
    display: block;
    clear: both;
}

.numberBall>span,
.numberBall-primary>span,
.numberBall-disable>span {
    display: block;
    width: inherit;
    height: inherit;
    font-size: .9em;
    position: absolute;
    left: -3%;
    top: 0;
}

.numberBall>span::before,
.numberBall-primary>span::before,
.numberBall-disable>span::before {
    content: "";
    color: #bbbbbb;
    position: absolute;
    left: -1em;
    font-size: .75em;
    font-weight: normal;
}

[class*="numberBall"]:first-child>span::before {
    content: "";
}

.numberBall+[class*="numberBall"],
.numberBall-primary+[class*="numberBall"],
.numberBall-disable+[class*="numberBall"] {
    margin-left: 1.1em;
}

.smallBalll.numberBall,
.smallBalll.numberBall-primary,
.smallBalll.numberBall-disable {
    font-size: 1em;
}

.numberBall {
    background: linear-gradient(to top, #9c0000 0%, #f85252 100%);
    background: -webkit-linear-gradient(top, #f85252 0%, #9c0000 100%);
}

.numberBall-primary {
    background: linear-gradient(to top, #233d67 0%, #5574a7 100%);
    background: -webkit-linear-gradient(top, #5574a7 0%, #233d67 100%);
}

.numberBall-disable {
    background: linear-gradient(to top, #a3a3a3 0%, #bbbbbb 100%);
    background: -webkit-linear-gradient(top, #bbbbbb 0%, #a3a3a3 100%);
}

.numberGame-d .live-a .time [class*="numberBall"].smallBalll {
    font-size: 1.3em;
}

.numericKeypad {
    margin-right: -0.15em;
    margin-bottom: -0.15em;
}

.numericKeypad [class*="Btn"] {
    width: calc( (100% - 0.15em * 3) / 3);
    float: left;
    text-align: center;
    font-weight: bolder;
    margin-right: 0.15em;
    margin-bottom: 0.15em;
    margin-left: 0;
}

.numericKeypad [class*="Btn"]:nth-child(3n) {
    margin-right: 0;
}

.numericKeypad [class*="Btn"]:nth-last-child(1),
.numericKeypad [class*="Btn"]:nth-last-child(2),
.numericKeypad [class*="Btn"]:nth-last-child(3) {
    margin-bottom: 0;
}

.numericKeypad::after {
    content: "";
    display: block;
    clear: both;
}

.colorBall,
.colorBall-primary,
.colorBall-disable {
    display: inline-block;
    border-radius: 100%;
    width: 1.85em;
    height: 1.85em;
    color: #01122b;
    font-weight: bold;
    line-height: 1.85em;
    text-align: center;
    font-size: 1.3em;
    position: relative;
    background-image: url(../../_global/common/Images/colorball_white.png);
    background-position: top center;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #e60012;
    vertical-align: middle;
    font-family: "Arial", "Tahoma", "pmingliu", "新細明體";
}

.colorBall>span,
.colorBall-primary>span,
.colorBall-disable>span {
    display: block;
    width: inherit;
    height: inherit;
    font-size: inherit;
    position: absolute;
    left: 0;
    top: 0;
}

.colorBall+.colorBall,
.colorBall-primary+.colorBall,
.colorBall-disable+.colorBall,
.colorBall+.colorBall-primary,
.colorBall-primary+.colorBall-primary,
.colorBall-disable+.colorBall-primary,
.colorBall+.colorBall-disable,
.colorBall-primary+.colorBall-disable,
.colorBall-disable+.colorBall-disable {
    margin-left: .2em;
}

.colorBall.smallBalll,
.smallBalll.colorBall-primary,
.smallBalll.colorBall-disable {
    font-size: 1em;
}

.selectorScore .colorBall,
.selectorScore .colorBall-primary,
.selectorScore .colorBall-disable {
    margin: 3px;
}

.colorBall-primary {
    background-color: #0069b1;
}

.colorBall-disable {
    background-color: #dfdfdf;
    color: #a3a3a3;
}

.numberGame-d .live-a .time .colorBall.smallBalll,
.numberGame-d .live-a .time .smallBalll.colorBall-primary,
.numberGame-d .live-a .time .smallBalll.colorBall-disable {
    font-size: 1.3em;
}

.overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 37;
}

.overlay.secondary {
    background: transparent;
}

.overlay-negative {
    z-index: -1;
    background: transparent;
}

.panel {
    color: #545454;
    background: whitesmoke;
    border-radius: 3px;
    border: 1px solid #cdcdcd;
    margin-bottom: 0.5em;
}

.panel .panelContent {
    margin: 0 auto;
    padding: 2em;
}

.panel .btnArea {
    padding: 1.25em;
    text-align: center;
    background: #ececec;
}

.panel .btnArea .largeBtn {
    display: inline-block;
    float: none;
    min-width: 8em;
}

.panel .text-strong {
    font-weight: bold;
}

.backtoTop {
    position: fixed;
    -webkit-backface-visibility: hidden;
    bottom: 5em;
    right: 2em;
    z-index: 99;
}

.backtoTop a {
    display: inline-block;
    color: #ffffff;
    background: #5574a7;
    border-radius: 100%;
    cursor: pointer;
    opacity: 0.8;
}

.backtoTop a::before {
    display: inline-block;
    font-size: 3.5em;
    margin: 0.25em;
    width: 1em;
    height: 1em;
    line-height: 1;
}

.backtoTop a:hover {
    opacity: 1;
}

.alertArea {
    position: absolute;
    top: -0.5em;
    right: -0.7em;
    z-index: 10;
}

.alertArea .alert {
    background: #b53f39;
    color: #ffffff;
    border-radius: 3px;
    -ms-transform: scale(0.7);
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    text-align: center;
    min-width: 1em;
    padding: 0 .2em;
    line-height: 1.25;
    float: left;
    border: 1px solid transparent;
}

.alertArea .alert.secondary {
    background: #545454;
}

.alertArea .alert+.alert {
    margin-left: -0.333em;
}

.heading .alertArea {
    right: 0;
    top: .1em;
}

.nav-widgetPanel .alertArea {
    right: .2em;
    top: .2em;
}

.icon-message .alertArea {
    top: -.2em;
    right: -.1em;
}

.heading.current .alertArea,
.nav-widgetPanel.current .alertArea {
    display: none;
}

.mini .heading.current .alertArea,
.mini .nav-widgetPanel.current .alertArea {
    display: block;
}

.mini .widgetPanel.active .heading.current .alertArea,
.mini .widgetPanel.active .nav-widgetPanel.current .alertArea {
    display: none;
}

.filterBlock,
.filterBlock-title,
.filterBlock-other {
    padding: 0.5em;
    background: #ececec;
    color: rgba(0, 0, 0, 0.75);
    border: 1px solid #dfdfdf;
    border-radius: 3px 3px 0 0;
}

.filterBlock .accent,
.filterBlock-title .accent,
.filterBlock-other .accent {
    color: #b53f39;
}

.filterBlock+.filterBlock {
    border-top: 0;
}

.filterBlock .filterRow .filter.dropdown-flexible {
    margin-left: 0;
    margin-right: 0.25em;
    margin-top: 0.5em;
}

.filterBlock .filterRow .filter.dropdown-flexible+.largeBtn {
    margin-left: 0;
    margin-right: 0.25em;
    margin-top: 0.5em;
    padding: 0.15em 0.833em;
    border: 1px solid #7591c1;
}

.filterBlock .filterRow>.txt {
    float: left;
    color: #545454;
    padding: 0.25em 0.5em;
}

.filterBlock .filterRow>.txt+.filter {
    margin-left: 0;
}

.filterBlock .filterRow[class*="list"],
.filterBlock .filterRow[class*="text-block"] {
    font-weight: bold;
}

.filterBlock .filterRow[class*="list"] .active,
.filterBlock .filterRow[class*="text-block"] .active {
    cursor: default;
}

.filterBlock .filterRow.list-block .listTitle-secondary,
.filterBlock .filterRow.list-block .listTitle-content,
.filterBlock .filterRow.list-block .filter {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.filterBlock .filterRow.list-block .listTitle-secondary span+span,
.filterBlock .filterRow.list-block .listTitle-content span+span,
.filterBlock .filterRow.list-block .filter span+span {
    margin-left: 0.25em;
}

.filterBlock .filterRow.list-block .listTitle-secondary .secondary,
.filterBlock .filterRow.list-block .listTitle-content .secondary,
.filterBlock .filterRow.list-block .filter .secondary {
    font-weight: normal;
}

.filterBlock .filterRow.list-block .listTitle-secondary+div,
.filterBlock .filterRow.list-block .listTitle-content+div,
.filterBlock .filterRow.list-block .filter+div {
    margin: 0.25em 0 0 0;
}

.filterBlock .filterRow.list-block .filter {
    min-width: 0;
    height: auto;
    box-sizing: border-box;
}

.filterBlock .filterRow.list-block .filter .filter-listItem-time {
    min-width: 5em;
    margin-top: .1em;
    display: inline-block;
}

.filterBlock .filterRow.list-block .filter .filter-listItem-match {
    padding-top: .2em;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: block;
    margin-left: 0;
}

.filterBlock .filterRow.list-block .smallBtn {
    font-size: .95em;
}

.filterBlock .filterRow.list-block .smallBtn::before {
    -ms-transform: translate(0, -0.1em) scale(1.4);
    -webkit-transform: translate(0, -0.1em) scale(1.4);
    transform: translate(0, -0.1em) scale(1.4);
}

@media screen and (min-width: 0\0) {
    .filterBlock .filterRow.list-block .smallBtn::before {
        font-size: .75em;
    }
}

.filterBlock .filterRow.list {
    margin-bottom: -0.25em;
}

.filterBlock .filterRow.list .filter {
    margin: 0 0.25em 0.25em 0;
}

.filterBlock .filterRow .smallBtn {
    font-weight: normal !important;
    float: none;
}

.filterBlock .filterRow::after {
    content: "";
    display: block;
    clear: both;
}

.filterBlock .listTitle,
.filterBlock .listTitle-secondary {
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.filterBlock .listTitle {
    color: black;
}

.filterBlock .listTitle-secondary {
    color: #5574a7;
}

.filterBlock .listTitle-content {
    font-weight: normal;
    text-align: center;
}

.filterBlock .listTitle-content .accent {
    font-weight: bold;
}

.filterBlock.listGroup-5 {
    text-align: center;
    padding: 1.333em 1.333em 0;
}

.filterBlock.listGroup-5 .filterRow {
    float: left;
    width: calc( (100% - 1.333em*4) / 5 - 0.1em);
    margin: 0 1.333em 1.333em 0;
}

.filterBlock.listGroup-5 .filterRow:nth-child(5n) {
    margin-right: 0;
}

.filterBlock.listGroup-5 .filterRow:nth-child(6n) {
    clear: both;
}

.filterBlock.listGroup-5 .filterRow::after {
    content: "";
    display: block;
    clear: both;
}

.filterBlock.listGroup-5 .filterRow .filter {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.filterBlock.listGroup-9 {
    text-align: center;
}

.filterBlock.listGroup-9 .filterRow {
    float: left;
    width: calc( (100% - 1.333em*4) / 9);
    margin: 0 0.6665em 0 0;
}

.filterBlock.listGroup-9 .filterRow:nth-child(9n) {
    margin-right: 0;
}

.filterBlock.listGroup-9 .filterRow:nth-child(10n) {
    clear: both;
}

.filterBlock.listGroup-9 .filterRow::after {
    content: "";
    display: block;
    clear: both;
}

.filterBlock.listGroup-9 .filterRow .filter {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.filterBlock.textGroup {
    padding: 0.5em 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
}

.filterBlock.textGroup .filterRow,
.filterBlock.textGroup .filterRow-large,
.filterBlock.textGroup .filterRow-larger {
    padding: 0 0.5em;
    min-width: 0;
    overflow: hidden;
    border-left: 1px solid #dfdfdf;
}

.filterBlock.textGroup .filterRow:first-child,
.filterBlock.textGroup .filterRow-large:first-child,
.filterBlock.textGroup .filterRow-larger:first-child {
    border-left: 0;
}

.filterBlock.textGroup .filterRow {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.filterBlock.textGroup .filterRow-large {
    -webkit-box-flex: 2;
    -webkit-flex: 2;
    -ms-flex: 2;
    flex: 2;
}

.filterBlock.textGroup .filterRow-larger {
    -webkit-box-flex: 3;
    -webkit-flex: 3;
    -ms-flex: 3;
    flex: 3;
}

.filterBlock::after {
    content: "";
    display: block;
    clear: both;
}

.filterBlock+[class*="accountTable"] .tableHead,
.filterBlock+[class*="accountTable"] .tableHead-sub {
    border-radius: 0;
}

.filterBlock-title {
    border-bottom-width: 0;
    color: black;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.filterBlock-title[class*="icon-"]::before {
    font-size: 1.6em;
    margin-right: 0.25em;
    font-weight: normal;
    line-height: 1;
    vertical-align: top;
}

.filterBlock-title.accent {
    color: #b53f39;
}

.filterBlock-other {
    border: 0;
    background: #cdcdcd;
    color: black;
    font-weight: bold;
}

.filterBlock-other .floatRight {
    float: right;
    margin-top: 0.15em;
}

.filterBlock-other .floatRight [class*="icon-"] {
    font-weight: normal;
}

.filterBlock-other .txtBlock {
    display: inline-block;
    font-weight: lighter;
    padding-right: 1em;
    vertical-align: middle;
}

.filterBlock-other .txtBlock .smallBtn {
    float: none;
    vertical-align: middle;
}

[class*="heading-"]+[class*="filterBlock"],
.filterBlock-title+[class*="filterBlock"],
.filterBlock-other {
    border-radius: 0 0 0 0;
}

.hint,
.hint-absolute {
    background: #feec6e;
    padding: 0.5em 0.833em;
    color: #545454;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.hint .glyphIcon,
.hint-absolute .glyphIcon {
    margin-right: 0.5em;
    cursor: default;
}

.hint .content,
.hint-absolute .content {
    -webkit-box-flex: 1 0 0px;
    -webkit-flex: 1 0 0px;
    -ms-flex: 1 0 0px;
    flex: 1 0 0px;
}

.hint .accent,
.hint-absolute .accent {
    color: #b53f39;
    font-weight: bold;
}

.hint.hint-accent,
.hint-absolute.hint-accent {
    background-color: #f5e0df;
    color: #b53f39;
    text-align: center;
}

.quickBetPanel .hint,
.betSlip .hint {
    margin-top: 0.5em;
}

.hint-absolute {
    position: absolute;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    border-radius: 3px;
    z-index: 34;
    min-width: 15em;
    white-space: normal;
}

.hint-absolute::before {
    content: "";
    position: absolute;
    top: -.9em;
    left: calc(100%/2 - 0.5em);
    border-width: 0.5em;
    border-color: transparent transparent #feec6e transparent;
    border-style: solid;
    z-index: 2;
}

.hint-absolute::after {
    content: "";
    position: absolute;
    top: -1em;
    left: calc(100%/2 - 0.5em);
    border-width: 0.5em;
    border-color: transparent transparent rgba(0, 0, 0, 0.4) transparent;
    border-style: solid;
    z-index: 1;
}

.hint-absolute.arrowDown {
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.hint-absolute.arrowDown::before {
    top: auto;
    bottom: -.9em;
    border-color: #feec6e transparent transparent transparent;
}

.hint-absolute.arrowDown::after {
    top: auto;
    bottom: -1em;
    border-color: rgba(0, 0, 0, 0.4) transparent transparent transparent;
}

.oddsBet-circle .hint-absolute.arrowDown {
    min-width: 100%;
    text-align: center;
    -ms-transform: translateX(-0.25em);
    -webkit-transform: translateX(-0.25em);
    transform: translateX(-0.25em);
}

.hint-absolute.arrowLeft {
    top: 50%;
    -ms-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

.hint-absolute.arrowLeft::before {
    top: calc(100%/2 - 0.5em);
    left: -.9em;
    border-color: transparent #feec6e transparent transparent;
}

.hint-absolute.arrowLeft::after {
    top: calc(100%/2 - 0.5em);
    left: -1em;
    border-color: transparent rgba(0, 0, 0, 0.4) transparent transparent;
}

.smallBtn .hint-absolute.arrowLeft {
    left: 1.25em;
}

.header-search .hint-absolute {
    left: 0;
    top: 95%;
    min-width: 90%;
}

.oddsBet-circle:hover .hint-absolute {
    visibility: visible;
    top: -1.9em;
    left: -0.6em;
}

.oddsBet-circle .hint-absolute,
.oddsBet-circle.drawn .hint-absolute {
    visibility: hidden;
}

.field-group .hint-absolute {
    min-width: 12.5em;
}

.infoItem .hint-absolute {
    min-width: 12.5em;
    text-align: left;
}

.hint-absolute .left {
    float: left;
    margin-right: 0.833em;
    height: 98px;
}

.hint-absolute .circleBtn.icon-close {
    position: absolute;
    right: -.5em;
    top: -.5em;
    box-sizing: border-box;
    background-color: #545454;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.hint-absolute .circleBtn.icon-close::before {
    padding: 0;
    -ms-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
}

.hint-absolute .circleBtn.icon-close:hover {
    background-color: #7c7c7c;
}

.hint-absolute .title {
    font-size: 1.3em;
}

.hint-absolute .txt {
    padding: 0.5em 0;
}

.hint-absolute label {
    margin-top: 0.833em;
    font-weight: lighter;
}

.passStrength {
    display: block !important;
    border-radius: 3px;
    border: 1px solid #cdcdcd;
    margin-top: 0.5em;
    box-shadow: 1px 1px 3px 2px rgba(0, 0, 0, 0.1) inset;
    background: #ffffff;
}

.passStrength span {
    width: 100%;
    color: #ffffff;
    text-align: center;
    display: block;
}

.passStrength.shortPass span {
    color: #9c0000;
    text-align: left;
    border-left: 5px solid #dd5b57;
    padding-left: 0.5em;
}

.passStrength.otherPass span {
    background: linear-gradient(to left, #cf3d3a 0%, #dd5b57 100%);
    background: -webkit-linear-gradient(left, #dd5b57 0%, #cf3d3a 100%);
}

.passStrength.badPass span {
    width: 33%;
    background: linear-gradient(to left, #cf3d3a 0%, #dd5b57 100%);
    background: -webkit-linear-gradient(left, #dd5b57 0%, #cf3d3a 100%);
}

.passStrength.goodPass span {
    width: 66%;
    background: linear-gradient(to left, #ec9739 0%, #f2b556 100%);
    background: -webkit-linear-gradient(left, #f2b556 0%, #ec9739 100%);
}

.passStrength.strongPass span {
    background: linear-gradient(to left, #43a543 0%, #66bf66 100%);
    background: -webkit-linear-gradient(left, #66bf66 0%, #43a543 100%);
}

.passStrengthFake {
    border-radius: 3px;
    border: 1px solid #cdcdcd;
    margin-top: 0.5em;
    box-shadow: 1px 1px 3px 2px rgba(0, 0, 0, 0.1) inset;
    background: #ffffff;
    height: 5px;
    width: 70% !important;
    position: relative;
}

.passStrengthFake span {
    color: #545454;
    text-align: center;
    height: 10px;
    position: absolute;
    right: -72px;
    top: -5px;
}

.passStrengthFake:before {
    content: "";
    display: block;
    width: 100%;
    height: 5px;
}

.passStrengthFake.shortPass:before {
    border-left: 5px solid #dd5b57;
}

.preloader,
.preloader-small,
.preloader-secondary,
.preloader-secondary-small,
.preloader-third,
.preloader-third-small {
    text-align: center;
    background: rgba(0, 0, 0, 0.03);
}

.preloader .spin,
.preloader-small .spin,
.preloader-secondary .spin,
.preloader-secondary-small .spin,
.preloader-third .spin,
.preloader-third-small .spin {
    position: relative;
    display: inline-block;
    width: 1.4em;
    height: 1.4em;
    margin: 0.34708em 0;
    font-size: 2.4em;
}

.preloader .spin-circle,
.preloader-small .spin-circle,
.preloader-secondary .spin-circle,
.preloader-secondary-small .spin-circle,
.preloader-third .spin-circle,
.preloader-third-small .spin-circle {
    position: absolute;
    width: inherit;
    height: inherit;
    opacity: 0;
    -webkit-animation: spinCircle-rotate 1.7s linear infinite;
    animation: spinCircle-rotate 1.7s linear infinite;
    animation-delay: 0;
}

.preloader .spin-circle:after,
.preloader-small .spin-circle:after,
.preloader-secondary .spin-circle:after,
.preloader-secondary-small .spin-circle:after,
.preloader-third .spin-circle:after,
.preloader-third-small .spin-circle:after {
    content: "";
    position: absolute;
    width: .2em;
    height: .2em;
    left: 50%;
    border-radius: 100%;
}

.preloader .spin-circle:nth-child(2),
.preloader-small .spin-circle:nth-child(2),
.preloader-secondary .spin-circle:nth-child(2),
.preloader-secondary-small .spin-circle:nth-child(2),
.preloader-third .spin-circle:nth-child(2),
.preloader-third-small .spin-circle:nth-child(2) {
    animation-delay: 125ms;
}

.preloader .spin-circle:nth-child(3),
.preloader-small .spin-circle:nth-child(3),
.preloader-secondary .spin-circle:nth-child(3),
.preloader-secondary-small .spin-circle:nth-child(3),
.preloader-third .spin-circle:nth-child(3),
.preloader-third-small .spin-circle:nth-child(3) {
    animation-delay: 250ms;
}

.preloader .spin-circle:nth-child(4),
.preloader-small .spin-circle:nth-child(4),
.preloader-secondary .spin-circle:nth-child(4),
.preloader-secondary-small .spin-circle:nth-child(4),
.preloader-third .spin-circle:nth-child(4),
.preloader-third-small .spin-circle:nth-child(4) {
    animation-delay: 375ms;
}

.preloader .spin-circle:nth-child(5),
.preloader-small .spin-circle:nth-child(5),
.preloader-secondary .spin-circle:nth-child(5),
.preloader-secondary-small .spin-circle:nth-child(5),
.preloader-third .spin-circle:nth-child(5),
.preloader-third-small .spin-circle:nth-child(5) {
    animation-delay: 500ms;
}

.preloader-small .spin,
.preloader-secondary-small .spin,
.preloader-third-small .spin {
    font-size: 1.4em;
}

.preloader .spin-circle:after,
.preloader-small .spin-circle:after {
    background: #7591c1;
}

.preloader-secondary .spin-circle:after,
.preloader-secondary-small .spin-circle:after {
    background: #bbbbbb;
}

.preloader-third,
.preloader-third-small {
    background: rgba(85, 116, 167, 0.6);
}

.preloader-third .spin-circle:after,
.preloader-third-small .spin-circle:after {
    background: #ffffff;
}

.preloader-center {
    padding: 0;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 14;
}

.preloader-center .spin {
    margin: auto;
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
}

@keyframes spinCircle-rotate {
    0% {
        transform: rotate(0deg);
        opacity: 1;
    }
    45% {
        transform: rotate(225deg);
        opacity: 1;
        animation-timing-function: ease-out;
    }
    60% {
        transform: rotate(360deg);
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}

.noInfo {
    background: #dfdfdf;
    color: #7c7c7c;
    padding: 0.833em;
    text-align: center;
    font-weight: bold;
}

.msg {
    position: fixed;
    -webkit-backface-visibility: hidden;
    padding: 0 2em;
    border-radius: 3px;
    width: 35em;
    margin: 0 auto;
    left: 0;
    right: 0;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    z-index: 30;
    color: #ffffff;
    font-size: 1.2em;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.msg .icon-confirm,
.msg .icon-messageWarning,
.msg .icon-switch {
    font-size: 4em;
    margin: 0 .4em;
}

.msg .icon-confirm::before,
.msg .icon-messageWarning::before,
.msg .icon-switch::before {
    display: block;
    position: relative;
    top: .02em;
}

.msg .icon-history {
    font-size: 1.5em;
    margin-left: .2em;
    margin-right: .4em;
}

.msg .icon-history::before {
    display: block;
    position: relative;
    top: .02em;
}

.msg.center .icon-confirm,
.msg.center .icon-messageWarning,
.msg.center .icon-switch {
    margin-left: 0;
}

.msg.success {
    background: linear-gradient(to bottom, #5dad00 0%, #48a200 100%);
    background: -webkit-linear-gradient(bottom, #48a200 0%, #5dad00 100%);
}

.msg.warning {
    background: linear-gradient(to bottom, #b53f39 0%, #ab241e 100%);
    background: -webkit-linear-gradient(bottom, #ab241e 0%, #b53f39 100%);
}

.msg.highlight {
    background: linear-gradient(to bottom, #f77a00 0%, #f66800 100%);
    background: -webkit-linear-gradient(bottom, #f66800 0%, #f77a00 100%);
}

.msg.active {
    top: 40%;
}

.msg.highlightSmall {
    background: linear-gradient(to bottom, #f77a00 0%, #f66800 100%);
    background: -webkit-linear-gradient(bottom, #f66800 0%, #f77a00 100%);
    padding: 0 .4em;
    min-width: 40em;
    width: fit-content;
    max-width: 1350px;
    z-index: 35;
    top: 3px;
}

.msg.highlightSmall .smallBtn {
    font-size: .9em;
    color: #ffffff;
}

.msg.highlightSmall .msgText {
    color: white;
}

.msg.highlightSmall .msgText strong {
    color: #ffffff;
    font-size: 1.1em;
}

.msgText {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
}

.msgText strong {
    font-size: 1.167em;
    font-size: 1.2em;
}

.msgText p {
    opacity: 0.8;
    line-height: 1.2;
}

.msgText p strong {
    opacity: 1;
    font-size: 1.1em;
}

.center .msgText {
    -webkit-box-flex: 0;
    -webkit-flex: 0;
    -ms-flex: 0;
    flex: 0;
}

.ani-loading {
    position: relative;
    display: inline-block;
    margin-left: 0.25em;
}

.ani-loading span {
    background-color: #ffffff;
    -webkit-animation: loading-a 1s infinite normal;
    animation: loading-a 1s infinite normal;
    border-radius: 100%;
    margin-right: 0.167em;
    width: 8px;
    height: 8px;
    display: inline-block;
}

.ani-loading span:nth-child(0) {
    -webkit-animation-delay: 0.45s;
    animation-delay: 0.45s;
}

.ani-loading span:nth-child(1) {
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
}

.ani-loading span:nth-child(2) {
    -webkit-animation-delay: 0.75s;
    animation-delay: 0.75s;
}

.ani-loading span:nth-child(3) {
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
}

.child-col-1>* {
    width: 100%;
}

.child-col-2>* {
    width: 50%;
}

.child-col-3>* {
    width: 33.33333%;
}

.child-col-4>* {
    width: 25%;
}

.child-col-5>* {
    width: 20%;
}

.child-col-6>* {
    width: 16.66667%;
}

.child-col-7>* {
    width: 14.28571%;
}

.child-col-8>* {
    width: 12.5%;
}

.child-col-9>* {
    width: 11.11111%;
}

.child-col-10>* {
    width: 10%;
}

.js-bet-name {
    color: #b53f39;
}

.js-bet-name em {
    font-style: normal;
    color: black;
    opacity: .75;
}

.void .js-bet-name em {
    color: inherit;
    opacity: 1;
}

.oddsBet {
    color: #01122b;
    font-weight: bold;
    min-height: 1.5em;
    vertical-align: top;
    padding-right: 0.7em;
}

.oddsBet:hover {
    color: #01122b;
}

.oddsBet::before {
    content: "";
    position: absolute;
    right: -0.1em;
}

@-moz-document url-prefix() {
    .oddsBet::before {
        top: 0.1em;
    }
}

.oddsBet.disable {
    background: transparent;
    color: #a3a3a3;
    font-weight: normal;
}

.oddsBet.text-center {
    padding-right: 1px;
}

.moreBetTypeArea .oddsBet,
.multiOdds-moreBetType .oddsBet {
    padding-left: 0.7em;
}

.account .oddsBet {
    width: 2.7em;
    margin-right: .6em;
    margin-top: .2em;
    margin-left: -.3em;
}

.indicatorDown::before,
.indicatorUp::before,
.indicatorDown-a::before,
.indicatorUp-a::before {
    -ms-transform: scale(1, 1.5);
    -webkit-transform: scale(1, 1.5);
    transform: scale(1, 1.5);
}

.indicatorUp::before,
.indicatorUp-a::before {
    content: "";
}

.indicatorDown::before,
.indicatorDown-a::before {
    content: "";
}

.indicatorDown::before,
.indicatorUp-a::before {
    color: #b53f39;
}

.indicatorUp::before,
.indicatorDown-a::before {
    color: #5dad00;
}

.quickBetPanel {
    display: none;
    background: #ffffff;
    min-width: 17em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    cursor: default;
    padding: 0.5em;
    font-weight: normal;
    color: black;
    text-align: left;
}

.quickBetPanel .numericKeypad {
    width: 17em;
    background: #ffffff;
    padding: 0.5em 0.25em 0.5em 0.5em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    margin: 0 0.3em;
    border-radius: 3px;
}

.quickBetPanel .btnArea {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-top: 0.5em;
}

.quickBetPanel .largeBtn {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
}

.quickBetPanel .stakeBtnGroup {
    padding-bottom: 0.5em;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    float: left;
    width: 100%;
}

.quickBetPanel .stakeBtnGroup+.otherBtnArea {
    padding-top: 0;
}

.quickBetPanel .btnNote {
    padding-bottom: 0.5em;
    text-align: center;
    clear: both;
    background: #ffffff;
}

.quickBetPanel .otherBtnArea {
    padding-top: 0.5em;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    clear: both;
}

.quickBetPanel .otherBtnArea .largeBtn {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
}

.quickBetPanel .hint .content {
    white-space: normal;
}

.panelActive,
.panelActive-bottomRight,
.panelActive-upperLeft,
.panelActive-upperRight {
    position: relative;
}

.panelActive .quickBetPanel,
.panelActive-bottomRight .quickBetPanel,
.panelActive-upperLeft .quickBetPanel,
.panelActive-upperRight .quickBetPanel {
    display: block;
    position: absolute;
    z-index: 16;
}

.panelActive .quickBetPanel .numericKeypad,
.panelActive-bottomRight .quickBetPanel .numericKeypad,
.panelActive-upperLeft .quickBetPanel .numericKeypad,
.panelActive-upperRight .quickBetPanel .numericKeypad {
    position: absolute;
}

.selected.panelActive::after,
.selected.panelActive-bottomRight::after,
.selected.panelActive-upperLeft::after,
.selected.panelActive-upperRight::after {
    content: "";
    position: absolute;
    left: 0;
    width: 100%;
    height: 0.28rem;
    background-color: #ffffff;
    z-index: 28;
}

.selected.indicatorDown.panelActive:hover,
.selected.indicatorDown.panelActive-bottomRight:hover,
.selected.indicatorDown.panelActive-upperLeft:hover,
.selected.indicatorDown.panelActive-upperRight:hover,
.selected.indicatorDown.panelActive::after,
.selected.indicatorDown.panelActive-bottomRight::after,
.selected.indicatorDown.panelActive-upperLeft::after,
.selected.indicatorDown.panelActive-upperRight::after,
.selected.indicatorUp-a.panelActive:hover,
.selected.indicatorUp-a.panelActive-bottomRight:hover,
.selected.indicatorUp-a.panelActive-upperLeft:hover,
.selected.indicatorUp-a.panelActive-upperRight:hover,
.selected.indicatorUp-a.panelActive::after,
.selected.indicatorUp-a.panelActive-bottomRight::after,
.selected.indicatorUp-a.panelActive-upperLeft::after,
.selected.indicatorUp-a.panelActive-upperRight::after,
.selected.indicatorUp.panelActive:hover,
.selected.indicatorUp.panelActive-bottomRight:hover,
.selected.indicatorUp.panelActive-upperLeft:hover,
.selected.indicatorUp.panelActive-upperRight:hover,
.selected.indicatorUp.panelActive::after,
.selected.indicatorUp.panelActive-bottomRight::after,
.selected.indicatorUp.panelActive-upperLeft::after,
.selected.indicatorUp.panelActive-upperRight::after,
.selected.indicatorDown-a.panelActive:hover,
.selected.indicatorDown-a.panelActive-bottomRight:hover,
.selected.indicatorDown-a.panelActive-upperLeft:hover,
.selected.indicatorDown-a.panelActive-upperRight:hover,
.selected.indicatorDown-a.panelActive::after,
.selected.indicatorDown-a.panelActive-bottomRight::after,
.selected.indicatorDown-a.panelActive-upperLeft::after,
.selected.indicatorDown-a.panelActive-upperRight::after {
    background-color: #ffaf96;
}

.panelActive .quickBetPanel {
    left: 0;
    border-radius: 0 3px 3px 3px;
}

@media screen and (min-width: 0\0) {
    .panelActive .quickBetPanel {
        margin: -0.1em 0;
    }
}

.panelActive .quickBetPanel .numericKeypad {
    left: 100%;
    top: 0;
}

.panelActive.selected::after {
    bottom: -0.05em;
}

.panelActive-bottomRight .quickBetPanel {
    border-radius: 3px 0 3px 3px;
    right: 0;
}

.panelActive-bottomRight .quickBetPanel .numericKeypad {
    right: 100%;
    top: 0;
}

.panelActive-bottomRight.selected::after {
    bottom: -0.05em;
}

.panelActive-upperLeft .quickBetPanel {
    border-radius: 3px 3px 3px 0;
    left: 0;
    bottom: 1.5em;
}

.panelActive-upperLeft .quickBetPanel .numericKeypad {
    left: 100%;
    bottom: 0;
}

.panelActive-upperLeft.selected::after {
    top: -1px;
}

.panelActive-upperRight .quickBetPanel {
    border-radius: 3px 3px 0 3px;
    right: 0;
    bottom: 1.5em;
}

.panelActive-upperRight .quickBetPanel .numericKeypad {
    right: 100%;
    bottom: 0;
}

.panelActive-upperRight.selected::after {
    top: -1px;
}

a::before,
a::after,
div::before,
div::after,
span::before,
span::after,
form::before,
form::after,
label::before,
label.primary::before,
label::after,
label.primary::after,
button::before,
button::after,
h1::before,
h1::after,
h2::before,
h2::after,
h3::before,
h3::after,
h4::before,
h4::after,
h5::before,
h5::after,
h6::before,
h6::after,
p::before,
p::after,
li::before,
li::after,
ol::before,
ol::after,
ul::before,
ul::after {
    font-family: "iconFont";
}

.smallBtn,
.smallBtn-text {
    height: 1.25em;
    font-size: 1.1em;
}

.smallBtn::before,
.smallBtn-text::before {
    font-size: 1.25em;
    position: relative;
    line-height: 1;
}

.smallBtn+.smallBtn,
.smallBtn-text+.smallBtn,
.smallBtn+.smallBtn-text,
.smallBtn-text+.smallBtn-text {
    margin-left: 0.167em;
}

.icon-favorite.smallBtn::before,
.icon-favorite.smallBtn-text::before {
    font-size: 2em;
    top: -0.21em;
    left: -0.16em;
    color: #7591c1;
}

@media screen and (min-width: 0\0) {
    .icon-favorite.smallBtn::before,
    .icon-favorite.smallBtn-text::before {
        font-size: 0.75em;
    }
}

.icon-favorite.added.smallBtn,
.icon-favorite.added.smallBtn-text {
    text-shadow: 0 0 0.1em black;
}

.primary.smallBtn,
.primary.smallBtn-text {
    background: #5574a7;
    color: #ffffff !important;
}

.primary.smallBtn:hover,
.primary.smallBtn-text:hover {
    background: #768fb9;
}

.primary.unfilter.smallBtn,
.primary.unfilter.smallBtn-text {
    color: #ffd330 !important;
}

.accent.smallBtn,
.accent.smallBtn-text {
    background: #b53f39;
    color: #ffffff;
}

.accent.smallBtn:hover,
.accent.smallBtn-text:hover {
    background: #ca5d57;
}

.accent.icon-live.smallBtn:hover,
.accent.icon-live.smallBtn-text:hover {
    background: #b53f39;
}

.inactive-light.smallBtn,
.inactive-light.smallBtn-text {
    background: #ececec;
}

.inactive-light.smallBtn:hover,
.inactive-light.smallBtn-text:hover {
    background: white;
}

.inactive.smallBtn,
.inactive.smallBtn-text {
    background: #bbbbbb;
    color: #ffffff;
}

.inactive.smallBtn:hover,
.inactive.smallBtn-text:hover {
    background: #a1a1a1;
}

.inactive.icon-streaming.smallBtn:hover,
.inactive.icon-streaming.smallBtn-text:hover {
    background: #bbbbbb;
}

.inactive-dark.smallBtn,
.inactive-dark.smallBtn-text {
    background: #545454;
    color: #ffffff;
}

.inactive-dark.smallBtn:hover,
.inactive-dark.smallBtn-text:hover {
    background: #6e6e6e;
}

.special.smallBtn,
.special.smallBtn-text {
    background: #adbed6;
    color: #ffffff;
}

.special.smallBtn:hover,
.special.smallBtn-text:hover {
    background: #8ba3c5;
}

.specialA.smallBtn,
.specialA.smallBtn-text {
    background: #3485c7;
    color: #ffffff;
}

.specialB.smallBtn,
.specialB.smallBtn-text {
    background: #c14a99;
    color: #ffffff;
}

.specialC.smallBtn,
.specialC.smallBtn-text {
    background: #7591c1;
    color: #ffffff;
}

.specialC.smallBtn:hover,
.specialC.smallBtn-text:hover {
    background: #5275b1;
}

.specialD.smallBtn,
.specialD.smallBtn-text {
    background: #f77a00;
    color: #ffffff;
}

.specialD.smallBtn:hover,
.specialD.smallBtn-text:hover {
    background: #ff942b;
}

.specialE.smallBtn,
.specialE.smallBtn-text {
    background: #f2be00;
    color: #ffffff;
}

.specialE.smallBtn:hover,
.specialE.smallBtn-text:hover {
    background: #fcc600;
}

.cashoutStyle.smallBtn,
.cashoutStyle.smallBtn-text {
    background: #ffd330;
    color: #52332c;
}

.cashoutStyle.smallBtn:hover,
.cashoutStyle.smallBtn-text:hover {
    background: #fcc600;
}

.textAccent.smallBtn,
.textAccent.smallBtn-text {
    color: #b53f39;
}

.textA.smallBtn,
.textA.smallBtn-text {
    color: #435f8b;
}

.textB.smallBtn,
.textB.smallBtn-text {
    color: #73483e;
}

.textC.smallBtn,
.textC.smallBtn-text {
    color: #545454;
}

.circle.smallBtn,
.circle.smallBtn-text {
    border-radius: 100%;
}

.flexible.smallBtn,
.widgetPanel .smallBtn.bottomArea.flexible-open,
.widgetPanel .smallBtn.bottomArea.flexible-close,
.flexible.smallBtn-text,
.widgetPanel .smallBtn-text.bottomArea.flexible-open,
.widgetPanel .smallBtn-text.bottomArea.flexible-close {
    width: auto;
}

.smallBtn {
    width: 1.25em;
    padding: 0;
}

.smallBtn.icon-rain::before,
.smallBtn.icon-coffee::before {
    font-size: 1.4em;
    top: -0.1em;
}

@media screen and (min-width: 0\0) {
    .smallBtn.icon-rain::before,
    .smallBtn.icon-coffee::before {
        font-size: 0.8em;
    }
}

.smallBtn.icon-result::before {
    font-size: 1em;
    top: 0.15em;
    left: 0.1em;
}

@media screen and (min-width: 0\0) {
    .smallBtn.icon-result::before {
        font-size: 0.8em;
    }
}

.smallBtn.icon-new {
    background: #ffd330;
    color: #73483e;
    padding: 0 0.3em;
    -ms-transform: scale(0.9);
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
    font-weight: bold;
}

.smallBtn.icon-trophy {
    width: 1.23em;
    background: #f77a00;
    color: #ffffff;
    font-weight: normal;
}

.smallBtn-text {
    min-width: 3.5em;
    line-height: 1;
    padding: 0 .3em;
}

.smallBtn-text::before {
    top: .15em;
    margin-left: -.3em;
    margin-right: -.05em;
    line-height: 0;
}

@media screen and (min-width: 0\0) {
    .smallBtn-text::before {
        font-size: 0.8em;
    }
}

.smallBtn-text.icon-moreCollapse::before,
.smallBtn-text.icon-moreExpand::before {
    font-size: 1em;
}

.moreInfo {
    color: #b53f39;
}

.moreInfo .smallBtn.icon-info {
    position: absolute;
    margin-left: .5em;
    margin-top: 1px;
}

.buttonNav,
.buttonNav-upper {
    background: #ffffff;
    color: #545454;
    position: absolute;
    z-index: 31;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    white-space: nowrap;
    border: 1px solid #cdcdcd;
    visibility: hidden;
    opacity: 0;
    transition: all 0.1s ease-in;
    -ms-transform: translateX(calc(-50% + (1.25em/2)));
    -webkit-transform: translateX(calc(-50% + (1.25em/2)));
    transform: translateX(calc(-50% + (1.25em/2)));
}

.buttonNav>li+li,
.buttonNav-upper>li+li {
    border-top: 1px solid #cdcdcd;
}

.buttonNav>li,
.buttonNav-upper>li {
    padding: 0.15em 0.3em;
    height: 0;
}

.buttonNav>li:hover,
.buttonNav-upper>li:hover {
    background: #d6d6d6;
}

.smallBtn:hover .buttonNav,
.smallBtn:hover .buttonNav-upper {
    visibility: visible;
    opacity: 1;
}

.smallBtn:hover .buttonNav>li,
.smallBtn:hover .buttonNav-upper>li {
    height: auto;
}

.buttonNav {
    top: 0;
}

.smallBtn:hover .buttonNav {
    top: 1.25em;
}

.buttonNav-upper {
    bottom: 0;
}

.smallBtn:hover .buttonNav-upper {
    bottom: 1.25em;
}

.largeBtn.secondary,
.largeBtn.tertiary {
    border-width: 1px;
    border-style: solid;
    padding: 0.2em 0.75em;
}

.largeBtn {
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
    color: #ffffff;
    padding: 0.3em 0.833em;
    text-align: center;
    white-space: nowrap;
}

.largeBtn:hover {
    background: linear-gradient(to bottom, #879fc9 0%, #6582b1 100%);
    background: -webkit-linear-gradient(bottom, #6582b1 0%, #879fc9 100%);
}

.largeBtn::before {
    float: left;
    -ms-transform: translate(0, 0) scale(1.3);
    -webkit-transform: translate(0, 0) scale(1.3);
    transform: translate(0, 0) scale(1.3);
    margin: 0 .2em;
}

.largeBtn.spin::before {
    -webkit-animation: rotateScale 1.5s linear infinite;
    animation: rotateScale 1.5s linear infinite;
}

.largeBtn.secondary {
    background: #cdcdcd;
    color: #545454;
    border-color: #cdcdcd;
}

.largeBtn.secondary:hover {
    background: silver;
    border-color: silver;
}

.largeBtn.tertiary {
    background: linear-gradient(to bottom, #ececec 0%, #cdcdcd 100%);
    background: -webkit-linear-gradient(bottom, #cdcdcd 0%, #ececec 100%);
    color: #545454;
    border-color: #a3a3a3;
}

.largeBtn.tertiary:hover {
    background-image: none;
    background-color: whitesmoke;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.largeBtn.accent {
    background: linear-gradient(to bottom, #ca5d57 0%, #b53f39 100%);
    background: -webkit-linear-gradient(bottom, #b53f39 0%, #ca5d57 100%);
    color: #ffffff;
}

.largeBtn.accent:hover {
    background: #ca5d57;
}

.largeBtn.specialD {
    background: linear-gradient(to bottom, #ff942b 0%, #f77a00 100%);
    background: -webkit-linear-gradient(bottom, #f77a00 0%, #ff942b 100%);
}

.largeBtn.specialD:hover {
    background: #ff942b;
}

.largeBtn.disable {
    background: linear-gradient(to bottom, #d5d5d5 0%, #cdcdcd 100%);
    background: -webkit-linear-gradient(bottom, #cdcdcd 0%, #d5d5d5 100%);
    color: #a3a3a3;
    cursor: default;
}

.largestBtn,
.largestBtn-lightBlue,
.middleBtn {
    border-radius: 3px;
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
    color: #ffffff;
    padding: 0.3em;
    font-size: 2em;
}

.largestBtn:hover,
.largestBtn-lightBlue:hover,
.middleBtn:hover {
    color: #ffffff;
    background: linear-gradient(to bottom, #879fc9 0%, #6582b1 100%);
    background: -webkit-linear-gradient(bottom, #6582b1 0%, #879fc9 100%);
}

.largestBtn.text-lightBlue,
.text-lightBlue.largestBtn-lightBlue,
.text-lightBlue.middleBtn {
    color: #adbed6;
}

.largestBtn.text-lightBlue:hover,
.text-lightBlue.largestBtn-lightBlue:hover,
.text-lightBlue.middleBtn:hover {
    color: #ffffff;
    background: linear-gradient(to bottom, #879fc9 0%, #6582b1 100%);
    background: -webkit-linear-gradient(bottom, #6582b1 0%, #879fc9 100%);
}

.largestBtn-lightBlue {
    background: linear-gradient(to bottom, #adbed6 0%, #7591c1 100%);
    background: -webkit-linear-gradient(bottom, #7591c1 0%, #adbed6 100%);
}

.largestBtn-lightBlue:hover {
    background: linear-gradient(to bottom, #becbdf 0%, #879fc9 100%);
    background: -webkit-linear-gradient(bottom, #879fc9 0%, #becbdf 100%);
}

.middleBtn {
    padding: 0 .1em;
    font-size: 1.5em;
}

.circleBtn {
    border-radius: 100%;
    display: inline-block;
    padding: .3em;
    background: #5574a7;
}

.circleBtn::before {
    display: inline-block;
    color: #ffffff;
    font-size: 1.5em;
    line-height: 1;
}

.circleBtn:hover {
    background: #768fb9;
}

.tool {
    background: whitesmoke;
    color: #545454;
    border: 1px solid #cdcdcd;
    width: 1.8em;
    height: 1.8em;
    float: left;
    border-radius: 3px;
}

.tool::before {
    font-size: 1.25em;
    position: relative;
    top: .35em;
    left: .3em;
    line-height: 1;
}

.tool+.tool {
    margin-left: 0.167em;
}

.language {
    width: 12.083em;
}

.language.dropdown .selected {
    font-weight: bold;
    padding-top: .1em;
    padding-left: .5em;
}

.language.dropdown .dropdownPanel {
    border-top: 0;
    background: #545454;
    max-height: none;
}

.language.dropdown .dropdownPanel .content {
    color: #ececec;
    padding-left: .55em;
}

.language.dropdown .dropdownPanel .content:hover {
    background: #323232;
}

.language.dropdown .dropdownPanel .content:last-child {
    border-width: 0;
}

.language.dropdownActive,
.language:hover {
    background: #7c7c7c;
    color: #ffffff;
    border-color: #7c7c7c;
}

.setting {
    border-radius: 0 3px 3px 0;
    width: 0.667em;
    height: 2em;
    background: #cdcdcd;
    color: #ffffff;
    padding-left: 0;
    overflow: hidden;
    position: absolute;
    left: -0.667em;
}

.setting::before {
    content: "";
    position: relative;
    right: 0.65em;
    font-size: 1.3em;
    line-height: 1;
}

@-moz-document url-prefix() {
    .setting::before {
        top: 0.1em;
    }
}

.setting:hover {
    background: #7c7c7c;
    width: 2em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    z-index: 12;
}

.setting:hover::before {
    right: -0.2em;
}

.setting.fixed {
    border-radius: 3px;
    position: static;
    margin-left: 0.25em;
}

.setting.fixed::before {
    right: 0.25em;
}

.setting.fixed:hover {
    width: 0.667em;
    box-shadow: none;
}

.setting.showMore::before {
    content: "";
}

.setting.showLess::before {
    content: "";
}

.lineCircle-accent.glyphIcon,
.lineCircle-accent.glyphIcon-large,
.lineCircle-primary.glyphIcon,
.lineCircle-primary.glyphIcon-large {
    border-radius: 100%;
    border: 1px solid transparent;
    width: 1.5em;
    height: 1.5em;
}

.glyphIcon,
.glyphIcon-large {
    padding: 0;
    color: #7c7c7c;
    width: 1.25em;
    height: 1.25em;
}

.glyphIcon::before,
.glyphIcon-large::before {
    line-height: 1;
    font-size: 1.55em;
    font-weight: normal;
    display: block;
}

.lineCircle-accent.glyphIcon,
.lineCircle-accent.glyphIcon-large {
    border-color: #b53f39;
}

.lineCircle-primary.glyphIcon,
.lineCircle-primary.glyphIcon-large {
    border-color: #5574a7;
}

.primary.glyphIcon,
.primary.glyphIcon-large {
    color: #5574a7;
}

.accent.glyphIcon,
.accent.glyphIcon-large {
    color: #b53f39;
}

.secondary.glyphIcon,
.secondary.glyphIcon-large,
.accountTable .heading-default .secondary.glyphIcon,
.accountTable-verticalAlignTop .heading-default .secondary.glyphIcon,
.accountTable .heading-default .secondary.glyphIcon-large,
.accountTable-verticalAlignTop .heading-default .secondary.glyphIcon-large,
.accountTable-verticalAlignTop .heading-default .secondary.glyphIcon,
.accountTable-verticalAlignTop .heading-default .secondary.glyphIcon-large {
    color: #adbed6;
}

.specialA.glyphIcon,
.specialA.glyphIcon-large {
    color: #ffffff;
}

.glyphIcon {
    font-size: 1em;
}

.glyphIcon-large {
    font-size: 1.25em;
}

.withSwitch .icon-clock-thunder {
    color: #5574a7;
}

.withSwitch .switch {
    display: inline-block;
}

.withSwitch.filter .switch {
    margin-left: 0.3em;
}

.switch {
    border-radius: 3px;
    position: relative;
    top: 0.05em;
    color: #ffffff;
    background: #5574a7;
    border: 1px solid #5574a7;
    box-sizing: content-box;
    margin-left: 0.3em;
    transition: all 0.5s ease-in;
}

.switch>span {
    float: left;
    padding: 0 0.3em;
    text-align: center;
}

.switch>span:before {
    display: inline-block;
    font-size: 1.4em;
    line-height: 0.9;
}

.switch::after {
    content: "";
    display: block;
    clear: both;
}

.switch .switch-on::before {
    content: "";
}

.switch .switch-off::before {
    content: "";
}

.switch .switch-handle {
    border-radius: 3px;
    transition: all 0.5s ease-in;
    position: absolute;
    color: #5574a7;
    background: #ffffff;
}

.switch.on .switch-handle {
    right: 0;
}

.switch.off .switch-handle {
    right: 50%;
}

.switch.off {
    background: #a3a3a3;
    border-color: #a3a3a3;
}

.switch-mmr .switch-handle::before {
    content: "";
}

.flatBtn {
    font-size: 1.5em;
    padding: .3em 1em;
    cursor: pointer;
    border-radius: 1.8em;
    color: #ffffff;
    background-color: #7591c1;
}

.flatBtn:hover {
    background-color: #5574a7;
}

.flatBtn.secondary {
    background-color: #bbbbbb;
    color: #ffffff;
}

.flatBtn.secondary:hover {
    background-color: #a3a3a3;
}

.dropdown,
.dropdown-Date,
.dropdown-flexible {
    position: relative;
    border: 1px solid #cdcdcd;
    border-radius: 3px;
}

.dropdown::after,
.dropdown-Date::after,
.dropdown-flexible::after {
    content: "";
    position: absolute;
    top: .4em;
    right: .3em;
    line-height: 1;
    -ms-transform: scale(1.3);
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
}

.dropdown .selected,
.dropdown-Date .selected,
.dropdown-flexible .selected {
    padding-right: .7em;
    height: inherit;
}

.dropdown .txt,
.dropdown-Date .txt,
.dropdown-flexible .txt {
    display: block;
    padding: 0 0.5em;
}

.dropdown .dropdownPanel,
.dropdown-Date .dropdownPanel,
.dropdown-flexible .dropdownPanel {
    position: absolute;
    z-index: 15;
    box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 0, 0, 0.4);
    width: calc(100% + 2px);
    margin: -1px;
    max-height: 30em;
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 0 0 3px 3px;
    visibility: hidden;
    height: 0;
}

.dropdown .dropdownPanel .content,
.dropdown-Date .dropdownPanel .content,
.dropdown-flexible .dropdownPanel .content {
    margin-left: -1px;
    margin-right: -1px;
    padding: 0.4em 2.5em 0.4em 0.5em;
    border-style: solid;
    border-width: 0 1px;
    border-color: #cdcdcd;
}

.dropdown .dropdownPanel .content:last-child,
.dropdown-Date .dropdownPanel .content:last-child,
.dropdown-flexible .dropdownPanel .content:last-child {
    border-radius: 0 0 3px 3px;
    border-bottom-width: 1px;
}

.dropdownActive.dropdown,
.dropdownActive.dropdown-Date,
.dropdownActive.dropdown-flexible {
    border-radius: 3px 3px 0 0;
}

.dropdownActive.dropdown .dropdownPanel,
.dropdownActive.dropdown-Date .dropdownPanel,
.dropdownActive.dropdown-flexible .dropdownPanel {
    visibility: visible;
    height: auto;
}

.dropdown-Date:hover {
    border-radius: 3px;
}

.dropdown-Date::after {
    content: "";
    top: .45em;
}

.dropdown-Date .selected .txt {
    width: 11em;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.dropdown-flexible {
    padding: 0;
}

.dropdown-flexible .selected {
    margin: 0;
    padding: 0;
}

.dropdown-flexible .selected .txt {
    padding-right: 1.5em;
}

.dropdown-flexible .dropdownPanel {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    overflow-y: auto;
    overflow-x: hidden;
}

.dropdown-flexible .dropdownPanel .content {
    padding-right: 1.5em;
}

.dropdown-flexible .dropdownPanel .content:first-child {
    border-radius: 0 0 3px 3px;
}

.dropdown-styleA .dropdownPanel {
    padding: 0.5em;
}

.dropdown-styleA .dropdownPanel::after {
    content: "";
    display: block;
    clear: both;
}

.dropdown-styleA .dropdownPanel .content {
    float: left;
    width: calc(100% / 14.17);
    padding: 0.5em 0;
    text-align: center;
    font-weight: normal;
}

.dropdown-styleA .dropdownPanel .content:hover,
.dropdown-styleA .dropdownPanel .content.keySelected {
    background: #d5e0f0 !important;
}

.dropdown-styleA .dropdownPanel .content.current {
    background: #5574a7 !important;
    color: #ffffff;
    font-weight: bold;
}

.dropdown-styleA .dropdownPanel .content+.content-title {
    margin-top: 0.5em;
}

.dropdown-styleA .dropdownPanel .content-title {
    clear: both;
    font-weight: bold;
    width: 43em;
    background: #a3a3a3;
    color: #ffffff;
    margin-bottom: 0.5em;
    padding: 0.18em 0;
    border-radius: 0 !important;
}

.dropdown-styleA .dropdownPanel .content-title:hover {
    background: #a3a3a3 !important;
}

.hotKey {
    width: 1.25em;
    height: 1.25em;
    color: #ffffff;
    background: #5475a8;
    margin-right: 0.167em;
    font-size: 1.3em;
    border-radius: 3px;
    vertical-align: middle;
}

.hotKey:hover {
    background: #7590ba;
}

.hotKey::before {
    font-size: 1.25em;
    line-height: 1;
    display: block;
    position: relative;
    top: .04em;
}

.hotKey.accent {
    background: #b53f39;
}

.hotKey.accent:hover {
    background: #ca5d57;
}

.hotKey.cashoutStyle {
    background: #ffd330;
    color: #52332c;
}

.hotKey.cashoutStyle:hover {
    background: #fcc600;
}

.hotKey+.hotKey {
    margin-left: 0.25em;
}

.data {
    text-align: center;
    background: linear-gradient(to bottom, #44679f 0%, #35507b 100%);
    background: -webkit-linear-gradient(bottom, #35507b 0%, #44679f 100%);
    margin-left: 4px;
    padding: 0 4px;
    height: 22px;
    box-sizing: border-box;
    border: 1px solid #44679f;
    font-weight: bold;
    color: #ffffff;
    white-space: nowrap;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.data::before {
    display: inline-block;
    font-size: 14px;
    font-weight: normal;
    line-height: 1;
}

@-moz-document url-prefix() {
    .data::before {
        margin-top: -1px;
    }
}

.data:hover {
    background-image: none;
    background-color: #4c73b1;
}

.data .data-text {
    margin: 0 4px;
}

.filter.dropdown .selected,
.filter.dropdown-Date .selected,
.filter.dropdown-flexible .selected {
    margin-left: -0.5em;
    padding-left: 0.5em;
    min-height: inherit;
}

.filter.dropdown .selected .txt,
.filter.dropdown-Date .selected .txt,
.filter.dropdown-flexible .selected .txt {
    display: block;
    padding-left: 0;
}

.filter.dropdown .dropdownPanel,
.filter.dropdown-Date .dropdownPanel,
.filter.dropdown-flexible .dropdownPanel {
    white-space: nowrap;
    min-width: 100%;
    width: auto;
    background: white;
    border: 1px solid #cdcdcd;
    left: 0;
}

.filter.dropdown .dropdownPanel .content,
.filter.dropdown-Date .dropdownPanel .content,
.filter.dropdown-flexible .dropdownPanel .content {
    border-width: 0;
}

.filter.dropdown .dropdownPanel .content:hover,
.filter.dropdown-Date .dropdownPanel .content:hover,
.filter.dropdown-flexible .dropdownPanel .content:hover,
.filter.dropdown .dropdownPanel .content.keySelected,
.filter.dropdown-Date .dropdownPanel .content.keySelected,
.filter.dropdown-flexible .dropdownPanel .content.keySelected {
    background: #d6d6d6;
}

.filter.dropdown .dropdownPanel .content:first-child,
.filter.dropdown-Date .dropdownPanel .content:first-child,
.filter.dropdown-flexible .dropdownPanel .content:first-child {
    border-radius: 0 3px 0 0;
}

.filter.oddsTableStatus,
.filter.oddsTableStatus-offline,
.filter.oddsTableStatus-connecting {
    width: 2.5em;
    height: 1.5em;
    overflow: hidden;
    position: relative;
}

.filter.oddsTableStatus::before,
.filter.oddsTableStatus-offline::before,
.filter.oddsTableStatus-connecting::before {
    background: url(../../images/v2/connectIcon.png) no-repeat 0 0;
    content: "";
    position: absolute;
    top: -.05em;
    width: 897px;
    height: 22px;
    z-index: 1;
}

.filter {
    float: left;
    border-radius: 3px;
    padding: 0.15em 0.5em;
    color: #545454;
    border: 1px solid #cdcdcd;
    min-height: 1.5em;
    background: #ececec;
    margin: 0;
}

.filter:hover {
    background: white;
}

.filter::before,
.filter div::before {
    font-size: 1em;
    -ms-transform: scale(1.5);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
    line-height: 1;
    float: left;
    padding: .25em .5em;
}

.filter .text-fill {
    background: #ffffff;
    border-radius: 3px;
    padding: 0 0.833em;
    -ms-transform: scale(0.9);
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
    display: inline-block;
    margin: 0 -0.3em 0 -0.2em;
}

.filter+.filter {
    margin-left: 0.25em;
}

.filter.icon-selectLeague::before {
    color: #5574a7;
    -ms-transform: scale(1.4);
    -webkit-transform: scale(1.4);
    transform: scale(1.4);
}

.filter.icon-refresh::before {
    -ms-transform: scale(1.4);
    -webkit-transform: scale(1.4);
    transform: scale(1.4);
}

.filter.icon-refresh.spin::before {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation: rotateScale-large 1.5s linear infinite;
    animation: rotateScale-large 1.5s linear infinite;
}

.filter.block-center {
    width: 100%;
    margin: 0;
    text-align: center;
    box-sizing: border-box;
}

.filter.block-center::before {
    float: none;
    display: inline-block;
    -ms-transform: translate(0, 0) scale(1.5);
    -webkit-transform: translate(0, 0) scale(1.5);
    transform: translate(0, 0) scale(1.5);
}

.oddsTable .filter.block-center,
.accountTable .filter.block-center,
.accountTable-verticalAlignTop .filter.block-center,
.accountTable-verticalAlignTop .filter.block-center {
    border-radius: 0;
    border-width: 0 0 1px;
    float: none;
}

.moreBetType-filter-container .filter.dropdown {
    float: none;
    vertical-align: middle;
    margin: 0 0.5em;
}

.filter.dropdown-flexible.noHover .content:hover {
    background-color: white;
}

.filter.dropdown-flexible.noHover .content.keySelected:hover {
    background: #d6d6d6;
}

.filter.disable {
    color: #7c7c7c;
    background: #cdcdcd;
    color: rgba(84, 84, 84, 0.65);
    background: #cdcdcd;
    cursor: default;
}

.filter.disable.dropdownActive .dropdownPanel,
.filter.disable.dropdownActive.dropdown-flexible .dropdownPanel {
    visibility: hidden;
    height: 0;
}

.filter.disable.icon-selectLeague::before,
.filter.disable .hightlight {
    color: #7591c1;
}

.filter.disable .switch {
    opacity: 0.5;
}

.filter.disable label {
    cursor: default;
}

.filter.disable label .checkbox,
.filter.disable label .sportName::before {
    opacity: .5;
}

.filter.disable label input[type="checkbox"]:checked+.checkbox::before {
    opacity: 0;
}

.filter.disable label input[type="checkbox"]+.checkbox {
    cursor: default;
}

.filter.primary {
    background-color: #5574a7;
    color: #ffffff;
    font-weight: bold;
}

.filter.primary .text-fill {
    color: #5574a7;
}

.filter.secondary {
    background-color: #7c7c7c;
    color: #ffffff;
    font-weight: bold;
}

.filter.secondary .text-fill {
    color: #7c7c7c;
}

.filter.third {
    background-color: #bbbbbb;
    color: #ffffff;
    border-color: #bbbbbb;
}

.filter.fourth {
    background: #5574a7;
    color: #ffd330;
}

.filter.fourth::after,
.filter.fourth.icon-selectLeague {
    color: #ffffff;
}

.filter.fourth.icon-selectLeague::before,
.filter.fourth>[class*="icon-"]::before,
.filter.fourth .hightlight {
    color: #ffd330;
}

.filter.fourth .dropdownPanel {
    color: #545454;
}

.filter.frozen:hover,
.filter.frozen label {
    cursor: default;
    background: #ececec;
}

.filter.frozen .checkbox {
    cursor: default !important;
}

.filter.active {
    color: #ffffff;
    background: #5574a7;
}

.filter.active-live {
    color: #b53f39;
    background: #ffddd2;
}

.filter.cashoutStyle {
    color: #52332c;
    border-color: #ffd330;
    background-color: #ffd330;
}

.filter.withCheckbox .checkbox::before {
    color: #545454;
}

.filter.withCheckbox .sportName {
    padding-left: 1.4rem;
}

.filter.oddsTableStatus::before {
    transform: translateX(-819px);
    left: -.05em;
}

.filter.oddsTableStatus-offline {
    cursor: default;
}

.filter.oddsTableStatus-offline::before {
    transform: translateX(-857px);
    left: -.05em;
}

.filter.oddsTableStatus-offline:hover {
    background: #ececec;
}

.filter.oddsTableStatus-connecting {
    cursor: default;
}

.filter.oddsTableStatus-connecting:hover {
    background: #ececec;
}

.filter.oddsTableStatus-connecting::before {
    left: .15em;
    -webkit-animation: playConnect 4s steps(21) infinite normal;
    animation: playConnect 4s steps(21) infinite normal;
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000;
    -webkit-transform: translateZ(0);
}

@-ms-keyframes playConnect {
    .filter.oddsTableStatus-connecting from {
        transform: translateX(0);
    }
    .filter.oddsTableStatus-connecting to {
        transform: translateX(-819px);
    }
}

@-moz-keyframes playConnect {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-819px);
    }
}

@-webkit-keyframes playConnect {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-819px);
    }
}

@keyframes playConnect {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-819px);
    }
}

.filter .hint-absolute {
    z-index: 39;
    right: -65px;
    min-width: 20em;
    top: 2em;
}

.filter .hint-absolute::before {
    padding: 0;
}

.form.dropdown .selected .txt,
.form.dropdown-Date .selected .txt {
    padding-left: 0;
}

.form {
    background: #ffffff;
    color: #545454;
    border: 1px solid #cdcdcd;
    min-height: 1.5em;
}

.form.dropdown .content {
    background: #ffffff;
}

.form.dropdown .content:hover,
.form.dropdown .content.keySelected {
    background: #dfdfdf;
}

.form.dropdown .dropdownPanel {
    width: 100%;
    border: 1px solid #cdcdcd;
    left: 0;
}

.field-group .form.dropdown {
    width: 100%;
}

.field-group .form.dropdown-Date {
    width: 100%;
}

.form.drag>div {
    padding-right: 1.2em;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.form.drag:hover {
    background-color: #f5eeb8;
    cursor: move;
    border-color: #a5a5a5;
    color: #01122b;
}

.form.drag::after {
    content: "";
    position: absolute;
    top: 0.2em;
    right: 0.4em;
}

.form.btn {
    background: #ffffff;
}

.form.btn::before {
    line-height: 1.7em;
}

.form.input-accent {
    background: #ffccbc;
    background: #ffccbc;
    color: #9c0000;
    border: 1px solid #9c0000;
}

.form.input-inline {
    float: none;
    display: inline-block;
}

.form.disable {
    color: #7c7c7c;
    background: #cdcdcd;
    cursor: default;
}

.search .form {
    cursor: default;
}

input.form {
    cursor: text;
}

.inOddsTable {
    background: #ffffff;
    color: #545454;
    box-sizing: border-box;
    width: 100%;
}

.inOddsTable.dropdown .dropdownPanel {
    width: 100%;
    border: 1px solid #cdcdcd;
}

.inOddsTable.dropdown .content {
    background: #ffffff;
}

.inOddsTable.dropdown .content:hover {
    background: #d6d6d6;
}

.inOddsTable .dropdownPanel {
    left: 0;
}

.message {
    background: #e8eff5;
    color: #545454;
    border: 1px solid #cdcdcd;
    width: 1.8em;
    height: 1.8em;
    border-radius: 3px;
}

.message::before {
    font-size: 1em;
    position: relative;
    top: .4em;
    left: .35em;
    font-weight: bold;
    line-height: 1;
}

.message:hover {
    background: #7c7c7c;
    color: #ffffff;
    border-color: #7c7c7c;
}

.header-collapse .message {
    width: 2.5em;
    color: #ffffff;
    background: transparent;
    border: 0;
    height: 2em;
}

.header-collapse .message::before {
    left: .7em;
    top: .65em;
}

.header-collapse .message:hover {
    background: #4c73b1;
}

.trigger.toggle,
.trigger.toggle-primary,
.trigger.toggle-accent {
    border-right: 1px solid transparent;
}

.icon-open.trigger.toggle::before,
.icon-open.trigger.toggle-primary::before,
.icon-open.trigger.toggle-accent::before {
    content: "";
}

.icon-close.trigger.toggle::before,
.icon-close.trigger.toggle-primary::before,
.icon-close.trigger.toggle-accent::before {
    content: "";
}

.trigger {
    margin: 0;
    padding: 0;
    width: 2em;
    height: auto;
    border-radius: 0;
    color: #7c7c7c;
    -webkit-box-flex: 0 0 auto;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
}

.trigger::before {
    font-size: 2em;
    line-height: 1;
}

.league .trigger {
    margin-left: 0;
    margin-right: 0.25em;
}

.trigger.toggle {
    background: linear-gradient(to bottom, #cdcdcd 0%, #bbbbbb 100%);
    background: -webkit-linear-gradient(bottom, #bbbbbb 0%, #cdcdcd 100%);
    border-right-color: #a3a3a3;
    color: rgba(0, 0, 0, 0.9);
}

.trigger.toggle:hover {
    background: linear-gradient(to bottom, #d5d5d5 0%, #c3c3c3 100%);
    background: -webkit-linear-gradient(bottom, #c3c3c3 0%, #d5d5d5 100%);
}

.trigger.toggle-primary {
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
    border-right-color: #3b5174;
    color: rgba(255, 255, 255, 0.6);
}

.trigger.toggle-primary:hover {
    background: linear-gradient(to bottom, #879fc9 0%, #6582b1 100%);
    background: -webkit-linear-gradient(bottom, #6582b1 0%, #879fc9 100%);
}

.trigger.toggle-accent {
    background: linear-gradient(to bottom, #c44a44 0%, #b53f39 100%);
    background: -webkit-linear-gradient(bottom, #b53f39 0%, #c44a44 100%);
    border-right-color: #7b2b27;
    color: rgba(255, 255, 255, 0.6);
}

.trigger.toggle-accent:hover {
    background: linear-gradient(to bottom, #ca5d57 0%, #c44a44 100%);
    background: -webkit-linear-gradient(bottom, #c44a44 0%, #ca5d57 100%);
}

.trigger.icon-favorite.added {
    text-shadow: 0 0 0.1em black;
}

.oddsTitleWrap>.trigger {
    position: absolute;
    top: 0;
    bottom: 0;
}

.setCol+.setCol .trigger {
    margin-left: -1px;
}

.collapsible>li.active>a::before {
    content: "";
}

.collapsible>li.active .innerContent {
    display: block;
}

.collapsible>li>a {
    position: relative;
    display: block;
    padding: 0.5em 1em 0.5em 2.25em;
    cursor: pointer;
}

.collapsible>li>a label {
    display: inline-block;
}

.collapsible>li .innerContent {
    display: none;
    padding: 0;
}

.collapsible>li .innerContent.textArea {
    padding: 0.20825em 0.833em 0.20825em 2.25em;
}

.collapsible>li .innerContent.listArea {
    padding: 0.833em 0.333em 0.833em 2.25em;
}

.collapsible>li .innerContent.listArea label+label {
    margin-top: 0.5em;
}

.collapsible .text-primary {
    color: #5574a7;
    font-weight: bold;
}

.collapsible .text-secondary {
    font-weight: bold;
}

.showingNow .collapsible {
    background: #ffffff;
}

.streamingLists .collapsible {
    background: #ffffff;
}

.selectLeague .collapsible,
.scoreMap .collapsible {
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

.selectLeague .collapsible {
    height: calc( 100vh - 27em);
}

.scoreMap .collapsible {
    max-height: calc( 100vh - 30em);
}

.collapsible.vertical-line>li {
    position: relative;
    background: rgba(0, 0, 0, 0.04);
}

.collapsible.vertical-line>li:hover,
.collapsible.vertical-line>li.active {
    background: transparent;
}

.collapsible.vertical-line>li>a {
    font-weight: bold;
    color: #5574a7;
}

.collapsible.vertical-line>li>a::before {
    -ms-transform: scale(0.8);
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
    border-radius: 100%;
    color: #ffffff;
    background: #5574a7;
    z-index: 1;
}

.collapsible.vertical-line .innerContent {
    padding: 0 0 0.5em 1.1em;
}

.miniOdds .tabs {
    background-color: #ffffff;
}

.tabs-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.tabs-nav [class*="icon-"] {
    display: inline-block;
    vertical-align: middle;
}

.tabs-nav [class*="icon-"]::before {
    display: inline-block;
    font-size: 1.5em;
    line-height: 1;
    font-weight: normal;
}

.tabs-nav [class*="icon-"]+.text,
.tabs-nav .accountTable [class*="icon-"]+.text-auto,
.accountTable .tabs-nav [class*="icon-"]+.text-auto,
.tabs-nav .accountTable-verticalAlignTop [class*="icon-"]+.text-auto,
.accountTable-verticalAlignTop .tabs-nav [class*="icon-"]+.text-auto {
    padding-left: 0.25em;
    width: calc( 100% - 1.5em - 0.25em - 7px);
    vertical-align: middle;
}

.tabs-nav .text,
.tabs-nav .accountTable .text-auto,
.accountTable .tabs-nav .text-auto,
.tabs-nav .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .tabs-nav .text-auto {
    display: inline-block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.tabs-item {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-width: 0;
    position: relative;
    padding: 0.5em;
    padding-right: 0.25em;
    cursor: pointer;
}

.miniOdds .tabs-item:hover {
    background: #ffffff;
}

.tabs-item.active,
.tabs-item.active:hover {
    cursor: default;
}

.miniOdds .tabs-item.active,
.miniOdds .tabs-item.active:hover {
    background: #ffffff;
}

.is-tabs-theme--primary .tabs-item {
    color: #c6ced8;
    background: #5574a7;
}

.is-tabs-theme--primary .tabs-item:hover {
    color: #ffffff;
    background: #5574a7;
}

.is-tabs-theme--primary .tabs-item.active {
    color: #ffffff;
}

.is-tabs-theme--primary .tabs-item.active::after {
    content: "";
    display: block;
    position: absolute;
    bottom: 1px;
    background: #ffffff;
    width: calc( 100% - 0.5em * 2);
    height: 2px;
}

.is-tabs-theme--darken>.tabs-nav .tabs-item {
    color: #ffffff;
    background: #233d67;
}

.is-tabs-theme--darken>.tabs-nav .tabs-item:hover {
    color: #ffffff;
    background: #29487a;
}

.is-tabs-theme--darken>.tabs-nav .tabs-item.active {
    color: #545454;
    background: #ffffff;
    font-weight: bold;
}

.is-tabs-theme--darken .tabs {
    padding: 0.5em;
}

.is-tabs-theme--top-line .tabs-item {
    padding-top: 0.75em;
    color: #545454;
}

.is-tabs-theme--top-line .tabs-item::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0px;
    width: 100%;
    height: 2px;
    background: #cdcdcd;
}

.is-tabs-theme--top-line .tabs-item:hover {
    color: #5574a7;
}

.is-tabs-theme--top-line .tabs-item:hover::before {
    background: #98abca;
}

.is-tabs-theme--top-line .tabs-item.active,
.is-tabs-theme--top-line .tabs-item.active:hover {
    color: #5574a7;
}

.is-tabs-theme--top-line .tabs-item.active::before,
.is-tabs-theme--top-line .tabs-item.active:hover::before {
    background: #5574a7;
}

.is-tabs-theme--top-line .filterArea {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 0.25em;
}

.is-tabs-theme--top-line .filterArea .dropdown-flexible:first-child {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.tabs-nav-scrollable {
    position: relative;
    overflow: hidden;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
    background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.tabs-nav-scrollable .tabs-item-group {
    position: relative;
    white-space: nowrap;
}

.tabs-nav-scrollable .tabs-item {
    display: inline-block;
    float: none;
    width: 3em;
    height: 3em;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-top-width: 0;
    border-left-width: 0;
}

.tabs-nav-scrollable .tabs-item .sportsIcon {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -1em;
    margin-left: -1em;
}

.tabs-nav-scrollable .tabs-item.active {
    border-bottom-width: 0;
}

.tabs-nav-scrollable .tabs-nav-prev,
.tabs-nav-scrollable .tabs-nav-next {
    position: absolute;
    z-index: 1;
    cursor: pointer;
    display: none;
    width: 1.5em;
    height: 3em;
    line-height: 3em;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-top-width: 0;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    background: linear-gradient(to top, #dfdfdf 0%, whitesmoke 100%);
    background: -webkit-linear-gradient(top, whitesmoke 0%, #dfdfdf 100%);
}

.tabs-nav-scrollable .tabs-nav-prev:hover,
.tabs-nav-scrollable .tabs-nav-next:hover {
    background: linear-gradient(to top, #f9f9f9 0%, white 100%);
    background: -webkit-linear-gradient(top, white 0%, #f9f9f9 100%);
}

.miniOdds .tabs-nav-scrollable .tabs-nav-prev,
.miniOdds .tabs-nav-scrollable .tabs-nav-next {
    background: linear-gradient(to top, #e6e6e6 0%, #ffffff 100%);
    background: -webkit-linear-gradient(top, #ffffff 0%, #e6e6e6 100%);
}

.miniOdds .tabs-nav-scrollable .tabs-nav-prev:hover,
.miniOdds .tabs-nav-scrollable .tabs-nav-next:hover {
    background: #ffffff;
}

.tabs-nav-scrollable .tabs-nav-prev::before,
.tabs-nav-scrollable .tabs-nav-next::before {
    position: relative;
    font-size: 1.5em;
}

.tabs-nav-scrollable .tabs-nav-prev.show,
.tabs-nav-scrollable .tabs-nav-next.show {
    display: block;
}

.tabs-nav-scrollable .tabs-nav-prev {
    left: 0;
}

.tabs-nav-scrollable .tabs-nav-prev::before {
    content: "";
}

.tabs-nav-scrollable .tabs-nav-next {
    right: 0;
}

.tabs-nav-scrollable .tabs-nav-next::before {
    content: "";
}

.betInfo,
.betInfo-live,
.betInfo-closed {
    padding: 0.5em 0.833em;
    position: relative;
    white-space: normal;
}

.betInfo+.betInfo,
.betInfo-live+.betInfo,
.betInfo-closed+.betInfo {
    border-top: 1px solid #cdcdcd;
}

.suspended.betInfo,
.suspended.betInfo-live,
.suspended.betInfo-closed {
    background: #feec6e;
}

.betInfo .icon-close,
.betInfo-live .icon-close,
.betInfo-closed .icon-close {
    position: absolute;
    top: 0.5em;
    right: 0.5em;
}

.betInfo .betType,
.betInfo-live .betType,
.betInfo-closed .betType {
    font-weight: bold;
    color: #5574a7;
}

.betInfo .icon-close+.betType,
.betInfo-live .icon-close+.betType,
.betInfo-closed .icon-close+.betType {
    padding-right: 8px;
}

.betInfo {
    background: #ececec;
}

.betInfo.statusChanged {
    -webkit-animation: oddsChangeColor-betSlip 2.5s linear infinite normal;
    animation: oddsChangeColor-betSlip 2.5s linear infinite normal;
}

.expandAreaLayout .betInfo {
    background: #ececec;
}

.betInfo-live {
    background: #ffddd2;
}

.betInfo-live.statusChanged {
    -webkit-animation: oddsChangeColor-betSlipLive 2.5s linear infinite normal;
    animation: oddsChangeColor-betSlipLive 2.5s linear infinite normal;
}

.betInfo-live .betType {
    color: #b53f39;
}

.betInfo-live .betDetial {
    border-color: #d06f6a;
}

.betInfo-closed {
    background: #dfdfdf;
}

.betInfo-closed div,
.betInfo-closed span {
    text-decoration: line-through;
}

.betInfo-closed .icon-close {
    text-decoration: none;
}

.betInfo-closed .betType,
.betInfo-closed .matchInfo {
    color: rgba(0, 0, 0, 0.3);
}

.betInfo-closed .betDetial {
    border-color: rgba(0, 0, 0, 0.3);
    color: rgba(0, 0, 0, 0.3);
}

.betInfo-closed .betDetial .selectorOdds {
    color: rgba(0, 0, 0, 0.3);
}

.betInfo-closed .betDetial .selectorOdds.accent {
    color: rgba(0, 0, 0, 0.3);
}

.waiting .reject .betInfo,
.void .betInfo,
.accountTable .tableBody .tableRow.void .betInfo,
.accountTable-verticalAlignTop .tableBody .tableRow.void .betInfo,
.accountTable .tableBody .void.tableRow-pointer .betInfo,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .betInfo {
    background: #dfdfdf;
    color: rgba(0, 0, 0, 0.3);
}

.waiting .reject .betType,
.void .betType,
.accountTable .tableBody .tableRow.void .betType,
.accountTable-verticalAlignTop .tableBody .tableRow.void .betType,
.accountTable .tableBody .void.tableRow-pointer .betType,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .betType {
    color: rgba(0, 0, 0, 0.3);
}

.waiting .reject .betDetial,
.void .betDetial,
.accountTable .tableBody .tableRow.void .betDetial,
.accountTable-verticalAlignTop .tableBody .tableRow.void .betDetial,
.accountTable .tableBody .void.tableRow-pointer .betDetial,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .betDetial {
    border-color: rgba(0, 0, 0, 0.3);
    color: rgba(0, 0, 0, 0.3);
}

.waiting .reject .selectorOdds,
.void .selectorOdds,
.accountTable .tableBody .tableRow.void .selectorOdds,
.accountTable-verticalAlignTop .tableBody .tableRow.void .selectorOdds,
.accountTable .tableBody .void.tableRow-pointer .selectorOdds,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .selectorOdds,
.waiting .reject .accent,
.void .accent,
.accountTable .tableBody .tableRow.void .accent,
.accountTable-verticalAlignTop .tableBody .tableRow.void .accent,
.accountTable .tableBody .void.tableRow-pointer .accent,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .accent,
.waiting .reject .stacks,
.void .stacks,
.accountTable .tableBody .tableRow.void .stacks,
.accountTable-verticalAlignTop .tableBody .tableRow.void .stacks,
.accountTable .tableBody .void.tableRow-pointer .stacks,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .stacks,
.waiting .reject .stakes,
.void .stakes,
.accountTable .tableBody .tableRow.void .stakes,
.accountTable-verticalAlignTop .tableBody .tableRow.void .stakes,
.accountTable .tableBody .void.tableRow-pointer .stakes,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .stakes {
    color: inherit !important;
}

.waiting .mainInfo.reject,
.mainInfo.void,
.accountTable .tableBody .mainInfo.tableRow.void,
.accountTable-verticalAlignTop .tableBody .mainInfo.tableRow.void,
.accountTable .tableBody .mainInfo.void.tableRow-pointer,
.accountTable-verticalAlignTop .tableBody .mainInfo.void.tableRow-pointer,
.waiting .reject .mainInfo,
.void .mainInfo,
.accountTable .tableBody .tableRow.void .mainInfo,
.accountTable-verticalAlignTop .tableBody .tableRow.void .mainInfo,
.accountTable .tableBody .void.tableRow-pointer .mainInfo,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .mainInfo {
    text-decoration: line-through;
    color: rgba(0, 0, 0, 0.3);
}

.waiting .reject .matchInfo .homeName,
.void .matchInfo .homeName,
.accountTable .tableBody .tableRow.void .matchInfo .homeName,
.accountTable-verticalAlignTop .tableBody .tableRow.void .matchInfo .homeName,
.accountTable .tableBody .void.tableRow-pointer .matchInfo .homeName,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .matchInfo .homeName,
.waiting .reject .matchInfo .vs,
.void .matchInfo .vs,
.accountTable .tableBody .tableRow.void .matchInfo .vs,
.accountTable-verticalAlignTop .tableBody .tableRow.void .matchInfo .vs,
.accountTable .tableBody .void.tableRow-pointer .matchInfo .vs,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .matchInfo .vs,
.waiting .reject .matchInfo .awayName,
.void .matchInfo .awayName,
.accountTable .tableBody .tableRow.void .matchInfo .awayName,
.accountTable-verticalAlignTop .tableBody .tableRow.void .matchInfo .awayName,
.accountTable .tableBody .void.tableRow-pointer .matchInfo .awayName,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .matchInfo .awayName,
.waiting .reject .matchInfo .leagueName,
.void .matchInfo .leagueName,
.accountTable .tableBody .tableRow.void .matchInfo .leagueName,
.accountTable-verticalAlignTop .tableBody .tableRow.void .matchInfo .leagueName,
.accountTable .tableBody .void.tableRow-pointer .matchInfo .leagueName,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .matchInfo .leagueName {
    text-decoration: line-through;
    color: rgba(0, 0, 0, 0.3);
}

.accountTable .choice .waiting .reject .matchInfo .leagueName,
.waiting .accountTable .choice .reject .matchInfo .leagueName,
.accountTable-verticalAlignTop .choice .waiting .reject .matchInfo .leagueName,
.waiting .accountTable-verticalAlignTop .choice .reject .matchInfo .leagueName,
.accountTable .choice .void .matchInfo .leagueName,
.accountTable-verticalAlignTop .choice .void .matchInfo .leagueName {
    color: rgba(0, 0, 0, 0.3);
}

.waiting .reject .ticketType,
.void .ticketType,
.accountTable .tableBody .tableRow.void .ticketType,
.accountTable-verticalAlignTop .tableBody .tableRow.void .ticketType,
.accountTable .tableBody .void.tableRow-pointer .ticketType,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .ticketType {
    color: #7c7c7c;
}

.waiting .timerArea {
    text-align: right;
    font-weight: bold;
    color: #b53f39;
    margin-bottom: 0.5em;
}

.waiting .betInfo,
.waiting .extraInfo {
    background: #ffddd2;
}

.waiting .betType {
    color: #b53f39;
}

.waiting .betDetial {
    border-color: #b53f39;
}

.waiting .reject .extraInfo {
    background: #dfdfdf;
}

.matchInfo,
.matchInfo-line {
    color: #7c7c7c;
}

.matchInfo .leagueName,
.matchInfo-line .leagueName {
    font-weight: bold;
    color: #7c7c7c;
}

.expandAreaLayout .matchInfo .leagueName,
.expandAreaLayout .matchInfo-line .leagueName,
.choice .matchInfo .leagueName,
.choice .matchInfo-line .leagueName,
.event .matchInfo .leagueName,
.event .matchInfo-line .leagueName {
    padding: 0;
    background: none;
    border-bottom: none;
    color: #7c7c7c;
}

.matchInfo .smallBtn,
.matchInfo-line .smallBtn {
    cursor: default;
}

.matchInfo .smallBtn,
.matchInfo-line .smallBtn,
.matchInfo .smallBtn+.smallBtn,
.matchInfo-line .smallBtn+.smallBtn {
    margin-left: 0.25em;
}

.matchInfo .teamName-pointer,
.matchInfo-line .teamName-pointer {
    cursor: pointer;
}

.matchInfo .homeName,
.matchInfo .awayName,
.matchInfo .vs {
    float: left;
}

.matchInfo .homeName,
.matchInfo .awayName {
    max-width: 42%;
}

.matchInfo .vs {
    max-width: 12%;
    text-align: center;
    margin: 0 0.3em;
}

.matchInfo::after {
    content: "";
    display: block;
    clear: both;
}

.matchInfo-line .homeName,
.matchInfo-line .awayName {
    max-width: 100%;
}

.ticketStatus.running,
.running .ticketStatus {
    background: #5574a7;
}

.ticketStatus.waiting,
.waiting .ticketStatus {
    background: #b53f39;
}

.ticketStatus.void,
.void .ticketStatus,
.waiting .reject .ticketStatus {
    background: rgba(0, 0, 0, 0.28);
}

.ticketStatus {
    color: #ffffff;
    border-radius: 10px;
    padding: 0 0.5em;
    display: inline-block;
}

.ticketStatus {
    position: absolute;
    right: 0.833em;
    bottom: 0.5em;
}

.betList .ticketStatus {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 42%;
}

.otherContent .ticketStatus {
    position: static;
}

.entryInfo {
    background: #ececec;
    padding: 0.5em 0.833em;
    clear: both;
    white-space: normal;
}

.entryInfo>div {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.entryInfo>div .title,
.entryInfo>div .content {
    display: block;
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
}

.entryInfo>div .title {
    color: #7c7c7c;
}

.entryInfo>div .content {
    text-align: right;
    color: #5574a7;
}

.entryInfo>div .content .smallBtn {
    float: right;
    margin-left: 0.25em;
}

.entryInfo .accent {
    color: #b53f39;
}

.extraInfo,
.extraInfo-live,
.extraInfo-closed {
    position: relative;
    padding: 0 0.5em 0.5em;
}

.extraInfo>.content,
.extraInfo-live>.content,
.extraInfo-closed>.content {
    background: #ffffff;
}

.extraInfo>.ticketInfo,
.extraInfo-live>.ticketInfo,
.extraInfo-closed>.ticketInfo {
    margin: -0.5em 0.333em 0.5em;
}

.extraInfo {
    background: #ececec;
}

.extraInfo-live {
    background: #ffddd2;
}

.extraInfo-closed {
    background: #dfdfdf;
}

.extraInfo-closed>.ticketInfo {
    text-decoration: line-through;
}

.cashout-switch {
    position: relative;
    margin-bottom: 0.5em;
    text-transform: uppercase;
}

.cashout-switch .filter {
    display: block;
    float: none;
    padding-right: 3.8em;
}

.cashout-switch .filter .icon-cashout {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cashout-switch-btn {
    position: absolute;
    right: .5em;
    top: .3em;
}

.cashout {
    padding: 0.5em;
}

.cashout-minimize .largeBtn,
.cashout__button .largeBtn {
    display: block;
    position: relative;
    float: none;
    white-space: normal;
}

.cashout-minimize .largeBtn {
    padding-left: 1.8em;
    padding-right: 1.8em;
}

.cashout-minimize .largeBtn:before {
    position: absolute;
    right: 5px;
    top: .2em;
}

.cashout-minimize .largeBtn span {
    color: #ffd330;
}

.cashout__header {
    position: relative;
    margin-bottom: 12px;
}

.cashout__header:after {
    content: "";
    display: table;
    clear: both;
}

.cashout__header .width-full {
    position: absolute;
    left: 0;
    right: 0;
    text-align: right;
    cursor: pointer;
}

.cashout__header .width-full .glyphIcon {
    float: none;
}

.cashout__header .smallBtn {
    z-index: 1;
}

.cashout__message {
    margin-bottom: 4px;
    padding-bottom: 4px;
    border-bottom: 1px solid #cccccc;
}

.cashout__amount .stakeArea,
.cashout__amount .comboList>li .innerContent,
.comboList>li .cashout__amount .innerContent,
.cashout__amount .entryInfo {
    padding: 0;
    background-color: transparent;
}

.cashout__amount .confirmPanel {
    padding-top: 0;
}

.cashout__amount .confirmPanel .confirmInfo .choiseInfo {
    padding-right: 4px;
    background-color: transparent;
}

.cashout__amount .currency {
    color: #7c7c7c;
}

.cashout__button {
    margin-top: 4px;
}

.cashout__button .largeBtn.icon-refresh:before {
    float: none;
    text-align: center;
}

.cashout__slider .stake-range {
    display: table;
    width: 100%;
    margin-top: 5px;
}

.cashout__slider .stake-range .stake-min,
.cashout__slider .stake-range .stake-all {
    position: relative;
    display: table-cell;
}

.cashout__slider .stake-range .stake-all {
    text-align: right;
}

.cashout .hint {
    margin-top: 6px;
}

.rangeslider,
.rangeslider__fill {
    display: block;
    border-radius: 2px;
}

.rangeslider {
    background: #cccccc;
    position: relative;
}

.rangeslider--horizontal {
    height: 4px;
    width: 100%;
}

.rangeslider--disabled {
    filter: progid: DXImageTransform.Microsoft.Alpha(Opacity=40);
    opacity: 0.4;
}

.rangeslider__fill {
    background: #7591c1;
    position: absolute;
}

.rangeslider--horizontal .rangeslider__fill {
    top: 0;
    height: 100%;
}

.rangeslider__handle {
    background: white;
    cursor: pointer;
    display: inline-block;
    top: 50%;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    position: absolute;
    background-color: #fff;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.6);
    border-radius: 50%;
}

.rangeslider--horizontal .rangeslider__handle {
    touch-action: pan-y;
    -ms-touch-action: pan-y;
}

.bonus__heading {
    padding: 0.5em;
    color: black;
}

.bonus__heading .glyphIcon {
    float: right;
}

.bonus__list {
    padding: 0.5em;
    border-top: 1px solid #cccccc;
    color: #7c7c7c;
}

.collapse .bonus__content {
    display: none;
}

fieldset {
    max-width: 60em;
    margin: 0 auto;
    padding: 2em 1em;
}

fieldset li {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    padding: 1em;
    position: relative;
    padding-right: 25%;
}

.openAccount fieldset li {
    padding-right: 0;
}

fieldset label,
fieldset label.primary {
    margin-right: 0.5em;
    margin-top: .1em;
    padding-top: .1em;
    padding-left: 1.6em;
}

fieldset label+label,
fieldset label.primary+label,
fieldset label+label.primary {
    margin-left: 1em;
}

fieldset .firstName,
fieldset .secondName {
    padding-right: 1em;
    padding-top: 0.2em;
    text-align: right;
    width: 50%;
}

.openAccount fieldset .firstName,
.openAccount fieldset .secondName {
    width: 100%;
    text-align: left;
    margin-bottom: 0.5em;
}

fieldset .firstName {
    font-weight: bold;
}

fieldset .field-group,
fieldset .formInput,
fieldset .nickname {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

fieldset .field-group.block {
    -webkit-box-flex: 1 0 100%;
    -webkit-flex: 1 0 100%;
    -ms-flex: 1 0 100%;
    flex: 1 0 100%;
    margin-top: 0.5em;
}

fieldset .field-group.block.subInput {
    display: none;
}

fieldset .field-group.block.showElement {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
}

fieldset .field-group.inline {
    padding-left: calc(50% + 1em);
    margin-top: 0.5em;
}

fieldset .field-group.inline.flex {
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
}

fieldset .field-group.inline p {
    width: 100%;
    word-wrap: break-word;
    word-break: normal;
}

fieldset .field-group .fixed {
    margin-left: 0.3em;
}

fieldset .field-group .smallBtn {
    -ms-transform: translateY(0.25em);
    -webkit-transform: translateY(0.25em);
    transform: translateY(0.25em);
}

fieldset .field-text {
    margin: 0 0.5em;
}

fieldset .formInput {
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
}

fieldset .formInput input {
    width: 100%;
    float: none;
    box-sizing: border-box;
}

fieldset .formInput>div {
    text-align: left;
    width: 100%;
}

.openAccount .dropdown+.formInput,
fieldset .formInput+.formInput {
    margin-left: 0.3em;
}

.subInput .formInput {
    margin-right: 1.6em;
}

fieldset .nickname {
    padding-top: 0.2em;
}

fieldset .nickname-setting {
    width: 40%;
    margin: 0 auto;
}

fieldset .nickname-setting .formInput {
    position: relative;
    margin-bottom: .5em;
}

fieldset .nickname-setting .formInput.check .icon-tick {
    left: 101%;
    top: -.9em;
}

fieldset .nickname-setting .formInput input {
    padding: 4px;
}

fieldset .textContent {
    font-weight: bold;
    padding-bottom: 1.5em;
    margin-bottom: 1.5em;
    border-bottom: 1px solid #cdcdcd;
}

.numberList .textContent {
    margin-left: 1em;
    margin-right: 1em;
}

fieldset .required::after {
    content: "*";
    color: #b53f39;
    margin-left: .5em;
    float: right;
}

.openAccount fieldset .required::after {
    margin-left: 0;
    margin-right: .3em;
    float: left;
    margin-top: -.3em;
}

fieldset .form.dropdown {
    float: none;
    margin-left: 0;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

fieldset .form.dropdown .selected {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 11em;
}

fieldset .primary {
    color: #5574a7;
}

fieldset .height-3x {
    min-height: 4em;
}

fieldset .height-3x .form {
    height: 1.9em;
}

.warn,
.singlePage .warn {
    position: relative;
}

.warn input,
.singlePage .warn input {
    border: 1px solid #ff0000;
}

.warn .remark,
.singlePage .warn .remark {
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    top: 16px;
    left: 0;
    display: block;
    background-color: #ff7e7e;
    border: 1px solid #ff0000;
    color: #fff;
    text-align: center;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    padding: 3px;
    box-shadow: 1px 1px 1px 0px rgba(0, 0, 0, 0.2);
}

.check .icon-tick,
.singlePage .check .icon-tick {
    display: block;
    color: #00cc17;
    font-size: 2em;
    position: absolute;
    left: 76%;
    min-width: 100px;
    top: 0;
}

.check .icon-tick span,
.singlePage .check .icon-tick span {
    font-size: .6em;
    font-weight: bold;
}

.numberList {
    max-width: none;
    padding-bottom: 1em;
}

.numberList ul {
    padding: 0 0.5em;
    counter-reset: section;
    -webkit-column-count: 4;
    -moz-column-count: 4;
    column-count: 4;
    -webkit-column-gap: 1em;
    -moz-column-gap: 1em;
    column-gap: 1em;
}

.numberList li {
    padding: 0;
    -webkit-column-break-inside: avoid-column;
    page-break-inside: avoid-column;
    break-inside: avoid-column;
    overflow: hidden;
    min-height: 2.5em;
}

.numberList li:not(.active)::before {
    counter-increment: section;
    content: counter(section) ".";
    font-family: "Arial", "Tahoma", "pmingliu", "新細明體";
    width: 1.5em;
    display: inline-block;
    position: relative;
    top: 0.2em;
    text-align: right;
    margin-right: .3em;
}

.numberList li::after {
    display: none;
}

.numberList li>div {
    float: none;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden;
    margin-bottom: 0.5em;
}

.numberList li.active .drag {
    background-color: #f5eeb8;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.slides-container {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.slides-control {
    position: relative;
    margin: 0 auto;
    overflow: hidden;
}

.header-news .slides-control {
    width: 204px;
    height: 260px;
}

.slides-control-item {
    position: absolute;
    width: inherit;
    height: inherit;
}

.slides-control-item a {
    display: block;
}

.slides-btn-prev,
.slides-btn-next {
    transition: all 0.2s ease-in;
    position: absolute;
    top: 50%;
    z-index: 1;
    margin-top: -1em;
    background: #7c7c7c;
    opacity: 0;
    visibility: hidden;
    border-radius: 100%;
    display: inline-block;
    color: #ffffff;
    cursor: pointer;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.slides-btn-prev::before,
.slides-btn-next::before {
    display: inline-block;
    font-size: 2em;
    margin: 0em;
    width: 1em;
    height: 1em;
    line-height: 1;
}

.slides-btn-prev.active,
.slides-btn-prev:hover,
.slides-btn-next.active,
.slides-btn-next:hover {
    background: #7591c1;
}

.slides:hover .slides-btn-prev,
.slides:hover .slides-btn-next {
    opacity: 0.6;
    visibility: visible;
}

.slides:hover .slides-btn-prev:hover,
.slides:hover .slides-btn-next:hover {
    opacity: 1;
}

.promo .slides-btn-prev,
.promo .slides-btn-next {
    box-shadow: none;
}

.slides-btn-prev {
    left: 0.25em;
}

.slides-btn-prev::before {
    content: "";
}

.slides-btn-next {
    right: 0.25em;
}

.slides-btn-next::before {
    content: "";
}

.slides-pagination {
    width: 100%;
    padding-top: 0.5em;
    text-align: center;
}

.slides-pagination-item {
    transition: all 0.2s ease-in;
    border-radius: 100%;
    cursor: pointer;
    display: inline-block;
    margin: 0 0.25em;
    width: 0.75em;
    height: 0.75em;
    background: #bbbbbb;
}

.slides-pagination-item.active,
.slides-pagination-item:hover {
    background: #7591c1;
}

.is-slides-btn--white .slides-btn-prev,
.is-slides-btn--white .slides-btn-next {
    color: #7c7c7c;
    background: #ffffff;
}

.panelItem {
    width: 640px;
    margin: auto;
}

.panelItem>img {
    width: 100%;
    height: auto;
}

.panelItem+.panelItem {
    margin-top: 4em;
}

.panelItem-text-wrap {
    width: 480px;
}

.panelItem-text-wrap::after {
    content: "";
    display: block;
    clear: both;
}

.panelItem-text-wrap img {
    float: left;
    margin: 0 2em 2em 0;
}

.panelItem-text-wrap ol,
.panelItem-text-wrap ul {
    margin-left: 45%;
}

.articleFormat.rules>p:first-child {
    margin-top: 0;
}

.articleFormat.rules h1,
.articleFormat.rules h2,
.articleFormat.rules h3,
.articleFormat.rules h4,
.articleFormat.rules h5,
.articleFormat.rules h6,
.articleFormat.rules .h7,
.articleFormat.rules p {
    margin: 1.5em 0;
}

.articleFormat.rules h1 {
    font-size: 2em;
    font-weight: bold;
}

.articleFormat.rules h2 {
    font-size: 1.3em;
    font-weight: bold;
    margin-top: 2em;
}

.articleFormat.rules h1+h2 {
    margin-top: -1em;
}

.articleFormat.rules h2+h3 {
    margin-top: -1em;
}

.articleFormat.rules h3+h3 {
    margin-top: 2em;
}

.articleFormat.rules ol {
    padding-left: 1em;
}

.articleFormat.rules ol li+li {
    padding-top: 1em;
}

.articleFormat.rules .smallBtn {
    float: none;
}

.articleFormat.rules .smallBtn.icon-help {
    cursor: default;
    background: #5574a7;
}

.articleFormat.rules h3,
.articleFormat.rules h4,
.articleFormat.rules h5,
.articleFormat.rules h6,
.articleFormat.rules .h7 {
    font-size: 100%;
    font-weight: normal;
    position: relative;
}

.articleFormat.rules h3>span,
.articleFormat.rules h4>span,
.articleFormat.rules h5>span,
.articleFormat.rules h6>span,
.articleFormat.rules .h7>span {
    padding-left: 6.5em;
    display: block;
}

.articleFormat.rules h3>strong,
.articleFormat.rules h4>strong,
.articleFormat.rules h5>strong,
.articleFormat.rules h6>strong,
.articleFormat.rules .h7>strong {
    width: 6.5em;
    position: absolute;
}

.articleFormat.rules>div {
    padding-left: 6.5em;
}

.articleFormat.rules>div+div {
    padding-top: 1em;
}

.articleFormat.rules h3.style1,
.articleFormat.rules h4.style1,
.articleFormat.rules h5.style1,
.articleFormat.rules h4.style3,
.articleFormat.rules h5.style3 {
    padding-left: 6.5em;
}

.articleFormat.rules h3.style1 strong,
.articleFormat.rules h3.style1 span,
.articleFormat.rules h4.style1 strong,
.articleFormat.rules h4.style1 span,
.articleFormat.rules h5.style1 strong,
.articleFormat.rules h5.style1 span,
.articleFormat.rules h4.style3 strong,
.articleFormat.rules h4.style3 span,
.articleFormat.rules h5.style3 strong,
.articleFormat.rules h5.style3 span {
    display: inline;
    width: auto;
    position: static;
    padding-left: 0;
}

.articleFormat.rules .oddsTable {
    padding-left: 0;
}

.articleFormat.rules .oddsTable+span.style2 {
    display: inline;
    padding-left: 0;
}

.articleFormat.rules table th {
    color: #ffffff;
    background: #5574a7;
}

.articleFormat.rules .button {
    visibility: hidden;
}

.sticker {
    list-style: none;
}

.sticker::after {
    content: "";
    display: block;
    clear: both;
}

.sticker li {
    list-style: none;
    width: 32%;
    float: left;
    border-radius: 3px;
    background-color: #e4e4e4;
    margin-right: 2%;
    margin-bottom: 0.5em;
}

.sticker li:nth-child(3n) {
    margin-right: 0;
}

.sticker li>div {
    float: left;
    padding: 0.3em 0.5em;
}

.sticker li>div:nth-child(2) {
    padding-left: 1.1em;
}

.sticker li .no {
    border-radius: 3px 0 0 3px;
    background-color: #5574a7;
    color: #ffffff;
    position: relative;
    width: 25px;
    text-align: center;
}

.sticker li .no::before {
    content: "";
    position: absolute;
    border-width: 0.5em;
    border-style: solid;
    top: calc(100%/2 - 0.5em);
    right: -.8em;
    border-color: transparent transparent transparent #5574a7;
}

.selectorName,
.selectorScore,
.selectorOther,
.selectorOdds,
.comboName {
    margin-right: 0.25em;
}

.sidebar-first .wrapper {
    background: #e8eff5;
}

.widgetPanel {
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    border-radius: 3px 3px 0 0;
    background: #c6ced8;
    position: relative;
    margin-bottom: 0.5em;
}

.selected .widgetPanel {
    box-shadow: none;
}

.widgetPanel .icon-widgetCollapse {
    display: none;
    width: 3.333em;
    height: 3.333em;
    position: absolute;
    left: 0;
    color: #5574a7;
    background: #01122b;
}

.widgetPanel .icon-widgetCollapse::before {
    font-size: 3em;
    position: relative;
    top: .1em;
    left: .1em;
}

@media screen and (min-width: 0\0) {
    .widgetPanel .icon-widgetCollapse::before {
        font-size: 0.6em;
    }
}

.sidebar-first .widgetPanel .icon-widgetCollapse {
    border-radius: 3px 0 0 3px;
}

.sidebar-second .widgetPanel .icon-widgetCollapse {
    border-radius: 0 3px 3px 0;
}

.widgetPanel::after {
    content: "";
    display: block;
    clear: both;
}

.widgetPanel .btnArea {
    text-align: center;
}

.widgetPanel .bottomArea {
    border-top: 2px solid #5574a7;
}

.widgetPanel .bottomArea.flexible,
.widgetPanel .bottomArea.flexible-open,
.widgetPanel .bottomArea.flexible-close {
    cursor: pointer;
    position: relative;
}

.widgetPanel .bottomArea.flexible::before,
.widgetPanel .bottomArea.flexible-open::before,
.widgetPanel .bottomArea.flexible-close::before {
    position: absolute;
    left: 45%;
}

.widgetPanel .bottomArea.flexible-open::before {
    content: "V";
}

.widgetPanel .bottomArea.flexible-close::before {
    content: "^";
}

.widgetPanel.collapse {
    border-radius: 3px;
}

.widgetPanel.collapse .bottomArea {
    display: none;
}

.widgetPanel.fixed-viewport-bottom {
    width: 224px;
    margin-bottom: 0;
    z-index: 36;
}

.widgetPanel.fixed-viewport-bottom .bottomArea {
    display: none;
}

.widgetPanel.betList .btnArea {
    padding-top: 0.5em;
}

.widgetPanel.betList .btnArea [class*="Btn"] {
    float: none;
}

.widgetPanel.sportsMenu .fixed .category {
    max-height: 355px;
}

.widgetPanel.bonus {
    border-radius: 3px;
    cursor: pointer;
}

.widgetPanel.bonus .heading {
    display: none;
}

.widgetPanel.promotionClient {
    cursor: pointer;
}

.widgetPanel.promotionClient .promotionClient-img {
    border-radius: 3px;
}

.widgetPanel.promotionClient:hover {
    -webkit-filter: brightness(1.1);
    filter: brightness(1.1);
}

.mini .widgetArea .widgetPanel.betList .icon-widgetCollapse {
    top: 3.333em;
}

.mini .widgetArea .widgetPanel.betList.active .contentArea {
    top: 6.1em;
}

.mini .widgetArea::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 3px;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
}

.sidebar-first.mini .widgetArea::after {
    height: .15em;
}

.mini .widgetArea .widgetPanel {
    box-shadow: none;
    background-color: #7591c1;
    margin-bottom: 0;
    border-radius: 0;
    z-index: 20;
}

.mini .widgetArea .widgetPanel+.widgetPanel {
    margin-top: 0;
}

.mini .widgetArea .widgetPanel.fixed-viewport-bottom {
    width: 3.333em;
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
}

.mini .widgetArea .widgetPanel.active .icon-widgetCollapse {
    display: block;
}

.mini .widgetArea .widgetPanel:first-of-type,
.mini .widgetArea .widgetPanel.fixed-viewport-bottom {
    border-radius: 3px 3px 0 0;
}

.sidebar-second.mini .widgetPanel:nth-last-of-type(2) {
    border-radius: 0 0 3px 3px;
}

.mini .widgetArea .widgetPanel .bottomArea {
    display: none;
}

.mini .widgetArea .widgetPanel.personalAccount {
    background: #ffffff;
    border-radius: 3px 3px 0 0;
}

.mini .widgetArea .widgetPanel.personalAccount.active .icon-widgetCollapse {
    color: #ffffff;
    background: #adadad;
}

.mini .widgetArea .widgetPanel.favorite .icon-widgetCollapse {
    display: none;
}

.mini .widgetArea .widgetPanel.sportsMenu {
    z-index: 2;
    margin-bottom: 0.5em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    border-radius: 3px;
}

.mini .widgetArea .widgetPanel.sportsMenu .icon-widgetCollapse {
    display: none;
}

.mini .widgetArea .widgetPanel.sportsMenu .contentArea {
    display: block;
    position: relative;
    padding: 3.333em 0 0;
    width: 3.333em;
    left: 0;
    top: 0;
}

.mini .widgetArea .widgetPanel.sportsMenu .contentArea.fixed .category {
    max-height: none;
}

.mini .widgetArea .widgetPanel.sportsMenu.active .contentArea {
    box-shadow: none;
}

.mini .widgetArea .widgetPanel.sportsMenu .bottomArea {
    display: block;
}

.mini .widgetArea .widgetPanel.sportsMenu.collapse .contentArea {
    display: none;
}

.mini .widgetArea .widgetPanel.sportsMenu.collapse .bottomArea {
    display: none;
}

.mini .widgetArea .widgetPanel.sportsMenu.collapse.special {
    background-color: #5574a7;
}

.mini .widgetArea .widgetPanel.sportsMenu.special {
    background: #01122b;
}

.mini .widgetArea .widgetPanel.sportsMenu.special .contentArea {
    background-color: #dbdbdb;
}

.mini .widgetArea .widgetPanel.sportsMenu.special .contentArea .nav-widgetPanel {
    background: #ffffff;
}

.mini .widgetArea .widgetPanel.sportsMenu.special .contentArea .nav-widgetPanel .item {
    color: #a1a1a1;
}

.mini .widgetArea .widgetPanel.sportsMenu.special .contentArea .nav-widgetPanel .item.active,
.mini .widgetArea .widgetPanel.sportsMenu.special .contentArea .nav-widgetPanel .item:hover {
    color: #01122b;
}

.mini .widgetArea .widgetPanel.sportsMenu.special .contentArea .nav-widgetPanel::before {
    background-color: #dbdbdb;
    color: #01122b;
}

.mini .widgetArea .widgetPanel.sportsMenu.special .contentArea .nav-widgetPanel::after {
    border-color: transparent transparent transparent #dbdbdb;
}

.mini.sidebar-first .widgePanelGroup {
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    width: 3.333em;
}

.mini.sidebar-second .widgetPanel.active .icon-widgetCollapse::before {
    content: "";
}

.mini.sidebar-second .widgetPanel.active .contentArea {
    left: auto;
    right: 3.333em;
}

.mini .category {
    background: whitesmoke;
}

.scroll-content .widgePanelGroup {
    pointer-events: none;
}

.scroll-content .widgePanelGroup>div {
    pointer-events: auto;
}

.ticket .betInfoSub label .countdown {
    margin-left: 0.25em;
}

.ticket .betInfoSub label+[class*="Btn"] {
    float: right;
    position: relative;
    top: -17px;
}

.ticket .btnArea {
    padding-top: 0.5em;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    align-items: flex-start;
}

.ticket .btnArea .largeBtn {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center;
}

.ticket .btnArea .largeBtn.focus {
    margin: 0 0.25em 0.25em;
}

.ticket .btnArea>.specialD {
    -webkit-box-flex: 1 1 100%;
    -webkit-flex: 1 1 100%;
    -ms-flex: 1 1 100%;
    flex: 1 1 100%;
    margin-bottom: .5em;
}

.ticket .btnArea>.specialD+.largeBtn {
    margin-left: 0;
}

.betDetial {
    border-left: 2px solid #879dc2;
    padding: 0 0 0 0.5em;
    margin: 0 0 0 0.5em;
}

.betDetial .photo {
    vertical-align: middle;
    display: inline-block;
}

.betDetial .photo img {
    margin-right: 2px;
}

.betDetial .photo+.name {
    display: inline-block;
}

.betDetial .primary {
    color: #2556B3;
}

.betDetial .blue {
    color: #2556B3;
}

.betDetial .red {
    color: #b53f39;
}

.betDetial .accent {
    color: #b53f39;
}

.betDetial .hightlight {
    font-weight: bold;
}

.mainInfo .betDetial .name {
    position: relative;
    padding-right: 1.5em;
}

.mainInfo .betDetial .name .icon-scoreMap {
    position: absolute;
    right: 0;
}

.otherDetail .description {
    display: block;
}

.otherDetail .description .icon-info {
    float: none;
    vertical-align: middle;
}

.selectorOther {
    position: relative;
    top: -0.1em;
}

.selectorOdds {
    font-weight: bold;
    color: #01122b;
}

.selectorScore {
    color: #7c7c7c;
}

.comboName {
    display: inline;
}

.stacks {
    font-weight: bold;
    color: black;
    float: right;
}

.comboItem .stacks {
    font-weight: normal;
    color: #5574a7;
}

.ticketInfo {
    color: #7c7c7c;
}

.ticketType {
    color: black;
    font-weight: bold;
    min-height: 1.5em;
}

.betInfoSub {
    padding: 0.833em;
}

.betInfoSub label+label {
    margin-top: 0.25em;
}

.stakeArea,
.comboList>li .innerContent {
    background: #dfe7f3;
    padding: 0.833em;
    color: #5574a7;
}

.quickBetPanel .stakeArea,
.quickBetPanel .comboList>li .innerContent,
.comboList>li .quickBetPanel .innerContent {
    background: transparent;
    padding: 0 0.8em 0.5em 0;
    float: left;
    width: 55.5%;
}

.stakeArea .entry,
.comboList>li .innerContent .entry {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stakeArea .entry::after,
.comboList>li .innerContent .entry::after {
    content: "";
    display: block;
    clear: both;
}

.stakeArea .entry .currency,
.comboList>li .innerContent .entry .currency {
    display: block;
    -webkit-box-flex: 1 1 32%;
    -webkit-flex: 1 1 32%;
    -ms-flex: 1 1 32%;
    flex: 1 1 32%;
}

.stakeArea .entry .content,
.comboList>li .innerContent .entry .content {
    display: block;
    -webkit-box-flex: 1 1 68%;
    -webkit-flex: 1 1 68%;
    -ms-flex: 1 1 68%;
    flex: 1 1 68%;
    position: relative;
    text-align: right;
}

.stakeArea .entry .content input,
.comboList>li .innerContent .entry .content input {
    width: 80%;
    text-align: right;
    padding: 0.18em 2em 0.15em 0.18em;
    border: 1px solid #7591c1;
    color: #5574a7;
    font-weight: bold;
    border-radius: 3px;
}

@media screen and (min-width: 0\0) {
    .stakeArea .entry .content input,
    .comboList>li .innerContent .entry .content input {
        min-height: 1.6em;
    }
}

.quickBetPanel .stakeArea .entry .content,
.quickBetPanel .comboList>li .innerContent .entry .content,
.comboList>li .quickBetPanel .innerContent .entry .content {
    width: 100%;
}

.stakeArea .entry .content .smallBtn,
.comboList>li .innerContent .entry .content .smallBtn {
    position: absolute;
    top: .3em;
    right: .3em;
}

.quickBetPanel .stakeArea .entry .content .smallBtn,
.quickBetPanel .comboList>li .innerContent .entry .content .smallBtn,
.comboList>li .quickBetPanel .innerContent .entry .content .smallBtn {
    right: -.1em;
    top: .35em;
}

@media screen and (min-width: 0\0) {
    .quickBetPanel .stakeArea .entry .content .smallBtn,
    .quickBetPanel .comboList>li .innerContent .entry .content .smallBtn,
    .comboList>li .quickBetPanel .innerContent .entry .content .smallBtn {
        right: .1em;
    }
}

.stakeArea .entry .content.count input,
.comboList>li .innerContent .entry .content.count input {
    width: 3em;
    text-align: center;
    padding: 0.18em 0.18em 0.15em;
    vertical-align: bottom;
    margin-left: -0.833em;
    margin-right: -0.5em;
}

.stakeArea .entry .entryInfoMini,
.comboList>li .innerContent .entry .entryInfoMini {
    text-align: right;
}

.stakeArea .entrySub,
.comboList>li .innerContent .entrySub {
    margin-top: 0.5em;
}

.stakeArea .numericKeypad,
.comboList>li .innerContent .numericKeypad {
    margin-top: 0.833em;
}

.btnNote .accent {
    font-weight: bold;
    color: #b53f39;
}

.keypad:nth-child(3n) {
    margin-right: 0;
}

.titleGroup {
    padding-bottom: 0.5em;
}

.titleGroup .title {
    font-weight: bold;
}

.titleGroup .smallBtn {
    float: right;
    margin-left: .25em;
    position: relative;
    top: 0.15em;
}

.comboList {
    margin-bottom: 0.5em;
}

.comboList>li:hover {
    background: #ececec;
}

.comboList>li.active {
    background: #ececec;
    padding-bottom: 0.5em;
}

.comboList>li.active.hasStake {
    background: #dfe7f3;
}

.comboList>li.hasStake a {
    color: #5574a7;
}

.comboList>li a .selectorOther {
    margin-right: 0.25em;
}

.comboList>li a .selectorOdds {
    font-size: 100% !important;
    font-weight: bold;
    top: inherit !important;
    -ms-transform: translate(0, 0) scale(1);
    -webkit-transform: translate(0, 0) scale(1);
    transform: translate(0, 0) scale(1);
}

.comboList>li a:before {
    left: 0.4em;
    top: 0.3em;
}

.comboList>li .innerContent {
    background: transparent !important;
}

.comboList>li .innerContent .entryInfoMini {
    text-align: right;
}

.ticketListGroup+.ticketListGroup {
    margin-top: 0.5em;
}

.ticketListGroup .mainInfoGroup {
    display: none;
}

.ticketListGroup.active .mainInfoGroup {
    display: block;
}

.ticketListGroup.active .smallBtn.primary {
    background: #7591c1;
    color: #ffffff;
}

.ticketListGroup.active .icon-arrow-down::before {
    content: "";
}

.ticketListGroup.active .mainInfo {
    margin-bottom: 1em;
}

.ticketListGroup.active .mainInfo::after {
    content: "";
    display: block;
    width: 110.2%;
    height: 0.01em;
    border-bottom: 1px solid #cdcdcd;
    position: absolute;
    left: -0.8em;
    bottom: -0.5em;
}

.ticketListGroup .mainSection {
    color: black;
}

.ticketListGroup .mainInfo {
    position: relative;
}

.ticketListGroup .mainInfo .leagueName {
    color: #7c7c7c;
}

.confirmPanel {
    padding-top: 0.5em;
    width: 100%;
}

.confirmPanel .betInfo {
    margin-bottom: 0.5em;
}

.confirmPanel .confirmInfo .choiseInfo {
    background-color: #dfe7f3;
}

.confirmPanel .confirmInfo .confirmMsg {
    text-align: center;
    font-weight: bold;
    font-size: 1.2em;
    color: #323232;
}

.confirmPanel .confirmInfo .confirmMsg.icon-confirm::before {
    color: #5dad00;
    font-size: 1.8em;
    position: relative;
    top: 3px;
    margin-right: 8px;
}

.confirmPanel .confirmInfo .confirmMsg .confirmText {
    max-width: 11em;
    text-align: left;
    display: inline-block;
    line-height: 1.5em;
    vertical-align: top;
    margin: 0.6em 0;
    white-space: pre-wrap;
}

.cashout__amount .cashoutCheck {
    position: relative;
    padding-left: 2.3em;
}

.cashout__amount .cashoutCheck.icon-confirm::before {
    color: #5dad00;
    font-size: 1.8em;
    position: absolute;
    top: 0.2em;
    left: 0;
}

.widgetPanel .mainSection {
    background: #ffffff;
    color: black;
    padding: 0.5em;
    border-radius: inherit;
}

.mini .widgetPanel.personalAccount .mainSection {
    padding-top: 0;
}

.mini .widgetPanel .contentArea {
    display: none;
}

.widgetPanel.collapse .contentArea {
    display: none;
}

.mini .widgetPanel.active .contentArea {
    display: block;
    position: absolute;
    top: 2.8em;
    left: 3.333em;
    width: 224px;
    box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 0, 0, 0.4);
    z-index: 23;
    border-radius: 0 0 3px 3px;
}

.mini .widgetPanel.active.fixed-viewport-bottom .contentArea {
    bottom: 0;
    top: auto;
    border-radius: 0;
}

.widgetPanel.personalAccount .contentArea {
    display: none;
    position: absolute;
    top: 2.4em;
    box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 0, 0, 0.4);
    z-index: 23;
    width: 25em;
    border-radius: 0 3px 3px 3px;
}

.widgetPanel.personalAccount.openContent .contentArea {
    display: block;
}

.mini .widgetPanel.personalAccount.active .contentArea {
    padding: 0;
    width: 25em;
}

.mini .widgetPanel.sportsMenu .contentArea {
    background: #4c6896;
}

.widgetPanel.miniCasino .contentArea {
    height: 225px;
}

.widgetPanel.miniCasino.miniCasino-lobby .contentArea {
    height: auto;
}

.widgetPanel.miniCasino.miniCasino-game .contentArea {
    height: auto;
}

.mini .widgetPanel.miniCasino.active .contentArea {
    height: calc(225px + 0.3em);
}

.mini .widgetPanel.miniCasino.miniCasino-lobby.active .contentArea {
    height: auto;
}

.mini .widgetPanel.miniCasino.miniCasino-game.active .contentArea {
    height: auto;
}

.widgetPanel.miniCasino-game .icon-close {
    position: absolute;
    right: 2.4em;
    z-index: 1;
    border-right: 1px solid #526fa0;
    border-left: 1px solid #526fa0;
    border-radius: 0;
    padding: 0.8em 0.55em;
}

.widgetPanel.miniCasino-game .icon-close:before {
    margin-top: -0.1em;
}

.widgetPanel.miniCasino-game .icon-close:hover {
    color: #ffffff;
    background: linear-gradient(to bottom, #879fc9 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #879fc9 100%);
}

.mini .widgetPanel.miniCasino-game .icon-close {
    right: -220px;
    top: -280px;
    z-index: 99;
    border: none;
}

.mini .widgetPanel.miniCasino-game .icon-close:hover {
    background: transparent;
}

.nav-widgetPanel {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    color: #c6ced8;
    background: #5574a7;
    cursor: pointer;
    clear: both;
}

.nav-widgetPanel::before,
.nav-widgetPanel::after {
    position: absolute;
    visibility: hidden;
    left: -1000em;
    top: 0;
}

.nav-widgetPanel::before {
    font-weight: normal;
    background: #4c6896;
    color: #ffffff;
    font-size: 1em;
    padding: .1em .35em .05em;
    -ms-transform: translate(-0.9em, 0.8em) scale(1.9);
    -webkit-transform: translate(-0.9em, 0.8em) scale(1.9);
    transform: translate(-0.9em, 0.8em) scale(1.9);
}

.nav-widgetPanel::after {
    content: "";
    border-style: solid;
    border-width: 1.7em 0 1.7em .5em;
    border-color: transparent transparent transparent #4c6896;
}

.nav-widgetPanel .item {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-width: 0;
    position: relative;
}

.nav-widgetPanel .item::before {
    float: left;
    position: relative;
    top: .45em;
    font-weight: normal;
    font-size: 1.3em;
    line-height: 1;
}

.nav-widgetPanel .item:hover {
    color: #ffffff;
}

.nav-widgetPanel .item+.item {
    padding-left: 0.25em;
}

.nav-widgetPanel .item:first-child {
    padding-left: 0.833em;
}

.nav-widgetPanel .item:last-child {
    padding-right: 0.833em;
}

.nav-widgetPanel .item .itemContent {
    padding: 0.5em 0 0.3em;
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.nav-widgetPanel .item .itemContent .text,
.nav-widgetPanel .item .itemContent .accountTable .text-auto,
.accountTable .nav-widgetPanel .item .itemContent .text-auto,
.nav-widgetPanel .item .itemContent .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .nav-widgetPanel .item .itemContent .text-auto {
    margin-left: 0.25em;
}

.nav-widgetPanel .item.active {
    cursor: default;
    color: #ffffff;
}

.nav-widgetPanel .item.active::after {
    content: "";
    display: block;
    height: 2px;
    background: #ffffff;
    width: calc(100% + 5%);
    margin-left: -5%;
    position: relative;
    bottom: 2px;
}

.mini .nav-widgetPanel .item {
    padding: 0 0.5em;
}

.mini .nav-widgetPanel .item.active {
    background: #ffffff;
    color: #545454;
    font-weight: bold;
}

.mini .nav-widgetPanel .item.active:after {
    display: none;
}

.mini .sportsMenu .contentArea .nav-widgetPanel .item {
    display: none;
}

.mini .sportsMenu .contentArea .nav-widgetPanel.showNav .item {
    display: block;
}

.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-early::before,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-early::after,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-today::before,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-today::after,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-live::before,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-live::after {
    visibility: visible;
}

.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-early::before,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-today::before,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-live::before {
    left: -1.6em;
}

.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-early::after,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-today::after,
.mini .sportsMenu .contentArea .nav-widgetPanel.icon-sportsMenu-live::after {
    left: -1px;
}

.sportsMenu .nav-widgetPanel {
    background: #233d67;
}

.mini .sportsMenu .nav-widgetPanel {
    width: auto;
    position: absolute;
    top: 0;
    left: 3.333em;
    z-index: 21;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    -webkit-flex-flow: column nowrap;
    -ms-flex-flow: column nowrap;
    flex-flow: column nowrap;
    border-radius: 0 3px 3px 0;
}

.mini .sportsMenu .nav-widgetPanel .item {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 0 1em;
    color: #adbed6;
}

.mini .sportsMenu .nav-widgetPanel .item::before {
    -ms-transform: translate(0, 0.1em) scale(1.2);
    -webkit-transform: translate(0, 0.1em) scale(1.2);
    transform: translate(0, 0.1em) scale(1.2);
}

.mini .sportsMenu .nav-widgetPanel .item:hover {
    color: #ffffff;
}

.mini .sportsMenu .nav-widgetPanel .item.active {
    background: transparent;
    color: #ffffff;
    font-weight: bold;
}

.mini .sportsMenu .nav-widgetPanel .item .itemContent {
    padding: 0.6em 2em 0.6em 0;
    overflow: initial;
}

.mini .sportsMenu .nav-widgetPanel .item+.item {
    margin-top: -0.6em;
}

.sportsMenu .heading .nav-widgetPanel {
    display: none;
}

.mini .sportsMenu .heading.showNav .nav-widgetPanel {
    display: block;
}

.infoGroup .infoTitle,
.infoGroup .infoItem {
    white-space: nowrap;
    text-align: right;
    padding: 0.5em 0.833em;
}

.creditArea .infoGroup .infoTitle,
.creditArea .infoGroup .infoItem {
    padding-top: 0.3em;
    padding-bottom: 0.3em;
}

.infoGroup .infoRow {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 1px;
}

.infoGroup .largeBtn,
.infoGroup .smallBtn {
    float: right;
}

.infoGroup .largeBtn {
    margin: -.3em 0;
}

.infoGroup .smallBtn {
    margin-left: 0.25em;
}

.infoGroup .currency {
    margin-right: 0.25em;
}

.infoGroup .infoTitle {
    color: #545454;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 40%;
    background: #d5e0f0;
}

.infoGroup .infoItem {
    width: 60%;
    background: #dfe7f3;
    font-weight: bold;
    color: #5574a7;
}

.infoGroup .infoItem.accent {
    color: #b53f39;
}

.infoGroup.loginInfo .infoTitle,
.infoGroup .second .infoTitle {
    background: #ececec;
}

.infoGroup.loginInfo .infoItem,
.infoGroup .second .infoItem {
    background: whitesmoke;
}

.creditArea .infoGroup .infoTitle {
    width: 35%;
}

.creditArea .infoGroup .infoItem {
    width: 65%;
    padding-right: 2.5em;
}

.creditArea {
    padding: 0 0.5em 0.5em 0.5em;
    position: relative;
}

.creditArea .icon-refresh {
    position: absolute;
    top: 4px;
    right: 12px;
}

.mini .personalAccount .creditArea {
    display: none;
}

.mini .personalAccount.active .creditArea {
    display: none;
    position: absolute;
    right: -25em;
    top: 0;
    z-index: 21;
}

.panelFunction {
    margin-bottom: 0.5em;
    position: relative;
}

.panelFunction .floatLeft {
    float: left;
    padding: .3em .4em;
}

.panelFunction .floatRight {
    float: right;
}

.panelFunction::after {
    content: "";
    display: block;
    clear: both;
}

.personalAccount {
    background: #ffffff;
    color: #545454;
}

.personalAccount::after {
    content: "";
    display: block;
    clear: both;
}

.memberBalance {
    width: 25em;
    background: #ffffff;
    color: #545454;
    padding: 0.5em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    border-radius: 3px;
}

.account .personalAccount .heading {
    cursor: default;
}

.account .personalAccount .text:after,
.account .personalAccount .accountTable .text-auto:after,
.accountTable .account .personalAccount .text-auto:after,
.account .personalAccount .accountTable-verticalAlignTop .text-auto:after,
.accountTable-verticalAlignTop .account .personalAccount .text-auto:after {
    display: none;
}

.account .personalAccount .nickname {
    color: #5574a7;
    background: #dfe7f3;
    padding: 0.5em 0.833em;
}

.account .personalAccount .smallBtn {
    color: #5574a7;
    float: right;
}

.category-sportList {
    background: whitesmoke;
    cursor: pointer;
    color: #545454;
    font-weight: bold;
}

.category-sportList:hover,
.category-sportList.active {
    background: white;
}

.mini .category-sportList {
    position: relative;
}

.mini .category-sportList.active {
    background: #adadad;
}

.mini .category-sportList.active::after {
    content: "";
    color: #ffffff;
    font-size: 1.25em;
    position: absolute;
    left: 27px;
    top: 7px;
}

.mini .live .category-sportList.active::after {
    display: none;
}

.category-sportList .icon-streaming,
.category-sportList .icon-live {
    font-weight: normal;
    margin-top: .05em;
}

.mini .category-sportList .icon-streaming,
.mini .category-sportList .icon-new {
    display: none;
}

.mini .category-sportList .category-sub .icon-new {
    display: inline-block;
}

.category-sportList .floatRight {
    display: flex;
}

.mini .category-sportList .icon-live {
    position: absolute;
    top: .2em;
    right: .2em;
    width: 0.5em;
    height: 0.5em;
    border-radius: 100%;
    overflow: hidden;
}

.category-sportList .amount {
    float: left;
    margin-left: 0.5em;
    min-width: 1.75em;
    text-align: right;
}

.mini .category-sportList .amount {
    display: none;
}

.mini .category-sportList.active .category-sub .amount {
    display: block;
}

.category-sportList-container {
    padding: 0.3em 0.5em;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.category-sportList-main {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.category-sportList-main .sportName {
    position: relative;
    padding-left: 20px;
}

.mini .category-sportList-main .sportName {
    padding-left: 3em;
    top: -.1em;
    line-height: 2.5;
}

.category-sportList-main .checkbox {
    -ms-transform: translate(0.3em, 3px);
    -webkit-transform: translate(0.3em, 3px);
    transform: translate(0.3em, 3px);
    margin-right: .45em;
}

.mini .category-sportList-main .checkbox {
    position: absolute;
    -ms-transform: translate(-0.4em, -0.2em) scale(0.8);
    -webkit-transform: translate(-0.4em, -0.2em) scale(0.8);
    transform: translate(-0.4em, -0.2em) scale(0.8);
}

.mini .category-sportList-main {
    text-overflow: unset;
}

.category-sub {
    background: #ececec;
}

.mini .category-sub {
    display: none;
}

.mini .category-sportList.active .category-sub {
    display: block;
    position: absolute;
    left: 3.333em;
    top: 0;
    z-index: 23;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    padding: 0.5em;
    background: whitesmoke;
    border-radius: 0 3px 3px 0;
}

.mini .category-sportList.active .category-sub:first-child {
    border-radius: 0 3px 0 0;
}

.mini .category-sportList.active .category-sub:last-child {
    border-radius: 0 0 3px 0;
}

.mini .category-sportList:nth-last-child(10)~.category-sportList .category-sub {
    top: auto;
    bottom: 0;
}

.category-sub-list {
    padding: 0.18em 0.5em;
    font-weight: normal;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.category-sub-list:hover {
    background: #cdcdcd;
}

.category-sub-list .betTypeName {
    padding-left: 2.45em;
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: block;
}

.mini .category-sub-list .betTypeName {
    padding-left: 0;
}

.category-sub-list.active {
    color: #5574a7;
    font-weight: bold;
}

.category-sub-list.active:hover {
    background: transparent;
}

.category-sub-list.active .smallBtn.icon-trophy {
    font-weight: lighter;
}

.baseList>li {
    position: relative;
    border-bottom: solid 1px #dfdfdf;
    background: whitesmoke;
    font-weight: bold;
    cursor: pointer;
}

.baseList>li a {
    color: #545454;
    text-decoration: none;
    padding: 0.6em 0.833em;
    display: block;
}

.baseList>li>div {
    padding: 0;
    position: relative;
}

.baseList>li:hover,
.baseList>li.active {
    background: white;
}

.baseList>li.active {
    cursor: default;
}

.baseList>li.sub::after {
    content: "";
    position: absolute;
    right: 0.2em;
    top: 0;
    padding-top: 0.3em;
    padding-left: 0.3em;
    color: #7c7c7c;
    font-size: 1.5em;
}

.baseList>li.sub.active .category-sub {
    display: block;
}

.baseList>li.sub.active .category-sub>li {
    padding: 0;
    font-weight: normal;
}

.baseList>li.sub.active .category-sub>li.link-none {
    padding: 0;
    font-weight: bold;
}

.baseList>li.sub.active .category-sub>li.active a {
    color: #5574a7;
    font-weight: bold;
}

.baseList>li.sub.active::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0.2em;
    padding-top: 0.1em;
    font-size: 1.5em;
}

.baseList>li.title {
    cursor: default;
}

.baseList>li.title>div {
    padding: 0.3em 0.833em;
    font-weight: normal;
    color: #adbed6;
    background: #233d67;
}

.baseList>li .alert {
    background: #b53f39;
    color: #ffffff;
    border-radius: 3px;
    -ms-transform: scale(0.7);
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    padding: 0 0.2em;
    margin-left: 0.25em;
    text-align: center;
    font-weight: lighter;
    font-size: 12px;
}

.baseList .category-sub {
    display: none;
}

.baseList .category-sub+ul {
    border-top: 1px solid #cdcdcd;
}

.baseList .category-sub li {
    padding-left: 2.4em;
}

.baseList .category-sub li:hover {
    background: #cdcdcd !important;
    cursor: pointer !important;
}

.baseList .category-sub li a {
    padding: 0.5em 0.833em 0.5em 2.3em;
    position: relative;
}

.baseList .category-sub li a::before {
    content: "";
    display: block;
    position: absolute;
    margin-left: -0.75em;
    margin-top: 0.5em;
    width: 0.33333em;
    height: 0.33333em;
    background: #7c7c7c;
    border-radius: 100%;
}

.baseList .category-sub li.link-none {
    padding-left: 1.4em;
    font-weight: bold;
}

.baseList .category-sub li.link-none a {
    padding-left: 1.6em;
}

.baseList .category-sub li.link-none:hover {
    cursor: default !important;
    background: transparent !important;
}

.baseList .category-sub li.link-none a::before {
    display: none;
}

.betSlip .mainSection.ticket-selected {
    border-color: #f77a00;
}

.betSlip .btnNote {
    padding-top: 0.5em;
    text-align: center;
}

.betSlip .single [class*="matchInfo"] {
    padding: 0 0 0.833em 0.833em;
}

.betSlip .single .betInfoSub {
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

.betSlip .parlay .stakeArea,
.betSlip .parlay .comboList>li .innerContent,
.comboList>li .betSlip .parlay .innerContent {
    background: #dfdfdf;
    padding: 0 0.833em;
}

.betSlip .parlay .stakeArea.hasStake,
.betSlip .parlay .comboList>li .hasStake.innerContent,
.comboList>li .betSlip .parlay .hasStake.innerContent {
    background: #dfe7f3;
}

.betSlip .parlay .stakeArea.hasStake a,
.betSlip .parlay .comboList>li .hasStake.innerContent a,
.comboList>li .betSlip .parlay .hasStake.innerContent a {
    color: #5574a7;
}

.betSlip .parlay .stakeArea li,
.betSlip .parlay .comboList>li .innerContent li,
.comboList>li .betSlip .parlay .innerContent li {
    list-style: none;
}

.betSlip .parlay .stakeArea a,
.betSlip .parlay .comboList>li .innerContent a,
.comboList>li .betSlip .parlay .innerContent a {
    position: relative;
    display: block;
    padding: 0.5em 1em;
    padding-left: 2.25em;
    cursor: pointer;
    margin-left: -0.833em;
    color: black;
}

.betSlip .parlay .stakeArea a::before,
.betSlip .parlay .comboList>li .innerContent a::before,
.comboList>li .betSlip .parlay .innerContent a::before {
    left: 0.4em;
    top: 0.1em;
    font-size: 1.5em;
    content: "";
    position: absolute;
    text-align: center;
    font-weight: bold;
    width: 1em;
    height: 1em;
}

@-moz-document url-prefix() {
    .betSlip .parlay .stakeArea a::before,
    .betSlip .parlay .comboList>li .innerContent a::before,
    .comboList>li .betSlip .parlay .innerContent a::before {
        top: 0.2em;
    }
}

@media screen and (min-width: 0\0) {
    .betSlip .parlay .stakeArea a::before,
    .betSlip .parlay .comboList>li .innerContent a::before,
    .comboList>li .betSlip .parlay .innerContent a::before {
        top: 0em;
    }
}

.betSlip .parlay .stakeArea .innerContent,
.betSlip .parlay .comboList>li .innerContent .innerContent,
.comboList>li .betSlip .parlay .innerContent .innerContent {
    display: none;
}

.betSlip .parlay .stakeArea .entry,
.betSlip .parlay .comboList>li .innerContent .entry,
.comboList>li .betSlip .parlay .innerContent .entry {
    padding-bottom: 0.15em;
}

.betSlip .parlay .stakeArea.active .innerContent,
.betSlip .parlay .comboList>li .active.innerContent .innerContent,
.comboList>li .betSlip .parlay .active.innerContent .innerContent {
    display: block;
    padding-bottom: 0.5em;
}

.betSlip .parlay .stakeArea.active a::before,
.betSlip .parlay .comboList>li .active.innerContent a::before,
.comboList>li .betSlip .parlay .active.innerContent a::before {
    content: "";
}

.showingNow {
    color: #545454;
}

.showingNow .widgetPanelInfo {
    background: rgba(0, 0, 0, 0.07);
    padding: 0.5em 0.833em;
}

.showingNow .circleBtn.icon-alarmClock {
    top: 0.5em;
    right: 0.83333em;
    float: right;
    z-index: 3;
}

.showingNow .fixed {
    max-height: 240px;
    position: relative;
}

.mini .showingNow .fixed {
    max-height: none;
}

.myScore {
    color: #545454;
}

.myScore .fixed {
    max-height: 162px;
}

.mini .myScore .fixed {
    max-height: none;
}

.myScore ul {
    padding: 0.5em;
    background: #ffffff;
}

.myScore li {
    border-radius: 3px;
    border: 1px solid #d7d7d7;
    cursor: pointer;
    padding: 0.3em;
    position: relative;
    margin-bottom: 0.3em;
}

.myScore li::after {
    content: "";
    display: block;
    clear: both;
}

.myScore li:hover {
    background-color: #f5eeb8;
}

.myScore li:nth-child(2n + 1) {
    background-color: #f6f6f6;
}

.myScore li:nth-child(2n + 1):hover {
    background-color: #f5eeb8;
}

.myScore li .glyphIcon {
    position: absolute;
    right: 0.5em;
    top: 0.2em;
}

.myScore .teamHome,
.myScore .teamAway {
    width: calc( 50% - (20px / 2) - 0.3em);
    float: left;
    text-align: right;
}

.myScore .teamAway {
    text-align: left;
}

.myScore .teamName {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.myScore .score {
    font-weight: bold;
    color: #5574a7;
    font-size: 1.2em;
}

.myScore .iconSpace {
    float: left;
    text-align: center;
    width: 20px;
    margin: 0 0.3em;
}

.miniOdds .tabs-content {
    padding: 0.5em;
}

.miniOdds .slides {
    margin: -0.5em;
}

.miniOdds .slides-container {
    padding: 0.5em;
    box-sizing: border-box;
}

.miniOdds .slides-btn-prev {
    left: 0;
}

.miniOdds .slides-btn-next {
    right: 0;
}

.miniOdds .slides-pagination {
    margin-top: -0.75em;
}

.miniOdds-sport-title {
    padding: 0 0.25em;
    text-align: center;
    color: #5574a7;
    font-weight: bold;
    margin-top: -0.25em;
    margin-bottom: 0.5em;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}

.miniOdds-oddstable {
    text-align: center;
    border-radius: 3px;
    color: rgba(255, 255, 255, 0.8);
}

.miniOdds-oddstable+.miniOdds-oddstable {
    margin-top: 0.5em;
}

.miniOdds-league {
    padding: 0.25em 0.5em;
    border-style: solid;
    border-bottom-width: 1px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.miniOdds-event,
.miniOdds-odds {
    display: table;
    width: calc(100% - 0.5em);
    padding: 0.25em;
}

.miniOdds-event>li,
.miniOdds-odds>li {
    display: table-cell;
    vertical-align: middle;
}

.miniOdds-event {
    table-layout: fixed;
    margin-top: 0.25em;
    margin-bottom: 0.75em;
    padding-left: 0;
    padding-right: 0;
}

.miniOdds-event .teamName:first-of-type {
    padding-left: 0.25em;
    padding-right: 0.5em;
}

.miniOdds-event .teamName:last-of-type {
    padding-left: 0.5em;
    padding-right: 0.25em;
}

.miniOdds-event .teamName>div {
    max-height: 3em;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: normal;
}

.miniOdds-event .time {
    color: #545454;
}

.miniOdds-event .smallBtn {
    float: none;
    display: inline-block;
    cursor: default;
}

.miniOdds-event .status,
.miniOdds-event .accountTable .status-smaller,
.accountTable .miniOdds-event .status-smaller,
.miniOdds-event .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .miniOdds-event .status-smaller {
    width: 6em;
    position: relative;
    height: 3em;
}

.miniOdds-event .status>.time,
.miniOdds-event .accountTable .status-smaller>.time,
.accountTable .miniOdds-event .status-smaller>.time,
.miniOdds-event .accountTable-verticalAlignTop .status-smaller>.time,
.accountTable-verticalAlignTop .miniOdds-event .status-smaller>.time,
.miniOdds-event .status .icon-live,
.miniOdds-event .accountTable .status-smaller .icon-live,
.accountTable .miniOdds-event .status-smaller .icon-live,
.miniOdds-event .accountTable-verticalAlignTop .status-smaller .icon-live,
.accountTable-verticalAlignTop .miniOdds-event .status-smaller .icon-live,
.miniOdds-event .status font,
.miniOdds-event .accountTable .status-smaller font,
.accountTable .miniOdds-event .status-smaller font,
.miniOdds-event .accountTable-verticalAlignTop .status-smaller font,
.accountTable-verticalAlignTop .miniOdds-event .status-smaller font {
    position: relative;
}

.miniOdds-event .status::before,
.miniOdds-event .accountTable .status-smaller::before,
.accountTable .miniOdds-event .status-smaller::before,
.miniOdds-event .accountTable-verticalAlignTop .status-smaller::before,
.accountTable-verticalAlignTop .miniOdds-event .status-smaller::before {
    content: "";
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: calc( -9em / 2);
    margin-top: calc( -9em / 2 + 0.25em);
    width: 9em;
    height: 9em;
    font-size: 12px;
}

.miniOdds-event .score {
    color: #b53f39;
}

.miniOdds-odds {
    background: rgba(0, 0, 0, 0.4);
}

.miniOdds-odds .betType {
    width: 100%;
    display: block;
}

.miniOdds-odds .betType .txt {
    width: 100%;
}

.miniOdds-odds .betArea {
    width: 46.5%;
    float: left;
    padding-left: 0.25em;
    padding-right: 0.25em;
    white-space: nowrap;
    cursor: pointer;
    border-radius: 3px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.85) 100%);
    background: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.85) 0%, rgba(255, 255, 255, 0.65) 100%);
}

.miniOdds-odds .betArea:hover {
    background: rgba(255, 255, 255, 0.9);
}

.miniOdds-odds .betArea .txt {
    color: rgba(77, 77, 77, 0.9);
}

.miniOdds-odds .betArea .oddsBet {
    margin-left: 0.25em;
    color: #01122b;
}

.miniOdds-odds .betArea+.betArea {
    margin-top: 0;
    float: right;
}

.miniOdds-odds .oddsBet {
    min-width: 2.7em;
}

.miniOdds-odds::after {
    content: "";
    display: block;
    clear: both;
}

.miniOdds-is-live {
    background: linear-gradient(to top, #a25465 0%, #753F6C 100%);
    background: -webkit-linear-gradient(top, #753F6C 0%, #a25465 100%);
}

.miniOdds-is-live .miniOdds-league {
    border-color: #b67381;
}

.miniOdds-is-deadball {
    background: linear-gradient(to top, #4fa5b0 0%, #4d65a4 100%);
    background: -webkit-linear-gradient(top, #4d65a4 0%, #4fa5b0 100%);
}

.miniOdds-is-deadball .miniOdds-league {
    border-color: #72b7c0;
}

.tabletVersion {
    border-radius: 3px;
    overflow: hidden;
    display: none;
}

.promotions {
    border-radius: 3px;
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
    padding-top: 0.3em;
    padding-bottom: 0.5em;
}

.promotions::before {
    content: "";
    background: url(../../images/v2/promo_01.png) center no-repeat;
    width: 2em;
    height: 100%;
    position: absolute;
    left: .7em;
    top: 0;
    background-size: 85%;
}

.promotions .contentArea {
    margin-right: 4em;
}

.promotions a {
    color: #ffffff;
    text-decoration: none;
    display: block;
    height: 100%;
    vertical-align: middle;
    -ms-transform: translate(2.7em, 0.2em) scale(0.98);
    -webkit-transform: translate(2.7em, 0.2em) scale(0.98);
    transform: translate(2.7em, 0.2em) scale(0.98);
}

.mini .promotions {
    display: none;
}

.switchView {
    border-radius: 3px;
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
}

.switchView .heading {
    padding: .4em .4em .4em 2.4em;
    border-radius: 3px;
}

.switchView .heading::before {
    content: "";
    background: url(../../images/v2/promo_01.png) center no-repeat;
    width: 1.6em;
    height: 100%;
    position: absolute;
    left: .3em;
    top: 0;
    background-size: 85%;
}

.switchView .heading .text::after,
.switchView .heading .accountTable .text-auto::after,
.accountTable .switchView .heading .text-auto::after,
.switchView .heading .accountTable-verticalAlignTop .text-auto::after,
.accountTable-verticalAlignTop .switchView .heading .text-auto::after {
    content: "";
    font-size: 2em;
    position: absolute;
    right: 0.2em;
    top: 0.1em;
    color: #ffffff;
    line-height: 1;
}

.switchView .heading::after {
    display: none;
}

.switchView.openContent {
    z-index: 28;
}

.switchView.openContent .text::after,
.switchView.openContent .accountTable .text-auto::after,
.accountTable .switchView.openContent .text-auto::after,
.switchView.openContent .accountTable-verticalAlignTop .text-auto::after,
.accountTable-verticalAlignTop .switchView.openContent .text-auto::after {
    content: "";
}

.switchView.openContent .contentArea {
    display: block;
}

.switchView .contentArea {
    display: none;
    position: absolute;
    top: 2.8em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    background: #ececec;
    z-index: 23;
    width: 100%;
    border-radius: 3px;
}

.mini .switchView {
    display: none;
}

.switchView-list {
    font-weight: bold;
    padding: .35em 1em;
}

.switchView-list.current {
    color: #5574a7;
}

.switchView-list:hover {
    background-color: #cdcdcd;
    cursor: pointer;
}

.switchView-list.new {
    position: relative;
}

.switchView-list.new::after {
    font-family: "Arial", "Tahoma", "pmingliu", "新細明體";
    content: "NEW";
    position: absolute;
    right: .5em;
    top: .35em;
    color: #73483e;
    background: #ffd330;
    border-radius: 3px;
    line-height: 1;
    padding: .2em;
}

.crossSelling .slides {
    background-color: #3b3b3b;
    color: #ffffff;
}

.mini .crossSelling.active .heading {
    bottom: 105px;
}

.crossSelling-list {
    padding-top: 1em;
}

.crossSelling-list::after {
    content: "";
    display: block;
    clear: both;
}

.crossSelling-list li {
    float: left;
    width: 33.3333%;
    text-align: center;
}

.crossSelling-list a {
    display: inline-block;
    position: relative;
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
}

.crossSelling-list a:hover::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: black;
    opacity: 0.5;
}

.crossSelling-list a:hover span {
    display: block;
}

.crossSelling-list span {
    display: none;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    margin-top: calc( -3em / 2);
    padding: .4em;
    height: 2em;
    line-height: 1;
    color: #ffffff;
    font-weight: bold;
    background: #ff942b;
    border: solid 1px #c46100;
}

.crossSelling-list .playNow,
.crossSelling-list .playFun {
    display: none;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    margin-top: calc( -3em / 2);
    padding: .4em;
    height: 2em;
    line-height: 1;
    color: #ffffff;
    font-weight: bold;
}

.crossSelling-list .playNow {
    background: #ff942b;
    border: solid 1px #c46100;
}

.crossSelling-list .playFun {
    padding: .4em .3em;
    background: #1fc000;
    border: 1px solid #007110;
}

.crossSelling-list div {
    height: 2.5em;
    line-height: 1.25;
    overflow: hidden;
}

.colossus-bet {
    line-height: 1.333333333;
    font-size: 12px;
}

.colossus-bet__iframe {
    display: block;
    padding: 0;
    width: 100%;
    height: 78px;
    border: none;
}

.colossus-bet__banner {
    display: block;
    position: relative;
    cursor: pointer;
}

.colossus-bet__banner::before {
    content: "";
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #ffd330;
    opacity: .1;
}

.colossus-bet__banner img {
    width: 100%;
    height: auto;
    border: none;
}

.colossus-bet__banner:hover:before {
    display: block;
}

.colossus-bet__banner:hover .colossus-bet__btn {
    background: linear-gradient(to bottom, #ff942b 0%, #ff7614 100%);
    background: -webkit-linear-gradient(bottom, #ff7614 0%, #ff942b 100%);
}

.colossus-bet__btn {
    display: inline-block;
    position: absolute;
    left: 50%;
    bottom: .5em;
    transform: translateX(-50%) translateX(-3.2em);
    padding: .5em;
    line-height: 1;
    color: white;
    font-weight: 700;
    border-radius: .25em;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(to bottom, #f77a00 0%, #e05d00 100%);
    background: -webkit-linear-gradient(bottom, #e05d00 0%, #f77a00 100%);
    text-align: center;
    min-width: 40%;
    max-width: 59%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.jackpots {
    line-height: 1.333333333;
    font-size: 12px;
}

.jackpots .contentArea {
    position: relative;
    height: 163px;
    width: 224px;
}

.casino-jackpot__pools {
    width: 100%;
    background-color: #ffffff;
    overflow: hidden;
    box-sizing: border-box;
}

.casino-jackpot__list {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    display: table;
}

.casino-jackpot__row {
    width: 100%;
    padding: 0;
    margin: 0;
    list-style: none;
    display: table-row;
}

.casino-jackpot__col {
    width: 65%;
    height: 27px;
    padding: 0em 1em;
    box-sizing: border-box;
    display: table-cell;
    border-bottom: 1px solid #dfdfdf;
    text-align: right;
    vertical-align: middle;
}

.casino-jackpot__col:first-child {
    width: 35%;
}

.casino-jackpot__text {
    font-weight: 700;
}

.casino-jackpot__money {
    color: #5574a7;
    font-size: 14px;
    font-weight: 900;
}

.casino-jackpot_pool-1 .casino-jackpot__col {
    border: none;
}

.casino-jackpot_pool-1 .casino-jackpot__banner {
    height: 138px;
}

.casino-jackpot_pool-1 .casino-jackpot__iframe {
    height: 25px;
}

.casino-jackpot_pool-2 .casino-jackpot__banner {
    height: 112px;
}

.casino-jackpot_pool-2 .casino-jackpot__iframe {
    height: 54px;
}

.casino-jackpot_pool-3 .casino-jackpot__banner {
    height: 85px;
}

.casino-jackpot_pool-3 .casino-jackpot__iframe {
    height: 81px;
}

.casino-jackpot_pool-4 .casino-jackpot__banner {
    height: 56px;
}

.casino-jackpot_pool-4 .casino-jackpot__iframe {
    height: 108px;
}

.casino-jackpot-win {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    font-family: Arial;
    text-align: center;
    letter-spacing: 0.05em;
    z-index: 3;
    box-sizing: border-box;
    overflow: hidden;
}

.casino-jackpot-win img {
    position: absolute;
    top: 0;
    left: 0;
}

.casino-jackpot-win__info {
    width: 100%;
    height: 139px;
    position: relative;
    margin-top: 9px;
    padding: 22px 0 12px;
    box-sizing: border-box;
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
}

.casino-jackpot-win__id {
    color: #ffff01;
}

.casino-jackpot-win__title {
    height: 41px;
    line-height: 41px;
}

.casino-jackpot-win__time {
    color: black;
}

.casino-jackpot-win__money {
    position: relative;
    z-index: 1;
    padding: .15em;
    color: #ffff01;
    font-size: 15px;
    margin-top: 8px;
}

.casino-jackpot-win__game {
    padding: .3em .4em;
    z-index: 4;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    color: #ffffff;
    text-align: left;
    background-color: rgba(0, 0, 0, 0.6);
}

.casino-jackpot-win__text {
    display: block;
    padding: 0 .5em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.casino-jackpot-win__id,
.casino-jackpot-win__title,
.casino-jackpot-win__time {
    position: relative;
    line-height: 12px;
    z-index: 4;
}

.miniOdds-event .timeInfo .timePlaying,
.oddsTable .time .timeInfo .timePlaying {
    font-weight: bold;
    white-space: nowrap;
    display: block;
}

.miniOdds-event .timeInfo .timePlaying>span,
.oddsTable .time .timeInfo .timePlaying>span {
    display: block;
    text-align: center;
}

.miniOdds-event .timeInfo .timePlaying.primary,
.oddsTable .time .timeInfo .timePlaying.primary {
    color: #2556b3;
}

.oddsTable.maskActive {
    position: relative;
    overflow: hidden;
}

.oddsTable.maskActive::after {
    content: "";
    background: #233d67;
    opacity: 0.5;
    position: absolute;
    z-index: 6;
    top: 0;
    width: 100%;
    height: 100%;
}

.oddsTable.maskActive::before {
    content: "";
    position: absolute;
    z-index: 7;
    width: 235px;
    height: 180px;
    top: calc( 50% - 90px);
    left: calc( 50% - 115px);
}

.oddsTable .time.statusChanged {
    -webkit-animation: oddsChangeColor-transparent 2.5s linear infinite normal;
    animation: oddsChangeColor-transparent 2.5s linear infinite normal;
}

.oddsTable .time .score {
    margin-bottom: 0.167em;
}

.oddsTable .time .score>span {
    color: #b53f39;
    font-weight: bold;
    text-align: center;
}

.oddsTable .time .score>span+span {
    margin-left: 0.15em;
}

.oddsTable .time .score>span:first-child {
    text-align: right;
}

.oddsTable .time .score>span:last-child {
    text-align: left;
}

.oddsTable .time .resultInfo {
    text-align: center;
}

.oddsTable .time .resultInfo .ballArea {
    font-size: 0.8em;
    text-align: right;
    display: inline-block;
    padding: 0.3em 0;
}

.oddsTable .time .resultInfo .countdown {
    color: #b53f39;
    font-weight: bold;
}

.oddsTable .time .resultInfo .icon-clock::before {
    font-size: 1.4em;
    position: relative;
    top: 0.1em;
}

.oddsTable .event {
    padding-left: 0.25em;
    padding-right: 0.25em;
}

.oddsTable .event .team {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.oddsTable .event .team+.team {
    margin-top: 0.167em;
}

.oddsTable .event .team .name {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #01122b;
    font-weight: bold;
}

.oddsTable .event .team .name.accent {
    color: #b53f39;
}

.oddsTable .event .team .name.primary {
    color: #2556B3;
}

.oddsTable .event .team .name.blue {
    color: #2556B3;
}

.oddsTable .event .team .name.red {
    color: #b53f39;
}

.oddsTable .event .team .extra {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #545454;
    font-weight: bold;
}

.oddsTable .event .team .result {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #01122b;
    padding-left: 0.833em;
}

.oddsTable .event .team .name-pointer {
    cursor: pointer;
}

.oddsTable .event .team .text,
.oddsTable .event .team .accountTable .text-auto,
.accountTable .oddsTable .event .team .text-auto,
.oddsTable .event .team .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .oddsTable .event .team .text-auto {
    display: inline;
}

.oddsTable .event .team .card {
    display: inline-block;
    padding: 0 0.3em;
    margin-left: 0.25em;
    background: #b53f39;
    color: #ffffff;
    border-radius: 3px;
    -ms-transform: rotate(15deg) scale(0.85);
    -webkit-transform: rotate(15deg) scale(0.85);
    transform: rotate(15deg) scale(0.85);
}

.oddsTable .event .team .iconSet {
    -webkit-box-flex: 0 1 auto;
    -webkit-flex: 0 1 auto;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    margin-left: 2em;
}

.oddsTable .event .team .iconSet .smallBtn {
    -webkit-box-flex: 1 0 auto;
    -webkit-flex: 1 0 auto;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
}

.oddsTable .event .team .iconSet .inactive {
    cursor: default;
}

.oddsTable.numberGame-a .event {
    padding-left: 0.5em;
}

.oddsTable .addedParlay {
    position: relative;
}

.oddsTable .addedParlay::before,
.oddsTable .addedParlay::after {
    content: "";
    position: absolute;
    -ms-transform: scale(0.7);
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
}

.oddsTable .addedParlay::before {
    content: "";
    color: #ffffff;
    top: -0.4em;
    left: 0em;
    z-index: 5;
}

.oddsTable .addedParlay::after {
    top: -0.35em;
    left: -0.3em;
    z-index: 4;
    width: 0;
    height: 0;
    border-width: 2em 2em 0 0;
    border-color: #5dad00 transparent transparent #5dad00;
    border-style: solid;
}

.oddsTitleWrap {
    position: relative;
}

.oddsTitle,
.oddsTitle-accent,
.oddsTitleSub {
    height: 22px;
    border-style: solid;
    border-width: 0 0 1px;
}

.oddsTable .oddsTitle .time+.event,
.oddsTable .oddsTitle-accent .time+.event,
.oddsTable .oddsTitleSub .time+.event,
.numberGame-a .oddsTitle .event,
.numberGame-a .oddsTitle-accent .event,
.numberGame-a .oddsTitleSub .event {
    padding-left: 2.5em;
}

.oddsTable .oddsTitle .time,
.oddsTable .oddsTitle-accent .time,
.oddsTable .oddsTitleSub .time {
    display: none;
}

.oddsTable.hdpou-e .oddsTitle .time,
.oddsTable.hdpou-e .oddsTitle-accent .time,
.oddsTable.hdpou-e .oddsTitleSub .time {
    display: table-cell;
    width: 6.4em;
    max-width: 6.4em;
}

.oddsTitle>div,
.oddsTitle-accent>div,
.oddsTitleSub>div {
    color: #ffffff;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.oddsTitle>div+div,
.oddsTitle-accent>div+div,
.oddsTitleSub>div+div {
    border-left: 1px solid;
}

.oddsTitle,
.oddsTitleSub {
    background: #5574a7;
    border-color: #5574a7;
}

.oddsTitle>div+div,
.oddsTitleSub>div+div {
    border-color: #3b5174;
}

.oddsTitle .odds-changed {
    background: #1063ec;
}

.oddsTitle-accent {
    background: #b53f39;
    border-color: #b53f39;
}

.oddsTitle-accent>div+div {
    border-color: #7b2b27;
}

.oddsTitle-accent .odds-changed {
    background: #d3241b;
}

.oddsTitleSub>div {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    border-bottom: 1px solid #3b5174;
}

.league {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    background: #b1b1b1;
    border-bottom: 1px solid #a3a3a3;
}

.league:hover {
    background: #b9b9b9;
}

.league .leagueName {
    -webkit-box-flex: 1 0 0;
    -webkit-flex: 1 0 0;
    -ms-flex: 1 0 0;
    flex: 1 0 0;
    -webkit-align-self: center;
    -ms-flex-item-align: center;
    align-self: center;
    padding: 0 0.15em;
    color: black;
    font-weight: bold;
    cursor: pointer;
}

.league .leagueName .icon-turbo:before {
    font-size: 1.6em;
    margin-right: 0.25em;
    line-height: 0;
    position: relative;
    top: .2em;
}

.league .leagueName .accent {
    color: #b53f39;
}

.league .leagueName .label-accent {
    background: #b53f39;
    color: #ffffff;
    padding: 0 0.3em;
    border-radius: 3px;
    margin: 0 0.25em;
}

.live-a,
.live-b,
.normal-a,
.normal-b,
.mmr-a,
.mmr-b {
    border-bottom: 1px solid;
}

.live-a:hover,
.live-b:hover,
.normal-a:hover,
.normal-b:hover,
.mmr-a:hover,
.mmr-b:hover {
    background: #f5eeb8;
}

.live-a:hover .multiOdds.mmr,
.live-b:hover .multiOdds.mmr,
.normal-a:hover .multiOdds.mmr,
.normal-b:hover .multiOdds.mmr,
.mmr-a:hover .multiOdds.mmr,
.mmr-b:hover .multiOdds.mmr {
    background: transparent;
}

.live-a:hover .multiOdds-moreBetType,
.live-b:hover .multiOdds-moreBetType,
.normal-a:hover .multiOdds-moreBetType,
.normal-b:hover .multiOdds-moreBetType,
.mmr-a:hover .multiOdds-moreBetType,
.mmr-b:hover .multiOdds-moreBetType,
.live-a:hover .multiOdds-moreBetType .betTypeContent,
.live-b:hover .multiOdds-moreBetType .betTypeContent,
.normal-a:hover .multiOdds-moreBetType .betTypeContent,
.normal-b:hover .multiOdds-moreBetType .betTypeContent,
.mmr-a:hover .multiOdds-moreBetType .betTypeContent,
.mmr-b:hover .multiOdds-moreBetType .betTypeContent {
    background: #f5eeb8;
}

.live-a .multiOdds+.multiOdds,
.live-b .multiOdds+.multiOdds,
.normal-a .multiOdds+.multiOdds,
.normal-b .multiOdds+.multiOdds,
.mmr-a .multiOdds+.multiOdds,
.mmr-b .multiOdds+.multiOdds {
    border-top: 1px solid;
}

.live-a .multiOdds.mmr,
.live-b .multiOdds.mmr,
.normal-a .multiOdds.mmr,
.normal-b .multiOdds.mmr,
.mmr-a .multiOdds.mmr,
.mmr-b .multiOdds.mmr {
    background: #D3D5EC;
}

.live-a .time,
.live-b .time,
.normal-a .time,
.normal-b .time,
.mmr-a .time,
.mmr-b .time {
    border-right: 1px solid;
}

.live-a .others,
.live-b .others,
.normal-a .others,
.normal-b .others,
.mmr-a .others,
.mmr-b .others {
    border-left: 1px solid;
}

.live-a .time,
.live-b .time,
.normal-a .time,
.normal-b .time,
.mmr-a .time,
.mmr-b .time,
.live-a .event+div,
.live-b .event+div,
.normal-a .event+div,
.normal-b .event+div,
.mmr-a .event+div,
.mmr-b .event+div,
.hdpou-a .live-a .multiOdds>div:nth-of-type(5),
.hdpou-a .live-b .multiOdds>div:nth-of-type(5),
.hdpou-a .normal-a .multiOdds>div:nth-of-type(5),
.hdpou-a .normal-b .multiOdds>div:nth-of-type(5),
.hdpou-a .mmr-a .multiOdds>div:nth-of-type(5),
.hdpou-a .mmr-b .multiOdds>div:nth-of-type(5),
.hdpou-g .live-a .multiOdds>div:nth-of-type(4),
.hdpou-g .live-b .multiOdds>div:nth-of-type(4),
.hdpou-g .normal-a .multiOdds>div:nth-of-type(4),
.hdpou-g .normal-b .multiOdds>div:nth-of-type(4),
.hdpou-g .mmr-a .multiOdds>div:nth-of-type(4),
.hdpou-g .mmr-b .multiOdds>div:nth-of-type(4),
.hdpouSingle-a .live-a .multiOdds>div:nth-of-type(n+2),
.hdpouSingle-a .live-b .multiOdds>div:nth-of-type(n+2),
.hdpouSingle-a .normal-a .multiOdds>div:nth-of-type(n+2),
.hdpouSingle-a .normal-b .multiOdds>div:nth-of-type(n+2),
.hdpouSingle-a .mmr-a .multiOdds>div:nth-of-type(n+2),
.hdpouSingle-a .mmr-b .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .live-a .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .live-b .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .normal-a .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .normal-b .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .mmr-a .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .mmr-b .multiOdds>div:nth-of-type(n+2),
.onextwo-a .live-a>div:nth-last-of-type(3),
.onextwo-a .live-b>div:nth-last-of-type(3),
.onextwo-a .normal-a>div:nth-last-of-type(3),
.onextwo-a .normal-b>div:nth-last-of-type(3),
.onextwo-a .mmr-a>div:nth-last-of-type(3),
.onextwo-a .mmr-b>div:nth-last-of-type(3),
.correctScore-a .live-a>div:nth-last-of-type(6),
.correctScore-a .live-b>div:nth-last-of-type(6),
.correctScore-a .normal-a>div:nth-last-of-type(6),
.correctScore-a .normal-b>div:nth-last-of-type(6),
.correctScore-a .mmr-a>div:nth-last-of-type(6),
.correctScore-a .mmr-b>div:nth-last-of-type(6),
.correctScore-d .live-a>div:nth-last-of-type(5),
.correctScore-d .live-b>div:nth-last-of-type(5),
.correctScore-d .normal-a>div:nth-last-of-type(5),
.correctScore-d .normal-b>div:nth-last-of-type(5),
.correctScore-d .mmr-a>div:nth-last-of-type(5),
.correctScore-d .mmr-b>div:nth-last-of-type(5),
.correctScore-b .live-a>div:nth-last-of-type(3),
.correctScore-b .live-b>div:nth-last-of-type(3),
.correctScore-b .normal-a>div:nth-last-of-type(3),
.correctScore-b .normal-b>div:nth-last-of-type(3),
.correctScore-b .mmr-a>div:nth-last-of-type(3),
.correctScore-b .mmr-b>div:nth-last-of-type(3),
.oddEven-a .live-a>div:nth-last-of-type(2),
.oddEven-a .live-b>div:nth-last-of-type(2),
.oddEven-a .normal-a>div:nth-last-of-type(2),
.oddEven-a .normal-b>div:nth-last-of-type(2),
.oddEven-a .mmr-a>div:nth-last-of-type(2),
.oddEven-a .mmr-b>div:nth-last-of-type(2),
.totalGoal-a .live-a>div:nth-of-type(7),
.totalGoal-a .live-b>div:nth-of-type(7),
.totalGoal-a .normal-a>div:nth-of-type(7),
.totalGoal-a .normal-b>div:nth-of-type(7),
.totalGoal-a .mmr-a>div:nth-of-type(7),
.totalGoal-a .mmr-b>div:nth-of-type(7),
.halfTimeFullTime-a .live-a>div:nth-last-of-type(3),
.halfTimeFullTime-a .live-b>div:nth-last-of-type(3),
.halfTimeFullTime-a .normal-a>div:nth-last-of-type(3),
.halfTimeFullTime-a .normal-b>div:nth-last-of-type(3),
.halfTimeFullTime-a .mmr-a>div:nth-last-of-type(3),
.halfTimeFullTime-a .mmr-b>div:nth-last-of-type(3),
.halfTimeFullTime-a .live-a>div:nth-last-of-type(6),
.halfTimeFullTime-a .live-b>div:nth-last-of-type(6),
.halfTimeFullTime-a .normal-a>div:nth-last-of-type(6),
.halfTimeFullTime-a .normal-b>div:nth-last-of-type(6),
.halfTimeFullTime-a .mmr-a>div:nth-last-of-type(6),
.halfTimeFullTime-a .mmr-b>div:nth-last-of-type(6),
.firstGoalLastGoal-a .live-a>div:nth-last-of-type(3),
.firstGoalLastGoal-a .live-b>div:nth-last-of-type(3),
.firstGoalLastGoal-a .normal-a>div:nth-last-of-type(3),
.firstGoalLastGoal-a .normal-b>div:nth-last-of-type(3),
.firstGoalLastGoal-a .mmr-a>div:nth-last-of-type(3),
.firstGoalLastGoal-a .mmr-b>div:nth-last-of-type(3),
.racing-a .live-a>div,
.racing-a .live-b>div,
.racing-a .normal-a>div,
.racing-a .normal-b>div,
.racing-a .mmr-a>div,
.racing-a .mmr-b>div,
.racing-b .live-a>div,
.racing-b .live-b>div,
.racing-b .normal-a>div,
.racing-b .normal-b>div,
.racing-b .mmr-a>div,
.racing-b .mmr-b>div,
.racing-c .live-a>div,
.racing-c .live-b>div,
.racing-c .normal-a>div,
.racing-c .normal-b>div,
.racing-c .mmr-a>div,
.racing-c .mmr-b>div,
.racing-d .live-a>div,
.racing-d .live-b>div,
.racing-d .normal-a>div,
.racing-d .normal-b>div,
.racing-d .mmr-a>div,
.racing-d .mmr-b>div,
.numberGame-a .live-a>div,
.numberGame-a .live-b>div,
.numberGame-a .normal-a>div,
.numberGame-a .normal-b>div,
.numberGame-a .mmr-a>div,
.numberGame-a .mmr-b>div,
.numberGame-b .live-a>div,
.numberGame-b .live-b>div,
.numberGame-b .normal-a>div,
.numberGame-b .normal-b>div,
.numberGame-b .mmr-a>div,
.numberGame-b .mmr-b>div,
.numberGame-c .live-a>div,
.numberGame-c .live-b>div,
.numberGame-c .normal-a>div,
.numberGame-c .normal-b>div,
.numberGame-c .mmr-a>div,
.numberGame-c .mmr-b>div,
.numberGame-d .live-a>div,
.numberGame-d .live-b>div,
.numberGame-d .normal-a>div,
.numberGame-d .normal-b>div,
.numberGame-d .mmr-a>div,
.numberGame-d .mmr-b>div,
.lotto-c .live-a>div,
.lotto-c .live-b>div,
.lotto-c .normal-a>div,
.lotto-c .normal-b>div,
.lotto-c .mmr-a>div,
.lotto-c .mmr-b>div {
    border-left: 1px solid;
}

.oddsTable .live-a>div:first-child,
.oddsTable .live-b>div:first-child,
.oddsTable .normal-a>div:first-child,
.oddsTable .normal-b>div:first-child,
.oddsTable .mmr-a>div:first-child,
.oddsTable .mmr-b>div:first-child,
.numberGame-a .live-a>div:nth-last-of-type(3),
.numberGame-a .live-b>div:nth-last-of-type(3),
.numberGame-a .normal-a>div:nth-last-of-type(3),
.numberGame-a .normal-b>div:nth-last-of-type(3),
.numberGame-a .mmr-a>div:nth-last-of-type(3),
.numberGame-a .mmr-b>div:nth-last-of-type(3),
.numberGame-a .live-a>div:nth-last-of-type(5),
.numberGame-a .live-b>div:nth-last-of-type(5),
.numberGame-a .normal-a>div:nth-last-of-type(5),
.numberGame-a .normal-b>div:nth-last-of-type(5),
.numberGame-a .mmr-a>div:nth-last-of-type(5),
.numberGame-a .mmr-b>div:nth-last-of-type(5),
.numberGame-d .live-a>div:nth-last-of-type(2),
.numberGame-d .live-b>div:nth-last-of-type(2),
.numberGame-d .normal-a>div:nth-last-of-type(2),
.numberGame-d .normal-b>div:nth-last-of-type(2),
.numberGame-d .mmr-a>div:nth-last-of-type(2),
.numberGame-d .mmr-b>div:nth-last-of-type(2),
.numberGame-d .live-a>div:nth-last-of-type(5),
.numberGame-d .live-b>div:nth-last-of-type(5),
.numberGame-d .normal-a>div:nth-last-of-type(5),
.numberGame-d .normal-b>div:nth-last-of-type(5),
.numberGame-d .mmr-a>div:nth-last-of-type(5),
.numberGame-d .mmr-b>div:nth-last-of-type(5),
.numberGame-d .live-a>div:nth-last-of-type(7),
.numberGame-d .live-b>div:nth-last-of-type(7),
.numberGame-d .normal-a>div:nth-last-of-type(7),
.numberGame-d .normal-b>div:nth-last-of-type(7),
.numberGame-d .mmr-a>div:nth-last-of-type(7),
.numberGame-d .mmr-b>div:nth-last-of-type(7),
.lotto-c .live-a>div:nth-last-of-type(6),
.lotto-c .live-b>div:nth-last-of-type(6),
.lotto-c .normal-a>div:nth-last-of-type(6),
.lotto-c .normal-b>div:nth-last-of-type(6),
.lotto-c .mmr-a>div:nth-last-of-type(6),
.lotto-c .mmr-b>div:nth-last-of-type(6) {
    border-left: 0;
}

.disable.live-a,
.disable.live-b,
.disable.normal-a,
.disable.normal-b,
.disable.mmr-a,
.disable.mmr-b,
.disable.live-a .team .name,
.disable.live-b .team .name,
.disable.normal-a .team .name,
.disable.normal-b .team .name,
.disable.mmr-a .team .name,
.disable.mmr-b .team .name {
    color: #a3a3a3 !important;
}

.live-a {
    background: #ffccbc;
    border-bottom-color: #cea193;
}

.live-a .event+div,
.live-a .time,
.live-a .others,
.live-a .multiOdds+.multiOdds,
.hdpou-a .live-a .multiOdds>div:nth-of-type(5),
.hdpou-g .live-a .multiOdds>div:nth-of-type(4),
.hdpouSingle-a .live-a .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .live-a .multiOdds>div:nth-of-type(n+2),
.onextwo-a .live-a>div:nth-last-of-type(3),
.correctScore-a .live-a>div:nth-last-of-type(6),
.correctScore-d .live-a>div:nth-last-of-type(5),
.correctScore-b .live-a>div:nth-last-of-type(3),
.oddEven-a .live-a>div:nth-last-of-type(2),
.totalGoal-a .live-a>div:nth-of-type(7),
.halfTimeFullTime-a .live-a>div:nth-last-of-type(3),
.halfTimeFullTime-a .live-a>div:nth-last-of-type(6),
.firstGoalLastGoal-a .live-a>div:nth-last-of-type(3),
.racing-a .live-a>div,
.racing-b .live-a>div,
.racing-c .live-a>div,
.racing-d .live-a>div,
.numberGame-a .live-a>div,
.numberGame-b .live-a>div,
.numberGame-c .live-a>div,
.numberGame-d .live-a>div,
.lotto-c .live-a>div {
    border-color: rgba(206,161,147, 0.5);
}

.live-b {
    background: #ffddd2;
    border-bottom-color: #cea193;
}

.live-b .event+div,
.live-b .time,
.live-b .others,
.live-b .multiOdds+.multiOdds,
.hdpou-a .live-b .multiOdds>div:nth-of-type(5),
.hdpou-g .live-b .multiOdds>div:nth-of-type(4),
.hdpouSingle-a .live-b .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .live-b .multiOdds>div:nth-of-type(n+2),
.onextwo-a .live-b>div:nth-last-of-type(3),
.correctScore-a .live-b>div:nth-last-of-type(6),
.correctScore-d .live-b>div:nth-last-of-type(5),
.correctScore-b .live-b>div:nth-last-of-type(3),
.oddEven-a .live-b>div:nth-last-of-type(2),
.totalGoal-a .live-b>div:nth-of-type(7),
.halfTimeFullTime-a .live-b>div:nth-last-of-type(3),
.halfTimeFullTime-a .live-b>div:nth-last-of-type(6),
.firstGoalLastGoal-a .live-b>div:nth-last-of-type(3),
.racing-a .live-b>div,
.racing-b .live-b>div,
.racing-c .live-b>div,
.racing-d .live-b>div,
.numberGame-a .live-b>div,
.numberGame-b .live-b>div,
.numberGame-c .live-b>div,
.numberGame-d .live-b>div,
.lotto-c .live-b>div {
    border-color: #cea193;
}

.normal-a {
    background: #c6d4f1;
    border-bottom-color: #bbbbbb;
}

.normal-a .event+div,
.normal-a .time,
.normal-a .others,
.normal-a .multiOdds+.multiOdds,
.hdpou-a .normal-a .multiOdds>div:nth-of-type(5),
.hdpou-g .normal-a .multiOdds>div:nth-of-type(4),
.hdpouSingle-a .normal-a .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .normal-a .multiOdds>div:nth-of-type(n+2),
.onextwo-a .normal-a>div:nth-last-of-type(3),
.correctScore-a .normal-a>div:nth-last-of-type(6),
.correctScore-d .normal-a>div:nth-last-of-type(5),
.correctScore-b .normal-a>div:nth-last-of-type(3),
.oddEven-a .normal-a>div:nth-last-of-type(2),
.totalGoal-a .normal-a>div:nth-of-type(7),
.halfTimeFullTime-a .normal-a>div:nth-last-of-type(3),
.halfTimeFullTime-a .normal-a>div:nth-last-of-type(6),
.firstGoalLastGoal-a .normal-a>div:nth-last-of-type(3),
.racing-a .normal-a>div,
.racing-b .normal-a>div,
.racing-c .normal-a>div,
.racing-d .normal-a>div,
.numberGame-a .normal-a>div,
.numberGame-b .normal-a>div,
.numberGame-c .normal-a>div,
.numberGame-d .normal-a>div,
.lotto-c .normal-a>div {
    border-color: #bbbbbb;
}

.normal-b {
    background: #e4e4e4;
    border-bottom-color: #bbbbbb;
}

.normal-b .event+div,
.normal-b .time,
.normal-b .others,
.normal-b .multiOdds+.multiOdds,
.hdpou-a .normal-b .multiOdds>div:nth-of-type(5),
.hdpou-g .normal-b .multiOdds>div:nth-of-type(4),
.hdpouSingle-a .normal-b .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .normal-b .multiOdds>div:nth-of-type(n+2),
.onextwo-a .normal-b>div:nth-last-of-type(3),
.correctScore-a .normal-b>div:nth-last-of-type(6),
.correctScore-d .normal-b>div:nth-last-of-type(5),
.correctScore-b .normal-b>div:nth-last-of-type(3),
.oddEven-a .normal-b>div:nth-last-of-type(2),
.totalGoal-a .normal-b>div:nth-of-type(7),
.halfTimeFullTime-a .normal-b>div:nth-last-of-type(3),
.halfTimeFullTime-a .normal-b>div:nth-last-of-type(6),
.firstGoalLastGoal-a .normal-b>div:nth-last-of-type(3),
.racing-a .normal-b>div,
.racing-b .normal-b>div,
.racing-c .normal-b>div,
.racing-d .normal-b>div,
.numberGame-a .normal-b>div,
.numberGame-b .normal-b>div,
.numberGame-c .normal-b>div,
.numberGame-d .normal-b>div,
.lotto-c .normal-b>div {
    border-color: #bbbbbb;
}

.mmr-a {
    background: #D3D5EC;
    border-bottom-color: #B4B6CA;
}

.mmr-a .event+div,
.mmr-a .time,
.mmr-a .others,
.mmr-a .multiOdds+.multiOdds,
.hdpou-a .mmr-a .multiOdds>div:nth-of-type(5),
.hdpou-g .mmr-a .multiOdds>div:nth-of-type(4),
.hdpouSingle-a .mmr-a .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .mmr-a .multiOdds>div:nth-of-type(n+2),
.onextwo-a .mmr-a>div:nth-last-of-type(3),
.correctScore-a .mmr-a>div:nth-last-of-type(6),
.correctScore-d .mmr-a>div:nth-last-of-type(5),
.correctScore-b .mmr-a>div:nth-last-of-type(3),
.oddEven-a .mmr-a>div:nth-last-of-type(2),
.totalGoal-a .mmr-a>div:nth-of-type(7),
.halfTimeFullTime-a .mmr-a>div:nth-last-of-type(3),
.halfTimeFullTime-a .mmr-a>div:nth-last-of-type(6),
.firstGoalLastGoal-a .mmr-a>div:nth-last-of-type(3),
.racing-a .mmr-a>div,
.racing-b .mmr-a>div,
.racing-c .mmr-a>div,
.racing-d .mmr-a>div,
.numberGame-a .mmr-a>div,
.numberGame-b .mmr-a>div,
.numberGame-c .mmr-a>div,
.numberGame-d .mmr-a>div,
.lotto-c .mmr-a>div {
    border-color: #B4B6CA;
}

.mmr-b {
    background: #E1E3F9;
    border-bottom-color: #B4B6CA;
}

.mmr-b .event+div,
.mmr-b .time,
.mmr-b .others,
.mmr-b .multiOdds+.multiOdds,
.hdpou-a .mmr-b .multiOdds>div:nth-of-type(5),
.hdpou-g .mmr-b .multiOdds>div:nth-of-type(4),
.hdpouSingle-a .mmr-b .multiOdds>div:nth-of-type(n+2),
.hdpouFullHalf-a .mmr-b .multiOdds>div:nth-of-type(n+2),
.onextwo-a .mmr-b>div:nth-last-of-type(3),
.correctScore-a .mmr-b>div:nth-last-of-type(6),
.correctScore-d .mmr-b>div:nth-last-of-type(5),
.correctScore-b .mmr-b>div:nth-last-of-type(3),
.oddEven-a .mmr-b>div:nth-last-of-type(2),
.totalGoal-a .mmr-b>div:nth-of-type(7),
.halfTimeFullTime-a .mmr-b>div:nth-last-of-type(3),
.halfTimeFullTime-a .mmr-b>div:nth-last-of-type(6),
.firstGoalLastGoal-a .mmr-b>div:nth-last-of-type(3),
.racing-a .mmr-b>div,
.racing-b .mmr-b>div,
.racing-c .mmr-b>div,
.racing-d .mmr-b>div,
.numberGame-a .mmr-b>div,
.numberGame-b .mmr-b>div,
.numberGame-c .mmr-b>div,
.numberGame-d .mmr-b>div,
.lotto-c .mmr-b>div {
    border-color: #B4B6CA;
}

.betArea {
    white-space: nowrap;
}

.betArea+.betArea {
    margin-top: 0.167em;
}

.betArea .txt {
    display: inline-block;
    padding-right: 0.3em;
    text-align: left;
    font-weight: bold;
    color: #545454;
}

.betArea .txt .underdog {
    color: #b53f39;
}

.hdpouSingle-a .betArea .txt,
.hdpouFullHalf-a .betArea .txt {
    text-align: center;
}

.betArea .txt.statusChanged {
    -webkit-animation: oddsChangeColor-transparent 2.5s linear infinite normal;
    animation: oddsChangeColor-transparent 2.5s linear infinite normal;
}

.live-a .betInfo,
.live-b .betInfo,
.live-a+.moreBetTypeArea .betInfo,
.live-b+.moreBetTypeArea .betInfo,
.liveScore+.moreBetTypeArea .betInfo {
    background: #ffddd2;
}

.live-a .betInfo .betType,
.live-b .betInfo .betType,
.live-a+.moreBetTypeArea .betInfo .betType,
.live-b+.moreBetTypeArea .betInfo .betType,
.liveScore+.moreBetTypeArea .betInfo .betType {
    color: #b53f39;
}

.live-a .betInfo .betDetial,
.live-b .betInfo .betDetial,
.live-a+.moreBetTypeArea .betInfo .betDetial,
.live-b+.moreBetTypeArea .betInfo .betDetial,
.liveScore+.moreBetTypeArea .betInfo .betDetial {
    border-color: #d06f6a;
}

.oddsTotal {
    background: #ffddd2;
    border: 1px solid #ffddd2;
    text-align: right;
    font-weight: bold;
}

.oddsTotal>div {
    color: rgba(1, 18, 43, 0.7);
}

.oddsTotal .odds {
    color: #01122b;
}

.icon {
    width: 1.25em;
    height: 1.25em;
    border-radius: 3px;
}

.icon.accent {
    background: #b53f39;
}

.icon.live {
    width: 2.25em;
}

.oddsTable.tableGroup-2+.tableGroup-2 .oddsTitle,
.oddsTable.tableGroup-2+.tableGroup-2 .oddsTitleSub,
.oddsTable.tableGroup-2+.tableGroup-3 .oddsTitle,
.oddsTable.tableGroup-2+.tableGroup-3 .oddsTitleSub,
.oddsTable.tableGroup-3+.tableGroup-2 .oddsTitle,
.oddsTable.tableGroup-3+.tableGroup-2 .oddsTitleSub,
.oddsTable.tableGroup-3+.tableGroup-3 .oddsTitle,
.oddsTable.tableGroup-3+.tableGroup-3 .oddsTitleSub,
.oddsTable.tableGroup-2+.tableGroup-2 .normal-a,
.oddsTable.tableGroup-2+.tableGroup-2 .normal-b,
.oddsTable.tableGroup-2+.tableGroup-3 .normal-a,
.oddsTable.tableGroup-2+.tableGroup-3 .normal-b,
.oddsTable.tableGroup-3+.tableGroup-2 .normal-a,
.oddsTable.tableGroup-3+.tableGroup-2 .normal-b,
.oddsTable.tableGroup-3+.tableGroup-3 .normal-a,
.oddsTable.tableGroup-3+.tableGroup-3 .normal-b,
.oddsTable.tableGroup-2+.tableGroup-2 .live-a,
.oddsTable.tableGroup-2+.tableGroup-2 .live-b,
.oddsTable.tableGroup-2+.tableGroup-3 .live-a,
.oddsTable.tableGroup-2+.tableGroup-3 .live-b,
.oddsTable.tableGroup-3+.tableGroup-2 .live-a,
.oddsTable.tableGroup-3+.tableGroup-2 .live-b,
.oddsTable.tableGroup-3+.tableGroup-3 .live-a,
.oddsTable.tableGroup-3+.tableGroup-3 .live-b {
    border-left: 1px solid;
}

.oddsTable:last-child .matchArea:last-child>div:last-child,
.oddsTable.tableGroup-2:last-child .matchArea:last-child>div:last-child,
.oddsTable.tableGroup-2:nth-last-child(2) .matchArea:last-child>div:last-child,
.oddsTable.tableGroup-3:last-child .matchArea:last-child>div:last-child,
.oddsTable.tableGroup-3:nth-last-child(2) .matchArea:last-child>div:last-child,
.oddsTable.tableGroup-3:nth-last-child(3) .matchArea:last-child>div:last-child {
    border-bottom: 0;
}

.oddsTable .matchArea {
    line-height: 1.4;
}

.oddsTable.tableGroup-2,
.oddsTable.tableGroup-3 {
    float: left;
}

.oddsTable.tableGroup-2 .oddsTitle:first-child,
.oddsTable.tableGroup-3 .oddsTitle:first-child {
    border-radius: 0 0 0 0;
}

.oddsTable.tableGroup-2+.tableGroup-2 .oddsTitle,
.oddsTable.tableGroup-2+.tableGroup-2 .oddsTitleSub,
.oddsTable.tableGroup-2+.tableGroup-3 .oddsTitle,
.oddsTable.tableGroup-2+.tableGroup-3 .oddsTitleSub,
.oddsTable.tableGroup-3+.tableGroup-2 .oddsTitle,
.oddsTable.tableGroup-3+.tableGroup-2 .oddsTitleSub,
.oddsTable.tableGroup-3+.tableGroup-3 .oddsTitle,
.oddsTable.tableGroup-3+.tableGroup-3 .oddsTitleSub {
    border-left-color: #3b5174;
    border-radius: 0 0 0 0;
}

.oddsTable.tableGroup-2+.tableGroup-2 .normal-a,
.oddsTable.tableGroup-2+.tableGroup-2 .normal-b,
.oddsTable.tableGroup-2+.tableGroup-3 .normal-a,
.oddsTable.tableGroup-2+.tableGroup-3 .normal-b,
.oddsTable.tableGroup-3+.tableGroup-2 .normal-a,
.oddsTable.tableGroup-3+.tableGroup-2 .normal-b,
.oddsTable.tableGroup-3+.tableGroup-3 .normal-a,
.oddsTable.tableGroup-3+.tableGroup-3 .normal-b {
    border-left-color: #bbbbbb;
}

.oddsTable.tableGroup-2+.tableGroup-2 .live-a,
.oddsTable.tableGroup-2+.tableGroup-2 .live-b,
.oddsTable.tableGroup-2+.tableGroup-3 .live-a,
.oddsTable.tableGroup-2+.tableGroup-3 .live-b,
.oddsTable.tableGroup-3+.tableGroup-2 .live-a,
.oddsTable.tableGroup-3+.tableGroup-2 .live-b,
.oddsTable.tableGroup-3+.tableGroup-3 .live-a,
.oddsTable.tableGroup-3+.tableGroup-3 .live-b {
    border-left-color: #cea193;
}

.oddsTable.tableGroup-2.other-2cols .odds,
.oddsTable.tableGroup-3.other-2cols .odds {
    width: 33.3%;
}

.oddsTable.tableGroup-2.other-5cols .odds,
.oddsTable.tableGroup-3.other-5cols .odds {
    width: 20%;
}

.oddsTable.tableGroup-2 {
    width: 50%;
}

.oddsTable.tableGroup-2+div:not(.tableGroup-2) {
    clear: both;
}

.oddsTable.tableGroup-2.width-3-2 {
    width: 66.666666%;
}

.oddsTable.tableGroup-2.width-3-1 {
    width: 33.333333%;
}

.oddsTable.tableGroup-3 {
    width: 33.333333%;
}

.oddsTable.tableGroup-3+div:not(.tableGroup-3) {
    clear: both;
}

.expandArea {
    padding: 0.833em;
    background: #dfdfdf;
}

.numberGame-a .expandArea,
.numberGame-d .expandArea {
    background: transparent url(../../_global/common/Images/numberGame_expandBg.jpg);
    background-size: 100% auto;
}

.baseArea .betGroup-a,
.baseArea .betGroup-b,
.baseArea .betGroup-c,
.baseArea .betGroup-d {
    width: 100%;
    pointer-events: none;
}

.baseArea .betGroup-a .colWidth,
.baseArea .betGroup-b .colWidth,
.baseArea .betGroup-c .colWidth,
.baseArea .betGroup-d .colWidth,
.baseArea .betGroup-a .colWidth-rest {
    text-align: center;
    vertical-align: middle;
}

.baseArea .betGroup-a .colWidth>div,
.baseArea .betGroup-b .colWidth>div,
.baseArea .betGroup-c .colWidth>div,
.baseArea .betGroup-d .colWidth>div,
.baseArea .betGroup-a .colWidth-rest>div,
.baseArea .betGroup-a .betGroup-b .colWidth-rest>div,
.baseArea .betGroup-a .betGroup-c .colWidth-rest>div,
.baseArea .betGroup-a .betGroup-d .colWidth-rest>div {
    pointer-events: auto;
}

.baseArea .betGroup-a .colWidth:hover .betArea,
.baseArea .betGroup-b .colWidth:hover .betArea,
.baseArea .betGroup-c .colWidth:hover .betArea,
.baseArea .betGroup-d .colWidth:hover .betArea,
.baseArea .betGroup-a .colWidth-rest:hover .betArea,
.baseArea .betGroup-a .colWidth:hover .relativeArea,
.baseArea .betGroup-b .colWidth:hover .relativeArea,
.baseArea .betGroup-c .colWidth:hover .relativeArea,
.baseArea .betGroup-d .colWidth:hover .relativeArea,
.baseArea .betGroup-a .colWidth-rest:hover .relativeArea {
    background: #f5eeb8;
}

.baseArea .betGroup-b,
.baseArea .betGroup-c,
.baseArea .betGroup-d {
    top: 3.43em;
}

.baseArea .betGroup-b .colWidth,
.baseArea .betGroup-c .colWidth,
.baseArea .betGroup-d .colWidth,
.baseArea .betGroup-b .betGroup-a .colWidth-rest,
.baseArea .betGroup-a .betGroup-b .colWidth-rest,
.baseArea .betGroup-c .betGroup-a .colWidth-rest,
.baseArea .betGroup-a .betGroup-c .colWidth-rest,
.baseArea .betGroup-d .betGroup-a .colWidth-rest,
.baseArea .betGroup-a .betGroup-d .colWidth-rest {
    width: 100%;
    height: auto;
}

.baseArea .betGroup-b .colWidth:last-child .betArea,
.baseArea .betGroup-c .colWidth:last-child .betArea,
.baseArea .betGroup-d .colWidth:last-child .betArea,
.baseArea .betGroup-b .betGroup-a .colWidth-rest:last-child .betArea,
.baseArea .betGroup-a .betGroup-b .colWidth-rest:last-child .betArea,
.baseArea .betGroup-c .betGroup-a .colWidth-rest:last-child .betArea,
.baseArea .betGroup-a .betGroup-c .colWidth-rest:last-child .betArea,
.baseArea .betGroup-d .betGroup-a .colWidth-rest:last-child .betArea,
.baseArea .betGroup-a .betGroup-d .colWidth-rest:last-child .betArea {
    border-bottom-width: 0;
}

.baseArea .betGroup-b .colWidth::after,
.baseArea .betGroup-c .colWidth::after,
.baseArea .betGroup-d .colWidth::after,
.baseArea .betGroup-b .betGroup-a .colWidth-rest::after,
.baseArea .betGroup-a .betGroup-b .colWidth-rest::after,
.baseArea .betGroup-c .betGroup-a .colWidth-rest::after,
.baseArea .betGroup-a .betGroup-c .colWidth-rest::after,
.baseArea .betGroup-d .betGroup-a .colWidth-rest::after,
.baseArea .betGroup-a .betGroup-d .colWidth-rest::after {
    content: "";
    display: block;
    clear: both;
}

.baseArea .betGroup-b .betArea,
.baseArea .betGroup-c .betArea,
.baseArea .betGroup-d .betArea,
.baseArea .betGroup-b .relativeArea,
.baseArea .betGroup-c .relativeArea,
.baseArea .betGroup-d .relativeArea {
    float: right;
}

.baseArea {
    position: relative;
    height: 624px;
}

.baseArea .betGroup-a .betArea,
.baseArea .betGroup-b .betArea,
.baseArea .betGroup-c .betArea,
.baseArea .betGroup-d .betArea {
    border-style: solid;
    border-width: 0 0 1px 1px;
}

.baseArea.live {
    background: #ffccbc;
}

.baseArea.live .betGroup-a .betArea,
.baseArea.live .betGroup-b .betArea,
.baseArea.live .betGroup-c .betArea,
.baseArea.live .betGroup-d .betArea {
    background: #ffddd2;
    border-bottom-color: #cea193;
    border-left-color: #cea193;
}

.baseArea.live .betGroup-a .colWidth-rest .betArea:hover,
.baseArea.live .betGroup-b .colWidth-rest .betArea:hover,
.baseArea.live .betGroup-c .colWidth-rest .betArea:hover,
.baseArea.live .betGroup-d .colWidth-rest .betArea:hover {
    background: #ffddd2;
}

.baseArea.normal {
    background: #c6d4f1;
}

.baseArea.normal .betGroup-a .betArea,
.baseArea.normal .betGroup-b .betArea,
.baseArea.normal .betGroup-c .betArea,
.baseArea.normal .betGroup-d .betArea {
    background: #e4e4e4;
    border-bottom-color: #bbbbbb;
    border-left-color: #bbbbbb;
}

.baseArea.normal .betGroup-a .colWidth-rest .betArea:hover,
.baseArea.normal .betGroup-b .colWidth-rest .betArea:hover,
.baseArea.normal .betGroup-c .colWidth-rest .betArea:hover,
.baseArea.normal .betGroup-d .colWidth-rest .betArea:hover {
    background: #e4e4e4;
}

.baseArea [class*="betGroup-"] {
    position: absolute;
}

.baseArea .betGroup-ball {
    top: 3.4em;
    width: 62.5%;
}

.baseArea .betGroup-ball .betArea {
    float: left;
    width: 20%;
    height: 36.5px;
    padding-top: 2.5px;
    text-align: center;
    margin: 0;
}

.baseArea .betGroup-ball .betArea:hover {
    background: #f5eeb8;
}

.baseArea .betGroup-ball .betArea:nth-child(5n+1) {
    clear: both;
}

.baseArea .betGroup-a {
    height: inherit;
}

.baseArea .betGroup-a .colWidth,
.baseArea .betGroup-a .colWidth-rest {
    float: left;
    height: 100%;
}

.baseArea .betGroup-a .colWidth:first-child .betArea,
.baseArea .betGroup-a .colWidth-rest:first-child .betArea {
    border-left-width: 0;
}

.baseArea .betGroup-a .betArea {
    padding: 0.92em 0;
}

.baseArea .betGroup-a .relativeArea {
    height: calc(100% - 3.4em);
}

.baseArea .betGroup-a .colWidth-rest .betArea .txt {
    width: auto;
}

.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet {
    background: transparent;
    cursor: default;
}

.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorDown:hover,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorUp-a:hover,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorUp:hover,
.baseArea .betGroup-a .colWidth-rest .betArea .oddsBet.indicatorDown-a:hover {
    background: #ffaf96;
}

.baseArea .betGroup-b {
    right: 25%;
}

.baseArea .betGroup-b .betArea {
    height: 39px;
    padding-top: 12px;
}

.baseArea .betGroup-b .relativeArea {
    height: 39px;
    width: calc(100% - 12.5%*3);
}

.baseArea .betGroup-c {
    right: 12.5%;
}

.baseArea .betGroup-c .betArea {
    height: 117px;
    padding-top: 50px;
}

.baseArea .betGroup-c .relativeArea {
    height: 116px;
    width: calc(100% - 12.5%*2);
}

.baseArea .betGroup-d {
    right: 0;
}

.baseArea .betGroup-d .colWidth:last-child .betArea,
.baseArea .betGroup-d .betGroup-a .colWidth-rest:last-child .betArea,
.baseArea .betGroup-a .betGroup-d .colWidth-rest:last-child .betArea {
    padding-bottom: 7.98em;
}

.baseArea .betGroup-d .betArea {
    height: 195px;
    padding-top: 90px;
}

.baseArea .betGroup-d .relativeArea {
    height: 194px;
    width: calc(100% - 12.5%);
}

.baseArea::after {
    content: "";
    float: right;
    background: #e8eff5;
    width: 100%;
    height: 5px;
    position: absolute;
    bottom: -3px;
}

.expandArea .baseArea::after {
    background: #dadada;
}

.lotto-statistics {
    line-height: 1.333333333;
    font-size: 12px;
    padding-top: 0.3em;
    margin-left: 0.5em;
    margin-right: 0.25em;
    background-color: #f3dadb;
    text-align: center;
}

.lotto-statistics.blue {
    background-color: #e1e6ef;
}

.lotto-statistics.blue .lotto-statistics__title {
    color: #5975a5;
}

.filterBlock.textGroup .lotto-statistics.filterRow {
    border-left: 0;
}

.lotto-statistics__title {
    text-align: left;
    color: #bd3f42;
    font-weight: bold;
    display: inline-block;
    vertical-align: middle;
}

.lotto-statistics__row {
    vertical-align: middle;
    display: inline-table;
    width: calc( 100% - 6em);
}

.lotto-statistics__col {
    display: table-cell;
    table-layout: fixed;
    padding: 4px;
    text-align: center;
    width: 11.111%;
}

.lotto-statistics__number {
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    display: inline-block;
    font-weight: bold;
    text-align: center;
    color: #ffffff;
    border-radius: 100%;
    background-color: #a00008;
}

.lotto-statistics__number.primary {
    background-color: #0e2b93;
}

.lotto-statistics__percent {
    color: #545454;
    display: inline-block;
}

.lotto-game {
    position: relative;
    background: #ffffff;
    margin-bottom: 1em;
}

.lotto-game::after {
    content: "";
    display: block;
    clear: both;
}

.lotto-game .oddsTitle {
    border-radius: 0 !important;
}

.lotto-game .lotto-wheel {
    width: calc( 100% - 15% - 224px);
    float: left;
}

.lotto-game .lotto-fold {
    background: #bbbbbb;
    color: #ffffff;
    border: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.lotto-game .lotto-fold::before {
    padding: 0;
}

.lotto-wheel .baseArea {
    height: 308px;
}

.lotto-wheel .baseArea.normal {
    background: #d5e0f0;
}

.lotto-wheel .baseArea:after {
    display: none;
}

.lotto-wheel .colWidth,
.lotto-wheel .baseArea .betGroup-a .colWidth-rest,
.baseArea .betGroup-a .lotto-wheel .colWidth-rest {
    width: 9.09091%;
}

.lotto-wheel .colWidth.frozen .checkbox,
.lotto-wheel .baseArea .betGroup-a .frozen.colWidth-rest .checkbox,
.baseArea .betGroup-a .lotto-wheel .frozen.colWidth-rest .checkbox {
    cursor: default;
}

.lotto-wheel .colWidth.frozen .betArea:hover,
.lotto-wheel .baseArea .betGroup-a .frozen.colWidth-rest .betArea:hover,
.baseArea .betGroup-a .lotto-wheel .frozen.colWidth-rest .betArea:hover {
    background: #c6d4f1;
    cursor: default;
}

.lotto-wheel .selectGroup-a .betArea,
.lotto-wheel .selectGroup-b .betArea {
    box-sizing: border-box;
    padding: 0.8em 0;
    height: 35px;
    text-align: center;
    background: #c6d4f1;
    border-style: solid;
    border-width: 0 1px 1px 0;
    border-bottom-color: #bbbbbb;
    border-right-color: #bbbbbb;
}

.lotto-wheel .selectGroup-a .betArea:hover,
.lotto-wheel .selectGroup-b .betArea:hover {
    background: #f5eeb8;
    cursor: pointer;
}

.lotto-wheel .selectGroup-a .betArea.disable,
.lotto-wheel .selectGroup-b .betArea.disable {
    cursor: default;
}

.lotto-wheel .selectGroup-a .betArea.disable:hover,
.lotto-wheel .selectGroup-b .betArea.disable:hover {
    background: #c6d4f1;
}

.lotto-wheel .selectGroup-a .betArea.disable .checkbox,
.lotto-wheel .selectGroup-b .betArea.disable .checkbox {
    cursor: default;
    border-color: #bbbbbb !important;
    background: #cdcdcd !important;
    color: #7c7c7c !important;
}

.lotto-wheel .selectGroup-a .betArea {
    height: 28px;
    padding-top: .5em;
}

.lotto-wheel .selectGroup-a .colWidth,
.lotto-wheel .selectGroup-a .baseArea .betGroup-a .colWidth-rest,
.baseArea .betGroup-a .lotto-wheel .selectGroup-a .colWidth-rest {
    float: left;
}

.lotto-wheel .selectGroup-a .colWidth:last-child .betArea,
.lotto-wheel .selectGroup-a .baseArea .betGroup-a .colWidth-rest:last-child .betArea,
.baseArea .betGroup-a .lotto-wheel .selectGroup-a .colWidth-rest:last-child .betArea {
    border-right: none;
}

.lotto-wheel .selectGroup-a::after {
    content: "";
    display: block;
    clear: both;
}

.lotto-wheel .selectGroup-b .colWidth:last-child .betArea,
.lotto-wheel .selectGroup-b .baseArea .betGroup-a .colWidth-rest:last-child .betArea,
.baseArea .betGroup-a .lotto-wheel .selectGroup-b .colWidth-rest:last-child .betArea {
    border-bottom: none;
}

.lotto-wheel .selectGroup-ball {
    position: absolute;
    top: 28px;
    left: 9.09091%;
    width: 90.90909%;
}

.lotto-wheel .selectGroup-ball .betArea {
    float: left;
    width: 10%;
    height: 32px;
    padding-top: 3px;
    text-align: center;
    margin: 0;
}

.lotto-wheel .selectGroup-ball .betArea:nth-child(10n+1) {
    clear: both;
}

.lotto-wheel .colorBall:before,
.lotto-wheel .colorBall-primary:before,
.lotto-wheel .colorBall-disable:before,
.lotto-wheel .colorBall-primary:before {
    font-family: "iconFont";
    content: "";
    border-radius: 100%;
    -ms-transform: scale(0.7);
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    color: #fff;
    background-color: #bbbbbb;
    position: absolute;
    right: -8px;
    top: -4px;
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    z-index: 12;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    display: none;
}

.lotto-wheel .betArea.selected .colorBall:before,
.lotto-wheel .betArea.selected .colorBall-primary:before,
.lotto-wheel .betArea.selected .colorBall-disable:before,
.lotto-wheel .betArea.selected .colorBall-primary:before {
    display: block;
    background-color: #5dad00;
}

.lotto-wheel .betArea.hover {
    cursor: pointer;
    background: #f5eeb8;
}

.lotto-wheel .betArea.hover .colorBall:before,
.lotto-wheel .betArea.hover .colorBall-primary:before,
.lotto-wheel .betArea.hover .colorBall-disable:before,
.lotto-wheel .betArea.hover .colorBall-primary:before {
    display: block;
}

.lotto-wheel .betArea.disable:hover {
    cursor: default;
    background: transparent;
}

.lotto-wheel .betArea.disable:hover .colorBall:before,
.lotto-wheel .betArea.disable:hover .colorBall-primary:before,
.lotto-wheel .betArea.disable:hover .colorBall-disable:before,
.lotto-wheel .betArea.disable:hover .colorBall-primary:before {
    display: none;
}

.lotto-wheel .betArea.disable .colorBall,
.lotto-wheel .betArea.disable .colorBall-primary,
.lotto-wheel .betArea.disable .colorBall-disable,
.lotto-wheel .betArea.disable .colorBall-primary {
    background-color: #dfdfdf;
    color: #a3a3a3;
}

.lotto-event {
    height: 331px;
    width: 15%;
    float: left;
    background-color: #ececec;
}

.lotto-event .oddsTitle {
    text-align: center;
    border-radius: 0;
}

.lotto-event .filter {
    font-size: 12px;
    padding-top: .22em;
    padding-bottom: .22em;
}

.lotto-event .filter.block-center {
    border: none;
    border-top: 1px solid #cdcdcd;
    border-radius: 0;
    margin-left: -.5em;
    margin-right: -.5em;
    width: calc( 100% + 1em);
    margin-top: .2em;
}

.lotto-event .filter.block-center.icon-arrow-up {
    background-color: #cdcdcd;
}

.lotto-event .filter.icon-arrow-down {
    padding: 0;
}

.lotto-event .filterBlock {
    padding-bottom: 0;
}

.lotto-event .withCheckbox {
    position: relative;
}

.lotto-event .withCheckbox .accent {
    position: absolute;
}

.lotto-event .oddsTitle {
    position: relative;
}

.lotto-event .alertArea {
    top: .2em;
    right: .5em;
    border-left: none;
    padding: 0;
}

.lotto-event__more {
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    top: 331px;
    z-index: 13;
    border: 3px solid #cdcdcd !important;
}

.lotto-event__list .filter {
    box-sizing: border-box;
    margin-left: .25em;
    margin-bottom: 1em;
    width: calc( 100% / 9 - .25em);
    font-weight: bold;
}

.lotto-betSlip {
    box-sizing: border-box;
    background: #ffffff;
    color: black;
    float: left;
    width: 224px;
    height: 331px;
}

.lotto-betSlip .contentArea {
    padding: 0.5em;
}

.lotto-betSlip .oddsTitle,
.lotto-betSlip .oddsTitleSub {
    text-align: center;
    background-color: #233d67;
    border-color: #233d67;
}

.lotto-betSlip .leagueName {
    padding: .5em;
}

.lotto-betSlip .hint-absolute {
    right: -62px;
    margin-top: 2px;
}

.lotto-betSlip .hint-absolute::before {
    left: 127px;
}

.lotto-betSlip .promoAd {
    padding: 1em .5em;
    text-align: center;
    background-color: #ffd4ab;
    color: #7c7c7c;
    font-size: 1.7em;
}

.lotto-betSlip .promoAd .colorBall-disable {
    font-size: 1.1em;
    background-color: #a3a3a3;
    margin-left: .5em;
}

.lotto-betSlip .promoAd .colorBall-disable:before {
    font-family: "iconFont";
    content: "";
    border-radius: 100%;
    -ms-transform: scale(0.7);
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    color: #fff;
    background-color: #5dad00;
    position: absolute;
    right: -8px;
    top: -4px;
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    z-index: 12;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.lotto-betSlip .promoAd .ball {
    padding-top: .5em;
    padding-bottom: .5em;
}

.lotto-betSlip .promoAd .small {
    font-size: 12px;
}

.lotto-betSlip .promoAd .accent {
    color: #f77a00;
    font-weight: bold;
}

.lotto-betSlip .txt-center {
    text-align: center;
    padding-top: 1.5em;
}

.guide {
    border: 1px solid #cdcdcd;
    height: 100%;
}

.guide-title {
    font-weight: bold;
    color: #5574a7;
    font-size: 1.3em;
}

.guide-number {
    padding-bottom: 5px;
}

.guide-block {
    padding: 10px;
}

.guide-draws {
    position: relative;
    background: #e8eff5;
    padding-top: 5px;
    padding-bottom: 5px;
}

.guide-draws:before {
    content: "";
    position: absolute;
    top: 0;
    left: calc(100%/2 - 0.7em);
    border-width: 0.7em;
    border-color: #ffffff transparent transparent transparent;
    border-style: solid;
}

.guide-bets {
    position: relative;
    background: #d5e0f0;
    padding-top: 5px;
    padding-bottom: 5px;
}

.guide-bets:before {
    content: "";
    position: absolute;
    top: 0;
    left: calc(100%/2 - 0.7em);
    border-width: 0.7em;
    border-color: #e8eff5 transparent transparent transparent;
    border-style: solid;
}

.guide .largeBtn {
    display: inline-block;
    float: right;
    width: 85%;
    margin-top: -2px;
}

.guide .ball {
    text-align: center;
    padding: 5px 0 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #cdcdcd;
}

.guide .colorBall,
.guide .colorBall-primary,
.guide .colorBall-disable {
    background-color: #5dad00;
}

.guide .colorBall+.colorBall,
.guide .colorBall-primary+.colorBall,
.guide .colorBall-disable+.colorBall,
.guide .colorBall+.colorBall-primary,
.guide .colorBall-primary+.colorBall-primary,
.guide .colorBall-disable+.colorBall-primary,
.guide .colorBall+.colorBall-disable,
.guide .colorBall-primary+.colorBall-disable,
.guide .colorBall-disable+.colorBall-disable {
    margin-left: 1em;
}

.liveCasino {
    margin-bottom: 0.5em;
}

.liveCasino .contentArea {
    background: #cdcdcd;
    width: 100%;
    height: 250px;
    text-align: center;
}

.liveCasino iframe {
    width: 100%;
    height: 100%;
    max-width: 774px;
}

.promotionBoard {
    background: #cdcdcd;
    border-radius: 3px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding: 0.5em 1.2em;
    overflow: hidden;
    position: relative;
    margin-bottom: 0.5em;
}

.promotionBoard .icon-clear {
    position: absolute;
    right: .1em;
    top: .1em;
}

.promotionBoard-Items {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    margin: 0 1px;
    text-align: center;
}

.promotionBoard-img {
    border-radius: 3px;
    border: 1px solid #a3a3a3;
    cursor: pointer;
    width: 193px;
}

.promotionBoard-img:hover {
    -webkit-filter: brightness(1.2);
    filter: brightness(1.2);
}

.oddsTableNav,
.moreBetTypeNav {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    border-bottom: 3px solid #5574a7;
    margin-bottom: 0.2em;
}

.oddsTableNav-Item,
.moreBetTypeNav-Item {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 3px;
    border-radius: 3px;
    background: #7c7c7c;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.oddsTableNav-Item+.oddsTableNav-Item,
.moreBetTypeNav-Item+.oddsTableNav-Item,
.oddsTableNav-Item+.moreBetTypeNav-Item,
.moreBetTypeNav-Item+.moreBetTypeNav-Item {
    margin-left: 4px;
}

.oddsTableNav-Item:hover,
.moreBetTypeNav-Item:hover {
    background: #898989;
}

.active.oddsTableNav-Item,
.active.moreBetTypeNav-Item {
    background: #5574a7;
    font-weight: bold;
    cursor: default;
    border-radius: 3px 3px 0 0;
    padding: 3px 3px 6px;
}

.moreBetTypeArea,
.multiOdds-moreBetType {
    padding: 0.833em;
}

.moreBetTypeArea .betTypeTitle,
.multiOdds-moreBetType .betTypeTitle {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    background: #c5c5c5;
    border-bottom: 1px solid #acacac;
    color: rgba(0, 0, 0, 0.75);
}

.moreBetTypeArea .betTypeTitle .betCol,
.multiOdds-moreBetType .betTypeTitle .betCol {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.moreBetTypeArea .betTypeTitle .betCol+.betCol,
.multiOdds-moreBetType .betTypeTitle .betCol+.betCol {
    border-left-color: #929292;
}

.moreBetTypeArea .betCol,
.multiOdds-moreBetType .betCol {
    text-align: center;
}

.moreBetTypeArea .betCol+.betCol,
.multiOdds-moreBetType .betCol+.betCol {
    border-left: 1px solid #acacac;
}

.moreBetTypeArea .betCol.txt,
.moreBetTypeArea .betCol.match,
.multiOdds-moreBetType .betCol.txt,
.multiOdds-moreBetType .betCol.match {
    text-align: left;
}

.moreBetTypeArea .betCol .dropdown,
.multiOdds-moreBetType .betCol .dropdown {
    text-align: left;
}

.moreBetTypeArea .betTypeContent,
.multiOdds-moreBetType .betTypeContent {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    background: #e5ecf5;
    border-bottom: 1px solid #acacac;
}

.moreBetTypeArea .betTypeContent:hover,
.multiOdds-moreBetType .betTypeContent:hover {
    background: #f5eeb8 !important;
}

.live-a+.moreBetTypeArea .betTypeContent,
.live-a .multiOdds-moreBetType .betTypeContent,
.live-b+.moreBetTypeArea .betTypeContent,
.live-b .multiOdds-moreBetType .betTypeContent,
.liveScore+.moreBetTypeArea .betTypeContent,
.liveScore .multiOdds-moreBetType .betTypeContent {
    background: #ffe9e1;
}

.moreBetTypeArea .betTypeContent .betCol.txt,
.multiOdds-moreBetType .betTypeContent .betCol.txt {
    color: #545454;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.moreBetTypeArea .betTypeContent .betCol.match,
.multiOdds-moreBetType .betTypeContent .betCol.match {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.moreBetTypeArea .betTypeContent .betCol.match>div,
.multiOdds-moreBetType .betTypeContent .betCol.match>div {
    font-weight: bold;
}

.moreBetTypeArea .betTypeContent .betCol.match .score,
.multiOdds-moreBetType .betTypeContent .betCol.match .score {
    width: 3.5em;
    vertical-align: middle;
    text-align: center;
}

.moreBetTypeArea .betTypeContent .betCol.match .teamName>span,
.multiOdds-moreBetType .betTypeContent .betCol.match .teamName>span {
    display: block;
}

.moreBetTypeArea .betTypeContent .betCol.match .teamName .accent,
.multiOdds-moreBetType .betTypeContent .betCol.match .teamName .accent {
    color: #b53f39;
}

.moreBetTypeArea .betTypeContent .betCol-title,
.multiOdds-moreBetType .betTypeContent .betCol-title {
    background: #c5c5c5;
    color: rgba(0, 0, 0, 0.75);
}

.moreBetTypeArea .betTypeContent .betCol-title__txt,
.multiOdds-moreBetType .betTypeContent .betCol-title__txt {
    text-align: left;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.moreBetTypeArea .betTypeContent .betCol-title__width,
.multiOdds-moreBetType .betTypeContent .betCol-title__width {
    width: 85%;
    float: left;
}

.moreBetTypeArea .betTypeContent .ballhead,
.multiOdds-moreBetType .betTypeContent .ballhead {
    float: right;
}

.moreBetTypeArea .betTypeContent .betCol-separate,
.multiOdds-moreBetType .betTypeContent .betCol-separate {
    padding-left: 0.5em;
    padding-right: 0.5em;
    -webkit-box-flex: 1 0 auto;
    -webkit-flex: 1 0 auto;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
    box-sizing: border-box;
}

.moreBetTypeArea .betTypeContent .betCol-separate .betArea .txt,
.multiOdds-moreBetType .betTypeContent .betCol-separate .betArea .txt {
    float: left;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0;
    max-width: calc(100% - 3.8em);
}

.moreBetTypeArea .betTypeContent .betCol-separate .betArea .oddsBet,
.multiOdds-moreBetType .betTypeContent .betCol-separate .betArea .oddsBet {
    float: right;
}

.moreBetTypeArea .betTypeContent[class*="child-col-"],
.multiOdds-moreBetType .betTypeContent[class*="child-col-"] {
    display: table;
    table-layout: fixed;
    width: 100%;
}

.moreBetTypeArea .betTypeContent[class*="child-col-"] .betCol,
.multiOdds-moreBetType .betTypeContent[class*="child-col-"] .betCol {
    display: table-cell;
    vertical-align: middle;
}

.moreBetTypeArea .twoFrame-a,
.multiOdds-moreBetType .twoFrame-a {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.moreBetTypeArea .twoFrame-a-part,
.multiOdds-moreBetType .twoFrame-a-part {
    -webkit-box-flex: 0 0 49.8%;
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
    padding-right: 4px;
}

.moreBetTypeArea .twoFrame-a-part:last-child,
.multiOdds-moreBetType .twoFrame-a-part:last-child {
    padding-right: 0;
}

.moreBetTypeArea .oneSet-c .setCol+.setCol .trigger,
.moreBetTypeArea .twoSet-a .setCol+.setCol .trigger,
.moreBetTypeArea .threeSet-a .setCol+.setCol .trigger,
.multiOdds-moreBetType .oneSet-c .setCol+.setCol .trigger,
.multiOdds-moreBetType .twoSet-a .setCol+.setCol .trigger,
.multiOdds-moreBetType .threeSet-a .setCol+.setCol .trigger {
    border-left: 1px solid #3b5174;
}

.moreBetTypeArea .oneSet-c .setCol+.setCol .betTypeTitle,
.moreBetTypeArea .twoSet-a .setCol+.setCol .betTypeTitle,
.moreBetTypeArea .threeSet-a .setCol+.setCol .betTypeTitle,
.multiOdds-moreBetType .oneSet-c .setCol+.setCol .betTypeTitle,
.multiOdds-moreBetType .twoSet-a .setCol+.setCol .betTypeTitle,
.multiOdds-moreBetType .threeSet-a .setCol+.setCol .betTypeTitle {
    border-left: 1px solid #929292;
}

.moreBetTypeArea .oneSet-c .setCol+.setCol .betTypeContent,
.moreBetTypeArea .twoSet-a .setCol+.setCol .betTypeContent,
.moreBetTypeArea .threeSet-a .setCol+.setCol .betTypeContent,
.multiOdds-moreBetType .oneSet-c .setCol+.setCol .betTypeContent,
.multiOdds-moreBetType .twoSet-a .setCol+.setCol .betTypeContent,
.multiOdds-moreBetType .threeSet-a .setCol+.setCol .betTypeContent {
    border-left: 1px solid #acacac;
}

.moreBetTypeArea .oddsBet,
.multiOdds-moreBetType .oddsBet {
    min-width: 2em;
}

.moreBetTypeArea .betTypeName,
.multiOdds-moreBetType .betTypeName {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 2px 4px;
}

.moreBetTypeArea .betTypeName .accent,
.multiOdds-moreBetType .betTypeName .accent {
    color: #ffd330;
    font-weight: bold;
}

.moreBetTypeArea .betTypeName .text-nowrap,
.multiOdds-moreBetType .betTypeName .text-nowrap {
    white-space: nowrap;
}

.moreBetTypeArea {
    background: #dfdfdf;
    border-bottom: 1px solid #a3a3a3;
}

.multiOdds-moreBetType {
    border-top: 1px solid #bbbbbb;
}

.betTypeHeader {
    background: #5574a7;
    color: rgba(255, 255, 255, 0.75);
    border: 1px solid #5574a7;
    border-bottom-color: #3b5174;
    cursor: pointer;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.betTypeHeader:hover {
    background: #6582b1;
}

.betTypeHeader:hover .trigger {
    background: linear-gradient(to bottom, #879fc9 0%, #6582b1 100%);
    background: -webkit-linear-gradient(bottom, #6582b1 0%, #879fc9 100%);
}

.betTypeHeader .icon-help {
    margin: 3px;
}

.moreBetTypeNav li {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    max-width: 20%;
    padding: 3px;
    border-radius: 3px;
    background: #7c7c7c;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.moreBetTypeNav li+li {
    margin-left: 4px;
}

.moreBetTypeNav li:hover {
    background: #898989;
}

.moreBetTypeNav li.active {
    background: #5574a7;
    font-weight: bold;
    cursor: default;
    border-radius: 3px 3px 0 0;
    padding: 3px 3px 6px;
}

.moreBetTypeNav-Item {
    max-width: 20%;
}

.moreBetType-filter {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    background: #3b5174;
    color: #ffffff;
    margin: -0.2em 0 0.2em;
    padding: 0.3em 0.5em;
}

.moreBetType-filter-container {
    font-weight: bold;
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
}

.liveScore {
    position: relative;
    overflow: hidden;
    padding: 0.833em calc(10% - 50px) 0.833em 10%;
    background: #dfdfdf;
    border-bottom: 1px solid #a3a3a3 !important;
}

.liveScore_container {
    display: table;
    position: relative;
    width: 100%;
    text-align: center;
    color: #ffffff;
    background: #636363;
    border-radius: 3px;
}

.liveScore_container .title,
.liveScore_container .content {
    display: table-row-group;
}

.liveScore_container .status,
.liveScore_container .accountTable .status-smaller,
.accountTable .liveScore_container .status-smaller,
.liveScore_container .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .liveScore_container .status-smaller,
.liveScore_container .serve,
.liveScore_container .event-name,
.liveScore_container .score {
    display: table-cell;
    padding: 0.125rem;
    vertical-align: middle;
}

.liveScore_container .status,
.liveScore_container .accountTable .status-smaller,
.accountTable .liveScore_container .status-smaller,
.liveScore_container .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .liveScore_container .status-smaller {
    width: 2em;
}

.liveScore_container .serve {
    width: 1em;
}

.liveScore_container .serve .current {
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 100%;
    background: #ffd330;
}

.liveScore_container .event-name {
    width: 44%;
    text-align: left;
}

.liveScore_container .event-name>span {
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.liveScore_container .event-name .group {
    display: inline-block;
    text-align: right;
    margin-left: -4px;
}

.liveScore_container .score {
    width: auto;
    white-space: nowrap;
}

.liveScore_container .current {
    color: #ffd330;
}

.liveScore_container .text-highlight {
    color: #ffd330;
    font-weight: bold;
}

.liveScore_container li:last-child:not(:nth-child(-n+8)) {
    width: 10%;
}

.liveScore_container ul.content .event-name {
    font-weight: bold;
}

.liveScore_container ul.content .event-name .group {
    width: 35%;
}

.liveScore_container ul.content .event-name>span:nth-last-child(2) {
    width: 60%;
}

.liveScore_container ul.title {
    border-radius: inherit;
}

.liveScore_container ul.title li {
    background: linear-gradient(to bottom, #545454 0%, #474747 100%);
    background: -webkit-linear-gradient(bottom, #474747 0%, #545454 100%);
}

.liveScore_container ul.title li:first-child {
    border-top-left-radius: inherit;
}

.liveScore_container ul.title li:last-child {
    border-top-right-radius: inherit;
}

.liveScore_container ul.title+ul li {
    border-bottom: solid thin #7c7c7c;
}

.liveScore_container ul.title .score {
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}

.liveScore_container ul.title .event-name div {
    display: inline-block;
    margin-right: 1em;
}

.liveScore_container ul.title .event-name div:first-child {
    margin-right: 3em;
}

.liveScore_container ul.title .event-name span {
    display: inline;
}

.liveScore_container ul.title .event-name span+span {
    margin-left: 0.25rem;
}

.liveScore [class*="icon"] {
    display: inline-block;
    vertical-align: middle;
    background: #ececec;
    border-radius: 2px;
}

.liveScore [class*="icon"]::before {
    display: inline-block;
    font-size: 1rem;
    width: 1em;
    height: 1em;
    line-height: 1;
}

.liveScore .icon-powerPlayer {
    color: #bbbbbb;
}

.liveScore .icon-powerPlay,
.liveScore .icon-redCross {
    color: #b53f39;
}

.liveScore .icon-baseballBat,
.liveScore .icon-football {
    color: #323232;
}

.caption {
    padding: 0.833em;
    margin-bottom: 0.5em;
}

.mainLayout .mainArea .caption {
    padding-top: 0.3em;
    padding-bottom: 0.3em;
}

.caption .mainTitle {
    font-size: 1.167em;
    font-weight: bold;
    margin-top: .2em;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.caption .mainTitle::before {
    margin-right: 0.3em;
    margin-top: -.1em;
    float: left;
}

.static .caption .mainTitle::before,
.inIframe .caption .mainTitle::before,
.account .caption .mainTitle::before {
    display: none;
}

.account .caption .mainTitle.icon-betList::before,
.account .caption .mainTitle.icon-statement::before,
.account .caption .mainTitle.icon-result::before,
.account .caption .mainTitle.icon-message::before,
.account .caption .mainTitle.icon-preferences::before,
.account .caption .mainTitle.icon-account::before,
.account .caption .mainTitle.icon-lock::before {
    background-image: none;
    display: block;
    font-size: 1.5em;
    margin-top: -.24em;
}

.caption .icon-arrowLong_left {
    float: left;
    font-size: 1em;
    margin-right: 0.25em;
}

.caption+.filterBlock {
    border-radius: 3px 3px 0 0;
}

.caption .filterArea {
    float: right;
}

.caption .filterArea>button.primary::before {
    content: "\00a0";
    width: 0;
    padding: 0;
}

.caption::after {
    content: "";
    display: block;
    clear: both;
}

.caption-fixed {
    position: fixed;
    -webkit-backface-visibility: hidden;
    z-index: 17;
    background: #e8eff5;
}

.mainLayout .mainArea .caption-fixed {
    margin-top: -0.5em;
    padding-top: 0.8em;
    padding-bottom: 0.8em;
    box-sizing: border-box;
}

.caption--fixed {
    position: fixed;
    z-index: 17;
    display: flex;
    height: 36px;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-color: #e8eff5;
    width: 100%;
    max-width: inherit;
}

.caption--fixed::before {
    content: "";
    flex: 0 0 auto;
    width: 234px;
    background-color: red;
}

.caption--fixed::after {
    flex: 0 0 auto;
    width: 234px;
    padding-right: 0.833em;
    background-color: red;
}

.secondMini::after,
.firstMini::before {
    width: 3.333em;
}

.caption__info {
    flex: 1;
    display: flex;
    min-width: 810px;
    align-items: center;
    justify-content: space-between;
    padding-right: 0.833em;
    box-sizing: border-box;
    background-color: cyan;
}

.progress {
    display: inline-block;
    margin-left: 0.25em;
}

.progress-item {
    width: 8px;
    height: 8px;
    margin-right: 0.167em;
    background-color: #5574a7;
    display: inline-block;
    border-radius: 100%;
    -webkit-animation: loading-a 1s infinite normal;
    animation: loading-a 1s infinite normal;
}

.progress-item:nth-child(0) {
    -webkit-animation-delay: 0.45s;
    animation-delay: 0.45s;
}

.progress-item:nth-child(1) {
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
}

.progress-item:nth-child(2) {
    -webkit-animation-delay: 0.75s;
    animation-delay: 0.75s;
}

.progress-item:nth-child(3) {
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
}

.progress-item:nth-child(4) {
    -webkit-animation-delay: 1.05s;
    animation-delay: 1.05s;
}

.heading,
.heading-default,
.heading-noMoving {
    color: #ffffff;
    border-radius: 3px 3px 0 0;
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
    position: relative;
}

.heading::before,
.heading-default::before,
.heading-noMoving::before {
    font-size: 1.4em;
    float: left;
    line-height: 1;
    position: relative;
    top: .1em;
}

[class*="icon-sport"].heading::before,
[class*="icon-sport"].heading-default::before,
[class*="icon-sport"].heading-noMoving::before {
    font-size: 1em;
}

@media screen and (min-width: 0\0) {
    [class*="icon-sport"].heading::before,
    [class*="icon-sport"].heading-default::before,
    [class*="icon-sport"].heading-noMoving::before {
        font-size: 0.7em;
    }
}

.icon-favorite.heading::before,
.icon-favorite.heading-default::before,
.icon-favorite.heading-noMoving::before {
    -ms-transform: translate(0, 0.05em) scale(1.3);
    -webkit-transform: translate(0, 0.05em) scale(1.3);
    transform: translate(0, 0.05em) scale(1.3);
}

.icon-favorite.added.heading::before,
.icon-favorite.added.heading-default::before,
.icon-favorite.added.heading-noMoving::before {
    color: #ffd330;
}

.darken.heading,
.darken.heading-default,
.darken.heading-noMoving {
    background: #233d67;
}

.darken.heading:hover,
.darken.heading-default:hover,
.darken.heading-noMoving:hover {
    background: #233d67;
}

.secondary.heading,
.secondary.heading-default,
.secondary.heading-noMoving {
    background: #989898;
}

.third.heading,
.third.heading-default,
.third.heading-noMoving {
    background: linear-gradient(to bottom, #b53f39 0%, #ab241e 100%);
    background: -webkit-linear-gradient(bottom, #ab241e 0%, #b53f39 100%);
}

.heading [class^="text"],
.heading-default [class^="text"],
.heading-noMoving [class^="text"] {
    font-weight: bold;
}

.heading .lighter,
.heading-default .lighter,
.heading-noMoving .lighter {
    font-weight: lighter;
}

.heading>.text,
.heading-default>.text,
.heading-noMoving>.text,
.accountTable .heading>.text-auto,
.accountTable .heading-default>.text-auto,
.accountTable .heading-noMoving>.text-auto,
.accountTable-verticalAlignTop .heading>.text-auto,
.accountTable-verticalAlignTop .heading-default>.text-auto,
.accountTable-verticalAlignTop .heading-noMoving>.text-auto {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.heading .text-group,
.heading-default .text-group,
.heading-noMoving .text-group {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.heading .team-vs,
.heading-default .team-vs,
.heading-noMoving .team-vs {
    -webkit-box-flex: 0 0 auto;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    margin: 0 .5em;
}

.heading .team,
.heading-default .team,
.heading-noMoving .team {
    -webkit-box-flex: 0 1 auto;
    -webkit-flex: 0 1 auto;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    position: relative;
    overflow: hidden;
}

.heading .team-name,
.heading-default .team-name,
.heading-noMoving .team-name {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.team-color+.team-name {
    padding-left: 1.5em;
}

.heading .team-color,
.heading-default .team-color,
.heading-noMoving .team-color {
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -.5em;
    width: 1em;
    height: 1em;
    border-radius: 100%;
    box-shadow: inset 1px 1px 0 1px rgba(0, 0, 0, 0.5);
}

.heading:hover,
.multiple .heading.swap:hover {
    background: linear-gradient(to bottom, #879fc9 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #879fc9 100%);
}

.heading:hover::after,
.multiple .heading.swap:hover::after {
    color: #ffffff !important;
}

.heading::after,
.fixed-viewport-bottom.collapse .heading::after,
.collapse .heading::after,
.fixed-viewport-bottom .heading::after {
    color: #adbed6;
    -ms-transform: translate(-0.1em, 0.15em) scale(1.25);
    -webkit-transform: translate(-0.1em, 0.15em) scale(1.25);
    transform: translate(-0.1em, 0.15em) scale(1.25);
    position: absolute;
    right: 0.5em;
    top: .7em;
    line-height: 1;
}

.heading::after,
.fixed-viewport-bottom.collapse .heading::after {
    content: "";
}

.collapse .heading::after,
.fixed-viewport-bottom .heading::after {
    content: "";
}

.heading {
    cursor: pointer;
    padding: 0.61em 0.7em;
}

.heading .text,
.heading .accountTable .text-auto,
.accountTable .heading .text-auto,
.heading .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .heading .text-auto {
    display: inline-block;
    width: calc(100% - 2.5em);
    padding: .1em 0 .1em .3em;
}

.mini .widgetPanel.active .heading.icon-sportCrossSelling .text,
.mini .widgetPanel.active .heading.icon-sportCrossSelling .accountTable .text-auto,
.accountTable .mini .widgetPanel.active .heading.icon-sportCrossSelling .text-auto,
.mini .widgetPanel.active .heading.icon-sportCrossSelling .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .mini .widgetPanel.active .heading.icon-sportCrossSelling .text-auto {
    padding-left: 1.5em;
}

.mini .heading::before,
.mini .group .heading.current::before {
    font-size: 1.75em;
    position: relative;
    top: .1em;
    left: .05em;
}

@media screen and (min-width: 0\0) {
    .mini .heading::before,
    .mini .group .heading.current::before {
        font-size: 0.6em;
    }
}

.mini .heading[class*="icon-sport"]::before {
    top: 0;
    left: -.1em;
}

.mini .heading {
    height: 3.333em;
    background-image: none;
    float: none;
    cursor: pointer;
}

.mini .heading:hover {
    background: #adbed6;
}

.mini .heading.swap {
    display: none;
}

.mini .heading::after {
    display: none;
}

.mini .heading>.text,
.mini .accountTable .heading>.text-auto,
.accountTable .mini .heading>.text-auto,
.mini .accountTable-verticalAlignTop .heading>.text-auto,
.accountTable-verticalAlignTop .mini .heading>.text-auto {
    display: none;
}

.mini .widgetPanel.active .heading {
    position: relative;
    z-index: 23;
}

.mini .widgetPanel.active .heading.current {
    background: #233d67;
    width: 224px;
    color: #ffffff;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    padding-left: 0.3em;
    padding-right: 0.3em;
}

.mini .widgetPanel.active .heading.current .text,
.mini .widgetPanel.active .heading.current .accountTable .text-auto,
.accountTable .mini .widgetPanel.active .heading.current .text-auto,
.mini .widgetPanel.active .heading.current .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .mini .widgetPanel.active .heading.current .text-auto {
    display: inline-block;
    margin-top: 0.3em;
}

.mini.sidebar-first .widgetPanel.active .heading.current {
    left: 3.333em;
}

.mini.sidebar-second .widgetPanel.active .heading.current {
    right: 224px;
}

.mini .widgetPanel.active.fixed-viewport-bottom .heading {
    width: 224px;
}

.mini .widgetPanel.collapse .heading::after {
    display: none;
}

.group .heading {
    width: 50%;
    float: left;
    background-image: none;
    color: #4b5d7b;
}

.group .heading.current {
    color: #ffffff;
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
}

.group .heading.current:hover {
    background: linear-gradient(to bottom, #879fc9 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #879fc9 100%);
}

.group .heading.current::after {
    display: block;
}

.group .heading:hover {
    background: #cfd6de;
}

.group .heading::after {
    display: none;
}

.mini .group .heading {
    width: 100%;
    color: #ffffff;
}

.mini .group .heading.current {
    background-image: none;
}

.mini .group .heading.current::after {
    display: none;
}

.mini .group .heading:hover {
    background: #adbed6;
}

.multiple .heading {
    position: relative;
}

.multiple .heading::before {
    position: relative;
    z-index: 8;
}

.multiple .heading::after {
    display: none;
}

.multiple .heading .text,
.multiple .heading .accountTable .text-auto,
.accountTable .multiple .heading .text-auto,
.multiple .heading .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .multiple .heading .text-auto {
    width: 90%;
    position: relative;
    z-index: 8;
}

.multiple .heading.current {
    background: #879dc2;
}

.multiple .heading.icon-sportCup,
.multiple .heading.icon-sportOlympics {
    background: #5574a7;
    border-top: 1px solid #4c6896;
}

.multiple .heading.icon-sportCup:hover,
.multiple .heading.icon-sportOlympics:hover {
    background: #879dc2;
}

.mini .multiple .heading {
    border-top: 0;
}

.mini .multiple .heading:hover {
    background: #adbed6;
}

.mini .multiple .heading.current,
.mini .multiple .heading.currentMain {
    cursor: default;
    background: #01122b;
}

.mini .multiple .heading.current::before,
.mini .multiple .heading.currentMain::before {
    color: #ffffff;
}

.mini .multiple .heading.current::after,
.mini .multiple .heading.currentMain::after {
    display: none;
}

.mini .widgetPanel.active.multiple .heading.current,
.mini .widgetPanel.active.multiple .heading.currentMain {
    left: 0;
    background: #01122b;
}

.mini .widgetPanel.active.multiple .heading.current>.text,
.mini .widgetPanel.active.multiple .accountTable .heading.current>.text-auto,
.accountTable .mini .widgetPanel.active.multiple .heading.current>.text-auto,
.mini .widgetPanel.active.multiple .accountTable-verticalAlignTop .heading.current>.text-auto,
.accountTable-verticalAlignTop .mini .widgetPanel.active.multiple .heading.current>.text-auto,
.mini .widgetPanel.active.multiple .heading.currentMain>.text,
.mini .widgetPanel.active.multiple .accountTable .heading.currentMain>.text-auto,
.accountTable .mini .widgetPanel.active.multiple .heading.currentMain>.text-auto,
.mini .widgetPanel.active.multiple .accountTable-verticalAlignTop .heading.currentMain>.text-auto,
.accountTable-verticalAlignTop .mini .widgetPanel.active.multiple .heading.currentMain>.text-auto {
    display: none;
}

.personalAccount .heading {
    color: #545454;
    background-image: none;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
    position: relative;
}

.personalAccount .heading .text::after,
.personalAccount .heading .accountTable .text-auto::after,
.accountTable .personalAccount .heading .text-auto::after,
.personalAccount .heading .accountTable-verticalAlignTop .text-auto::after,
.accountTable-verticalAlignTop .personalAccount .heading .text-auto::after {
    content: "";
    font-size: 2em;
    position: absolute;
    right: 0.2em;
    top: 0.1em;
    color: #b3b3b3;
    line-height: 1;
}

.personalAccount .heading::after {
    display: none;
}

.personalAccount.openContent .heading {
    z-index: 28;
}

.personalAccount.openContent .heading .text::after,
.personalAccount.openContent .heading .accountTable .text-auto::after,
.accountTable .personalAccount.openContent .heading .text-auto::after,
.personalAccount.openContent .heading .accountTable-verticalAlignTop .text-auto::after,
.accountTable-verticalAlignTop .personalAccount.openContent .heading .text-auto::after {
    content: "";
}

.mini .personalAccount .heading {
    background: #ffffff;
}

.mini .personalAccount .heading .text::after,
.mini .personalAccount .heading .accountTable .text-auto::after,
.accountTable .mini .personalAccount .heading .text-auto::after,
.mini .personalAccount .heading .accountTable-verticalAlignTop .text-auto::after,
.accountTable-verticalAlignTop .mini .personalAccount .heading .text-auto::after {
    display: none;
}

.mini .personalAccount.active .heading.current {
    background: #ffffff;
    color: #545454;
    width: 25em;
}

.memberBalance .heading {
    font-weight: bold;
    background-image: none;
    color: #545454;
}

.memberBalance .heading::after {
    display: none;
}

.favorite .heading::after {
    display: none;
}

.mini .widgetPanel.favorite.active .heading.current {
    left: 0;
    width: 100%;
    background: none;
    box-shadow: none;
    cursor: pointer;
    padding-left: 0.7em;
}

.mini .widgetPanel.favorite.active .heading.current:hover {
    background: #adbed6;
}

.sportsMenu .heading.icon-favorite,
.sportsMenu .heading .icon-favorite {
    display: none;
}

.sportsMenu .heading.swap {
    display: none;
}

.sportsMenu.collapse .heading[class*="icon-sport"] {
    display: none;
}

.sportsMenu.collapse .heading.swap {
    display: block;
}

.mini .sportsMenu .heading {
    width: 3.333em;
    height: 3.333em;
    left: 0;
    box-shadow: none;
}

.mini .sportsMenu.active .heading {
    z-index: 24;
}

.mini .sportsMenu.active .heading.current {
    width: 3.333em;
    box-shadow: none;
    padding-left: 0.7em;
}

.mini .sportsMenu.collapse .heading[class*="icon-sport"] {
    display: block;
}

.mini .sportsMenu.collapse .heading.swap {
    display: none;
}

.mini .miniCasino.active .heading {
    bottom: 220px;
}

.mini .miniCasino.miniCasino-lobby.active .heading {
    bottom: 120px;
}

.mini .miniCasino.miniCasino-game.active .heading {
    bottom: 280px;
}

.heading-default::before,
.heading-noMoving::before {
    margin-top: .4em;
}

[class*="icon-"].heading-default,
[class*="icon-"].heading-noMoving {
    padding-left: 0.833em;
}

[class*="icon-"].heading-default .text,
[class*="icon-"].heading-noMoving .text,
[class*="icon-"].heading-default .accountTable .text-auto,
.accountTable [class*="icon-"].heading-default .text-auto,
[class*="icon-"].heading-noMoving .accountTable .text-auto,
.accountTable [class*="icon-"].heading-noMoving .text-auto,
[class*="icon-"].heading-default .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop [class*="icon-"].heading-default .text-auto,
[class*="icon-"].heading-noMoving .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop [class*="icon-"].heading-noMoving .text-auto {
    padding-left: .5em;
}

.heading-default .text,
.heading-noMoving .text,
.heading-default .accountTable .text-auto,
.accountTable .heading-default .text-auto,
.heading-noMoving .accountTable .text-auto,
.accountTable .heading-noMoving .text-auto,
.heading-default .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .heading-default .text-auto,
.heading-noMoving .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .heading-noMoving .text-auto,
.heading-default .text-group,
.heading-noMoving .text-group {
    line-height: 2.5;
}

.heading-default .text,
.heading-noMoving .text,
.heading-default .accountTable .text-auto,
.accountTable .heading-default .text-auto,
.heading-noMoving .accountTable .text-auto,
.accountTable .heading-noMoving .text-auto,
.heading-default .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .heading-default .text-auto,
.heading-noMoving .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .heading-noMoving .text-auto {
    padding: 0 0.833em;
}

.heading-default .text-group,
.heading-noMoving .text-group {
    padding-right: 0.833em;
}

.heading-default .floatLeft,
.heading-noMoving .floatLeft {
    float: left;
}

.heading-default .floatLeft .glyphIcon-large,
.heading-noMoving .floatLeft .glyphIcon-large {
    border-right-width: 1px;
}

.heading-default .floatLeft .glyphIcon-large:first-child,
.heading-noMoving .floatLeft .glyphIcon-large:first-child {
    border-radius: 3px 0 0 0;
}

.heading-default .floatRight,
.heading-noMoving .floatRight {
    float: right;
}

.heading-default .floatRight .glyphIcon-large,
.heading-noMoving .floatRight .glyphIcon-large {
    border-left-width: 1px;
}

.heading-default .floatRight .glyphIcon-large:last-child,
.heading-noMoving .floatRight .glyphIcon-large:last-child {
    border-radius: 0 3px 0 0;
}

.heading-default::after,
.heading-noMoving::after {
    content: "";
    display: block;
    clear: both;
}

.heading-default .glyphIcon-large,
.heading-noMoving .glyphIcon-large {
    width: 2.5em;
    height: 100%;
    text-align: center;
    border: 0 solid #526fa0;
    border-radius: 0;
    padding: .25em 0;
}

.heading-default .glyphIcon-large:hover,
.heading-noMoving .glyphIcon-large:hover {
    color: #ffffff;
    background: linear-gradient(to bottom, #879fc9 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #879fc9 100%);
}

.heading-default .glyphIcon-large.active,
.heading-noMoving .glyphIcon-large.active {
    color: #ffffff;
    background: #233d67;
}

.heading-default .glyphIcon-large+.glyphIcon-large,
.heading-noMoving .glyphIcon-large+.glyphIcon-large {
    margin-left: 0;
}

.heading-default .glyphIcon-large.accent,
.heading-noMoving .glyphIcon-large.accent {
    padding-left: .6em;
}

.heading-default .glyphIcon-large.accent::before,
.heading-noMoving .glyphIcon-large.accent::before {
    background: #b53f39;
    color: #ffffff;
    border-radius: 3px;
    width: 1.2em;
}

.heading-default .glyphIcon-large.accent:hover::before,
.heading-noMoving .glyphIcon-large.accent:hover::before {
    background: #ca5d57;
}

.heading-default {
    cursor: move;
}

.heading-noMoving {
    cursor: default;
}

.mainSection .heading-noMoving .text,
.mainSection .heading-noMoving .accountTable .text-auto,
.accountTable .mainSection .heading-noMoving .text-auto,
.mainSection .heading-noMoving .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .mainSection .heading-noMoving .text-auto {
    line-height: 3.2;
}

.mainSection .heading-noMoving.numberGame .text,
.mainSection .heading-noMoving.numberGame .accountTable .text-auto,
.accountTable .mainSection .heading-noMoving.numberGame .text-auto,
.mainSection .heading-noMoving.numberGame .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .mainSection .heading-noMoving.numberGame .text-auto {
    line-height: 2.5;
}

.section-two .heading-noMoving .text,
.section-two .heading-noMoving .accountTable .text-auto,
.accountTable .section-two .heading-noMoving .text-auto,
.section-two .heading-noMoving .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .section-two .heading-noMoving .text-auto {
    line-height: 2.5;
}

.sportsMenu .heading.icon-sportWorldcup .text,
.sportsMenu .heading.icon-sportWorldcup .accountTable .text-auto,
.accountTable .sportsMenu .heading.icon-sportWorldcup .text-auto,
.sportsMenu .heading.icon-sportWorldcup .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .sportsMenu .heading.icon-sportWorldcup .text-auto {
    z-index: 2;
    position: relative;
}

.sportsMenu .heading.icon-sportWorldcup .adorn::after {
    background: url(../../_global/common/Images/worldcup_menu_bg.png) right no-repeat;
    position: absolute;
    width: 93px;
    height: 2.8em;
    right: 0;
    top: 0;
    z-index: 1;
    content: " ";
}

.mini .sportsMenu .heading.icon-sportWorldcup .adorn::after {
    display: none;
}

.popupPanel,
.popupPanel-center,
.popupPanel-smaller,
.popupPanel-small,
.popupPanel-smaller-center,
.popupPanel-small-center,
.popupPanel-large,
.popupPanel-large-center,
.popupPanel-larger,
.popupPanel-larger-center {
    width: 45%;
    min-width: 25em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    border-radius: 3px;
    position: absolute;
    z-index: 38;
}

.popupPanel .contentArea,
.popupPanel-center .contentArea,
.popupPanel-smaller .contentArea,
.popupPanel-small .contentArea,
.popupPanel-smaller-center .contentArea,
.popupPanel-small-center .contentArea,
.popupPanel-large .contentArea,
.popupPanel-large-center .contentArea,
.popupPanel-larger .contentArea,
.popupPanel-larger-center .contentArea {
    padding: 0.833em;
    background: #ececec;
    color: #545454;
    border-radius: 0 0 3px 3px;
}

.popupPanel .contentArea iframe,
.popupPanel-center .contentArea iframe,
.popupPanel-smaller .contentArea iframe,
.popupPanel-small .contentArea iframe,
.popupPanel-smaller-center .contentArea iframe,
.popupPanel-small-center .contentArea iframe,
.popupPanel-large .contentArea iframe,
.popupPanel-large-center .contentArea iframe,
.popupPanel-larger .contentArea iframe,
.popupPanel-larger-center .contentArea iframe {
    width: 100%;
    height: 70vh;
    border: 0px;
    padding: 0;
    margin: 0;
}

.popupPanel .contentArea-darkBlue,
.popupPanel-center .contentArea-darkBlue,
.popupPanel-smaller .contentArea-darkBlue,
.popupPanel-small .contentArea-darkBlue,
.popupPanel-smaller-center .contentArea-darkBlue,
.popupPanel-small-center .contentArea-darkBlue,
.popupPanel-large .contentArea-darkBlue,
.popupPanel-large-center .contentArea-darkBlue,
.popupPanel-larger .contentArea-darkBlue,
.popupPanel-larger-center .contentArea-darkBlue {
    border-radius: 0 0 3px 3px;
}

.popupPanel .symbol,
.popupPanel-center .symbol,
.popupPanel-smaller .symbol,
.popupPanel-small .symbol,
.popupPanel-smaller-center .symbol,
.popupPanel-small-center .symbol,
.popupPanel-large .symbol,
.popupPanel-large-center .symbol,
.popupPanel-larger .symbol,
.popupPanel-larger-center .symbol {
    background-color: #b53f39;
    width: 80px;
    height: 80px;
    font-size: 75px;
    color: #ffffff;
    text-align: center;
    border-radius: 90px;
    display: inline-block;
    vertical-align: middle;
    line-height: 75px;
    margin: 0 5px 10px 0;
    overflow: hidden;
}

.popupPanel-center,
.popupPanel-smaller-center,
.popupPanel-small-center,
.popupPanel-large-center,
.popupPanel-larger-center {
    top: 9em;
    left: 0;
    right: 0;
    margin: 0 auto;
}

.popupPanel-smaller {
    width: 25%;
}

.popupPanel-small,
.popupPanel-smaller-center,
.popupPanel-small-center {
    width: 30%;
}

.popupPanel-large,
.popupPanel-large-center {
    min-width: 50em;
    width: 60%;
}

.popupPanel-larger,
.popupPanel-larger-center {
    width: 90%;
}

.otherContent {
    padding: 0.833em 0;
}

.otherContent .text-strong {
    font-weight: bold;
}

.otherContent .text-title-accent {
    width: calc(100% - 90px);
    font-size: 1.2em;
    font-weight: bold;
    display: inline-block;
    vertical-align: middle;
    color: #b53f39;
}

.otherContent .primary {
    color: #5574a7;
    font-weight: bold;
}

.otherContent:first-child {
    padding-top: 0;
}

.otherContent:last-child {
    padding-bottom: 0;
}

.otherContent.noted-line {
    padding: 0;
    padding-top: 0.833em;
}

.otherContent.noted-line div {
    display: inline-block;
}

.otherContent.form-list {
    background: white;
    padding: 0.833em;
    margin-top: 0.833em;
}

.otherContent.form-list .filter {
    margin-left: 10.25em;
    float: none;
}

.otherContent.form-list .formInput {
    float: none;
    vertical-align: middle;
    width: calc(100% - 12.5em);
    margin-left: 0.25em;
}

.otherContent.form-list .formInput>.form {
    width: 100%;
}

.otherContent.form-list .dropdown {
    float: none;
    vertical-align: middle;
    width: calc(100% - 12.5em);
}

.otherContent.form-list .confirm-form-block {
    padding: 0.3em 0;
    vertical-align: top;
}

.otherContent.form-list .confirm-form-block>div {
    display: inline-block;
}

.otherContent.form-list .confirm-form-block>div:first-child~div+div {
    margin-left: 10.5em;
    margin-top: 0.5em;
}

.otherContent.form-list .confirm-form-list,
.otherContent.form-list .confirm-form-list div,
.otherContent.form-list input {
    display: inline-block;
    float: none;
}

.otherContent.form-list .confirm-form-list {
    width: 10em;
    text-align: right;
    vertical-align: top;
}

.otherContent.form-list .text-input-block,
.otherContent.form-list .form.dropdown+input {
    margin-left: 10.5em;
    margin-top: 0.5em;
}

.otherContent.form-list .icon-messageWarning {
    width: 30px;
    display: inline-block;
    vertical-align: middle;
    font-size: 30px;
    line-height: 29px;
    margin-right: 10px;
    color: #b53f39;
}

.otherContent.form-list .icon-messageWarning+.text-strong.accentShow {
    width: calc( 100% - 45px);
    display: inline-block;
    vertical-align: middle;
}

.otherContent .accentShow {
    color: #b53f39;
}

.otherContent ul.Contentlist {
    margin-left: 13px;
    list-style-type: decimal;
    list-style-position: outside;
}

.otherContent label {
    display: inline-block;
    font-weight: bold;
}

.otherContent label+label {
    margin-left: 0.5em;
}

.otherContent .btnArea {
    padding: 0.3em 0;
    text-align: center;
}

.otherContent .btnArea .largeBtn {
    min-width: 33%;
    cursor: pointer;
}

.otherContent .btnArea .largeBtn+.largeBtn {
    margin-left: 0.5em;
}

.otherContent .btnArea-left {
    padding: 0.3em 0;
    margin-left: 10.5em;
    text-align: left;
    display: block;
}

.otherContent .btnArea-right {
    float: right;
    margin-top: -0.25em;
}

.otherContent .btnArea-right::after {
    content: "";
    display: block;
    clear: both;
}

.otherContent .btnArea-right .largeBtn {
    cursor: pointer;
}

.otherContent [class*="Btn"] {
    float: none;
    font-weight: normal;
    margin-left: 0;
    vertical-align: top;
    cursor: default;
}

.otherContent [class*="glyphIcon"] {
    margin-right: 0.25em;
    cursor: default;
}

.otherContent .search {
    margin-right: 0.25em;
    float: left;
    position: relative;
}

.otherContent .search.icon-search::before {
    position: absolute;
    left: 0.5em;
    top: 0.25em;
    z-index: 1;
}

.otherContent .search .icon-clear {
    position: absolute;
    right: 0.3em;
    top: 0.3em;
    cursor: pointer;
}

.otherContent .search .form {
    padding-left: 2em;
    padding-right: 1.7em;
}

.otherContent .dropdown {
    width: 30%;
}

.otherContent .dropdown+.search {
    margin-left: 0.25em;
}

.otherContent .withCheckbox.primary label {
    color: #ffffff;
}

.otherContent::after {
    content: "";
    display: block;
    clear: both;
}

.otherContent .bonus {
    padding-top: 0.5em;
}

.otherContent .bonus li {
    background-color: #dfdfdf;
    margin-bottom: 1px;
    padding: 0.25em 0.5em;
}

.otherContent .bonus li::after {
    content: "";
    display: block;
    clear: both;
}

.otherContent .bonus .title {
    float: left;
}

.otherContent .bonus .content {
    float: right;
    font-weight: bold;
}

.alertMessage-normal,
.alertMessage-error,
.alertMessage-warning {
    top: 30vh;
    left: 0;
    right: 0;
    margin: 0 auto;
}

.alertMessage-normal .heading-default,
.alertMessage-error .heading-default,
.alertMessage-warning .heading-default {
    height: auto;
}

.alertMessage-normal .icon-messageInfo .text,
.alertMessage-error .icon-messageInfo .text,
.alertMessage-warning .icon-messageInfo .text,
.alertMessage-normal .icon-messageInfo .accountTable .text-auto,
.accountTable .alertMessage-normal .icon-messageInfo .text-auto,
.alertMessage-error .icon-messageInfo .accountTable .text-auto,
.accountTable .alertMessage-error .icon-messageInfo .text-auto,
.alertMessage-warning .icon-messageInfo .accountTable .text-auto,
.accountTable .alertMessage-warning .icon-messageInfo .text-auto,
.alertMessage-normal .icon-messageInfo .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-normal .icon-messageInfo .text-auto,
.alertMessage-error .icon-messageInfo .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-error .icon-messageInfo .text-auto,
.alertMessage-warning .icon-messageInfo .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-warning .icon-messageInfo .text-auto,
.alertMessage-normal .icon-messageError .text,
.alertMessage-error .icon-messageError .text,
.alertMessage-warning .icon-messageError .text,
.alertMessage-normal .icon-messageError .accountTable .text-auto,
.accountTable .alertMessage-normal .icon-messageError .text-auto,
.alertMessage-error .icon-messageError .accountTable .text-auto,
.accountTable .alertMessage-error .icon-messageError .text-auto,
.alertMessage-warning .icon-messageError .accountTable .text-auto,
.accountTable .alertMessage-warning .icon-messageError .text-auto,
.alertMessage-normal .icon-messageError .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-normal .icon-messageError .text-auto,
.alertMessage-error .icon-messageError .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-error .icon-messageError .text-auto,
.alertMessage-warning .icon-messageError .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-warning .icon-messageError .text-auto,
.alertMessage-normal .icon-messageWarning .text,
.alertMessage-error .icon-messageWarning .text,
.alertMessage-warning .icon-messageWarning .text,
.alertMessage-normal .icon-messageWarning .accountTable .text-auto,
.accountTable .alertMessage-normal .icon-messageWarning .text-auto,
.alertMessage-error .icon-messageWarning .accountTable .text-auto,
.accountTable .alertMessage-error .icon-messageWarning .text-auto,
.alertMessage-warning .icon-messageWarning .accountTable .text-auto,
.accountTable .alertMessage-warning .icon-messageWarning .text-auto,
.alertMessage-normal .icon-messageWarning .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-normal .icon-messageWarning .text-auto,
.alertMessage-error .icon-messageWarning .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-error .icon-messageWarning .text-auto,
.alertMessage-warning .icon-messageWarning .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-warning .icon-messageWarning .text-auto,
.alertMessage-normal .icon-logout .text,
.alertMessage-error .icon-logout .text,
.alertMessage-warning .icon-logout .text,
.alertMessage-normal .icon-logout .accountTable .text-auto,
.accountTable .alertMessage-normal .icon-logout .text-auto,
.alertMessage-error .icon-logout .accountTable .text-auto,
.accountTable .alertMessage-error .icon-logout .text-auto,
.alertMessage-warning .icon-logout .accountTable .text-auto,
.accountTable .alertMessage-warning .icon-logout .text-auto,
.alertMessage-normal .icon-logout .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-normal .icon-logout .text-auto,
.alertMessage-error .icon-logout .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-error .icon-logout .text-auto,
.alertMessage-warning .icon-logout .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .alertMessage-warning .icon-logout .text-auto {
    padding-top: .2em;
}

.alertMessage-normal .otherContent,
.alertMessage-error .otherContent,
.alertMessage-warning .otherContent {
    padding: 0 1.9em;
}

.alertMessage-normal .text-larger,
.alertMessage-error .text-larger,
.alertMessage-warning .text-larger {
    margin: 0.5em 0 0.833em;
    min-height: 3em;
    color: #323232;
}

.alertMessage-normal .text-larger+.text-larger,
.alertMessage-error .text-larger+.text-larger,
.alertMessage-warning .text-larger+.text-larger {
    min-height: initial;
}

.alertMessage-normal label,
.alertMessage-error label,
.alertMessage-warning label {
    font-weight: normal;
    color: rgba(50, 50, 50, 0.6);
    margin-top: 0.5em;
    margin-bottom: 0.833em;
}

.alertMessage-normal p,
.alertMessage-error p,
.alertMessage-warning p {
    line-height: 2em;
}

.alertMessage-normal .ticketStatus,
.alertMessage-error .ticketStatus,
.alertMessage-warning .ticketStatus {
    line-height: 1.5em;
    min-width: 60px;
    text-align: center;
}

.alertMessage-normal .heading-default {
    background: #5574a7;
}

.alertMessage-error .heading-default {
    background: #9c0000;
}

.alertMessage-error .largeBtn:not(.secondary) {
    background: linear-gradient(to bottom, #b53f39 0%, #9c0000 100%);
    background: -webkit-linear-gradient(bottom, #9c0000 0%, #b53f39 100%);
    border-color: #b53f39;
}

.alertMessage-error .largeBtn:not(.secondary):hover {
    background: linear-gradient(to bottom, #c44a44 0%, #b60000 100%);
    background: -webkit-linear-gradient(bottom, #b60000 0%, #c44a44 100%);
}

.alertMessage-warning .heading-default {
    background: #f77a00;
}

.alertMessage-warning .largeBtn:not(.secondary) {
    background: linear-gradient(to bottom, #ff9270 0%, #f77a00 100%);
    background: -webkit-linear-gradient(bottom, #f77a00 0%, #ff9270 100%);
    border-color: #ff9270;
}

.alertMessage-warning .largeBtn:not(.secondary):hover {
    background: linear-gradient(to bottom, #ffa589 0%, #ff8712 100%);
    background: -webkit-linear-gradient(bottom, #ff8712 0%, #ffa589 100%);
}

.scoreMap {
    width: 38em;
    top: 11.5em;
}

.betItem,
.betItem-closed {
    padding: 0.3em 2em 0.3em 0.75em;
    position: relative;
    font-weight: bold;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.betItem label,
.betItem-closed label {
    position: absolute;
}

.betItem label input[type="checkbox"]+.checkbox,
.betItem-closed label input[type="checkbox"]+.checkbox {
    left: -.2em;
    top: .4em;
}

.betItem .icon-close,
.betItem-closed .icon-close {
    position: absolute;
    right: 0.5em;
    top: .7em;
}

.betItem .listContent,
.betItem-closed .listContent {
    padding-left: 1.25em;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end;
}

.betItem .listContent-leftArea,
.betItem-closed .listContent-leftArea {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: .3em 0 .1em;
}

.betItem .listContent-rightArea,
.betItem-closed .listContent-rightArea {
    padding-bottom: .1em;
    margin-left: .3em;
}

.betItem .listContent-item,
.betItem-closed .listContent-item,
.betItem .listContent-group,
.betItem-closed .listContent-group {
    margin-left: 0.25em;
    display: inline-block;
}

.betItem .listContent-group>.listContent-item:first-child,
.betItem-closed .listContent-group>.listContent-item:first-child {
    margin-left: 0;
}

.betItem .glyph,
.betItem-closed .glyph {
    position: relative;
    top: -.15em;
}

.betItem {
    cursor: pointer;
}

.betItem:hover {
    background: #f5eeb8;
}

.betItem .primary {
    color: #01122b;
}

.betItem .accent {
    color: #b53f39;
}

.betItem .secondary,
.betItem .icon-close {
    color: #7c7c7c;
}

.betItem .special-a {
    color: #5574a7;
}

.betItem-closed {
    background: #dfdfdf;
    color: rgba(0, 0, 0, 0.3);
    cursor: default;
}

.betItem-closed:hover {
    background: #dfdfdf;
}

.betItem-closed .primary,
.betItem-closed .accent,
.betItem-closed .secondary,
.betItem-closed .glyph,
.betItem-closed .special-a,
.betItem-closed .icon-close {
    color: rgba(0, 0, 0, 0.3);
}

.betItem-closed label input[type="checkbox"]+.checkbox {
    border-color: rgba(0, 0, 0, 0.3);
    color: rgba(0, 0, 0, 0.3);
    background: #dfdfdf;
    cursor: default;
}

.promo {
    font-family: "Tahoma", "Microsoft JhengHei", "Microsoft YaHei";
    width: 55em;
}

.promo .contentArea {
    border-radius: 3px;
}

.promo .welcomeNewVersion {
    background: url(../../images/v2/popup_promo_changeVerPop.png) top no-repeat;
    padding: 138px 0 0;
    overflow: hidden;
    background-color: #ffffff;
    background-size: 100% auto;
    text-align: center;
}

.promo .welcomeNewVersion ul {
    text-align: left;
    font-size: 1.3em;
    line-height: 1.7em;
    font-weight: bold;
    padding-left: 5em;
    padding-right: 0.833em;
}

.promo .welcomeNewVersion li {
    color: black;
}

.promo .welcomeNewVersion li::before {
    color: #5dad00;
    font-weight: bold;
    padding-right: .5em;
}

.promo .welcomeNewVersion .btnArea {
    padding: 1em;
}

.promo .welcomeNewVersion .btnArea .flatBtn {
    display: inline-block;
    width: 50%;
}

.promo .welcomeNewVersion .btnArea .flatBtn.secondary {
    width: 20%;
    margin-left: 2%;
}

.promo .welcomeNewVersion .btnArea .tertiary {
    text-align: right;
    padding-right: 15%;
    padding-bottom: 10px;
}

.promo .tertiary {
    color: #bbbbbb;
    display: block;
    padding-top: .2em;
}

.promo .tertiary:hover {
    color: #a3a3a3;
}

.promo .txtBox {
    padding: 1em 2.5em;
}

.promo h1 {
    padding: 0 0.833em;
    font-size: 2.2em;
    margin-bottom: 15px;
    font-weight: bold;
    color: #f77a00;
    letter-spacing: -0.5px;
}

.promo h2 {
    font-size: 1.8em;
    margin-bottom: 15px;
    font-weight: bold;
    color: #5574a7;
    letter-spacing: -0.5px;
}

.promo p {
    font-size: 1.2em;
    line-height: 1.5em;
    color: #545454;
}

.promo .slides-control-item {
    background: #ececec;
}

.promo .slides-pagination {
    position: relative;
    width: 40%;
    margin: .5em auto 1em;
    padding-top: 0;
}

.promo .slides-pagination a {
    vertical-align: middle;
}

.promo .slides-pagination a.slides-btn-prev,
.promo .slides-pagination a.slides-btn-next {
    color: #a3a3a3;
    opacity: 1;
    visibility: visible;
    background: none;
    font-size: 2em;
    font-weight: bolder;
}

.promo .slides-pagination a.slides-btn-prev:hover,
.promo .slides-pagination a.slides-btn-next:hover {
    color: #5574a7;
}

.promo .slides-pagination-item {
    margin: 0 .5em;
    border: 2px solid #ececec;
}

.promo .slides-pagination-item.active,
.promo .slides-pagination-item:hover {
    border: 2px solid #7591c1;
}

.promo .slides {
    position: relative;
    padding-bottom: 2.7em;
}

.promo .slides .btnArea {
    position: absolute;
    right: 2em;
    bottom: 1.5em;
    text-align: center;
}

.promo .slides .btnArea div {
    display: none;
}

.promo .slides .btnArea .flatBtn {
    font-size: 1.2em;
    width: 150px;
}

.promo .slides .btnArea .secondary {
    width: 120px;
}

.imgBox {
    background: top center no-repeat #ffffff;
    border: 1px solid #cdcdcd;
    height: 320px;
    margin-bottom: 1em;
    text-align: center;
    position: relative;
}

.imgBox .txt {
    position: absolute;
    bottom: .5em;
    height: 2.2em;
    line-height: 1.1em;
    font-size: 1.4em;
    text-align: center;
    color: #7c7c7c;
    font-weight: bold;
}

.imgBox .txt.step1-left {
    width: 230px;
    left: 90px;
}

.imgBox .txt.step1-right {
    width: 200px;
    right: 45px;
}

.imgBox .txt.step4-left {
    width: 290px;
    left: 20px;
}

.imgBox .txt.step4-right {
    width: 220px;
    right: 20px;
}

.imgBox .step3-left,
.imgBox .step3-right {
    position: absolute;
    font-weight: lighter;
    color: #ffffff;
    font-size: 1em;
    right: 132px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.imgBox .step3-left {
    width: 70px;
    bottom: 130px;
}

.imgBox .step3-right {
    width: 42px;
    bottom: 100px;
}

.step4 .imgBox::before,
.step4 .imgBox::after {
    content: " ";
    position: absolute;
    width: 5em;
    height: 2em;
    background-size: 100% auto;
    background-repeat: no-repeat;
}

.step4 .imgBox::before {
    top: 57px;
    left: 40px;
}

.step4 .imgBox::after {
    top: 92px;
    right: 160px;
}

.step5 .imgBox::before,
.step5 .imgBox::after {
    content: " ";
    position: absolute;
    width: 5em;
    height: 2em;
    background-size: 100% auto;
    background-repeat: no-repeat;
    top: 77px;
}

.step5 .imgBox::before {
    left: 33px;
}

.step5 .imgBox::after {
    right: 210px;
}

.step1 .imgBox {
    background-image: url(../../images/v2/popup_promo_howToUse_step1.png);
}

.step2 .imgBox {
    background-image: url(../../images/v2/popup_promo_howToUse_step2.png);
}

.step3 .imgBox {
    background-image: url(../../images/v2/popup_promo_howToUse_step3.png);
}

.step4 .imgBox {
    background-image: url(../../images/v2/popup_promo_howToUse_step4.png);
}

.step5 .imgBox {
    background-image: url(../../images/v2/popup_promo_howToUse_step5.png);
}

.overlay .popupInfo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
}

.popupInfo {
    font-family: "Tahoma", "Microsoft JhengHei", "Microsoft YaHei";
}

.popupInfo .contentArea {
    padding-left: 50px;
    padding-right: 50px;
    border-radius: 3px;
}

.popupInfo__content {
    margin-bottom: 1em;
    padding: 10px 20px;
    background: top center no-repeat #ffffff;
    border: 1px solid #cdcdcd;
}

.popupInfo__btnArea:after {
    content: "";
    display: block;
    clear: both;
}

.popupInfo__btnArea .flatBtn {
    float: right;
    padding-left: 4em;
    padding-right: 4em;
    font-size: 1.2em;
}

.popupInfo h2,
.popupInfo h3 {
    font-weight: bold;
    color: #5574a7;
}

.popupInfo h2 {
    font-size: 1.8em;
    margin: 10px auto;
}

.popupInfo h3,
.popupInfo li,
.popupInfo p {
    margin-bottom: 5px;
}

.popupInfo h3 {
    font-size: 1.4em;
}

.popupInfo ul {
    list-style: disc;
    margin-left: 3em;
}

.popupInfo p {
    margin-left: 2em;
}

.popupInfo hr {
    margin: 1em 0;
    height: 1px;
    border: 0;
    background-color: #dfdfdf;
}

.popupInfo a {
    color: #5574a7;
    text-decoration: none;
}

.popupInfo a:hover {
    text-decoration: underline;
}

.popupFlash {
    width: 780px;
}

.popupFlash [class*=icon-browser-] {
    margin-right: 3px;
}

.popupFlash .icon-lock {
    color: #366314;
}

.popupFlash .icon-messageWarning {
    color: #c90000;
}

.popupFlash .icon-info,
.popupFlash .icon-puzzle {
    color: #666;
}

.popupFlash .icon-info,
.popupFlash .icon-arrowSolid-down {
    display: inline-block;
    line-height: 1;
}

.popupFlash .icon-info {
    width: 1em;
    height: 1em;
    border: 1px solid #666;
    border-radius: 100%;
}

.popupFlash .icon-arrowSolid-down {
    background-color: whitesmoke;
    border: 1px dotted #666;
}

[class*=icon-browser-] {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    background: url(../../../_global/common/Images/SinglePage/all_browser.svg);
    background-size: 100% auto;
}

.icon-browser-chrome {
    background-position: 0 0;
}

.icon-browser-firefox {
    background-position: 0 20%;
}

.icon-browser-ie {
    background-position: 0 40%;
}

.icon-browser-safari {
    background-position: 0 60%;
}

.icon-browser-opera {
    background-position: 0 80%;
}

.icon-browser-edge {
    background-position: 0 100%;
}

.liveStreaming+div {
    margin-top: 0.5em;
}

.streamingInfo {
    background: rgba(0, 0, 0, 0.07);
    padding: 0.5em 0.833em;
}

.streamingList>ul {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.streamingList>ul:hover {
    background: #f5eeb8;
}

.streamingList .iconSet .smallBtn:first-child {
    margin-left: 0;
}

.streamingList .team {
    overflow: hidden;
}

.streamingList .team>div {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.streamingList .sportTitle {
    position: relative;
    margin-right: 0.3em;
}

.streamingList .sportTitle .icon-streaming {
    position: absolute;
    bottom: .2em;
    right: -.1em;
}

.streamingList .sportTitle--none::before {
    background-image: none;
}

.streamingList .sportInfo {
    width: 4.5em;
    font-weight: bold;
    text-align: center;
}

.streamingList .sportInfo .text-accent {
    color: #b53f39;
}

.streamingList .sportInfo .text-primary {
    color: #2556b3;
}

.streamingList .sportInfo .smallBtn {
    float: none;
    font-weight: normal;
}

.streamingList .sportInfo-detail {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.streamingList .team {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    min-width: 0;
    padding: 0 0.5em 0 0.25em;
    overflow: hidden;
}

.streamingList .team .team-name {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.streamingList .teamName-pointer {
    cursor: pointer;
}

.streamingList .otherInfo {
    padding-right: 0.5em;
}

.streamingList .buttonNav,
.streamingList .buttonNav-upper {
    -ms-transform: translateX(-1em);
    -webkit-transform: translateX(-1em);
    transform: translateX(-1em);
}

.streamingList-content {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.streamingList-content:hover {
    background: #f5eeb8;
}

.streamingList-content+.streamingList-content {
    margin-top: 0.5em;
}

.hint.not-available {
    display: none;
    height: auto;
    background: transparent;
    font-weight: normal;
    padding: 0;
    margin-top: 0.25em;
    margin-left: 0.5em;
    margin-bottom: 0.5em;
    position: relative;
}

.hint.not-available .content {
    background: #dfdfdf;
    padding: .5em 1em .5em 2.7em;
    display: block;
    float: none;
    text-align: left;
}

.hint.not-available.show {
    display: block;
}

.bookingReminder {
    padding: 0.25em;
    padding-left: 1.1em;
    padding-bottom: 0;
    position: relative;
}

.bookingReminder>ul {
    border-radius: 0 3px 3px 0;
    display: table;
    table-layout: fixed;
    width: calc(100% - 0.5em);
    margin-left: -1px;
    padding: 0.25em;
    color: #b53f39;
    background: #f5e0df;
    border: 1px solid #b53f39;
    -webkit-animation: FadeInOut 10s linear 1 normal;
    animation: FadeInOut 10s linear 1 normal;
}

.bookingReminder>ul+ul {
    margin-top: 0.25em;
}

.bookingReminder>ul>li {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    font-weight: bold;
}

.bookingReminder>ul>li:first-child {
    width: 1.25em;
}

.bookingReminder>ul>li:last-child {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 30%;
    text-align: left;
}

.bookingReminder>ul>li.vs {
    width: 1.5em;
}

.bookingReminder>ul>li.team {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 0 0.25em;
}

.bookingReminder .buttonNav,
.bookingReminder .buttonNav-upper {
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
}

.bookingReminder .buttonNav li,
.bookingReminder .buttonNav-upper li {
    text-align: left;
    font-weight: normal;
}

.showingNow .bookingReminder {
    background: #ffffff;
}

.streamingFaq .heading {
    border-radius: 0;
}

.streamingFaq .heading+.contentArea {
    padding-top: 0;
}

.streamingFaq .contentArea {
    padding: 0.5em;
    background: #233d67;
}

.streamingFaq .collapsible {
    margin: 0.5em;
}

.streamingFaq .innerContent {
    color: #545454;
}

.streamingFaq .innerContent ul {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    padding-left: 2em;
}

.streamingFaq .innerContent li {
    list-style: disc;
    padding-left: 0.5em;
}

.streamingFaq .innerContent p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.streamingFaq .innerContent a {
    display: inline-block;
    word-break: break-all;
}

.liveStreaming-panelContent>[class*="liveStreaming-"] {
    float: left;
}

.liveStreaming-panelContent.double-video .liveStreaming-streamingContent>div {
    width: 50%;
    float: left;
}

.liveStreaming-panelContent.double-video .liveStreaming-streamingContent>div+div {
    padding-left: 0.25em;
}

.liveStreaming-panelContent.double-video .liveStreaming-streamingContent>div:first-child {
    padding-right: 0.25em;
}

.liveStreaming-panelContent::after {
    content: "";
    display: block;
    clear: both;
}

.liveStreaming-streamingContent {
    width: 100%;
    height: 100%;
}

.liveStreaming-video {
    width: 100%;
    height: 100%;
    background: black;
}

.video-container {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    align-items: center;
}

.video-container-fluid {
    position: relative;
    padding-bottom: 53%;
    padding-top: 1.65em;
    height: 0;
    overflow: hidden;
}

.video-container-fluid iframe,
.video-container-fluid object,
.video-container-fluid embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.liveStreaming-flashCrash,
.liveStreaming-defaultImage {
    width: 100%;
    height: 100%;
    text-align: center;
}

.liveStreaming-flashCrash::before,
.liveStreaming-defaultImage::before {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

.liveStreaming-flashCrash .inner-container,
.liveStreaming-defaultImage .inner-container {
    display: inline-block;
    vertical-align: middle;
}

.liveStreaming-flashCrash .text,
.liveStreaming-flashCrash .accountTable .text-auto,
.accountTable .liveStreaming-flashCrash .text-auto,
.liveStreaming-flashCrash .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .liveStreaming-flashCrash .text-auto,
.liveStreaming-defaultImage .text,
.liveStreaming-defaultImage .accountTable .text-auto,
.accountTable .liveStreaming-defaultImage .text-auto,
.liveStreaming-defaultImage .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .liveStreaming-defaultImage .text-auto {
    display: block;
    font-weight: bold;
    line-height: 1.2;
}

.liveStreaming-flashCrash .logo-large+.text,
.liveStreaming-flashCrash .accountTable .logo-large+.text-auto,
.accountTable .liveStreaming-flashCrash .logo-large+.text-auto,
.liveStreaming-flashCrash .accountTable-verticalAlignTop .logo-large+.text-auto,
.accountTable-verticalAlignTop .liveStreaming-flashCrash .logo-large+.text-auto,
.liveStreaming-defaultImage .logo-large+.text,
.liveStreaming-defaultImage .accountTable .logo-large+.text-auto,
.accountTable .liveStreaming-defaultImage .logo-large+.text-auto,
.liveStreaming-defaultImage .accountTable-verticalAlignTop .logo-large+.text-auto,
.accountTable-verticalAlignTop .liveStreaming-defaultImage .logo-large+.text-auto {
    margin-top: 0.833em;
}

.liveStreaming-flashCrash .text+.text,
.liveStreaming-flashCrash .accountTable .text-auto+.text,
.accountTable .liveStreaming-flashCrash .text-auto+.text,
.liveStreaming-flashCrash .accountTable-verticalAlignTop .text-auto+.text,
.accountTable-verticalAlignTop .liveStreaming-flashCrash .text-auto+.text,
.liveStreaming-flashCrash .accountTable .text+.text-auto,
.accountTable .liveStreaming-flashCrash .text+.text-auto,
.liveStreaming-flashCrash .accountTable .text-auto+.text-auto,
.accountTable .liveStreaming-flashCrash .text-auto+.text-auto,
.liveStreaming-flashCrash .accountTable-verticalAlignTop .text+.text-auto,
.accountTable-verticalAlignTop .liveStreaming-flashCrash .text+.text-auto,
.liveStreaming-flashCrash .accountTable-verticalAlignTop .text-auto+.text-auto,
.accountTable-verticalAlignTop .liveStreaming-flashCrash .text-auto+.text-auto,
.liveStreaming-defaultImage .text+.text,
.liveStreaming-defaultImage .accountTable .text-auto+.text,
.accountTable .liveStreaming-defaultImage .text-auto+.text,
.liveStreaming-defaultImage .accountTable-verticalAlignTop .text-auto+.text,
.accountTable-verticalAlignTop .liveStreaming-defaultImage .text-auto+.text,
.liveStreaming-defaultImage .accountTable .text+.text-auto,
.accountTable .liveStreaming-defaultImage .text+.text-auto,
.liveStreaming-defaultImage .accountTable .text-auto+.text-auto,
.accountTable .liveStreaming-defaultImage .text-auto+.text-auto,
.liveStreaming-defaultImage .accountTable-verticalAlignTop .text+.text-auto,
.accountTable-verticalAlignTop .liveStreaming-defaultImage .text+.text-auto,
.liveStreaming-defaultImage .accountTable-verticalAlignTop .text-auto+.text-auto,
.accountTable-verticalAlignTop .liveStreaming-defaultImage .text-auto+.text-auto {
    margin-top: 0;
}

.liveStreaming-flashCrash {
    background: #ececec;
}

.liveStreaming-flashCrash .icon-flashCrash {
    display: inline-block;
    padding: 2em;
    color: #ececec;
    background: #bbbbbb;
    border-radius: 100%;
}

.liveStreaming-flashCrash .icon-flashCrash::before {
    display: inline-block;
    font-size: 6.5em;
    width: 1em;
    height: 1em;
    line-height: 1;
}

.liveStreaming-flashCrash .text,
.liveStreaming-flashCrash .accountTable .text-auto,
.accountTable .liveStreaming-flashCrash .text-auto,
.liveStreaming-flashCrash .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .liveStreaming-flashCrash .text-auto {
    font-size: 1.5em;
}

.liveStreaming-defaultImage {
    background-image: url(../../_global/common/Images/streaming_defaultImage.jpg);
    background-size: cover;
    background-position: 50% 50%;
}

.liveStreaming-defaultImage .logo-large {
    display: inline-block;
    width: 288px;
    height: 66px;
}

.liveStreaming-defaultImage .text,
.liveStreaming-defaultImage .accountTable .text-auto,
.accountTable .liveStreaming-defaultImage .text-auto,
.liveStreaming-defaultImage .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .liveStreaming-defaultImage .text-auto {
    color: #233d67;
    font-size: 2em;
}

.liveStreaming.is-new-window {
    min-width: calc(720px + 380px);
}

.liveStreaming.is-new-window .liveStreaming-panelContent>div {
    height: 440px;
}

.liveStreaming.is-new-window .liveStreaming-streamingContent {
    width: calc(100% - 380px);
}

.liveStreaming.is-new-window .liveStreaming-sidebar {
    width: 380px;
    padding: 0.5em;
    position: relative;
    height: 440px;
    background: #233d67;
    overflow: hidden;
}

.liveStreaming.is-new-window .liveStreaming-sidebar::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 0.5em;
    background: #233d67;
}

.liveStreaming.is-new-window .tabs,
.liveStreaming.is-new-window .tabs-content,
.liveStreaming.is-new-window .contentArea,
.liveStreaming.is-new-window .collapsible-wrap {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: column nowrap;
    -ms-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-box-flex: auto;
    -webkit-flex: auto;
    -ms-flex: auto;
    flex: auto;
}

.liveStreaming.is-new-window .collapsible-wrap {
    height: 0;
    overflow-x: visible;
    overflow-y: auto;
}

.liveStreaming.is-new-window .streamingList-wrap {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: column nowrap;
    -ms-flex-flow: column nowrap;
    flex-flow: column nowrap;
    height: 422px;
    background: #ffffff;
}

.liveStreaming.is-new-window .streamingList-wrap .tabs-item:last-of-type {
    padding-right: 0;
}

.liveStreaming.is-new-window .streamingFaq {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: column nowrap;
    -ms-flex-flow: column nowrap;
    flex-flow: column nowrap;
    height: 100%;
    position: absolute;
    top: auto;
    left: 0;
    right: 0;
    padding: 0.5em;
    padding-top: 0;
    margin-top: -1.5em;
}

.liveStreaming.is-new-window .streamingFaq .contentArea {
    padding: 0;
    background: #ffffff;
}

.liveStreaming.is-new-window .streamingFaq .textArea a {
    color: #7591c1;
    text-decoration: none;
}

.liveStreaming.is-new-window .streamingFaq .textArea a:hover {
    color: #4b5d7b;
}

.liveStreaming.is-new-window .streamingFaq.show {
    top: 1.5em;
}

.liveStreaming.is-new-window .streamingFaq.show .heading.collapse::after {
    content: "";
}

.liveStreaming.is-new-window[class*="is-collapse"] {
    width: 720px;
    min-width: auto;
}

.liveStreaming.is-new-window[class*="is-collapse"] .liveStreaming-streamingContent {
    width: 100%;
}

.liveStreaming.is-new-window[class*="is-collapse"] .liveStreaming-sidebar {
    display: none;
}

.liveStreaming.is-new-window.is-collapse-wide {
    width: 1020px;
}

.game-visualization {
    height: 260px;
}

.betradar {
    background-color: whitesmoke;
}

.betradar-container {
    max-width: 750px;
    height: 390px;
    margin: 0 auto;
}

.betradarVirtualSport {
    background-color: whitesmoke;
}

.betradarVirtualSport-container {
    max-width: 878px;
    height: 358px;
    margin: 0 auto;
}

.betradarVirtualBasketball {
    max-width: 878px;
    height: 378px;
    margin: 0 auto;
}

.accountTable .tableHead,
.accountTable-verticalAlignTop .tableHead,
.accountTable .tableHead-sub,
.accountTable-verticalAlignTop .tableHead-sub,
.accountTable .tableBody .tableRow,
.accountTable-verticalAlignTop .tableBody .tableRow,
.accountTable .tableBody .tableRow-pointer,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer,
.accountTable .tableFooter .tableFooterRow,
.accountTable-verticalAlignTop .tableFooter .tableFooterRow {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.accountTable .tableHead>div,
.accountTable-verticalAlignTop .tableHead>div,
.accountTable .tableHead-sub>div,
.accountTable-verticalAlignTop .tableHead-sub>div,
.accountTable .tableBody .tableRow>div,
.accountTable-verticalAlignTop .tableBody .tableRow>div,
.accountTable .tableBody .tableRow-pointer>div,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer>div,
.accountTable .tableBody .tableRow .tableGroup>div,
.accountTable-verticalAlignTop .tableBody .tableRow .tableGroup>div,
.accountTable .tableBody .tableRow-pointer .tableGroup>div,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer .tableGroup>div,
.accountTable .tableFooter .tableFooterRow>div,
.accountTable-verticalAlignTop .tableFooter .tableFooterRow>div {
    display: table-cell;
    padding: 0.5em;
    box-sizing: border-box;
}

.accountTable .tableBody .tableRow,
.accountTable-verticalAlignTop .tableBody .tableRow,
.accountTable .tableBody .tableRow-pointer,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer,
.accountTable .tableBody .tableRow>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow>div+div,
.accountTable .tableBody .tableRow-pointer>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer>div+div,
.accountTable .tableBody .tableRow .tableGroup+.tableGroup,
.accountTable-verticalAlignTop .tableBody .tableRow .tableGroup+.tableGroup,
.accountTable .tableBody .tableRow-pointer .tableGroup+.tableGroup,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer .tableGroup+.tableGroup,
.accountTable .tableBody .tableRow .tableGroup>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow .tableGroup>div+div,
.accountTable .tableBody .tableRow-pointer .tableGroup>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer .tableGroup>div+div,
.accountTable .tableBody .expandArea,
.accountTable-verticalAlignTop .tableBody .expandArea {
    border: 0 solid #bbbbbb;
}

.accountTable .date .ref,
.accountTable-verticalAlignTop .date .ref,
.accountTable .date-small .ref,
.accountTable-verticalAlignTop .date-small .ref,
.accountTable .date-smaller .ref,
.accountTable-verticalAlignTop .date-smaller .ref,
.accountTable .date-smallest .ref,
.accountTable-verticalAlignTop .date-smallest .ref {
    font-weight: bold;
    color: #01122b;
}

.accountTable .no,
.accountTable-verticalAlignTop .no,
.accountTable .no-large,
.accountTable-verticalAlignTop .no-large {
    text-align: center;
}

.accountTable .team .accent,
.accountTable-verticalAlignTop .team .accent,
.accountTable .status .accent,
.accountTable-verticalAlignTop .status .accent,
.accountTable .status-smaller .accent,
.accountTable-verticalAlignTop .status-smaller .accent,
.accountTable .turnover .accent,
.accountTable-verticalAlignTop .turnover .accent,
.accountTable .credit .accent,
.accountTable-verticalAlignTop .credit .accent,
.accountTable .commission .accent,
.accountTable-verticalAlignTop .commission .accent,
.accountTable .balance .accent,
.accountTable-verticalAlignTop .balance .accent,
.accountTable .text .accent,
.accountTable-verticalAlignTop .text .accent,
.accountTable .text-auto .accent,
.accountTable-verticalAlignTop .text-auto .accent,
.accountTable .accent.team,
.accountTable-verticalAlignTop .accent.team,
.accountTable .accent.status,
.accountTable-verticalAlignTop .accent.status,
.accountTable .accent.status-smaller,
.accountTable-verticalAlignTop .accent.status-smaller,
.accountTable .accent.turnover,
.accountTable-verticalAlignTop .accent.turnover,
.accountTable .accent.credit,
.accountTable-verticalAlignTop .accent.credit,
.accountTable .accent.commission,
.accountTable-verticalAlignTop .accent.commission,
.accountTable .accent.balance,
.accountTable-verticalAlignTop .accent.balance,
.accountTable .accent.text,
.accountTable-verticalAlignTop .accent.text,
.accountTable .accent.text-auto,
.accountTable-verticalAlignTop .accent.text-auto {
    color: #b53f39;
}

.accountTable .team .secondary,
.accountTable-verticalAlignTop .team .secondary,
.accountTable .status .secondary,
.accountTable-verticalAlignTop .status .secondary,
.accountTable .status-smaller .secondary,
.accountTable-verticalAlignTop .status-smaller .secondary,
.accountTable .turnover .secondary,
.accountTable-verticalAlignTop .turnover .secondary,
.accountTable .credit .secondary,
.accountTable-verticalAlignTop .credit .secondary,
.accountTable .commission .secondary,
.accountTable-verticalAlignTop .commission .secondary,
.accountTable .balance .secondary,
.accountTable-verticalAlignTop .balance .secondary,
.accountTable .text .secondary,
.accountTable-verticalAlignTop .text .secondary,
.accountTable .text-auto .secondary,
.accountTable-verticalAlignTop .text-auto .secondary,
.accountTable .secondar.team,
.accountTable-verticalAlignTop .secondar.team,
.accountTable .secondar.status,
.accountTable-verticalAlignTop .secondar.status,
.accountTable .secondar.status-smaller,
.accountTable-verticalAlignTop .secondar.status-smaller,
.accountTable .secondar.turnover,
.accountTable-verticalAlignTop .secondar.turnover,
.accountTable .secondar.credit,
.accountTable-verticalAlignTop .secondar.credit,
.accountTable .secondar.commission,
.accountTable-verticalAlignTop .secondar.commission,
.accountTable .secondar.balance,
.accountTable-verticalAlignTop .secondar.balance,
.accountTable .secondar.text,
.accountTable-verticalAlignTop .secondar.text,
.accountTable .secondar.text-auto,
.accountTable-verticalAlignTop .secondar.text-auto {
    color: #2556b3;
}

.accountTable .team .highlight,
.accountTable-verticalAlignTop .team .highlight,
.accountTable .status .highlight,
.accountTable-verticalAlignTop .status .highlight,
.accountTable .status-smaller .highlight,
.accountTable-verticalAlignTop .status-smaller .highlight,
.accountTable .turnover .highlight,
.accountTable-verticalAlignTop .turnover .highlight,
.accountTable .credit .highlight,
.accountTable-verticalAlignTop .credit .highlight,
.accountTable .commission .highlight,
.accountTable-verticalAlignTop .commission .highlight,
.accountTable .balance .highlight,
.accountTable-verticalAlignTop .balance .highlight,
.accountTable .text .highlight,
.accountTable-verticalAlignTop .text .highlight,
.accountTable .text-auto .highlight,
.accountTable-verticalAlignTop .text-auto .highlight,
.accountTable .highlight.team,
.accountTable-verticalAlignTop .highlight.team,
.accountTable .highlight.status,
.accountTable-verticalAlignTop .highlight.status,
.accountTable .highlight.status-smaller,
.accountTable-verticalAlignTop .highlight.status-smaller,
.accountTable .highlight.turnover,
.accountTable-verticalAlignTop .highlight.turnover,
.accountTable .highlight.credit,
.accountTable-verticalAlignTop .highlight.credit,
.accountTable .highlight.commission,
.accountTable-verticalAlignTop .highlight.commission,
.accountTable .highlight.balance,
.accountTable-verticalAlignTop .highlight.balance,
.accountTable .highlight.text,
.accountTable-verticalAlignTop .highlight.text,
.accountTable .highlight.text-auto,
.accountTable-verticalAlignTop .highlight.text-auto {
    background: #feec6e;
}

.accountTable,
.accountTable-verticalAlignTop {
    color: #545454;
    margin-bottom: 0.5em;
}

.accountTable div,
.accountTable-verticalAlignTop div {
    vertical-align: middle;
}

.accountTable .tableHead,
.accountTable-verticalAlignTop .tableHead,
.accountTable .tableHead-sub,
.accountTable-verticalAlignTop .tableHead-sub {
    border-radius: 3px 3px 0 0;
    background: #5574a7;
    border-top: solid 1px #5574a7;
}

.accountTable .tableHead>div,
.accountTable-verticalAlignTop .tableHead>div,
.accountTable .tableHead-sub>div,
.accountTable-verticalAlignTop .tableHead-sub>div {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    border-radius: inherit inherit 0 0;
    max-width: 0;
    font-weight: normal;
    border-left: 1px solid #3b5174;
}

.accountTable .tableHead>div:first-child,
.accountTable-verticalAlignTop .tableHead>div:first-child,
.accountTable .tableHead-sub>div:first-child,
.accountTable-verticalAlignTop .tableHead-sub>div:first-child {
    border-left: 0;
}

.accountTable .tableHead,
.accountTable-verticalAlignTop .tableHead {
    border-bottom: solid 1px #5574a7;
}

.accountTable .tableHead>div,
.accountTable-verticalAlignTop .tableHead>div {
    color: #ffffff;
}

.accountTable .tableHead-sub,
.accountTable-verticalAlignTop .tableHead-sub {
    border-bottom: 1px solid #3b5174;
}

.accountTable .tableHead-sub>div,
.accountTable-verticalAlignTop .tableHead-sub>div {
    color: rgba(255, 255, 255, 0.6);
}

.accountTable .tableHead-sub+.tableHead,
.accountTable-verticalAlignTop .tableHead-sub+.tableHead {
    border-radius: 0;
}

.accountTable .tableHead-sub .tableHead-col-crossing,
.accountTable-verticalAlignTop .tableHead-sub .tableHead-col-crossing {
    text-align: center;
}

.accountTable .tableBody .tableRow,
.accountTable-verticalAlignTop .tableBody .tableRow,
.accountTable .tableBody .tableRow-pointer,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer {
    background: #c6d4f1;
    border-bottom-width: 1px;
}

.accountTable .tableBody .tableRow>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow>div+div,
.accountTable .tableBody .tableRow-pointer>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer>div+div {
    border-left-width: 1px;
}

.accountTable .tableBody .tableRow .tableGroup,
.accountTable-verticalAlignTop .tableBody .tableRow .tableGroup,
.accountTable .tableBody .tableRow-pointer .tableGroup,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer .tableGroup {
    display: table;
    table-layout: fixed;
    width: 100%;
    padding: 0;
}

.accountTable .tableBody .tableRow .tableGroup+.tableGroup,
.accountTable-verticalAlignTop .tableBody .tableRow .tableGroup+.tableGroup,
.accountTable .tableBody .tableRow-pointer .tableGroup+.tableGroup,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer .tableGroup+.tableGroup {
    border-top-width: 1px;
    border-left-width: 1px;
}

.accountTable .tableBody .tableRow .tableGroup>div,
.accountTable-verticalAlignTop .tableBody .tableRow .tableGroup>div,
.accountTable .tableBody .tableRow-pointer .tableGroup>div,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer .tableGroup>div {
    height: 100%;
}

.accountTable .tableBody .tableRow .tableGroup>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow .tableGroup>div+div,
.accountTable .tableBody .tableRow-pointer .tableGroup>div+div,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer .tableGroup>div+div {
    border-left-width: 1px;
}

.accountTable .tableBody .tableRow:nth-of-type(2n+0),
.accountTable-verticalAlignTop .tableBody .tableRow:nth-of-type(2n+0),
.accountTable .tableBody .tableRow-pointer:nth-of-type(2n+0),
.accountTable-verticalAlignTop .tableBody .tableRow-pointer:nth-of-type(2n+0) {
    background: #e4e4e4;
}

.accountTable .tableBody .tableRow:not(.no-hover):hover,
.accountTable-verticalAlignTop .tableBody .tableRow:not(.no-hover):hover,
.accountTable .tableBody .tableRow-pointer:not(.no-hover):hover,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer:not(.no-hover):hover,
.accountTable .tableBody .tableRow.hover,
.accountTable-verticalAlignTop .tableBody .tableRow.hover,
.accountTable .tableBody .hover.tableRow-pointer,
.accountTable-verticalAlignTop .tableBody .hover.tableRow-pointer {
    background: #f5eeb8;
}

.accountTable .tableBody .tableRow.void,
.accountTable-verticalAlignTop .tableBody .tableRow.void,
.accountTable .tableBody .void.tableRow-pointer,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer {
    background-color: #bbbbbb;
    border-color: #a1a1a1;
}

.accountTable .tableBody .tableRow.void>div,
.accountTable-verticalAlignTop .tableBody .tableRow.void>div,
.accountTable .tableBody .void.tableRow-pointer>div,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer>div {
    border-color: inherit;
}

.accountTable .tableBody .tableRow.void>.odds,
.accountTable-verticalAlignTop .tableBody .tableRow.void>.odds,
.accountTable .tableBody .void.tableRow-pointer>.odds,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer>.odds {
    color: inherit;
}

.accountTable .tableBody .tableRow.void>.odds .point,
.accountTable-verticalAlignTop .tableBody .tableRow.void>.odds .point,
.accountTable .tableBody .void.tableRow-pointer>.odds .point,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer>.odds .point {
    color: inherit;
}

.accountTable .tableBody .tableRow.void>.stake,
.accountTable-verticalAlignTop .tableBody .tableRow.void>.stake,
.accountTable .tableBody .void.tableRow-pointer>.stake,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer>.stake {
    color: inherit;
}

.accountTable .tableBody .tableRow.void .betInfo,
.accountTable-verticalAlignTop .tableBody .tableRow.void .betInfo,
.accountTable .tableBody .void.tableRow-pointer .betInfo,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .betInfo {
    background-color: transparent;
}

.accountTable .tableBody .tableRow.void .mainInfo,
.accountTable-verticalAlignTop .tableBody .tableRow.void .mainInfo,
.accountTable .tableBody .void.tableRow-pointer .mainInfo,
.accountTable-verticalAlignTop .tableBody .void.tableRow-pointer .mainInfo {
    text-decoration: none;
}

.accountTable .tableBody .tableRow-pointer,
.accountTable-verticalAlignTop .tableBody .tableRow-pointer {
    cursor: pointer;
}

.accountTable .tableBody .expandArea,
.accountTable-verticalAlignTop .tableBody .expandArea {
    display: block;
    background: #dfdfdf;
    padding: 0.833em;
    border-bottom-width: 1px;
}

.accountTable .tableBody .expandAreaLayout,
.accountTable-verticalAlignTop .tableBody .expandAreaLayout {
    margin: -0.5em;
    display: block;
}

.accountTable .tableBody .expandAreaLayout::before,
.accountTable-verticalAlignTop .tableBody .expandAreaLayout::before,
.accountTable .tableBody .expandAreaLayout::after,
.accountTable-verticalAlignTop .tableBody .expandAreaLayout::after {
    content: "";
    display: block;
    clear: both;
}

.accountTable .tableBody .expandAreaLayout li,
.accountTable-verticalAlignTop .tableBody .expandAreaLayout li {
    display: block;
    float: left;
    width: calc( (100% - 4em)/4);
    padding: 0.5em;
}

.accountTable .tableFooter,
.accountTable-verticalAlignTop .tableFooter {
    background: #ffddd2;
    border-bottom: 1px solid #cea193;
}

.accountTable .tableFooter .total,
.accountTable-verticalAlignTop .tableFooter .total {
    text-align: right;
    font-weight: bold;
}

.accountTable .tableFooter.void,
.accountTable-verticalAlignTop .tableFooter.void {
    background-color: #bbbbbb;
    border-color: #a1a1a1;
}

.accountTable .tableFooter.void div,
.accountTable-verticalAlignTop .tableFooter.void div,
.accountTable .tableFooter.void span,
.accountTable-verticalAlignTop .tableFooter.void span {
    color: inherit;
    border-color: inherit;
}

.accountTable .leagueName,
.accountTable-verticalAlignTop .leagueName {
    display: block;
    background: #b1b1b1;
    border-bottom: 1px solid #a3a3a3;
    padding: 0.5em;
    color: black;
}

.accountTable .otherTableBody .leagueName,
.accountTable-verticalAlignTop .otherTableBody .leagueName {
    padding: 0.15em 0.833em;
}

.accountTable .smallBtn,
.accountTable-verticalAlignTop .smallBtn,
.accountTable .largeBtn,
.accountTable-verticalAlignTop .largeBtn {
    float: none;
    margin-left: 0;
}

.accountTable .largeBtn,
.accountTable-verticalAlignTop .largeBtn {
    margin: -.3em -2.5em;
}

.accountTable .no,
.accountTable-verticalAlignTop .no {
    width: 5%;
}

.accountTable .no-large,
.accountTable-verticalAlignTop .no-large {
    width: 20%;
}

.accountTable .date,
.accountTable-verticalAlignTop .date {
    width: 20%;
}

.accountTable .date-small,
.accountTable-verticalAlignTop .date-small {
    width: 16%;
}

.accountTable .date-smaller,
.accountTable-verticalAlignTop .date-smaller {
    width: 10%;
}

.accountTable .date-smallest,
.accountTable-verticalAlignTop .date-smallest {
    width: 7%;
}

.accountTable .choice .betInfo,
.accountTable-verticalAlignTop .choice .betInfo,
.accountTable .event .betInfo,
.accountTable-verticalAlignTop .event .betInfo {
    background: none;
    padding: 0;
}

.accountTable .choice .betInfo+.betInfo,
.accountTable-verticalAlignTop .choice .betInfo+.betInfo,
.accountTable .event .betInfo+.betInfo,
.accountTable-verticalAlignTop .event .betInfo+.betInfo {
    border: 0;
}

.accountTable .choice .mainInfo+.mainInfo,
.accountTable-verticalAlignTop .choice .mainInfo+.mainInfo,
.accountTable .event .mainInfo+.mainInfo,
.accountTable-verticalAlignTop .event .mainInfo+.mainInfo {
    margin-top: 0.5em;
}

.accountTable .match .homeName,
.accountTable-verticalAlignTop .match .homeName,
.accountTable .match .awayName,
.accountTable-verticalAlignTop .match .awayName {
    max-width: 46%;
    color: #01122b;
}

.accountTable .gameTypes,
.accountTable-verticalAlignTop .gameTypes {
    font-weight: bold;
    color: #01122b;
}

.accountTable .remark,
.accountTable-verticalAlignTop .remark {
    font-weight: bold;
}

.accountTable .team,
.accountTable-verticalAlignTop .team {
    color: #01122b;
    font-weight: bold;
}

.accountTable .team>div,
.accountTable-verticalAlignTop .team>div {
    float: left;
}

.accountTable .team>.smallBtn,
.accountTable-verticalAlignTop .team>.smallBtn {
    font-weight: normal;
    cursor: default;
}

.accountTable .team div+div,
.accountTable-verticalAlignTop .team div+div {
    margin-left: 0.25em;
}

.accountTable .team .place,
.accountTable-verticalAlignTop .team .place {
    min-width: 1.5em;
    white-space: nowrap;
    text-align: center;
}

.accountTable .team .id,
.accountTable-verticalAlignTop .team .id {
    width: 1.5em;
    color: #435f8b;
    text-align: center;
}

.accountTable .team .primary,
.accountTable-verticalAlignTop .team .primary {
    color: #2556b3;
}

.accountTable .team:after,
.accountTable-verticalAlignTop .team:after {
    content: "";
    display: block;
    clear: both;
}

.accountTable .amount,
.accountTable-verticalAlignTop .amount {
    width: 20%;
    color: #01122b;
    font-weight: bold;
    text-align: right;
}

.accountTable .odds,
.accountTable-verticalAlignTop .odds {
    width: 10%;
    text-align: right;
}

.accountTable .odds .point,
.accountTable-verticalAlignTop .odds .point {
    font-weight: bold;
    color: #01122b;
}

.accountTable .odds .accent,
.accountTable-verticalAlignTop .odds .accent {
    color: #b53f39;
}

.accountTable .stake,
.accountTable-verticalAlignTop .stake {
    text-align: right;
    width: 10%;
}

.accountTable .status,
.accountTable-verticalAlignTop .status,
.accountTable .status-smaller,
.accountTable-verticalAlignTop .status-smaller {
    width: 15%;
    position: relative;
}

.accountTable .status .smallBtn.icon-arrow-down,
.accountTable-verticalAlignTop .status .smallBtn.icon-arrow-down,
.accountTable .status-smaller .smallBtn.icon-arrow-down,
.accountTable-verticalAlignTop .status-smaller .smallBtn.icon-arrow-down,
.accountTable .status .smallBtn.icon-arrow-up,
.accountTable-verticalAlignTop .status .smallBtn.icon-arrow-up,
.accountTable .status-smaller .smallBtn.icon-arrow-up,
.accountTable-verticalAlignTop .status-smaller .smallBtn.icon-arrow-up {
    float: right;
}

.accountTable .status .accent,
.accountTable-verticalAlignTop .status .accent,
.accountTable .status-smaller .accent,
.accountTable-verticalAlignTop .status-smaller .accent {
    font-weight: bold;
}

.accountTable .status .focus,
.accountTable-verticalAlignTop .status .focus,
.accountTable .status-smaller .focus,
.accountTable-verticalAlignTop .status-smaller .focus {
    color: #01122b;
    font-weight: bold;
}

.accountTable .status .mark,
.accountTable-verticalAlignTop .status .mark,
.accountTable .status-smaller .mark,
.accountTable-verticalAlignTop .status-smaller .mark {
    color: #5574a7;
    font-weight: bold;
}

.accountTable .status .popupPanel,
.accountTable-verticalAlignTop .status .popupPanel,
.accountTable .status-smaller .popupPanel,
.accountTable-verticalAlignTop .status-smaller .popupPanel {
    top: 0;
    right: 110%;
}

.accountTable .status-smaller,
.accountTable-verticalAlignTop .status-smaller {
    width: 9%;
}

.accountTable .turnover,
.accountTable-verticalAlignTop .turnover,
.accountTable .credit,
.accountTable-verticalAlignTop .credit,
.accountTable .commission,
.accountTable-verticalAlignTop .commission,
.accountTable .balance,
.accountTable-verticalAlignTop .balance {
    width: 13%;
    text-align: right;
    font-weight: bold;
    color: #01122b;
}

.accountTable .betTypes,
.accountTable-verticalAlignTop .betTypes {
    width: 15%;
    color: #01122b;
    font-weight: bold;
}

.accountTable .other,
.accountTable-verticalAlignTop .other,
.accountTable .other-large,
.accountTable-verticalAlignTop .other-large {
    text-align: center;
}

.accountTable .other,
.accountTable-verticalAlignTop .other {
    width: 5%;
}

.accountTable .other-large,
.accountTable-verticalAlignTop .other-large {
    width: 14%;
}

.accountTable .points,
.accountTable-verticalAlignTop .points,
.accountTable .points-large,
.accountTable-verticalAlignTop .points-large,
.accountTable .points-small,
.accountTable-verticalAlignTop .points-small,
.accountTable .points-smaller,
.accountTable-verticalAlignTop .points-smaller,
.accountTable .points-smallest,
.accountTable-verticalAlignTop .points-smallest,
.accountTable .points-flexible,
.accountTable-verticalAlignTop .points-flexible,
.accountTable .pointsX2,
.accountTable-verticalAlignTop .pointsX2,
.accountTable .pointsX3,
.accountTable-verticalAlignTop .pointsX3 {
    text-align: center;
    width: 8em;
    max-width: 8em !important;
    color: #01122b;
    font-weight: bold;
}

.MessageContainer [class*="points"] {
    font-weight: normal;
}

.accountTable .points-large,
.accountTable-verticalAlignTop .points-large {
    width: 11em;
    max-width: 11em !important;
}

.accountTable .points-small,
.accountTable-verticalAlignTop .points-small {
    width: 5em;
    max-width: 5em !important;
}

.accountTable .points-smaller,
.accountTable-verticalAlignTop .points-smaller {
    width: 3.5em;
    max-width: 3.5em !important;
}

.accountTable .points-smallest,
.accountTable-verticalAlignTop .points-smallest {
    width: 2.5em;
    max-width: 2.5em !important;
}

.accountTable .points-flexible,
.accountTable-verticalAlignTop .points-flexible {
    width: auto;
    max-width: auto !important;
}

.accountTable .pointsX2,
.accountTable-verticalAlignTop .pointsX2 {
    width: 16em;
    max-width: 16em !important;
}

.accountTable .pointsX3,
.accountTable-verticalAlignTop .pointsX3 {
    width: 24em;
    max-width: 24em !important;
}

.accountTable .text,
.accountTable-verticalAlignTop .text,
.accountTable .text-auto,
.accountTable-verticalAlignTop .text-auto {
    width: 20%;
    color: #01122b;
    font-weight: bold;
}

.MessageContainer .text,
.MessageContainer .accountTable .text-auto,
.accountTable .MessageContainer .text-auto,
.MessageContainer .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .MessageContainer .text-auto {
    font-weight: normal;
}

.accountTable .text-auto,
.accountTable-verticalAlignTop .text-auto {
    width: auto;
}

.accountTable .popupPanel .text,
.accountTable-verticalAlignTop .popupPanel .text,
.accountTable .popupPanel .text-auto,
.accountTable-verticalAlignTop .popupPanel .text-auto {
    width: auto;
    color: #ffffff;
}

.accountTable .ball,
.accountTable-verticalAlignTop .ball {
    width: 17%;
    text-align: center;
}

.accountTable-verticalAlignTop div {
    vertical-align: top;
}

.tabNav,
.tabNav-BottomLine {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    border-bottom: 2px solid #5574a7;
    margin-bottom: 0.2em;
}

.tabNav li,
.tabNav-BottomLine li {
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 0.5em;
    border-radius: 3px 3px 0 0;
    background: #c6ced8;
    color: #435f8b;
    text-align: center;
    cursor: pointer;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.tabNav li+li,
.tabNav-BottomLine li+li {
    margin-left: 4px;
}

.tabNav li:hover,
.tabNav-BottomLine li:hover {
    background: #adbed6;
}

.tabNav li.active,
.tabNav-BottomLine li.active {
    color: #ffffff;
    background: #5574a7;
    cursor: default;
}

.tabNav+.accountTable .tableHead,
.tabNav+.accountTable-verticalAlignTop .tableHead,
.tabNav+.accountTable-verticalAlignTop .tableHead,
.tabNav-BottomLine+.accountTable .tableHead,
.tabNav-BottomLine+.accountTable-verticalAlignTop .tableHead,
.tabNav-BottomLine+.accountTable-verticalAlignTop .tableHead {
    border-radius: 0;
}

.tabNav+.filterBlock,
.tabNav-BottomLine+.filterBlock {
    margin: -0.2em 0 0.2em;
}

.tabNav+.panel,
.tabNav-BottomLine+.panel {
    margin-top: -0.2em;
    border-radius: 0 0 3px 3px;
}

.paymentHolder {
    background: #ececec;
    border: 1px solid #dfdfdf;
    border-radius: 3px;
    padding: 0.5em 0em 0em 0.5em;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-bottom: 0.5em;
}

.paymentHolder-item {
    border: 1px solid #dfdfdf;
    border-radius: 3px;
    margin-bottom: 0.5em;
    margin-right: 0.5em;
    cursor: pointer;
    position: relative;
}

.paymentHolder-itemActive {
    border-width: 2px;
    border-color: #5574a7;
    cursor: default;
}

.paymentHolder-item-text {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: block;
    padding: 0.5em;
    font-weight: bold;
    z-index: 1;
}

.paymentHolder-item-img {
    border-radius: inherit;
}

.paymentHolder-item-img:hover {
    -webkit-filter: brightness(1.1);
    filter: brightness(1.1);
}

.paymentHolder-itemActive .paymentHolder-item-img:hover {
    -webkit-filter: brightness(1);
    filter: brightness(1);
}

.otherTableRow,
.otherTableTotal .totalRow,
.otherTableHead,
.otherTableHead-sub {
    display: table;
    width: 100%;
    text-align: center;
}

.otherTableRow>div,
.otherTableTotal .totalRow>div,
.otherTableHead>div,
.otherTableHead-sub>div,
.otherTableRow .otherTableGroup>div,
.gameResult .team {
    display: table-cell;
    padding: 0.3em;
}

.otherTable .rowUnit+.rowUnit,
.otherTableHead,
.otherTableHead-sub>div,
.scoreMap .otherTableHead-sub,
.otherTableRow,
.otherTableRow>div+div,
.otherTableRow .otherTableGroup+.otherTableGroup,
.otherTableRow .otherTableGroup>div+div {
    border: 0 solid #cdcdcd;
}

.otherTableRow,
.otherTableTotal .totalRow {
    color: black;
    font-weight: bold;
}

.otherTableRow .accent,
.otherTableTotal .totalRow .accent {
    color: #b53f39;
}

.otherTableRow .primary,
.otherTableTotal .totalRow .primary {
    color: #2556b3;
}

.otherTable .rowHead {
    background: #bbbbbb;
    color: #ffffff;
}

.otherTable .rowUnit {
    display: block;
    margin-left: -0.833em;
    margin-right: -0.833em;
    padding: 0.3em 0.833em;
}

.otherTable .rowUnit+.rowUnit {
    border-top-width: 1px;
}

.otherTable .checked {
    background: #feec6e;
}

.otherTable .current {
    background: #ffccbc;
}

.otherTable .waiting {
    background: #ffddd2;
}

.otherTable .disable {
    background: #848484;
}

.otherTable .disable.current {
    background: #561500;
}

.otherTable .disable.current {
    background: #be8270;
}

.otherTable .disable.waiting {
    background: #cea193;
}

.otherTable .disable .accent {
    color: #7b2b27;
}

.otherTable .highlight {
    background: #feec6e;
}

.otherTable .status,
.otherTable .accountTable .status-smaller,
.accountTable .otherTable .status-smaller,
.otherTable .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .otherTable .status-smaller {
    text-align: left;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.otherTable .text-right {
    text-align: right !important;
}

.otherTable .text-center {
    text-align: center !important;
}

.otherTable.pointer .otherTableRow:hover {
    background: white;
}

.otherTable.pointer .otherTableRow [class*="colUnit"] {
    cursor: pointer;
}

.otherTable.pointer .otherTableRow [class*="colUnit"].disable {
    cursor: default;
}

.otherTable.col1 .colUnit {
    width: 100%;
}

.otherTable.col2 .colUnit {
    width: 50%;
}

.otherTable.col3 .colUnit {
    width: 33.33%;
}

.otherTable.col3.extra1 .colUnit {
    width: 33.33%;
    vertical-align: middle;
}

.otherTable.col4 .colUnit {
    width: 25%;
}

.otherTable.col5 .colUnit,
.otherTable.col5 .rowHead {
    width: 20%;
}

.otherTable.col5 .colUnit-crossing-4col {
    width: 80%;
}

.otherTable.col5.extra2 .colUnit {
    width: 12%;
}

.otherTable.col5.extra2 .amount {
    width: 24%;
}

.otherTable.col5.extra3 .colUnit {
    width: 18%;
}

.otherTable.col5.extra3 .match {
    text-align: left;
}

.otherTable.col6 .colUnit,
.otherTable.col6 .rowHead {
    width: 16.6%;
    padding: 0.3em 0.1em;
}

.otherTable.col6 .colUnit-crossing-5col {
    width: 83.4%;
}

.otherTable.col6.extra1 .colUnit,
.otherTable.col6.extra2 .colUnit {
    width: 12%;
    padding: 0.3em;
}

.otherTable.col6.extra1 .time {
    vertical-align: middle;
}

.otherTable.col6.unite1 .colUnit,
.otherTable.col6.unite1 .rowHead {
    padding: 0.3em;
}

.otherTable.col7 .colUnit {
    width: 14.2%;
}

.otherTable.col7.extra1 .colUnit {
    width: 12%;
}

.otherTable.col13 .colUnit {
    width: 7.6%;
}

.otherTable.col14 .colUnit {
    width: 7.1%;
}

.otherTable.col14.extra1 .colUnit {
    width: 3.5em;
    max-width: 3.5em !important;
}

.otherTable.col14.extra1 .colUnit-crossing-2col {
    width: 7em;
}

.otherTable.col14.extra1 .colUnit-crossing-3col {
    width: 10.5em;
}

.otherTable.col14.extra1 .colUnit-crossing-4col {
    width: 14em;
}

.otherTable.col14.extra1 .colUnit-crossing-5col {
    width: 17.5em;
}

.otherTable.col14.extra1 .colUnit-crossing-6col {
    width: 21em;
}

.otherTable.col14.extra1 .colUnit-crossing-7col {
    width: 24.5em;
}

.otherTable.col14.extra1 .colUnit-crossing-8col {
    width: 28em;
}

.otherTable.col14.extra1 .colUnit-crossing-9col {
    width: 31.5em;
}

.otherTable.col14.extra1 .colUnit-crossing-10col {
    width: 35em;
}

.otherTable.col14.extra1 .colUnit-crossing-11col {
    width: 38.5em;
}

.otherTable.col14.extra1 .colUnit-crossing-12col {
    width: 42em;
}

.otherTable.col14.extra1 .colUnit-crossing-13col {
    width: 45.5em;
}

.otherTable .match .group {
    display: block;
}

.otherTable .match .group+.group {
    margin-top: 0.5em;
}

.otherTable .match .name,
.otherTable .match .other,
.otherTable .match .place,
.otherTable .match .smallBtn {
    display: inline-block;
    margin-right: 0.25em;
    margin-left: 0;
}

.otherTable .match .betInfo {
    background: none;
    padding: 0;
}

.otherTable .match .matchInfo-line {
    font-weight: normal;
}

.otherTable.extra1 .time,
.otherTable.extra2 .time,
.otherTable.extra3 .time {
    width: 17%;
}

.otherTable.extra1 .match,
.otherTable.extra2 .match,
.otherTable.extra3 .match {
    text-align: left;
}

.otherTable.extra1 .match .accent,
.otherTable.extra2 .match .accent,
.otherTable.extra3 .match .accent {
    color: #b53f39;
}

.otherTable.extra1 .match .place,
.otherTable.extra2 .match .place,
.otherTable.extra3 .match .place {
    min-width: 7%;
    white-space: nowrap;
    text-align: center;
}

.otherTable.extra1 .colUnit,
.otherTable.extra2 .colUnit,
.otherTable.extra3 .colUnit {
    width: 15%;
}

.otherTable.extra1 .status,
.otherTable.extra1 .accountTable .status-smaller,
.accountTable .otherTable.extra1 .status-smaller,
.otherTable.extra1 .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .otherTable.extra1 .status-smaller,
.otherTable.extra2 .status,
.otherTable.extra2 .accountTable .status-smaller,
.accountTable .otherTable.extra2 .status-smaller,
.otherTable.extra2 .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .otherTable.extra2 .status-smaller,
.otherTable.extra3 .status,
.otherTable.extra3 .accountTable .status-smaller,
.accountTable .otherTable.extra3 .status-smaller,
.otherTable.extra3 .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .otherTable.extra3 .status-smaller {
    width: 15%;
    max-width: 0;
}

.otherTable.extra2 .time {
    text-align: left;
}

.otherTable.extra2 .extraFont {
    color: #545454;
    font-weight: normal;
}

.otherTable.unite1 .time {
    width: 19%;
    white-space: nowrap;
}

.otherTable.unite1 .match {
    text-align: left;
}

.otherTable.unite1 .colUnit {
    width: 7em;
    max-width: 7em;
}

.otherTable.unite1 .status,
.otherTable.unite1 .accountTable .status-smaller,
.accountTable .otherTable.unite1 .status-smaller,
.otherTable.unite1 .accountTable-verticalAlignTop .status-smaller,
.accountTable-verticalAlignTop .otherTable.unite1 .status-smaller {
    width: 7em;
    max-width: 7em;
}

.otherTable .smallBtn {
    float: none;
    margin-left: 0.25em;
    cursor: default;
    font-weight: normal;
}

.otherTable .smallBtn.icon-info {
    cursor: pointer;
}

.otherTableHead {
    color: #ffffff;
    background: #a3a3a3;
    border-bottom-width: 1px;
}

.otherTableHead>div {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 0;
}

.otherTableHead-sub {
    color: #ffffff;
    background: #a3a3a3;
}

.otherTableHead-sub>div {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 0;
    border-bottom-width: 1px;
}

.otherTableHead-sub .rowHead {
    border-bottom-width: 0px;
}

.scoreMap .otherTableHead-sub {
    border-top-width: 1px;
}

.otherTableBody .leagueName {
    display: block;
    background: #b1b1b1;
    border-bottom: 1px solid #a3a3a3;
    padding: 0.15em 0.833em;
    color: black;
}

.otherTableBody .colUnit-crossing {
    width: 100%;
}

.otherTableRow {
    background: white;
    border-bottom-width: 1px;
}

.otherTableRow:not(.no-hover):hover,
.otherTableRow.hover {
    background: #f5eeb8;
}

.otherTableRow>div+div {
    border-left-width: 1px;
}

.otherTableRow .otherTableGroup {
    display: table;
    width: 100%;
    padding: 0;
    background: white;
}

.otherTableRow .otherTableGroup:hover {
    background: #f5eeb8;
}

.otherTableRow .otherTableGroup+.otherTableGroup {
    border-top-width: 1px;
    border-left-width: 1px;
}

.otherTableRow .otherTableGroup>div+div {
    border-left-width: 1px;
}

.otherTableRow .WinResult {
    background: #d9d9d9;
    display: inline-block;
    border-radius: 3px;
    padding: 0.3em 0.8em;
    margin: 0.5em;
}

.otherTableRow .WinResult>em {
    color: #b53f39;
    font-style: normal;
}

.otherTableTotal .totalRow {
    background: #ffddd2;
}

.otherTableTotal .totalRow .total {
    text-align: right;
    color: #545454;
}

.sport-typeTag {
    display: inline-block;
    color: #ffffff;
    background: #545454;
    border-radius: 3px;
    padding: 0.18em 0.5em;
}

.sport-typeTag+.sport-typeTag {
    margin: 0.167em 0 0.167em 0.167em;
}

.breadcrumbs {
    display: inline-block;
}

.breadcrumbs a {
    color: #5574a7;
    text-decoration: none;
}

.breadcrumbs>li {
    display: inline-block;
}

.breadcrumbs>li::after {
    margin: 0 0.3em;
    content: "";
    font-weight: bolder;
    position: relative;
    top: .15em;
}

.singlePage {
    overflow: hidden;
}

.singlePage .title {
    color: #5574a7;
    font-weight: bolder;
    font-size: 2.8em;
    text-align: center;
}

.singlePage .title h1 {
    line-height: 1em;
    font-weight: 900;
}

.singlePage .title h2 {
    font-size: .7em;
    margin-top: 1.5em;
}

.singlePage .title span {
    display: block;
    color: #545454;
    font-size: 13px;
    margin: 1em 0 2em;
}

.singlePage .primary {
    color: #5574a7;
}

.singlePage .loginBox {
    width: 24em;
    margin: 2em auto 0;
    border-radius: 3px;
    background-color: #f6f9fb;
    border: 1px solid #ffffff;
    padding: 3em 4em;
    text-align: left;
}

.singlePage .loginBox .largeBtn {
    width: 92%;
    float: none;
    margin-left: 0;
    font-size: 1.1em;
}

.singlePage .loginBox .icon-earth,
.singlePage .loginBox .icon-account,
.singlePage .loginBox .icon-lock {
    position: relative;
}

.singlePage .loginBox .icon-earth::before,
.singlePage .loginBox .icon-account::before,
.singlePage .loginBox .icon-lock::before {
    position: absolute;
    z-index: 1;
    left: .7em;
    color: #5574a7;
    display: inline-block;
}

.singlePage .loginBox .icon-earth .formInput,
.singlePage .loginBox .icon-account .formInput,
.singlePage .loginBox .icon-lock .formInput {
    margin-bottom: 0;
}

.singlePage .loginBox .icon-earth::before {
    top: .5em;
}

.singlePage .loginBox .icon-earth .form {
    padding-left: 1.7em;
}

.singlePage .loginBox .icon-account::before,
.singlePage .loginBox .icon-lock::before {
    top: 1.3em;
}

.singlePage .loginBox .icon-account .form,
.singlePage .loginBox .icon-lock .form {
    padding-left: 1.5em;
}

.singlePage .loginBox .icon-earth::before,
.singlePage .loginBox .icon-lock::before {
    -ms-transform: scale(1.4);
    -webkit-transform: scale(1.4);
    transform: scale(1.4);
}

.singlePage .loginBox .icon-account::before {
    -ms-transform: scale(1.8);
    -webkit-transform: scale(1.8);
    transform: scale(1.8);
}

.singlePage .loginBox .code,
.singlePage .loginBox .clear,
.singlePage .loginBox .formInput {
    margin-bottom: 1em;
}

.singlePage .loginBox .code {
    text-align: center;
    display: table;
    width: 100%;
}

.singlePage .loginBox .code .fixed {
    display: table-cell;
    vertical-align: middle;
}

.singlePage .loginBox .code .fixed img {
    height: 30px;
    width: 100%;
}

.singlePage .loginBox .clear {
    position: relative;
}

.singlePage .loginBox .icon-clear {
    position: absolute;
    right: .6em;
    top: .6em;
    z-index: 1;
}

.singlePage .loginBox .form {
    font-size: 1.2em;
    width: 100%;
    float: none;
    border: 1px solid #5574a7;
    box-sizing: border-box;
}

.singlePage .loginBox .form.dropdown {
    font-size: 1.3em;
}

.singlePage .loginBox .form.dropdown .selected {
    padding-top: 0;
}

.singlePage .loginBox .form.dropdown .dropdownPanel {
    border: 1px solid #5574a7;
    border-top-color: #ffffff;
    width: 100%;
}

.singlePage .loginBox .form.btn {
    display: inline-block;
    border: 1px solid #cdcdcd;
    padding: 0.15em 0.5em;
}

.singlePage .countdown {
    margin: 1em 0;
}

.singlePage .countdown>div {
    text-align: center;
    display: inline-block;
    margin-right: 15px;
}

.singlePage .countdown>div .box {
    line-height: 100px;
    margin-bottom: 5px;
    width: 100px;
    height: 100px;
    border-radius: 3px;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    background-color: #ffffff;
    color: #9c0000;
    font-size: 4.5em;
    font-weight: bolder;
}

.singlePage .countdown>div .sec {
    margin-right: 0;
}

.singlePage .formInput {
    float: none;
    margin-left: 0;
    font-size: 1.2em;
    color: #5574a7;
    position: relative;
}

.singlePage .formInput input {
    margin-top: 0.5em;
}

.static .oddsTitle,
.static .normal-a,
.static .normal-b {
    display: table;
    width: 100%;
    border-collapse: separate;
}

.static .singlesCombos .odds,
.singlesCombos .static .odds {
    display: table-cell;
    padding: 0.18em 0.15em;
    max-width: 0;
}

.static .oddsTitle {
    background: #5574a7;
    border: 1px solid #5574a7;
    box-sizing: border-box;
}

.static .oddsTitle>div {
    color: #ffffff;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.static .normal-a {
    background: #c6d4f1;
    border-bottom: 1px solid #bbbbbb;
}

.static .normal-b {
    background: #e4e4e4;
    border-bottom: 1px solid #bbbbbb;
}

.static table.combination td {
    border-left: 1px solid #bbbbbb;
}

.static table.combination td:first-child {
    border-left: none;
}

.static table.combination thead tr:first-child th {
    color: #ffffff;
}

.static table.combination thead tr:first-child th:first-child {
    border-radius: 3px 0 0 0;
}

.static table.combination thead tr:first-child th:last-child {
    border-radius: 0 3px 0 0;
}

.static table.combination thead tr:last-child {
    border-top: 1px solid #3b5174;
}

.static table {
    width: 100%;
}

.static table td,
.static table th {
    text-align: center;
    padding: 0.18em 0.15em;
    vertical-align: middle;
}

.static table thead {
    background: #5574a7;
    color: #ffffff;
}

.static table thead tr:first-child td,
.static table thead tr:first-child th {
    color: rgba(255, 255, 255, 0.6);
    border-bottom: 1px solid #3b5174;
}

.static table thead tr:first-child td:first-child,
.static table thead tr:first-child th:first-child {
    border-radius: 3px 0 0 0;
}

.static table thead tr:first-child td:last-child,
.static table thead tr:first-child th:last-child {
    border-radius: 0 3px 0 0;
}

.static table tbody tr {
    background: #c6d4f1;
    border-bottom: 1px solid #bbbbbb;
}

.static table tbody tr:nth-child(even) {
    background: #e4e4e4;
    border-bottom: 1px solid #bbbbbb;
}

.static table tfoot tr {
    background: #ffddd2;
    border-bottom: 1px solid #cea193;
}

.static table .border {
    border-left: 1px solid #3b5174;
}

.static table .liveligh {
    background-color: #ffddd2;
}

.static table .trbgov {
    background-color: #f5eeb8;
}

.bg-tool::before,
.bg-logout::before,
.bg-tool::after,
.bg-logout::after {
    font-family: "iconFont";
    font-size: 25em;
    color: white;
    position: absolute;
}

.bg-tool::before,
.bg-logout::before {
    opacity: 0.7;
    line-height: 1em;
}

.bg-tool::after,
.bg-logout::after {
    z-index: -1;
    opacity: 0.7;
}

.bg-tool::before {
    content: "";
    left: -100px;
    bottom: 60px;
}

.bg-tool::after {
    content: "";
    right: -100px;
    top: 20px;
}

.bg-logout::before {
    content: "";
    left: -30px;
    top: 100px;
    transform: scaleX(-1);
}

.bg-logout::after {
    content: "";
    right: -30px;
    bottom: 0;
}

.bg-logout .icon-users {
    display: inline-block;
    margin-bottom: 30px;
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5;
    font-size: 6em;
    color: #ffffff;
    background-color: #adbed6;
    border-radius: 50%;
}

.um {
    line-height: 1.8em;
}

.um::after {
    content: "";
    display: block;
    clear: both;
}

.um .mainContent {
    max-width: 900px;
    margin: 0 auto;
}

.um .mainContent>* {
    float: left;
    width: 50%;
    text-align: left;
}

.um .title {
    text-align: left;
}

.um .infoBox {
    padding-bottom: 1em;
    margin-bottom: 1em;
}

.um .infoBox:first-child {
    border-bottom: 1px solid #d5e0f0;
}

.um .infoBox:last-child {
    padding-top: 1em;
}

.um .infoBox p {
    margin-bottom: 1em;
    line-height: 1.5em;
    font-size: 15px;
}

.um .accent {
    color: #b53f39;
    font-weight: bold;
}

.iconWithBg {
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
    border-radius: 3px;
    position: relative;
    float: left;
    width: 5.5em;
    height: 5.5em;
    text-align: center;
    padding: 0.5em;
    margin-right: 0.833em;
}

.iconWithBg::before {
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-top: -1.5rem;
    margin-left: -1.5rem;
    width: 3rem;
    height: 3rem;
    line-height: 1;
    color: #ffffff;
    font-size: 3rem;
    font-weight: lighter;
}

.iconWithBg img {
    width: 100%;
}

.netPosition .smallBtn {
    cursor: default;
}

.netPosition .smallBtn.primary:hover {
    background: #5574a7;
}

.netPosition::after {
    content: "";
    display: block;
    clear: both;
}

.netPosition .panelContent ol.examples {
    padding-left: 0;
}

.netPosition .panelContent ol.examples>* {
    list-style-type: none;
    margin-bottom: 4em;
}

.netPosition .panelContent ol.examples>*:last-child {
    margin-bottom: 1em;
}

.netPosition .location {
    position: relative;
    width: 25em;
}

.netPosition .location>.creditInfo {
    position: absolute;
    z-index: 9999;
    top: 13em;
    width: 100%;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    transform: scale(1.1, 1.1);
}

.netPosition .location>.creditInfo>* {
    margin-bottom: 0;
}

.netPosition .location.mask .contentArea:before {
    content: "";
    background-color: black;
    opacity: 0.5;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 999;
}

.netPosition .location .panelFunction {
    margin-bottom: 0.5em;
    position: relative;
}

.netPosition .location .panelFunction .floatLeft {
    float: left;
    padding: 0.2em 0.5em;
}

.netPosition .location .panelFunction .floatRight {
    float: right;
}

.netPosition .location .panelFunction::after {
    content: "";
    display: block;
    clear: both;
}

.netPositionExample {
    border-radius: 3px;
    background-color: #ffddd2;
    display: table;
    width: 100%;
}

.netPositionExample::after {
    content: "";
    display: block;
    clear: both;
}

.netPositionExample>* {
    padding: 0.5em;
}

.netPositionExample .info {
    background-color: #5574a7;
    color: #ffffff;
    border-radius: 3px 0 0 3px;
    width: 66.666666%;
    position: relative;
    display: table-cell;
}

.netPositionExample .info .infoTable {
    display: table;
    width: 100%;
}

.netPositionExample .info .infoTable>* {
    display: table-row;
}

.netPositionExample .info .infoTable>*>* {
    display: table-cell;
    padding: 0.3em;
}

.netPositionExample .info::before {
    content: "";
    position: absolute;
    border-width: 1em;
    border-style: solid;
    top: calc(100%/2 - 1em);
    right: -2em;
    border-color: transparent transparent transparent #5574a7;
}

.netPositionExample .result {
    width: 33.333333%;
    text-align: center;
    font-weight: bold;
    display: table-cell;
    vertical-align: middle;
}

.netPositionExample .result .accent {
    font-size: 4.5em;
    font-weight: 600;
    color: #323232;
    line-height: 1em;
}

.singlesCombos {
    overflow: hidden;
    width: 50%;
    border-radius: 3px 3px 0 0;
}

.singlesCombos .odds {
    text-align: center;
    width: 50%;
}

.singlesCombos .oddsContent .odds+.odds {
    border-left: 1px solid #bbbbbb;
}

.mixParlay button,
.howtouse__content button {
    cursor: default;
}

.mixParlay button.smallBtn-text,
.howtouse__content button.smallBtn-text {
    display: inline-block;
    float: none;
}

.mixParlay button.primary.smallBtn-text:hover,
.howtouse__content button.primary.smallBtn-text:hover {
    background: #5574a7;
}

.mixParlay button:hover,
.howtouse__content button:hover {
    cursor: default;
}

.mixParlay button.trigger.toggle:hover,
.howtouse__content button.trigger.toggle:hover {
    background: linear-gradient(to bottom, #cdcdcd 0%, #bbbbbb 100%);
    background: -webkit-linear-gradient(bottom, #bbbbbb 0%, #cdcdcd 100%);
}

.mixParlay button.trigger.toggle-primary:hover,
.howtouse__content button.trigger.toggle-primary:hover {
    background: linear-gradient(to bottom, #7591c1 0%, #5574a7 100%);
    background: -webkit-linear-gradient(bottom, #5574a7 0%, #7591c1 100%);
}

.mixParlay .league,
.howtouse__content .league {
    background: #b1b1b1;
}

.mixParlay .league .leagueName,
.howtouse__content .league .leagueName {
    cursor: default;
}

.mixParlay .accent,
.howtouse__content .accent {
    color: #b53f39;
}

.mixParlay .oddsTable,
.howtouse__content .oddsTable {
    position: relative;
}

.mixParlay .oddsTable .events,
.mixParlay .oddsTable .odds,
.mixParlay .oddsTable .others,
.howtouse__content .oddsTable .events,
.howtouse__content .oddsTable .odds,
.howtouse__content .oddsTable .others {
    padding: 0.18em 0.15em;
    display: table-cell;
    box-sizing: border-box;
}

.mixParlay .others,
.howtouse__content .others {
    vertical-align: middle;
    width: 4.5em;
    max-width: 4.5em;
    text-align: center;
}

.mixParlay .hdpou-a.extra-a .odds.subtxt,
.howtouse__content .hdpou-a.extra-a .odds.subtxt {
    width: 7em;
    max-width: 7em;
    text-align: right;
}

.mixParlay .hdpou-a.extra-a .oddsTitle .odds.subtxt,
.howtouse__content .hdpou-a.extra-a .oddsTitle .odds.subtxt {
    text-align: center;
}

.mixParlay .time,
.howtouse__content .time {
    width: 4em;
    vertical-align: middle;
    text-align: center;
}

.mixParlay .multiOdds,
.howtouse__content .multiOdds {
    display: table;
    table-layout: fixed;
    width: 100%;
}

.mixParlay .oddsBet:hover,
.howtouse__content .oddsBet:hover {
    cursor: text;
    background: none;
}

.mixParlay-remark {
    font-weight: bold;
    padding: 0 0 2em;
}

.mixParlay h3 {
    padding: 1em 0 0;
}

.mixParlay h4 {
    font-weight: bold;
    font-size: 1.2em;
}

.mixParlay-bet {
    position: relative;
    padding: 0 0 1em;
}

.mixParlay-bet::after {
    content: "";
    display: block;
    clear: both;
}

.mixParlay-bet .betSlip {
    width: 224px;
    float: left;
}

.mixParlay-bet .comboList {
    margin-top: 0.5em;
}

.mixParlay-bet .comboList li {
    padding-bottom: 0;
}

.mixParlay-bet .betInfo {
    position: relative;
}

.mixParlay-bet .teachingBox {
    position: absolute;
    width: calc( 100% - 214px);
    right: 0;
    padding-left: 214px;
    padding-top: 64px;
    padding-bottom: 50px;
}

.mixParlay-bet .teachingBox::after {
    content: "";
    display: block;
    clear: both;
}

.mixParlay-bet__teaching {
    margin-top: 40px;
    position: relative;
    font-weight: bold;
    border-radius: 0 3px 3px 0;
    background-color: #e4e4e4;
    padding: 0.3em 0.833em;
    border-left: 3px solid #5574a7;
}

.mixParlay-bet__teaching::before {
    content: "";
    position: absolute;
    border-width: 0.5em;
    border-style: solid;
    top: calc(100%/2 - 0.5em);
    left: -15px;
    border-color: transparent #5574a7 transparent transparent;
}

.mixParlay-bet__teaching:nth-child(2),
.mixParlay-bet__teaching:nth-child(3) {
    margin-top: 60px;
}

.mixParlay-bet__teaching:nth-child(4) {
    margin-top: 53px;
}

.mixParlay-bet__teaching:nth-child(5),
.mixParlay-bet__teaching:nth-child(6) {
    margin-top: 2px;
}

.mixParlay-bet__teachingAccent {
    font-weight: bold;
    margin-top: 30px;
    margin-left: 20px;
    border-radius: 3px;
    background-color: #ffccbc;
    padding: 0.5em 0.833em;
}

.mixParlay-bet .btnArea {
    padding: 0.5em 0 0;
    background-color: #fff;
}

.mixParlay .accountTable-verticalAlignTop {
    padding-top: 1em;
}

.mixParlay .accountTable-verticalAlignTop .tableHead>div {
    padding: 0.18em 0.3em;
}

.mixParlay .accountTable-verticalAlignTop .tableBody .tableRow:hover,
.mixParlay .accountTable-verticalAlignTop .tableBody .tableRow-pointer:hover {
    background: #c6d4f1;
}

.mixParlay .collapsible>li>a {
    padding-bottom: 0;
}

.gameResult .oddsTitle {
    text-align: center;
}

.gameResult .team {
    width: 50%;
}

.gameResult .team strong {
    color: #01122b;
}

.gameResult .team strong.accent {
    color: #b53f39;
}

.gameResult .odds {
    text-align: center;
    width: 50%;
    border-left: solid 1px #bbbbbb;
}

.static.faq .collapsible .innerContent {
    padding: 0.5em;
    padding-left: 2.5em;
}

.openAccount .title {
    font-size: 30px;
    font-weight: bolder;
    text-align: center;
    color: #233d67;
}

.howtouse__title .smallBtn {
    float: none;
    vertical-align: middle;
    font-weight: normal;
}

.howtouse__step {
    position: relative;
}

.howtouse__step--tips {
    position: relative;
}

.howtouse__step--tips.withBetlist .howtouse__content {
    width: 224px;
}

.howtouse__step--tips.withBetlist .howtouse__tips {
    padding-left: 224px;
}

.howtouse__step--process {
    max-width: 800px;
}

.howtouse__step--process .howtouse__title {
    position: relative;
    margin-bottom: 25px;
    padding: .2em;
    width: calc( 100% - 25px);
    text-align: center;
    color: #ffffff;
    background-color: #5574a7;
}

.howtouse__step--process .howtouse__title:after {
    content: " ";
    position: absolute;
    left: 100%;
    top: 50%;
    border-style: solid;
    border-color: transparent;
    border-left-color: #5574a7;
    border-width: 25px;
    margin-top: -25px;
}

.howtouse__step--process .howtouse__content {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.howtouse__step--process .howtouse__content>.ticketListGroup {
    box-sizing: border-box;
    padding: 0.3em;
    width: 30%;
    background-color: #ffffff;
    border-bottom: 2px solid #5574a7;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
}

.howtouse__step--process .howtouse__content>.icon-arrow-right {
    margin-top: 1.8em;
    font-size: 4em;
    color: #5574a7;
}

.howtouse__step--process .howtouse__content .btnArea {
    padding: .5em 0 0;
    background-color: transparent;
}

.howtouse__step--process .howtouse__content .btnArea .largeBtn {
    min-width: auto;
}

.howtouse__mask,
.howtouse__focus,
.howtouse__tips {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

.howtouse__mask {
    background-color: black;
    opacity: 0.5;
}

.howtouse__focus-item {
    position: absolute;
    transform: scale(2);
}

.howtouse__focus-item .icon-cashout {
    border: 1px solid #ffffff;
}

.howtouse__tips {
    z-index: 99;
    background: rgba(255, 255, 255, 0);
}

.howtouse__tips-row {
    position: relative;
    display: block;
    font-weight: bold;
    border-radius: 0 3px 3px 0;
    background-color: #e4e4e4;
    padding: 0.3em 0.833em;
    border-left: 3px solid #5574a7;
}

.howtouse__tips-row::before {
    content: "";
    position: absolute;
    border-width: 0.5em;
    border-style: solid;
    top: calc(100%/2 - 0.5em);
    left: -15px;
    border-color: transparent #5574a7 transparent transparent;
}

.withMask {
    position: relative;
}

.withMask:after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 99;
    background: rgba(255, 255, 255, 0);
}

[class^="howtouse"] .smallBtn {
    cursor: default;
}

[class^="howtouse"] .smallBtn:hover {
    background: #ffd330;
}

.bg-flash:before,
.btn-enableFlash span:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    line-height: 1;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Ctitle/%3E%3Cpath fill='%23FFF' d='M4 18.16s3.538-.032 5.04-3.9c0 0 3.755-8.454 6.044-9.904 0 0 2.117-2.066 4.916-2.355v3.873s-4.508 1.45-5.36 4.934h3.107v3H13.82S10.705 21.197 4 22v-3.84z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
}

.bg-flash {
    position: relative;
    min-width: auto;
    background: linear-gradient(to bottom, #a3a3a3 0%, #323232 100%);
    background: -webkit-linear-gradient(bottom, #323232 0%, #a3a3a3 100%);
}

.bg-flash:before {
    opacity: .15;
}

.enableFlash {
    position: absolute;
    left: 15px;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
    color: #ffffff;
    line-height: 1.2;
}

.enableFlash p {
    margin-bottom: 10px;
    font-size: 16px;
}

.enableFlash p:last-of-type {
    margin-bottom: 25px;
}

@media only screen and (max-width: 375px) {
    .enableFlash p {
        font-size: 12px;
        text-align: left;
    }
}

.btn-enableFlash {
    display: inline-block;
    position: relative;
    padding: .75em 1em .75em 4.25em;
    text-align: left;
    line-height: 1.2;
    font-size: 13px;
    font-weight: bold;
    color: #970d03;
    text-decoration: none;
    background-color: #ffffff;
    border: 0;
    border-radius: 1em;
    cursor: pointer;
}

.btn-enableFlash span {
    position: absolute;
    top: .6em;
    left: 1em;
    width: 2.5em;
    height: 2.5em;
    background: linear-gradient(to left, #600b06 0%, #990d03 100%);
    background: -webkit-linear-gradient(left, #990d03 0%, #600b06 100%);
    border-radius: .3em;
}

.btn-enableFlash span:before {
    background-size: 80%;
}

.btn-enableFlash:hover {
    background-color: #ececec;
}

header {
    padding: 0.5em 0.833em 0;
    width: calc(100% - 0.833em*2);
    background: #e8eff5;
    z-index: 32;
}

header.logoNavigationOnly {
    height: 8.25em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
}

header.darkBg {
    background-color: #5574a7;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.4);
    min-height: 7em;
}

.header-belt {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    height: 100%;
}

.logo {
    background-size: 90% auto;
    background-repeat: no-repeat;
    width: 224px;
    -ms-transform: translate(1em, 0.2em);
    -webkit-transform: translate(1em, 0.2em);
    transform: translate(1em, 0.2em);
}

.logo::before {
    content: "";
    background-position: 0 0;
    background-repeat: no-repeat;
    display: block;
    width: 3.9em;
    height: 4.2em;
    -ms-transform: translate(-0.9em, -0.8em);
    -webkit-transform: translate(-0.9em, -0.8em);
    transform: translate(-0.9em, -0.8em);
}

.darkBg .logo {
    margin-left: 50%;
    -ms-transform: translate(-50%, 2em);
    -webkit-transform: translate(-50%, 2em);
    transform: translate(-50%, 2em);
    -webkit-filter: drop-shadow(0 0 0.1rem rgba(0, 0, 0, 0.8));
    filter: drop-shadow(0 0 0.1rem rgba(0, 0, 0, 0.8));
}

.header-collapse .logo {
    width: 10em;
    height: 2em;
    background-size: 80% auto;
    -ms-transform: translate(2em, 0);
    -webkit-transform: translate(2em, 0);
    transform: translate(2em, 0);
}

.header-collapse .logo::before {
    content: "NEW";
    background-image: none;
    background-color: #9c0000;
    color: #ffffff;
    text-align: center;
    font-family: "Arial", "Tahoma", "pmingliu", "新細明體";
    -ms-transform: translate(-2em, -0.3em) scale(0.7);
    -webkit-transform: translate(-2em, -0.3em) scale(0.7);
    transform: translate(-2em, -0.3em) scale(0.7);
    width: 3em;
    height: 1.4em;
    border-radius: 3px;
}

.logo-demo {
    position: relative;
}

.logo-demo::after {
    content: "Demo";
    position: absolute;
    right: 3%;
    bottom: 5%;
    background-color: rgba(255, 0, 0, 0.75);
    color: #ffffff;
    text-align: center;
    font-family: "Arial", "Tahoma", "pmingliu", "新細明體";
    width: 3em;
    height: 1.4em;
    border-radius: 3px;
    font-size: 2em;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.header-collapse .logo-demo::after {
    right: 15%;
    bottom: 15%;
    font-size: 1em;
}

.header-belt-main {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    margin-left: 0.833em;
}

.header-belt-main-tool {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 0.5em;
}

.messages {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    position: relative;
}

.messages-marquee {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    cursor: pointer;
    padding: 0.2em 0.833em;
    height: 1.4em;
    overflow: hidden;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #cdcdcd;
}

.messages-marquee .accent {
    color: #b53f39;
}

.messages-marquee .secondary {
    color: #2556b3;
}

.messages-rightArea {
    margin-left: 0.25em;
}

.header-collapse .messages-marquee {
    padding-left: 0.833em;
    padding-right: 0.833em;
    background: transparent;
    border-color: transparent;
}

.header-collapse .messages-marquee:hover {
    background: #4c73b1;
}

@media only screen and (max-width: 1008px) {
    .header-collapse .messages {
        -webkit-box-flex: 0 0 2.5em;
        -webkit-flex: 0 0 2.5em;
        -ms-flex: 0 0 2.5em;
        flex: 0 0 2.5em;
    }
    .header-collapse .messages-marquee {
        display: none;
    }
    .header-collapse .messages-rightArea {
        margin-left: 0;
    }
}

.language {
    margin-left: 0.5em;
}

.logout {
    background: #c6ced8;
    color: #4b5d7b;
    font-weight: bold;
    border-radius: 3px;
    margin-left: 0.25em;
    width: 8.2em;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
    padding: 0.2em 0.3em;
}

.logout::before {
    content: "";
    margin-right: 0.25em;
    position: relative;
    top: .1em;
}

.logout:hover {
    background: #647ca2;
    color: #ffffff;
}

.header-collapse .logout {
    margin-left: 0;
    margin-right: 4px;
    padding: 0 2px;
    width: 24px;
    height: 22px;
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.header-collapse .logout::before {
    margin-right: 0;
    display: inline-block;
    top: auto;
    left: 1px;
    font-size: 14px;
    line-height: 1;
}

@-moz-document url-prefix() {
    .header-collapse .logout::before {
        margin-top: -1px;
    }
}

.header-collapse .logout:hover {
    background: #a8b4c3;
    color: #4b5d7b;
}

.nav-main {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.nav-main>li {
    position: relative;
    color: #ffffff;
    background: #7899c6;
    text-align: center;
    cursor: pointer;
    margin-right: 0.25em;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-width: 0;
    border-radius: 3px;
}

.nav-main>li>span {
    padding: 0.5em 0.3em;
    display: block;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.nav-main>li:last-child {
    margin-right: 0;
}

.nav-main>li:hover {
    background: #547eb7;
}

.nav-main>li.active {
    background: #243d66;
}

.nav-main>li .icon-betList::before,
.nav-main>li .icon-statement::before,
.nav-main>li .icon-result::before,
.nav-main>li .icon-message::before,
.nav-main>li .icon-preferences::before {
    font-size: 1.3em;
    position: relative;
    top: 0.15em;
    margin-right: 0.25em;
    line-height: 0;
    font-weight: normal;
}

.nav-main_noBottomRadius>li {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.nav-main-sub {
    position: absolute;
    min-width: calc(100% - 0.2em*2);
    z-index: 35;
    box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 0, 0, 0.4);
    display: none;
    border: 0.2em solid #547eb7;
    background: whitesmoke;
}

.nav-main-sub>li {
    padding: 0 0.5em;
    position: relative;
    text-align: left;
    color: #545454;
}

.nav-main-sub>li:hover {
    background: #d5e0f0;
}

.nav-main>li:hover .nav-main-sub {
    display: block;
}

.nav-main>li.active .nav-main-sub {
    border-color: #243d66;
}

.nav-main-sub .smallBtn.icon-new {
    float: none;
    position: absolute;
    right: 0.5em;
    top: 0.5em;
}

.nav-main-sub-Item {
    display: block;
    padding: 0.5em 3.5em 0.5em 0.5em;
    border-bottom: 0.1em solid #cdcdcd;
    white-space: nowrap;
}

.nav-main-sub>li:last-child .nav-main-sub-Item {
    border-bottom: 0;
}

.nav-mark-new {
    position: absolute;
    right: 0;
    top: -0.5em;
    background: #ffd330;
    color: #73483e;
    border: 1px solid #73483e;
    padding: 0 0.3em;
    -ms-transform: scale(0.8);
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
    min-width: 3.2em;
    text-align: center;
    font-weight: bold;
    line-height: 1.25;
    border-radius: 0 0 3px 3px;
    font-family: "Tahoma", "Microsoft JhengHei", "Microsoft YaHei";
}

html:lang(ch) .nav-mark-new,
html:lang(cs) .nav-mark-new,
html:lang(jp) .nav-mark-new {
    -ms-transform: scale(0.9);
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
}

.nav-mark-new::after {
    content: "";
    border-color: transparent transparent transparent #73483e;
    border-style: solid;
    border-width: 0.5em 0 0 0.5em;
    position: absolute;
    right: -0.5em;
}

.nav-main-sub .nav-mark-new {
    right: -0.2em;
}

.header-topBar {
    background: #243d66;
    color: #ffffff;
    border-radius: 3px 0 3px 3px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    height: 30px;
    position: relative;
}

.header-collapse .header-topBar {
    border-radius: 3px;
}

.header-time {
    text-align: center;
    -webkit-box-flex: 0 0 234px;
    -webkit-flex: 0 0 234px;
    -ms-flex: 0 0 234px;
    flex: 0 0 234px;
}

.header-collapse .header-time {
    -webkit-box-flex: 0 0 9.483em;
    -webkit-flex: 0 0 9.483em;
    -ms-flex: 0 0 9.483em;
    flex: 0 0 9.483em;
}

.header-hotKeyArea {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-flex-flow: row nowrap;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    margin-left: 0.5em;
    margin-right: 0.5em;
}

.header-search {
    -webkit-box-flex: 0 0 150px;
    -webkit-flex: 0 0 150px;
    -ms-flex: 0 0 150px;
    flex: 0 0 150px;
    background: #d5e0f0;
    border-radius: 3px;
    margin-right: 6px;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 22px;
    box-sizing: border-box;
}

.header-search:hover {
    background: white;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
}

.header-search-input {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: #545454;
    background: transparent;
    border: 0;
    outline: none;
    padding: 0 4px;
}

.header-search-input:-ms-input-placeholder {
    color: #949494;
}

.header-search-input::-webkit-input-placeholder {
    color: #949494;
}

.header-search-button {
    width: 24px;
    margin-left: 4px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    color: #545454;
    cursor: pointer;
    border-radius: 0 3px 3px 0;
    position: relative;
}

.header-search-button::before {
    font-size: 14px;
    line-height: 1;
    display: inline-block;
}

.header-search-button:hover {
    color: #ffffff;
    background: #7590ba;
}

.header-search .icon-clear {
    background: rgba(0, 0, 0, 0.5);
    margin: 0;
    -webkit-align-self: center;
    -ms-flex-item-align: center;
    align-self: center;
}

@media only screen and (max-width: 1008px) {
    .header-collapse .header-search {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1;
    }
}

.header-dataArea {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row nowrap;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    margin-right: 0.5em;
}

.header-dataArea .data {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden;
    margin-left: 4px;
}

.header-dataArea .data-text {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.header-collapse .header-dataArea .data-text {
    display: none;
}

.header-collapse .header-dataArea {
    margin-right: 0.25em;
}

@media only screen and (max-width: 1260px) {
    .header-dataArea {
        margin-right: 0.25em;
    }
    .header-dataArea .data-text {
        display: none;
    }
    .header-collapse .header-dataArea .data-text {
        display: none;
    }
}

.header-otherArea {
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0 0 224px;
    -webkit-flex: 0 0 224px;
    -ms-flex: 0 0 224px;
    flex: 0 0 224px;
}

.header-collapse .header-otherArea {
    margin-left: 0.5em;
    -webkit-box-flex: 0 0 224px;
    -webkit-flex: 0 0 224px;
    -ms-flex: 0 0 224px;
    flex: 0 0 224px;
}

@media only screen and (max-width: 1008px) {
    .header-otherArea {
        width: 2.5em;
        margin-left: 0.5em;
    }
}

.header-news,
.header-supports {
    margin-right: 4px;
    color: #ffffff;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    cursor: pointer;
    position: relative;
    border: 1px solid;
    border-radius: 3px;
    padding: 0 4px;
    height: 22px;
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.header-news::before,
.header-supports::before {
    display: inline-block;
    font-size: 14px;
    line-height: 1;
}

@-moz-document url-prefix() {
    .header-news::before,
    .header-supports::before {
        margin-top: -1px;
    }
}

.header-news .text,
.header-news .accountTable .text-auto,
.accountTable .header-news .text-auto,
.header-news .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .header-news .text-auto,
.header-supports .text,
.header-supports .accountTable .text-auto,
.accountTable .header-supports .text-auto,
.header-supports .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .header-supports .text-auto {
    margin-left: 6px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.header-news.selected,
.header-supports.selected {
    border-radius: 3px 3px 0 0;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 4px;
    padding-bottom: 4px;
    height: 26px;
}

.header-news .dropdownPanel,
.header-supports .dropdownPanel {
    display: none;
    position: absolute;
    top: 100%;
    left: -1px;
    z-index: 1;
    background: #545454;
    box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 0, 0, 0.4);
}

.header-news .dropdownPanel.show,
.header-supports .dropdownPanel.show {
    display: block;
}

.header-collapse .header-news,
.header-collapse .header-supports {
    text-align: center;
}

.header-collapse .header-news .text,
.header-collapse .header-news .accountTable .text-auto,
.accountTable .header-collapse .header-news .text-auto,
.header-collapse .header-news .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .header-collapse .header-news .text-auto,
.header-collapse .header-supports .text,
.header-collapse .header-supports .accountTable .text-auto,
.accountTable .header-collapse .header-supports .text-auto,
.header-collapse .header-supports .accountTable-verticalAlignTop .text-auto,
.accountTable-verticalAlignTop .header-collapse .header-supports .text-auto {
    display: none;
}

@media only screen and (max-width: 1008px) {
    .header-news,
    .header-supports {
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
    .header-news .text,
    .header-news .accountTable .text-auto,
    .accountTable .header-news .text-auto,
    .header-news .accountTable-verticalAlignTop .text-auto,
    .accountTable-verticalAlignTop .header-news .text-auto,
    .header-supports .text,
    .header-supports .accountTable .text-auto,
    .accountTable .header-supports .text-auto,
    .header-supports .accountTable-verticalAlignTop .text-auto,
    .accountTable-verticalAlignTop .header-supports .text-auto {
        display: none;
        margin-left: 0;
    }
}

.header-news {
    border-color: #545454;
    background: #7c7c7c;
}

.header-news:hover {
    background: #545454;
}

.header-news.selected {
    background: #7c7c7c;
}

.header-collapse .header-news {
    position: static;
    padding: 0 2px;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.header-collapse .header-news.selected {
    padding-bottom: 5px;
    border-bottom-width: 0;
}

@media only screen and (max-width: 1008px) {
    .header-news {
        position: static;
        padding: 0 2px;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
}

.header-news .dropdownPanel {
    cursor: default;
    padding: 10px;
}

.header-collapse .header-news .dropdownPanel {
    left: auto;
    right: 0;
}

@media only screen and (max-width: 1008px) {
    .header-news .dropdownPanel {
        left: auto;
        right: 0;
    }
}

.header-news .dropdownPanel .smallBtn.icon-clear {
    position: absolute;
    right: 2px;
    top: 2px;
    z-index: 1;
    color: #ffffff;
    background-color: #545454;
    border-radius: 0 0 0 20px;
    padding: 3px 3px 8px 8px;
}

.header-news .dropdownPanel .smallBtn.icon-clear:hover {
    color: #dfdfdf;
}

.header-supports {
    border-color: #e05d00;
    background: #f77a00;
}

.header-supports:hover {
    background: #e05d00;
}

.header-supports.selected {
    background: #f77a00;
}

.header-collapse .header-supports {
    position: static;
    padding: 0 2px;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.header-collapse .header-supports.selected {
    padding-bottom: 5px;
    border-bottom-width: 0;
}

@media only screen and (max-width: 1008px) {
    .header-supports {
        position: static;
        padding: 0 2px;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
}

.header-supports .dropdownPanel {
    min-width: calc(100% + 2px);
    left: auto;
    right: -1px;
}

.header-collapse .header-supports .dropdownPanel {
    min-width: 0;
    left: auto;
    right: 0;
}

@media only screen and (max-width: 1008px) {
    .header-supports .dropdownPanel {
        min-width: 0;
        left: auto;
        right: 0;
    }
}

.header-supports .dropdownPanel .content {
    position: relative;
    color: #ececec;
    height: 30px;
    padding: 0 6px;
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.header-supports .dropdownPanel .content:hover {
    background: #323232;
}

.header-supports .dropdownPanel .content.selected {
    background: #181818;
}

.header-supports .dropdownPanel .content .item {
    white-space: nowrap;
    color: #ececec;
    text-decoration: none;
    line-height: 1;
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
}

.header-supports .dropdownPanel .content>.item::before {
    display: inline-block;
    margin-right: 6px;
    font-size: 14px;
    line-height: 1;
    box-sizing: border-box;
}

@-moz-document url-prefix() {
    .header-supports .dropdownPanel .content>.item::before {
        margin-top: -1px;
    }
}

.header-supports .dropdownPanel .subContent {
    display: none;
    position: absolute;
    top: 0;
    right: 100%;
    text-align: center;
    background: #181818;
    min-width: 100%;
}

.header-supports .dropdownPanel .subContent.show {
    display: block;
}

.header-supports .dropdownPanel .subContent .item {
    height: 30px;
    padding: 8px 4px;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.header-supports .dropdownPanel .subContent .item:hover {
    background: #3f3f3f;
}

.header-supports .dropdownPanel .subContent .qrcode {
    display: inline-block;
    margin: 0 6px 6px 6px;
    padding: 4px;
    background: #ffffff;
}

.header-supports .dropdownPanel .subContent .qrcode::before {
    display: none;
}

.header-supports .dropdownPanel .subContent .qrcode img {
    width: 100%;
}

footer {
    background: #cdcdcd;
    border-top: 2px solid #7c7c7c;
    color: rgba(84, 84, 84, 0.7);
    padding: 0 0.833em 0 0;
    position: absolute;
    bottom: 0;
    width: 100%;
}

footer .copyright,
footer .nav li,
footer .nav+.copyright {
    text-align: center;
    padding: 1.3em 0 1em;
}

footer .nav {
    float: left;
}

footer .nav li {
    float: left;
    padding-left: 10px;
    padding-right: 10px;
    border-right: 1px solid #bbbbbb;
    cursor: pointer;
}

footer .nav li:hover {
    background: #7c7c7c;
    color: #ececec;
}

footer .nav .nav+li,
footer .nav+.copyright {
    text-align: right;
}

footer.midFooter {
    background: none;
    border-top: 2px solid #768fb9;
    padding: 0;
    height: auto;
}

footer.midFooter .copyright,
footer.midFooter .nav li {
    text-align: center;
    padding: 0.3em 0 1em;
}

footer.midFooter .nav {
    float: none;
    display: block;
    text-align: center;
    margin-top: 0.5em;
}

footer.midFooter .nav li {
    float: none;
    display: inline-block;
    border: none;
    position: relative;
    color: #768fb9;
    padding: 0.5em 10px;
}

footer.midFooter .nav li:hover {
    background: none;
    color: #5574a7;
    text-decoration: underline;
}

footer.midFooter .nav li::after {
    content: "";
    border-right: 1px solid #768fb9;
    height: 10px;
    position: absolute;
    right: -3px;
    top: 10px;
}

footer.midFooter .nav li:last-child::after {
    display: none;
}

.calendar {
    color: black;
    background: #ececec;
    box-shadow: 0 0 0.2rem 0 rgba(0, 0, 0, 0.8);
    margin-left: -0.5em;
    z-index: 26;
    cursor: pointer;
    text-align: center;
}

.calendar td {
    padding: .1em .2em;
    text-align: center;
}

.calendar .calendar_button {
    background: #5574a7;
    color: #ffffff;
}

.calendar .calendar_title {
    font-weight: bold;
    background: #5574a7;
    color: #ffffff;
}

.calendar .headrow .calendar_button {
    background: #cdcdcd;
    color: black;
    border-bottom: 1px solid #a3a3a3;
}

.calendar .name {
    color: black;
}

.calendar .weekend {
    color: #f85252;
}

.calendar .day {
    width: 2em;
    color: black;
}

.calendar .day.hilite {
    background: #c6d4f1;
}

.calendar .day.active {
    background: #c6d4f1;
}

.calendar .day.weekend {
    color: #f85252;
}

.calendar .day.today {
    font-weight: bold;
    background: #cdcdcd;
    color: black;
}

.calendar .day.selected {
    font-weight: bold;
    background: #5574a7;
    color: #ffffff;
}

.calendar .disabled {
    color: black;
}

.calendar .emptycell {
    visibility: hidden;
}

.calendar .emptyrow {
    display: none;
}

.calendar .footrow {
    text-align: center;
    color: #ffffff;
}

.calendar .ttip {
    background: #c6d4f1;
    color: black;
    text-align: center;
}

.combo {
    position: absolute;
    display: none;
    top: 0px;
    left: 0px;
    width: 4em;
    cursor: default;
    border: 1px solid #cdcdcd;
    background: #ececec;
    color: black;
}

.combo .label {
    width: 100%;
}

.combo .hilite {
    background: #c6d4f1;
}

.combo .active {
    background: #5574a7;
    color: #ffffff;
    font-weight: bold;
}

.logo {
    background-image: url(../../images/v2/logo.svg);
    display: flex;
    width: 224px;
    height: 60px;
    background-repeat: no-repeat;
    background-position: center;
    transform: translateY(-3px);
    background-size: 60%;
}

.logo::before {
    background-image: url(../../images/v2/logo_new.png);
}

.darkBg .logo {
    background-image: url(../../images/v2/logo_white.png);
}

.header-collapse .logo {
    background-image: url(../../images/v2/logo_white.png);
}

.liveStreaming-defaultImage .logo-large {
    background-image: url(../../images/v2/logo.png);
}

.step4 .imgBox::before,
.step4 .imgBox::after,
.step5 .imgBox::before,
.step5 .imgBox::after {
    background-image: url(../../images/v2/logo.png);
}