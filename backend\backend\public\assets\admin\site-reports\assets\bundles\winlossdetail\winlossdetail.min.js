"use strict";
var AlphaReports = function() {
        function n(n, t) {
            var i = this;
            this.format = {
                monthFormat: "MMMM - yyyy",
                shortDateWithSlash_DatePicker: "MM/DD/YYYY",
                shortDateWithSlash_MonthPicker: "MM/dd/yyyy"
            };
            this.sort = {
                none: 0,
                ascending: 1,
                descending: -1
            };
            this.keyCode = {
                enter: 13
            };
            this.numberDisplayedProductsOnFilter = 1;
            this.maxTimesToShowTooltip = 5;
            this.screenSmallThan768 = function() {
                var n = screen.width || i.$(window).width();
                return n < 768
            };
            this.screenLandscapeMode = function() {
                return screen.width > screen.height || i.$(window).width() > i.$(window).height()
            };
            this.splitCell = function(n, t, r) {
                t === void 0 && (t = "");
                r === void 0 && (r = "bold");
                var u = i.$,
                    f = i.$("." + n + "-combined");
                f.each(function(n) {
                    for (var e = u(this).find("li"), o = e.length, s = "", i = 0, h = o - 1; i < o; i++) s += '<td class="hidden-stack-mode ' + t + (i === h ? " " + r : "") + '">' + u(e[i]).html() + "<\/td>";
                    u(f[n]).before(s)
                })
            };
            this.combineConsecutiveCells = function(n, t, r, u) {
                if (u === void 0 && (u = 1), !(t <= 1)) {
                    var f = i.$;
                    n.each(function(i) {
                        for (var o = '<td class="visible-stack-mode combinedcell ' + r + '" colspan="' + u + '">                    <ul>', e = f(n[i]), s = !1, h = 0; h < t; h++) o += "<li>" + e.html() + "<\/li>", e.next().length ? e = e.next() : s = !0;
                        o += "<\/ul>                        <\/td>";
                        s ? e.after(o) : e.prev().after(o)
                    })
                }
            };
            this.localStorage = {
                get: function(n) {
                    return i.store.enabled ? i.store.get(n) : i.$.cookie(n)
                },
                set: function(n, t) {
                    i.store.enabled ? i.store.set(n, t) : i.$.cookie(n, t, i.localStorage.getCookieExpireOptions())
                },
                getCookieExpireOptions: function() {
                    var n = {
                        expires: 365,
                        path: window.location.pathname
                    };
                    return i.isIe() && (n = {
                        expires: 365
                    }), n
                }
            };
            this.isIe = function() {
                var n = window.navigator.userAgent,
                    t = n.indexOf("MSIE ");
                return t > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)
            };
            this.replaceQueryStringValue = function(n, t, i) {
                if (typeof n == "undefined" || n === null) return "";
                var r = new RegExp("([?&])" + t + "=[^&]*", "i");
                return r.test(n) ? n.replace(r, "$1" + t + "=" + i) : n + (n.indexOf("?") >= 0 ? "&" : "?") + t + "=" + i
            };
            this.correctEvenRowClass = function(n) {
                var r = i.$,
                    t = !1;
                n.find("tbody > tr").each(function() {
                    var n = r(this);
                    n.hasClass("even-row") && !t ? n.removeClass("even-row") : !n.hasClass("even-row") && t && n.addClass("even-row");
                    t = !t
                })
            };
            this.convertAttributeToBool = function(n) {
                return !!n && n.toString().toLowerCase() === "true"
            };
            this.formatNumber = function(n) {
                return n.toLocaleString("en-US")
            };
            this.formatCurrency = function(n) {
                return n.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
            };
            this.$ = n;
            this.store = t
        }
        return n.prototype.getFormData = function(n) {
            var i = n.serializeArray(),
                t = {};
            return $.map(i, function(n) {
                t[n.name] = n.value
            }), t
        }, n
    }(),
    alphaReports = new AlphaReports($, store),
    BaseReport = function() {
        function n(n, t) {
            var i = this;
            this.onClickIconFilter = function() {
                var t = i.$reportForm,
                    n = t.hasClass("hide");
                n ? (i.$pageTitle.removeClass("hide-filter"), i.$reportForm.removeClass("hide")) : (i.$pageTitle.addClass("hide-filter"), i.$reportForm.addClass("hide"));
                i.replaceHrefParam("IsFilterVisible", n)
            };
            this.replaceHrefParam = function(n, t) {
                i.linkBuilderComponent.updateParam(n, t)
            };
            this.renderLegend = function(n) {
                
            };
            this.showLegend = function() {
                
            };
            this.$ = n;
            this.linkBuilderComponent = t
        }
        return n.prototype.cacheElements = function() {
            this.$reportForm = this.$("#report-form");
            this.$pageTitle = this.$(".page-title");
            this.$tableReport = this.$("#tbl-report");
            this.$isFilterVisible = this.$("#IsFilterVisible");
            this.$exportExcelForm = this.$("#export-excel-form")
        }, n.prototype.registerEvents = function() {
            this.$(".icon-filter").on("click", this.onClickIconFilter)
        }, n
    }(),
    DateRangePickerOption, DateRangePickerComponent, MonthPickerOption, MonthPickerComponent, PagerOption, PagerComponent, ProductMultiSelectionOption, ProductMultiSelectionComponent, ProductDetailMultiSelectionOption, ProductDetailMultiSelectionComponent, SortOption, SortComponent, __extends, WinLossDetailView;
DateRangePickerOption = function() {
    function n(n, t, i, r) {
        n === void 0 && (n = "daterange-picker");
        t === void 0 && (t = "report-form");
        i === void 0 && (i = null);
        r === void 0 && (r = null);
        this.selectorId = n;
        this.reportFormId = t;
        this.onChange = i;
        this.onBeforeQuickChooseChange = r;
        this.selectorId = n;
        this.reportFormId = t;
        this.onChange = i;
        this.onBeforeQuickChooseChange = r
    }
    return n
}();
DateRangePickerComponent = function() {
    function n(n) {
        var t = this;
        this.options = new DateRangePickerOption;
        this.quickChooseType = {
            specificDates: 7
        };
        this.isCompleteInitDateRangePicker = !1;
        this.render = function(n) {
            
        };
        this.$ = n
    }
    return n
}();
MonthPickerOption = function() {
    function n(n, t) {
        n === void 0 && (n = "dropdown-month");
        t === void 0 && (t = null);
        this.selectorId = n
    }
    return n
}();
MonthPickerComponent = function() {
    function n(n) {
        var t = this;
        this.options = new MonthPickerOption;
        this.render = function(n) {
            n === void 0 && (n = new MonthPickerOption);
            var u = t.$.extend({}, t.options, n),
                i = t.$("#" + u.selectorId),
                r = i.data("maxdate");
            return t.$fromDate = t.$("#from-date"), t.$toDate = t.$("#to-date"), i.monPickr({
                longMonthsInYear: i.data("longmonths-inyear").split(","),
                maxDate: r === "" ? i.data("currentdate") : r,
                minDate: i.data("mindate"),
                month: new Date(i.data("dateinmonth")),
                fromDay: i.data("fromdate"),
                toDay: i.data("todate"),
                showDateRange: !0,
                monthFormat: alphaReports.format.monthFormat,
                fromDayId: "ddl-fromday",
                toDayId: "ddl-today",
                fromDayLabel: "",
                toDayLabel: "-",
                sortDesc: !0,
                log: function(n) {
                    console.warn(n)
                },
                afterChangeMonth: t.onChange,
                afterChangeDay: t.onChange
            }), t.$dropdownMonth = i, i
        };
        this.onChange = function() {
            t.$fromDate.val(t.$dropdownMonth.monPickr("getFromDate", alphaReports.format.shortDateWithSlash_MonthPicker));
            t.$toDate.val(t.$dropdownMonth.monPickr("getToDate", alphaReports.format.shortDateWithSlash_MonthPicker))
        };
        this.$ = n
    }
    return n
}();
PagerOption = function() {
    function n(n, t, i, r, u) {
        n === void 0 && (n = null);
        t === void 0 && (t = "report-pager");
        i === void 0 && (i = "report-form");
        r === void 0 && (r = "PageIndex");
        u === void 0 && (u = "PageSize");
        this.onSelectPage = n;
        this.pagerId = t;
        this.formId = i;
        this.pageIndexId = r;
        this.pageSizeId = u
    }
    return n
}();
PagerComponent = function() {
    function n(n) {
        var t = this;
        this.pageList = [1e3, 2e3, 3e3, 5e3];
        this.render = function(n) {
            n === void 0 && (n = new PagerOption);
            var u = t,
                r = t.$.extend({}, t.options, n),
                i = t.$("#" + r.pagerId);
            return t.$reportForm = t.$("#" + t.options.formId), t.totalRecords = i.data("totalrecords"), t.pageSize = i.data("pagesize"), t.pageIndex = i.data("pageindex"), t.$pageIndex = t.$('<input type="hidden" id="' + r.pageIndexId + '" name="' + r.pageIndexId + '" value="' + t.pageIndex + '" />'), t.$pageSize = t.$('<input type="hidden" id="' + r.pageSizeId + '" name="' + r.pageSizeId + '" value="' + t.pageSize + '" />'), t.$reportForm.append(t.$pageIndex).append(t.$pageSize), t.isShow() ? (i.pagination({
                pageList: t.pageList,
                total: t.totalRecords,
                pageSize: t.pageSize,
                pageNumber: t.pageIndex,
                showRefresh: !1,
                onSelectPage: function(n, t) {
                    u.updateHiddenFields(n, t);
                    u.onChange()
                },
                beforePageText: i.data("lablepage"),
                afterPageText: i.data("labelof") + " {pages}",
                displayMsg: i.data("labeldisplayitems")
            }), i.removeClass("hide")) : i.addClass("hide"), i
        };
        this.onChange = function() {
            t.$reportForm.trigger("submit")
        };
        this.updateHiddenFields = function(n, i) {
            t.$pageIndex.val(n);
            t.$pageSize.val(i)
        };
        this.isShow = function() {
            return t.pageList[0] <= t.totalRecords
        };
        this.hasAllDataOnClient = function() {
            return t.totalRecords <= t.pageSize
        };
        this.options = new PagerOption(this.onChange);
        this.$ = n
    }
    return n
}();
ProductMultiSelectionOption = function() {
    function n(n, t, i, r) {
        n === void 0 && (n = "dropdown-product");
        t === void 0 && (t = !0);
        i === void 0 && (i = "product-ids");
        r === void 0 && (r = null);
        this.dropdownId = n;
        this.validateBeforeSubmit = t;
        this.productsInputId = i;
        this.onChange = r
    }
    return n
}();
ProductMultiSelectionComponent = function() {
    function n(n, t) {
        t === void 0 && (t = undefined);
        var i = this;
        this.options = new ProductMultiSelectionOption;
        this.handleSelectedAllChanged = !1;
        this.render = function(n) {
            var t, r;
            if (n === void 0 && (n = new ProductMultiSelectionOption), i.productDetailMultiSelectionComponent && i.productDetailMultiSelectionComponent.render(), i.options = i.$.extend({}, i.options, n), i.$dropdownProduct = i.$("#" + i.options.dropdownId), t = alphaReports.convertAttributeToBool(i.$dropdownProduct.data("has-any-product")), i.$hiddenInput = i.$("#" + i.options.productsInputId), i.$excludeProducts = i.$(".exclude-product"), i.$excludeProducts.length) {
                i.$excludeProducts.off("change").on("change", i.excludeProductOnChange)
            }
            return i.$dropdownProduct.multiselect({
                maxHeight: 300,
                numberDisplayed: alphaReports.numberDisplayedProductsOnFilter,
                nonSelectedText: t ? i.$dropdownProduct.data("label-select-products") : i.$dropdownProduct.data("label-there-is-no-product"),
                nSelectedText: " " + i.$dropdownProduct.data("label-selected"),
                allSelectedText: i.$dropdownProduct.data("label-all-products"),
                includeSelectAllOption: t,
                onChange: i.onChange,
                onSelectAll: i.onChange,
                language: i.$dropdownProduct.data("language"),
                enableCollapsibleOptGroups: !0,
                optionClass: i.optionClass
            }), i.$dropdownProduct.next().find(".multiselect-container li").removeClass("hidden"), i.$multiSelectButton = i.$dropdownProduct.parent().find(".multiselect.btn"), i.productDetailMultiSelectionComponent && (r = i, i.$dropdownProduct.find("option").each(function() {
                r.productDetailMultiSelectionComponent.shouldEnableDropdown(r.$(this).is(":selected"), this.value)
            })), i.handleSelectedAllChanged = !0, i.$dropdownProduct
        };
        this.onChange = function(n, t) {
            i.validate();
            i.$hiddenInput.val(i.getSelectedItems().join(","));
            i.handleSelectedAllChanged && i.productDetailMultiSelectionComponent && (typeof n == "boolean" ? i.productDetailMultiSelectionComponent.disableAllOptions(n) : i.productDetailMultiSelectionComponent.disableOptions(t, n.context.value));
            i.options.onChange && i.options.onChange()
        };
        this.optionClass = function(n) {
            if (i.$(n).data("isnested") === 1) return "multiselect-nested-item"
        };
        this.validate = function() {
            i.options.validateBeforeSubmit && i.$multiSelectButton && i.$dropdownProduct.find("option:selected").length && i.$multiSelectButton.hasClass("error") && (i.$multiSelectButton.removeClass("error"), i.removeErrorClassFromExcludeProductLabels())
        };
        this.getSelectedItems = function() {
            
        };
        this.excludeProductOnChange = function() {
            i.validateExcludeProductOnChange();
            i.$hiddenInput.val(i.getSelectedItems().join(","));
        };
        this.getExcludeSelectedItems = function() {
            var n = [],
                t;
            return i.$excludeProducts.length && (t = i, i.$excludeProducts.each(function() {
                this.checked && n.push(t.$(this).val())
            })), n
        };
        this.addClassToExcludeProductLabels = function(n) {
            if (i.$excludeProducts.length) {
                var t = i;
                i.$excludeProducts.each(function() {
                    t.$(this).closest("label").addClass(n)
                })
            }
        };
        this.removeErrorClassFromExcludeProductLabels = function() {
            if (i.$excludeProducts.length) {
                var n = i;
                i.$excludeProducts.each(function() {
                    n.$(this).closest("label").removeClass("error")
                })
            }
        };
        this.validateExcludeProductOnChange = function() {
            i.options.validateBeforeSubmit && i.$multiSelectButton && i.$multiSelectButton.hasClass("error") && (i.$multiSelectButton.removeClass("error"), i.removeErrorClassFromExcludeProductLabels())
        };
        this.isAnyEnabledProductDetailSelected = function() {
            return !i.productDetailMultiSelectionComponent || i.productDetailMultiSelectionComponent.isAnyEnabledSelected()
        };
        this.addErrorToProductDetailComponent = function() {
            i.productDetailMultiSelectionComponent && i.productDetailMultiSelectionComponent.addError()
        };
        this.$ = n;
        this.productDetailMultiSelectionComponent = t
    }
    return n
}();
ProductDetailMultiSelectionOption = function() {
    function n(n, t, i) {
        n === void 0 && (n = "dropdown-product-detail");
        t === void 0 && (t = !0);
        i === void 0 && (i = "resource-ids");
        this.dropdownId = n;
        this.validateBeforeSubmit = t;
        this.resourcesInputId = i
    }
    return n
}();
ProductDetailMultiSelectionComponent = function() {
    function n(n) {
        var t = this;
        this.options = new ProductDetailMultiSelectionOption;
        this.render = function(n) {
            n === void 0 && (n = new ProductDetailMultiSelectionOption);
            t.options = t.$.extend({}, t.options, n);
            t.$dropdownProductDetail = t.$("#" + t.options.dropdownId);
            t.$hiddenInput = t.$("#" + t.options.resourcesInputId);
            var r = "True" === t.$dropdownProductDetail.data("has-any-product-detail"),
                i = t.$dropdownProductDetail.data("label-all-product-detail");
            return t.$dropdownProductDetail.multiselect({
                maxHeight: 300,
                numberDisplayed: alphaReports.numberDisplayedProductsOnFilter,
                nonSelectedText: t.$dropdownProductDetail.data("label-select-product-detail"),
                nSelectedText: " " + t.$dropdownProductDetail.data("label-selected"),
                allSelectedText: i,
                selectAllText: i,
                includeSelectAllOption: r,
                onChange: t.onChange,
                onSelectAll: t.onChange,
                language: t.$dropdownProductDetail.data("language")
            }), t.$multiSelectButton = t.$dropdownProductDetail.parent().find(".multiselect.btn"), t.$dropdownProductDetail
        };
        this.onChange = function() {
            t.validate();
            t.$hiddenInput.val(t.getSelectedItems().join(","))
        };
        this.getSelectedItems = function() {
            var n = [],
                i = t.$;
            return t.$dropdownProductDetail.find("option:selected").each(function() {
                n.push(i(this).val())
            }), n
        };
        this.disableAllOptions = function(n) {
            t.$dropdownProductDetail.multiselect(n ? "enable" : "disable");
            t.$dropdownProductDetail.find("option").prop("selected", n).prop("disabled", !n);
            t.$dropdownProductDetail.multiselect("refresh")
        };
        this.disableOptions = function(n, i) {
            var r = t.$dropdownProductDetail.find('option[data-productid="' + i + '"]');
            r.length && (r.prop("selected", n).prop("disabled", !n), t.$dropdownProductDetail.multiselect("refresh"), n && t.$dropdownProductDetail.find("option:enabled").length ? t.$dropdownProductDetail.multiselect("enable") : t.$dropdownProductDetail.find("option:enabled").length || t.$dropdownProductDetail.multiselect("disable"), t.onChange())
        };
        this.shouldEnableDropdown = function(n, i) {
            var r = t.$dropdownProductDetail.find('option[data-productid="' + i + '"]');
            r.length && (n ? (r.prop("disabled", !1), t.$dropdownProductDetail.multiselect("enable")) : r.prop("selected", !1), t.$dropdownProductDetail.multiselect("refresh"))
        };
        this.validate = function() {
            t.options.validateBeforeSubmit && t.$multiSelectButton && t.isAnyEnabledSelected() && t.$multiSelectButton.hasClass("error") && (t.$multiSelectButton.removeClass("error"), t.$dropdownProductDetail.removeClass("error"))
        };
        this.addError = function() {
            t.$multiSelectButton.addClass("error");
            t.$dropdownProductDetail.addClass("error")
        };
        this.isAnyEnabledSelected = function() {
            return !0
        };
        this.$ = n
    }
    return n
}();
SortOption = function() {
    function n(n, t, i, r, u, f) {
        n === void 0 && (n = null);
        t === void 0 && (t = !0);
        i === void 0 && (i = !1);
        r === void 0 && (r = null);
        u === void 0 && (u = null);
        f === void 0 && (f = null);
        this.sortContainer = n;
        this.canSort = t;
        this.hasAllDataOnClient = i;
        this.afterSortFunction = r;
        this.headers = u;
        this.reportForm = f
    }
    return n
}();
SortComponent = function() {
    function n(n, t) {
        var i = this;
        this.options = new SortOption;
        this.render = function(n) {
            var t, r;
            if (n === void 0 && (n = new SortOption), i.options = i.$.extend({}, i.options, n), i.$sortContainer = i.options.sortContainer, i.$reportForm = i.options.reportForm, t = i.$sortContainer.find("[data-sortedname]"), !i.options.canSort) {
                t.removeAttr("data-sortedname").removeClass("sorting");
                return
            }
            if (r = i, !i.options.hasAllDataOnClient) {
                i.$sortContainer.find("[data-sortedname='" + i.$sortContainer.data("sortedcolumn") + "']").addClass(i.getSortClassWhenSortOnServer());
                t.off("click.sortcomponent").on("click.sortcomponent", function() {
                    var n = r.$(this);
                    r.onSort(n.data("sortedname"), r.getSortClassWhenSortOnServer() === "sorting-asc")
                })
            }
            return i.bindTableSorter(i.$sortContainer.data("sortedcolumn")), t
        };
        this.getSortClassWhenSortOnServer = function() {
            var n = i.$sortContainer.data("descendingsort").toString() === "1" || i.$sortContainer.data("descendingsort").toLowerCase() === "true";
            return n ? "sorting-desc" : "sorting-asc"
        };
        this.onSort = function(n, t) {
            i.$sortContainer.data("descendingsort", t ? "1" : "0").data("sortedcolumn", n);
            i.options.afterSortFunction && typeof i.options.afterSortFunction == "function" && i.options.afterSortFunction()
        };
        this.getSortInfo = function() {
            return {
                sortPropertyName: i.$sortContainer.data("sortedcolumn"),
                isDescendingSort: i.$sortContainer.data("descendingsort").toString() === "1" || i.$sortContainer.data("descendingsort").toString().toLowerCase() === "true"
            }
        };
        this.bindTableSorter = function(n) {
            var r, u, t, f, e;
            if (i.options.canSort && i.options.hasAllDataOnClient) {
                r = i;
                u = i.$sortContainer.find("th[data-disabledsort='true']");
                u.each(function() {
                    r.options.headers[this.cellIndex] = {
                        sorter: !1
                    }
                });
                t = [];
                n && n !== "" && (f = i.$sortContainer.find("[data-sortedname='" + n + "']" + (i.$sortContainer.hasClass("stack-mode") ? ":not([class*='hidden-stack-mode'])" : ":not([class*='col-combine'])"))[0], e = i.$sortContainer.data("descendingsort").toString() === "1" || i.$sortContainer.data("descendingsort").toString().toLowerCase() === "true", t = [
                    [i.$(f).data("colindex"), e ? 1 : 0]
                ]);
                try {
                    i.$sortContainer.tablesorter({
                        defaultParser: 1,
                        headers: i.options.headers,
                        sortList: t,
                        textExtraction: i.textExtraction
                    })
                } catch (o) {
                    console.log(o)
                }
                i.$sortContainer.off("sortEnd").on("sortEnd", function() {
                    var n = i.$sortContainer.find("thead.tableFloatingHeaderOriginal th.sorting.sorting-asc:visible,thead.tableFloatingHeaderOriginal th.sorting.sorting-desc:visible"),
                        t = i.$sortContainer.hasClass("stack-mode") ? ".hidden-stack-mode" : ".visible-stack-mode";
                    if (n = n.not(t), n.length === 1) i.onSort(n.attr("data-sortedname"), n.hasClass("sorting-desc"))
                })
            }
        };
        this.textExtraction = function(n) {
            var t = i.$(n);
            return t.data("islink") ? t.find("a").html() : t.data("sortedvalue") ? String(t.data("sortedvalue")) : t.hasClass("combinedcell") ? t.find("ul>li:first-child").html() : t.html()
        };
        this.afterSort = function() {
            var t = i.$,
                n, r, u;
            i.options.hasAllDataOnClient ? (n = t(".sorting-asc:visible"), r = !n.length, n.length === 0 && (n = t(".sorting-desc")), u = n.data("sortedname"), i.linkBuilderComponent.updateParam("SortPropertyName", u || n.parent().data("sortedname")), i.linkBuilderComponent.updateParam("IsDescendingSort", r.toString()), alphaReports.correctEvenRowClass(i.options.sortContainer)) : i.$reportForm.trigger("submit")
        };
        this.mapSortedColumnToStackMode = function(n, t) {
            for (var r, f, u, i = 0; i < t.length; i++)
                for (r = t[i], f = r[0], u = 0; u < r.length; u++)
                    if (n.toLowerCase() === r[u].toLowerCase()) return f;
            return n
        };
        this.$ = n;
        this.linkBuilderComponent = t
    }
    return n
}();
"use strict";
var ReportTooltipOption = function() {
        function n(n, t) {
            n === void 0 && (n = "tooltip");
            t === void 0 && (t = 0);
            this.tooltipId = n;
            this.custId = t
        }
        return n
    }(),
    ReportTooltip = function() {
        function n(n) {
            var t = this;
            this.tooltipCookieId = "TooltipShowCount";
            this.timeToShowTooltip = 5e3;
            this.show = function(n) {
                var r, u, i, f;
                if (n === void 0 && (n = new ReportTooltipOption), r = t.tooltipCookieId + "-" + n.tooltipId + "-" + n.custId, u = parseInt(alphaReports.localStorage.get(r), 0) || 0, !(u >= alphaReports.maxTimesToShowTooltip)) {
                    i = t.$("#" + n.tooltipId);
                    i.tooltip({
                        placement: "bottom"
                    });
                    i.tooltip("show");
                    alphaReports.localStorage.set(r, u + 1);
                    f = t.$;
                    setTimeout(function() {
                        i.tooltip("hide");
                        f(document).off("click.tooltip")
                    }, t.timeToShowTooltip);
                    t.$(document).off("click.tooltip").on("click.tooltip", function() {
                        i.tooltip("hide")
                    })
                }
            };
            this.$ = n
        }
        return n
    }(),
    LinkBuilderOption = function() {
        function n(n) {
            n === void 0 && (n = ["a.breadcrumb-link,a.downline-link"]);
            this.linkClasses = n
        }
        return n
    }(),
    LinkBuilderComponent = function() {
        function n(n) {
            var t = this;
            this.options = new LinkBuilderOption;
            this.render = function(n) {
                n === void 0 && (n = new LinkBuilderOption);
                t.options = t.$.extend({}, t.options, n)
            };
            this.updateParam = function(n, i) {
                var r = t;
                t.options.linkClasses.forEach(function(u) {
                    var f = t.$(u);
                    f.each(function(t, u) {
                        var e = r.$(u),
                            f = e.attr("href");
                        f = alphaReports.replaceQueryStringValue(f, n, i);
                        e.attr("href", f)
                    })
                })
            };
            this.$ = n
        }
        return n
    }();
__extends = this && this.__extends || function() {
    var n = function(t, i) {
        return n = Object.setPrototypeOf || {
            __proto__: []
        }
        instanceof Array && function(n, t) {
            n.__proto__ = t
        } || function(n, t) {
            for (var i in t) t.hasOwnProperty(i) && (n[i] = t[i])
        }, n(t, i)
    };
    return function(t, i) {
        function r() {
            this.constructor = t
        }
        n(t, i);
        t.prototype = i === null ? Object.create(i) : (r.prototype = i.prototype, new r)
    }
}();
WinLossDetailView = function(n) {
    function t(t, i, r, u, f, e, o, s) {
        var h = n.call(this, t, s) || this;
        return h.isFirstSwitchMode = !0, h.isHistoricReport = !1, h.isSwitchView = !1, h.hasAllDataOnClient = !1, h.levels = ["member", "agent", "master", "super"], h.init = function() {
            if (h.checkReady()) {
                h.$.blockUI();
                h.cacheElements();
                h.$isHistoricReport = h.$("#IsHistoricReport");
                h.isHistoricReport = h.$isHistoricReport.val().toLocaleLowerCase() === "true";
                h.isHistoricReport ? (h.$reportForm.removeClass("hide"), h.monthPickerComponent.render(), h.$isFilterVisible.val().toLowerCase() !== "true" && h.$reportForm.addClass("hide")) : h.dateRangePickerComponent.render();
                h.pagerComponent.render();
                h.hasAllDataOnClient = h.pagerComponent.hasAllDataOnClient();
                var n = parseInt(h.$tableReport.data("totalrecords"), 0) > 1;
                h.sortComponent.render(new SortOption(h.$tableReport, n, h.hasAllDataOnClient, h.sortComponent.afterSort, {
                    0: {
                        sorter: "text"
                    }
                }, h.$reportForm));
                h.$tableReport.stickyTableHeaders();
                h.$isSetDefaultStackMode.val().toString().toLowerCase() === "true" && alphaReports.screenSmallThan768() && (h.setStackMode(), h.reportTooltip.show(new ReportTooltipOption("icon-normal-column", parseInt(h.$("#CustId").val(), 0))));
                h.linkBuilderComponent.render();
                h.registerEvents();
                h.$.unblockUI()
            }
        }, h.checkReady = function() {
            return h.$("#IsHistoricReport").length > 0
        }, h.onSwitchReport = function() {
            h.isSwitchView = !0;
            h.isHistoricReport = !h.$reportSwitch.data("is-historic-report");
            h.$reportForm.trigger("submit")
        }, h.onClickIconFilter = function() {
            var t = h.$reportForm,
                n = t.hasClass("hide");
            n ? (h.$pageTitle.removeClass("hide-filter"), h.$reportForm.removeClass("hide")) : h.hideFilter();
            h.replaceHrefParam("IsFilterVisible", n)
        }, h.setStackMode = function() {
            var i, n, t;
            h.$.blockUI();
            i = h.$tableReport;
            h.$pageTitle.addClass("stack-mode");
            i.addClass("stack-mode");
            h.replaceHrefParam("IsStackMode", !0);
            n = h.$;
            alphaReports.combineConsecutiveCells(n("td.col-turnover"), 3, "col-turnover-combined");
            t = h.levels;
            n.each(t, function(i) {
                var r = t[i],
                    u = "col-" + r + "-winloss-combined" + (i % 2 ? " altercol" : "");
                alphaReports.combineConsecutiveCells(n("td.col-" + r + "-winloss"), 3, u)
            });
            h.isFirstSwitchMode = !1;
            h.sortComponent.bindTableSorter(h.getSortedColumnName());
            h.$.unblockUI()
        }, h.onSwitchMode = function() {
            var i, r, n, t;
            h.$.blockUI();
            i = h.$tableReport;
            r = i.hasClass("stack-mode");
            r ? (h.$pageTitle.removeClass("stack-mode"), i.removeClass("stack-mode")) : (h.$pageTitle.addClass("stack-mode"), i.addClass("stack-mode"));
            h.replaceHrefParam("IsStackMode", !r);
            n = h.levels;
            t = h.$;
            h.isFirstSwitchMode && (r ? (alphaReports.splitCell("col-turnover"), t.each(n, function(t) {
                var i = n[t];
                alphaReports.splitCell("col-" + i + "-winloss", t % 2 ? " altercol" : "")
            })) : (alphaReports.combineConsecutiveCells(t("td.col-turnover"), 3, "col-turnover-combined"), t.each(n, function(i) {
                var r = n[i],
                    u = "col-" + r + "-winloss-combined" + (i % 2 ? " altercol" : "");
                alphaReports.combineConsecutiveCells(t("td.col-" + r + "-winloss"), 3, u)
            })), h.isFirstSwitchMode = !1);
            h.sortComponent.bindTableSorter(h.getSortedColumnName());
            h.$.unblockUI()
        }, h.onExportExcel = function() {
            h.$exportExcelForm.submit();
            h.$.unblockUI()
        }, h.hideFilter = function() {
            h.$pageTitle.addClass("hide-filter");
            h.$reportForm.addClass("hide")
        }, h.getParams = function() {
            var t = h.productMultiSelectionComponent.getSelectedItems(),
                n = h.sortComponent.getSortInfo(),
                i = h.$tableReport;
            return {
                IsAnyProductSelected: true,
                IsAnyProductDetailSelected: h.productMultiSelectionComponent.isAnyEnabledProductDetailSelected(),
                SortPropertyName: n.sortPropertyName,
                IsDescendingSort: n.isDescendingSort,
                IsStackMode: i.hasClass("stack-mode"),
                IsHistoricReport: h.isHistoricReport,
                IsFilterVisible: !h.$reportForm.is(":hidden")
            }
        }, h.showValidateProductError = function() {
            h.$multiSelectButton.addClass("error");
            h.productMultiSelectionComponent.addClassToExcludeProductLabels("error")
        }, h.showValidaProductDetailError = function() {
            h.productMultiSelectionComponent.addErrorToProductDetailComponent()
        }, h.clearError = function() {
            h.$multiSelectButton.removeClass("error")
        }, h.replaceHrefParam = function(n, t) {
            h.linkBuilderComponent.updateParam(n, t)
        }, h.onSubmitReport = function(n) {
            var i = h.$,
                t;
            if (i.blockUI(), t = h.getParams(), h.isSwitchView) h.$fromDate.val(""), h.$toDate.val("");
            else if (t.IsAnyProductSelected) {
                if (!t.IsAnyProductDetailSelected) {
                    h.showValidaProductDetailError();
                    h.$.unblockUI();
                    n.preventDefault();
                    return
                }
            } else {
                h.showValidateProductError();
                i.unblockUI();
                n.preventDefault();
                return
            }
            h.$isFilterVisible.val(String(alphaReports.screenSmallThan768() ? !1 : t.IsFilterVisible));
            h.$isDescendingSort.val(String(t.IsDescendingSort));
            h.$isHistoricReport.val(String(t.IsHistoricReport));
            h.$isStackMode.val(String(t.IsStackMode));
            h.$sortPropertyName.val(t.SortPropertyName)
        }, h.getSortedColumnName = function() {
            var n = h.$tableReport.data("sortedcolumn");
            return h.$tableReport.hasClass("stack-mode") ? n === "NetTurnover" || n === "GrossComm" ? "Turnover" : n === "MemberNetWinLoss.Comm" || n === "MemberNetWinLoss.Total" ? "MemberNetWinLoss.WinLoss" : n === "AgentNetWinLoss.Comm" || n === "AgentNetWinLoss.Total" ? "AgentNetWinLoss.WinLoss" : n === "MasterNetWinLoss.Comm" || n === "MasterNetWinLoss.Total" ? "MasterNetWinLoss.WinLoss" : n === "SuperNetWinLoss.Comm" || n === "SuperNetWinLoss.Total" ? "SuperNetWinLoss.WinLoss" : n : n
        }, h.$ = t, h.dateRangePickerComponent = i, h.pagerComponent = r, h.productMultiSelectionComponent = u, h.sortComponent = f, h.reportTooltip = e, h.monthPickerComponent = o, h.linkBuilderComponent = s, h
    }
    return __extends(t, n), t.prototype.cacheElements = function() {
        n.prototype.cacheElements.call(this);
        this.$drilldownLinks = this.$(".downline-link");
        this.$isStackMode = this.$("#IsStackMode");
        this.$isDescendingSort = this.$("#IsDescendingSort");
        this.$sortPropertyName = this.$("#SortPropertyName");
        this.$userSelectedDateRangeOption = this.$("#UserSelectedDateRangeOption");
        this.$reportSwitch = this.$("#report-switch");
        this.$fromDate = this.$("#from-date");
        this.$toDate = this.$("#to-date");
        this.$iconNormalColumn = this.$("#icon-normal-column");
        this.$isSetDefaultStackMode = this.$("#IsSetDefaultStackMode");
        this.$breadcrumbLinks = this.$(".breadcrumb-link")
    }, t.prototype.registerEvents = function() {
        n.prototype.registerEvents.call(this);
        this.$reportSwitch.on("click", this.onSwitchReport);
        this.$(".icon-switchmode").on("click", this.onSwitchMode);
        this.$reportForm.submit(this.onSubmitReport);
        this.$("#icon-excel").on("click", this.onExportExcel)
    }, t
}(BaseReport);
$(function() {
    var n = new LinkBuilderComponent($),
        t = new WinLossDetailView($, new DateRangePickerComponent($), new PagerComponent($), new ProductMultiSelectionComponent($, new ProductDetailMultiSelectionComponent($)), new SortComponent($, n), new ReportTooltip($), new MonthPickerComponent($), n);
    t.init()
});