/*!
 * numeral.js
 * version : 1.5.3
 * author : <PERSON>
 * license : MIT
 * http://adamwdraper.github.com/Numeral-js/
 */
(function(){function e(n){this._value=n}function o(n,t,i,r){var f=Math.pow(10,t),e,u;return u=(i(n*f)/f).toFixed(t),r&&(e=new RegExp("0{1,"+r+"}$"),u=u.replace(e,"")),u}function l(n,t,i){return t.indexOf("$")>-1?v(n,t,i):t.indexOf("%")>-1?y(n,t,i):t.indexOf(":")>-1?p(n,t):s(n._value,t,i)}function a(i,u){var o=u,s,h,c,l,a=["KB","MB","GB","TB","PB","EB","ZB","YB"],f=!1,e;if(u.indexOf(":")>-1)i._value=w(u);else if(u===r)i._value=0;else{for(n[t].delimiters.decimal!=="."&&(u=u.replace(/\./g,"").replace(n[t].delimiters.decimal,".")),s=new RegExp("[^a-zA-Z]"+n[t].abbreviations.thousand+"(?:\\)|(\\"+n[t].currency.symbol+")?(?:\\))?)?$"),h=new RegExp("[^a-zA-Z]"+n[t].abbreviations.million+"(?:\\)|(\\"+n[t].currency.symbol+")?(?:\\))?)?$"),c=new RegExp("[^a-zA-Z]"+n[t].abbreviations.billion+"(?:\\)|(\\"+n[t].currency.symbol+")?(?:\\))?)?$"),l=new RegExp("[^a-zA-Z]"+n[t].abbreviations.trillion+"(?:\\)|(\\"+n[t].currency.symbol+")?(?:\\))?)?$"),e=0;e<=a.length;e++)if(f=u.indexOf(a[e])>-1?Math.pow(1024,e+1):!1,f)break;i._value=(f?f:1)*(o.match(s)?Math.pow(10,3):1)*(o.match(h)?Math.pow(10,6):1)*(o.match(c)?Math.pow(10,9):1)*(o.match(l)?Math.pow(10,12):1)*(u.indexOf("%")>-1?.01:1)*((u.split("-").length+Math.min(u.split("(").length-1,u.split(")").length-1))%2?1:-1)*Number(u.replace(/[^0-9\.]+/g,""));i._value=f?Math.ceil(i._value):i._value}return i._value}function v(i,r,u){var o=r.indexOf("$"),c=r.indexOf("("),l=r.indexOf("-"),e="",h,f;return r.indexOf(" $")>-1?(e=" ",r=r.replace(" $","")):r.indexOf("$ ")>-1?(e=" ",r=r.replace("$ ","")):r=r.replace("$",""),f=s(i._value,r,u),o<=1?f.indexOf("(")>-1||f.indexOf("-")>-1?(f=f.split(""),h=1,(o<c||o<l)&&(h=0),f.splice(h,0,n[t].currency.symbol+e),f=f.join("")):f=n[t].currency.symbol+e+f:f.indexOf(")")>-1?(f=f.split(""),f.splice(-1,0,e+n[t].currency.symbol),f=f.join("")):f=f+e+n[t].currency.symbol,f}function y(n,t,i){var u="",r,f=n._value*100;return t.indexOf(" %")>-1?(u=" ",t=t.replace(" %","")):t=t.replace("%",""),r=s(f,t,i),r.indexOf(")")>-1?(r=r.split(""),r.splice(-1,0,u+"%"),r=r.join("")):r=r+u+"%",r}function p(n){var i=Math.floor(n._value/3600),t=Math.floor((n._value-i*3600)/60),r=Math.round(n._value-i*3600-t*60);return i+":"+(t<10?"0"+t:t)+":"+(r<10?"0"+r:r)}function w(n){var i=n.split(":"),t=0;return i.length===3?(t=t+Number(i[0])*3600,t=t+Number(i[1])*60,t=t+Number(i[2])):i.length===2&&(t=t+Number(i[0])*60,t=t+Number(i[1])),Number(t)}function s(i,u,f){var b=!1,it=!1,rt=!1,e="",d=!1,g=!1,nt=!1,tt=!1,v=!1,y="",p="",l=Math.abs(i),ut=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],k,ft,a,h,s,et,c="",w=!1;if(i===0&&r!==null)return r;if(u.indexOf("(")>-1?(b=!0,u=u.slice(1,-1)):u.indexOf("+")>-1&&(it=!0,u=u.replace(/\+/g,"")),u.indexOf("a")>-1&&(d=u.indexOf("aK")>=0,g=u.indexOf("aM")>=0,nt=u.indexOf("aB")>=0,tt=u.indexOf("aT")>=0,v=d||g||nt||tt,u.indexOf(" a")>-1?(e=" ",u=u.replace(" a","")):u=u.replace("a",""),l>=Math.pow(10,12)&&!v||tt?(e=e+n[t].abbreviations.trillion,i=i/Math.pow(10,12)):l<Math.pow(10,12)&&l>=Math.pow(10,9)&&!v||nt?(e=e+n[t].abbreviations.billion,i=i/Math.pow(10,9)):l<Math.pow(10,9)&&l>=Math.pow(10,6)&&!v||g?(e=e+n[t].abbreviations.million,i=i/Math.pow(10,6)):(l<Math.pow(10,6)&&l>=Math.pow(10,3)&&!v||d)&&(e=e+n[t].abbreviations.thousand,i=i/Math.pow(10,3))),u.indexOf("b")>-1)for(u.indexOf(" b")>-1?(y=" ",u=u.replace(" b","")):u=u.replace("b",""),a=0;a<=ut.length;a++)if(k=Math.pow(1024,a),ft=Math.pow(1024,a+1),i>=k&&i<ft){y=y+ut[a];k>0&&(i=i/k);break}return u.indexOf("o")>-1&&(u.indexOf(" o")>-1?(p=" ",u=u.replace(" o","")):u=u.replace("o",""),p=p+n[t].ordinal(i)),u.indexOf("[.]")>-1&&(rt=!0,u=u.replace("[.]",".")),h=i.toString().split(".")[0],s=u.split(".")[1],et=u.indexOf(","),s?(s.indexOf("[")>-1?(s=s.replace("]",""),s=s.split("["),c=o(i,s[0].length+s[1].length,f,s[1].length)):c=o(i,s.length,f),h=c.split(".")[0],c=c.split(".")[1].length?n[t].delimiters.decimal+c.split(".")[1]:"",rt&&Number(c.slice(1))===0&&(c="")):h=o(i,null,f),h.indexOf("-")>-1&&(h=h.slice(1),w=!0),et>-1&&(h=h.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+n[t].delimiters.thousands)),u.indexOf(".")===0&&(h=""),(b&&w?"(":"")+(!b&&w?"-":"")+(!w&&it?"+":"")+h+c+(p?p:"")+(e?e:"")+(y?y:"")+(b&&w?")":"")}function b(t,i){n[t]=i}function h(n){var t=n.toString().split(".");return t.length<2?1:Math.pow(10,t[1].length)}function u(){var n=Array.prototype.slice.call(arguments);return n.reduce(function(n,t){var i=h(n),r=h(t);return i>r?i:r},-Infinity)}var i,n={},t="en",r=null,f="0,0",c=typeof module!="undefined"&&module.exports;i=function(n){return i.isNumeral(n)?n=n.value():n===0||typeof n=="undefined"?n=0:Number(n)||(n=i.fn.unformat(n)),new e(Number(n))};i.version="1.5.3";i.isNumeral=function(n){return n instanceof e};i.language=function(r,u){if(!r)return t;if(r&&!u){if(!n[r])throw new Error("Unknown language : "+r);t=r}return(u||!n[r])&&b(r,u),i};i.languageData=function(i){if(!i)return n[t];if(!n[i])throw new Error("Unknown language : "+i);return n[i]};i.language("en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th"},currency:{symbol:"$"}});i.zeroFormat=function(n){r=typeof n=="string"?n:null};i.defaultFormat=function(n){f=typeof n=="string"?n:"0.0"};"function"!=typeof Array.prototype.reduce&&(Array.prototype.reduce=function(n,t){"use strict";if(null===this||"undefined"==typeof this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!=typeof n)throw new TypeError(n+" is not a function");var i,r,f=this.length>>>0,u=!1;for(1<arguments.length&&(r=t,u=!0),i=0;f>i;++i)this.hasOwnProperty(i)&&(u?r=n(r,this[i],i,this):(r=this[i],u=!0));if(!u)throw new TypeError("Reduce of empty array with no initial value");return r});i.fn=e.prototype={clone:function(){return i(this)},format:function(n,t){return l(this,n?n:f,t!==undefined?t:Math.round)},unformat:function(n){return Object.prototype.toString.call(n)==="[object Number]"?n:a(this,n?n:f)},value:function(){return this._value},valueOf:function(){return this._value},set:function(n){return this._value=Number(n),this},add:function(n){function i(n,i){return n+t*i}var t=u.call(null,this._value,n);return this._value=[this._value,n].reduce(i,0)/t,this},subtract:function(n){function i(n,i){return n-t*i}var t=u.call(null,this._value,n);return this._value=[n].reduce(i,this._value*t)/t,this},multiply:function(n){function t(n,t){var i=u(n,t);return n*i*t*i/(i*i)}return this._value=[this._value,n].reduce(t,1),this},divide:function(n){function t(n,t){var i=u(n,t);return n*i/(t*i)}return this._value=[this._value,n].reduce(t),this},difference:function(n){return Math.abs(i(this._value).subtract(n).value())}};c&&(module.exports=i);typeof ender=="undefined"&&(this.numeral=i);typeof define=="function"&&define.amd&&define([],function(){return i})}).call(this);