{"name": "acorn", "description": "ECMAScript parser", "homepage": "https://github.com/ternjs/acorn", "main": "dist/acorn.js", "jsnext:main": "dist/acorn.es.js", "version": "3.3.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "http://rreverser.com/"}], "repository": {"type": "git", "url": "https://github.com/ternjs/acorn.git"}, "license": "MIT", "scripts": {"prepublish": "npm test", "test": "node test/run.js", "pretest": "npm run build", "build": "npm run build:main && npm run build:walk && npm run build:loose && npm run build:bin", "build:main": "rollup -c rollup/config.main.js", "build:walk": "rollup -c rollup/config.walk.js", "build:loose": "rollup -c rollup/config.loose.js", "build:bin": "rollup -c rollup/config.bin.js"}, "bin": {"acorn": "./bin/acorn"}, "devDependencies": {"rollup": "^0.34.1", "rollup-plugin-buble": "^0.11.0", "unicode-9.0.0": "^0.7.0"}}