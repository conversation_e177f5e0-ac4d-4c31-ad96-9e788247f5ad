function ConstraintInput(){this.bind=function(n){$(n).keypress(function(n){var t=$(this);t.val(t.val().replace(/[^\d].+/,""));(n.which<48||n.which>57)&&n.preventDefault()})}}var Sha1={},Sha256,Utf8;Sha1.hash=function(n,t){var d,u,r,i,k,g;t=typeof t=="undefined"?!0:t;t&&(n=Utf8.encode(n));d=[1518500249,1859775393,2400959708,3395469782];n+=String.fromCharCode(128);var nt=n.length/4+2,f=Math.ceil(nt/16),e=new Array(f);for(r=0;r<f;r++)for(e[r]=new Array(16),u=0;u<16;u++)e[r][u]=n.charCodeAt(r*64+u*4)<<24|n.charCodeAt(r*64+u*4+1)<<16|n.charCodeAt(r*64+u*4+2)<<8|n.charCodeAt(r*64+u*4+3);e[f-1][14]=(n.length-1)*8/Math.pow(2,32);e[f-1][14]=Math.floor(e[f-1][14]);e[f-1][15]=(n.length-1)*8&4294967295;var a=1732584193,v=4023233417,y=2562383102,p=271733878,w=3285377520,o=new Array(80),s,h,c,l,b;for(r=0;r<f;r++){for(i=0;i<16;i++)o[i]=e[r][i];for(i=16;i<80;i++)o[i]=Sha1.ROTL(o[i-3]^o[i-8]^o[i-14]^o[i-16],1);for(s=a,h=v,c=y,l=p,b=w,i=0;i<80;i++)k=Math.floor(i/20),g=Sha1.ROTL(s,5)+Sha1.f(k,h,c,l)+b+d[k]+o[i]&4294967295,b=l,l=c,c=Sha1.ROTL(h,30),h=s,s=g;a=a+s&4294967295;v=v+h&4294967295;y=y+c&4294967295;p=p+l&4294967295;w=w+b&4294967295}return Sha1.toHexStr(a)+Sha1.toHexStr(v)+Sha1.toHexStr(y)+Sha1.toHexStr(p)+Sha1.toHexStr(w)};Sha1.f=function(n,t,i,r){switch(n){case 0:return t&i^~t&r;case 1:return t^i^r;case 2:return t&i^t&r^i&r;case 3:return t^i^r}};Sha1.ROTL=function(n,t){return n<<t|n>>>32-t};Sha1.toHexStr=function(n){for(var i="",r,t=7;t>=0;t--)r=n>>>t*4&15,i+=r.toString(16);return i};Utf8={};Utf8.encode=function(n){var t=n.replace(/[\u0080-\u07ff]/g,function(n){var t=n.charCodeAt(0);return String.fromCharCode(192|t>>6,128|t&63)});return t.replace(/[\u0800-\uffff]/g,function(n){var t=n.charCodeAt(0);return String.fromCharCode(224|t>>12,128|t>>6&63,128|t&63)})};Utf8.decode=function(n){var t=n.replace(/[\u00e0-\u00ef][\u0080-\u00bf][\u0080-\u00bf]/g,function(n){var t=(n.charCodeAt(0)&15)<<12|(n.charCodeAt(1)&63)<<6|n.charCodeAt(2)&63;return String.fromCharCode(t)});return t.replace(/[\u00c0-\u00df][\u0080-\u00bf]/g,function(n){var t=(n.charCodeAt(0)&31)<<6|n.charCodeAt(1)&63;return String.fromCharCode(t)})};Sha256={};Sha256.hash=function(n,t){var k,i,f,e,h,l,a,p,c,v,y,w,u,r,b,d;t=typeof t=="undefined"?!0:t;t&&(n=Utf8.encode(n));k=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];i=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];n+=String.fromCharCode(128);var g=n.length/4+2,o=Math.ceil(g/16),s=new Array(o);for(u=0;u<o;u++)for(s[u]=new Array(16),f=0;f<16;f++)s[u][f]=n.charCodeAt(u*64+f*4)<<24|n.charCodeAt(u*64+f*4+1)<<16|n.charCodeAt(u*64+f*4+2)<<8|n.charCodeAt(u*64+f*4+3);for(s[o-1][14]=(n.length-1)*8/Math.pow(2,32),s[o-1][14]=Math.floor(s[o-1][14]),s[o-1][15]=(n.length-1)*8&4294967295,e=new Array(64),u=0;u<o;u++){for(r=0;r<16;r++)e[r]=s[u][r];for(r=16;r<64;r++)e[r]=Sha256.sigma1(e[r-2])+e[r-7]+Sha256.sigma0(e[r-15])+e[r-16]&4294967295;for(h=i[0],l=i[1],a=i[2],p=i[3],c=i[4],v=i[5],y=i[6],w=i[7],r=0;r<64;r++)b=w+Sha256.Sigma1(c)+Sha256.Ch(c,v,y)+k[r]+e[r],d=Sha256.Sigma0(h)+Sha256.Maj(h,l,a),w=y,y=v,v=c,c=p+b&4294967295,p=a,a=l,l=h,h=b+d&4294967295;i[0]=i[0]+h&4294967295;i[1]=i[1]+l&4294967295;i[2]=i[2]+a&4294967295;i[3]=i[3]+p&4294967295;i[4]=i[4]+c&4294967295;i[5]=i[5]+v&4294967295;i[6]=i[6]+y&4294967295;i[7]=i[7]+w&4294967295}return Sha256.toHexStr(i[0])+Sha256.toHexStr(i[1])+Sha256.toHexStr(i[2])+Sha256.toHexStr(i[3])+Sha256.toHexStr(i[4])+Sha256.toHexStr(i[5])+Sha256.toHexStr(i[6])+Sha256.toHexStr(i[7])};Sha256.ROTR=function(n,t){return t>>>n|t<<32-n};Sha256.Sigma0=function(n){return Sha256.ROTR(2,n)^Sha256.ROTR(13,n)^Sha256.ROTR(22,n)};Sha256.Sigma1=function(n){return Sha256.ROTR(6,n)^Sha256.ROTR(11,n)^Sha256.ROTR(25,n)};Sha256.sigma0=function(n){return Sha256.ROTR(7,n)^Sha256.ROTR(18,n)^n>>>3};Sha256.sigma1=function(n){return Sha256.ROTR(17,n)^Sha256.ROTR(19,n)^n>>>10};Sha256.Ch=function(n,t,i){return n&t^~n&i};Sha256.Maj=function(n,t,i){return n&t^n&i^t&i};Sha256.toHexStr=function(n){for(var i="",r,t=7;t>=0;t--)r=n>>>t*4&15,i+=r.toString(16);return i};Utf8={};Utf8.encode=function(n){var t=n.replace(/[\u0080-\u07ff]/g,function(n){var t=n.charCodeAt(0);return String.fromCharCode(192|t>>6,128|t&63)});return t.replace(/[\u0800-\uffff]/g,function(n){var t=n.charCodeAt(0);return String.fromCharCode(224|t>>12,128|t>>6&63,128|t&63)})};Utf8.decode=function(n){var t=n.replace(/[\u00e0-\u00ef][\u0080-\u00bf][\u0080-\u00bf]/g,function(n){var t=(n.charCodeAt(0)&15)<<12|(n.charCodeAt(1)&63)<<6|n.charCodeAt(2)&63;return String.fromCharCode(t)});return t.replace(/[\u00c0-\u00df][\u0080-\u00bf]/g,function(n){var t=(n.charCodeAt(0)&31)<<6|n.charCodeAt(1)&63;return String.fromCharCode(t)})};