!function(e){"use strict";function t(e,t){return t=t||Error,function(){var n,i,r=2,a=arguments,s=a[0],o="["+(e?e+":":"")+s+"] ",u=a[1];for(o+=u.replace(/\{\d+\}/g,function(e){var t=+e.slice(1,-1),n=t+r;return n<a.length?ye(a[n]):e}),o+="\nhttp://errors.angularjs.org/1.5.11/"+(e?e+"/":"")+s,i=r,n="?";i<a.length;i++,n="&")o+=n+"p"+(i-r)+"="+encodeURIComponent(ye(a[i]));return new t(o)}}function n(e){if(null==e||C(e))return!1;if(ir(e)||S(e)||Hi&&e instanceof Hi)return!0;var t="length"in Object(e)&&e.length;return M(t)&&(t>=0&&(t-1 in e||e instanceof Array)||"function"==typeof e.item)}function i(e,t,r){var a,s;if(e)if(x(e))for(a in e)"prototype"===a||"length"===a||"name"===a||e.hasOwnProperty&&!e.hasOwnProperty(a)||t.call(r,e[a],a,e);else if(ir(e)||n(e)){var o="object"!=typeof e;for(a=0,s=e.length;a<s;a++)(o||a in e)&&t.call(r,e[a],a,e)}else if(e.forEach&&e.forEach!==i)e.forEach(t,r,e);else if(b(e))for(a in e)t.call(r,e[a],a,e);else if("function"==typeof e.hasOwnProperty)for(a in e)e.hasOwnProperty(a)&&t.call(r,e[a],a,e);else for(a in e)Di.call(e,a)&&t.call(r,e[a],a,e);return e}function r(e,t,n){for(var i=Object.keys(e).sort(),r=0;r<i.length;r++)t.call(n,e[i[r]],i[r]);return i}function a(e){return function(t,n){e(n,t)}}function s(){return++tr}function o(e,t){t?e.$$hashKey=t:delete e.$$hashKey}function u(e,t,n){for(var i=e.$$hashKey,r=0,a=t.length;r<a;++r){var s=t[r];if(y(s)||x(s))for(var l=Object.keys(s),c=0,p=l.length;c<p;c++){var h=l[c],d=s[h];n&&y(d)?T(d)?e[h]=new Date(d.valueOf()):w(d)?e[h]=new RegExp(d):d.nodeName?e[h]=d.cloneNode(!0):O(d)?e[h]=d.clone():(y(e[h])||(e[h]=ir(d)?[]:{}),u(e[h],[d],!0)):e[h]=d}}return o(e,i),e}function l(e){return u(e,Yi.call(arguments,1),!1)}function c(e){return u(e,Yi.call(arguments,1),!0)}function p(e){return parseInt(e,10)}function h(e,t){return l(Object.create(e),t)}function d(){}function f(e){return e}function g(e){return function(){return e}}function m(e){return x(e.toString)&&e.toString!==Zi}function v(e){return"undefined"==typeof e}function $(e){return"undefined"!=typeof e}function y(e){return null!==e&&"object"==typeof e}function b(e){return null!==e&&"object"==typeof e&&!Qi(e)}function S(e){return"string"==typeof e}function M(e){return"number"==typeof e}function T(e){return"[object Date]"===Zi.call(e)}function x(e){return"function"==typeof e}function w(e){return"[object RegExp]"===Zi.call(e)}function C(e){return e&&e.window===e}function P(e){return e&&e.$evalAsync&&e.$watch}function k(e){return"[object File]"===Zi.call(e)}function _(e){return"[object FormData]"===Zi.call(e)}function A(e){return"[object Blob]"===Zi.call(e)}function I(e){return"boolean"==typeof e}function E(e){return e&&x(e.then)}function B(e){return e&&M(e.length)&&rr.test(Zi.call(e))}function L(e){return"[object ArrayBuffer]"===Zi.call(e)}function O(e){return!(!e||!(e.nodeName||e.prop&&e.attr&&e.find))}function N(e){var t,n={},i=e.split(",");for(t=0;t<i.length;t++)n[i[t]]=!0;return n}function R(e){return ji(e.nodeName||e[0]&&e[0].nodeName)}function V(e,t){var n=e.indexOf(t);return n>=0&&e.splice(n,1),n}function D(e,t){function n(e,t){var n,i=t.$$hashKey;if(ir(e))for(var a=0,s=e.length;a<s;a++)t.push(r(e[a]));else if(b(e))for(n in e)t[n]=r(e[n]);else if(e&&"function"==typeof e.hasOwnProperty)for(n in e)e.hasOwnProperty(n)&&(t[n]=r(e[n]));else for(n in e)Di.call(e,n)&&(t[n]=r(e[n]));return o(t,i),t}function r(e){if(!y(e))return e;var t=s.indexOf(e);if(t!==-1)return u[t];if(C(e)||P(e))throw Xi("cpws","Can't copy! Making copies of Window or Scope instances is not supported.");var i=!1,r=a(e);return void 0===r&&(r=ir(e)?[]:Object.create(Qi(e)),i=!0),s.push(e),u.push(r),i?n(e,r):r}function a(e){switch(Zi.call(e)){case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Float32Array]":case"[object Float64Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return new e.constructor(r(e.buffer),e.byteOffset,e.length);case"[object ArrayBuffer]":if(!e.slice){var t=new ArrayBuffer(e.byteLength);return new Uint8Array(t).set(new Uint8Array(e)),t}return e.slice(0);case"[object Boolean]":case"[object Number]":case"[object String]":case"[object Date]":return new e.constructor(e.valueOf());case"[object RegExp]":var n=new RegExp(e.source,e.toString().match(/[^\/]*$/)[0]);return n.lastIndex=e.lastIndex,n;case"[object Blob]":return new e.constructor([e],{type:e.type})}if(x(e.cloneNode))return e.cloneNode(!0)}var s=[],u=[];if(t){if(B(t)||L(t))throw Xi("cpta","Can't copy! TypedArray destination cannot be mutated.");if(e===t)throw Xi("cpi","Can't copy! Source and destination are identical.");return ir(t)?t.length=0:i(t,function(e,n){"$$hashKey"!==n&&delete t[n]}),s.push(e),u.push(t),n(e,t)}return r(e)}function j(e,t){if(e===t)return!0;if(null===e||null===t)return!1;if(e!==e&&t!==t)return!0;var n,i,r,a=typeof e,s=typeof t;if(a===s&&"object"===a){if(!ir(e)){if(T(e))return!!T(t)&&j(e.getTime(),t.getTime());if(w(e))return!!w(t)&&e.toString()===t.toString();if(P(e)||P(t)||C(e)||C(t)||ir(t)||T(t)||w(t))return!1;r=ge();for(i in e)if("$"!==i.charAt(0)&&!x(e[i])){if(!j(e[i],t[i]))return!1;r[i]=!0}for(i in t)if(!(i in r)&&"$"!==i.charAt(0)&&$(t[i])&&!x(t[i]))return!1;return!0}if(!ir(t))return!1;if((n=e.length)===t.length){for(i=0;i<n;i++)if(!j(e[i],t[i]))return!1;return!0}}return!1}function U(e,t,n){return e.concat(Yi.call(t,n))}function F(e,t){return Yi.call(e,t||0)}function q(e,t){var n=arguments.length>2?F(arguments,2):[];return!x(t)||t instanceof RegExp?t:n.length?function(){return arguments.length?t.apply(e,U(n,arguments,0)):t.apply(e,n)}:function(){return arguments.length?t.apply(e,arguments):t.call(e)}}function G(t,n){var i=n;return"string"==typeof t&&"$"===t.charAt(0)&&"$"===t.charAt(1)?i=void 0:C(n)?i="$WINDOW":n&&e.document===n?i="$DOCUMENT":P(n)&&(i="$SCOPE"),i}function H(e,t){if(!v(e))return M(t)||(t=t?2:null),JSON.stringify(e,G,t)}function z(e){return S(e)?JSON.parse(e):e}function W(e,t){e=e.replace(lr,"");var n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return nr(n)?t:n}function Y(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function J(e,t,n){n=n?-1:1;var i=e.getTimezoneOffset(),r=W(t,i);return Y(e,n*(r-i))}function K(e){e=Hi(e).clone();try{e.empty()}catch(t){}var n=Hi("<div>").append(e).html();try{return e[0].nodeType===mr?ji(n):n.match(/^(<[^>]+>)/)[1].replace(/^<([\w-]+)/,function(e,t){return"<"+ji(t)})}catch(t){return ji(n)}}function Z(e){try{return decodeURIComponent(e)}catch(t){}}function Q(e){var t={};return i((e||"").split("&"),function(e){var n,i,r;e&&(i=e=e.replace(/\+/g,"%20"),n=e.indexOf("="),n!==-1&&(i=e.substring(0,n),r=e.substring(n+1)),i=Z(i),$(i)&&(r=!$(r)||Z(r),Di.call(t,i)?ir(t[i])?t[i].push(r):t[i]=[t[i],r]:t[i]=r))}),t}function X(e){var t=[];return i(e,function(e,n){ir(e)?i(e,function(e){t.push(te(n,!0)+(e===!0?"":"="+te(e,!0)))}):t.push(te(n,!0)+(e===!0?"":"="+te(e,!0)))}),t.length?t.join("&"):""}function ee(e){return te(e,!0).replace(/%26/gi,"&").replace(/%3D/gi,"=").replace(/%2B/gi,"+")}function te(e,t){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%3B/gi,";").replace(/%20/g,t?"%20":"+")}function ne(e,t){var n,i,r=cr.length;for(i=0;i<r;++i)if(n=cr[i]+t,S(n=e.getAttribute(n)))return n;return null}function ie(e){var t=e.currentScript,n=t&&t.getAttribute("src");if(!n)return!0;var i=e.createElement("a");if(i.href=n,e.location.origin===i.origin)return!0;switch(i.protocol){case"http:":case"https:":case"ftp:":case"blob:":case"file:":case"data:":return!0;default:return!1}}function re(t,n){var r,a,s={};if(i(cr,function(e){var n=e+"app";!r&&t.hasAttribute&&t.hasAttribute(n)&&(r=t,a=t.getAttribute(n))}),i(cr,function(e){var n,i=e+"app";!r&&(n=t.querySelector("["+i.replace(":","\\:")+"]"))&&(r=n,a=n.getAttribute(i))}),r){if(!pr)return void e.console.error("Angular: disabling automatic bootstrap. <script> protocol indicates an extension, document.location.href does not match.");s.strictDi=null!==ne(r,"strict-di"),n(r,a?[a]:[],s)}}function ae(t,n,r){y(r)||(r={});var a={strictDi:!1};r=l(a,r);var s=function(){if(t=Hi(t),t.injector()){var i=t[0]===e.document?"document":K(t);throw Xi("btstrpd","App already bootstrapped with this element '{0}'",i.replace(/</,"&lt;").replace(/>/,"&gt;"))}n=n||[],n.unshift(["$provide",function(e){e.value("$rootElement",t)}]),r.debugInfoEnabled&&n.push(["$compileProvider",function(e){e.debugInfoEnabled(!0)}]),n.unshift("ng");var a=rt(n,r.strictDi);return a.invoke(["$rootScope","$rootElement","$compile","$injector",function(e,t,n,i){e.$apply(function(){t.data("$injector",i),n(t)(e)})}]),a},o=/^NG_ENABLE_DEBUG_INFO!/,u=/^NG_DEFER_BOOTSTRAP!/;return e&&o.test(e.name)&&(r.debugInfoEnabled=!0,e.name=e.name.replace(o,"")),e&&!u.test(e.name)?s():(e.name=e.name.replace(u,""),er.resumeBootstrap=function(e){return i(e,function(e){n.push(e)}),s()},void(x(er.resumeDeferredBootstrap)&&er.resumeDeferredBootstrap()))}function se(){e.name="NG_ENABLE_DEBUG_INFO!"+e.name,e.location.reload()}function oe(e){var t=er.element(e).injector();if(!t)throw Xi("test","no injector found for element argument to getTestability");return t.get("$$testability")}function ue(e,t){return t=t||"_",e.replace(hr,function(e,n){return(n?t:"")+e.toLowerCase()})}function le(){var t;if(!dr){var n=ur();zi=v(n)?e.jQuery:n?e[n]:void 0,zi&&zi.fn.on?(Hi=zi,l(zi.fn,{scope:Or.scope,isolateScope:Or.isolateScope,controller:Or.controller,injector:Or.injector,inheritedData:Or.inheritedData}),t=zi.cleanData,zi.cleanData=function(e){for(var n,i,r=0;null!=(i=e[r]);r++)n=zi._data(i,"events"),n&&n.$destroy&&zi(i).triggerHandler("$destroy");t(e)}):Hi=Ae,er.element=Hi,dr=!0}}function ce(e,t,n){if(!e)throw Xi("areq","Argument '{0}' is {1}",t||"?",n||"required");return e}function pe(e,t,n){return n&&ir(e)&&(e=e[e.length-1]),ce(x(e),t,"not a function, got "+(e&&"object"==typeof e?e.constructor.name||"Object":typeof e)),e}function he(e,t){if("hasOwnProperty"===e)throw Xi("badname","hasOwnProperty is not a valid {0} name",t)}function de(e,t,n){if(!t)return e;for(var i,r=t.split("."),a=e,s=r.length,o=0;o<s;o++)i=r[o],e&&(e=(a=e)[i]);return!n&&x(e)?q(a,e):e}function fe(e){for(var t,n=e[0],i=e[e.length-1],r=1;n!==i&&(n=n.nextSibling);r++)(t||e[r]!==n)&&(t||(t=Hi(Yi.call(e,0,r))),t.push(n));return t||e}function ge(){return Object.create(null)}function me(e){function n(e,t,n){return e[t]||(e[t]=n())}var i=t("$injector"),r=t("ng"),a=n(e,"angular",Object);return a.$$minErr=a.$$minErr||t,n(a,"module",function(){var e={};return function(t,a,s){var o=function(e,t){if("hasOwnProperty"===e)throw r("badname","hasOwnProperty is not a valid {0} name",t)};return o(t,"module"),a&&e.hasOwnProperty(t)&&(e[t]=null),n(e,t,function(){function e(e,t,n,i){return i||(i=r),function(){return i[n||"push"]([e,t,arguments]),c}}function n(e,n){return function(i,a){return a&&x(a)&&(a.$$moduleName=t),r.push([e,n,arguments]),c}}if(!a)throw i("nomod","Module '{0}' is not available! You either misspelled the module name or forgot to load it. If registering a module ensure that you specify the dependencies as the second argument.",t);var r=[],o=[],u=[],l=e("$injector","invoke","push",o),c={_invokeQueue:r,_configBlocks:o,_runBlocks:u,requires:a,name:t,provider:n("$provide","provider"),factory:n("$provide","factory"),service:n("$provide","service"),value:e("$provide","value"),constant:e("$provide","constant","unshift"),decorator:n("$provide","decorator"),animation:n("$animateProvider","register"),filter:n("$filterProvider","register"),controller:n("$controllerProvider","register"),directive:n("$compileProvider","directive"),component:n("$compileProvider","component"),config:l,run:function(e){return u.push(e),this}};return s&&l(s),c})}})}function ve(e,t){if(ir(e)){t=t||[];for(var n=0,i=e.length;n<i;n++)t[n]=e[n]}else if(y(e)){t=t||{};for(var r in e)"$"===r.charAt(0)&&"$"===r.charAt(1)||(t[r]=e[r])}return t||e}function $e(e){var t=[];return JSON.stringify(e,function(e,n){if(n=G(e,n),y(n)){if(t.indexOf(n)>=0)return"...";t.push(n)}return n})}function ye(e){return"function"==typeof e?e.toString().replace(/ \{[\s\S]*$/,""):v(e)?"undefined":"string"!=typeof e?$e(e):e}function be(n){l(n,{bootstrap:ae,copy:D,extend:l,merge:c,equals:j,element:Hi,forEach:i,injector:rt,noop:d,bind:q,toJson:H,fromJson:z,identity:f,isUndefined:v,isDefined:$,isString:S,isFunction:x,isObject:y,isNumber:M,isElement:O,isArray:ir,version:br,isDate:T,lowercase:ji,uppercase:Ui,callbacks:{$$counter:0},getTestability:oe,$$minErr:t,$$csp:or,reloadWithDebugInfo:se}),(Wi=me(e))("ng",["ngLocale"],["$provide",function(e){e.provider({$$sanitizeUri:Cn}),e.provider("$compile",gt).directive({a:ns,input:Ss,textarea:Ss,form:os,script:vo,select:bo,option:So,ngBind:xs,ngBindHtml:Cs,ngBindTemplate:ws,ngClass:ks,ngClassEven:As,ngClassOdd:_s,ngCloak:Is,ngController:Es,ngForm:us,ngHide:lo,ngIf:Os,ngInclude:Ns,ngInit:Vs,ngNonBindable:eo,ngPluralize:ro,ngRepeat:ao,ngShow:uo,ngStyle:co,ngSwitch:po,ngSwitchWhen:ho,ngSwitchDefault:fo,ngOptions:io,ngTransclude:mo,ngModel:Zs,ngList:Ds,ngChange:Ps,pattern:To,ngPattern:To,required:Mo,ngRequired:Mo,minlength:wo,ngMinlength:wo,maxlength:xo,ngMaxlength:xo,ngValue:Ts,ngModelOptions:Xs}).directive({ngInclude:Rs}).directive(is).directive(Bs),e.provider({$anchorScroll:at,$animate:Zr,$animateCss:ea,$$animateJs:Jr,$$animateQueue:Kr,$$AnimateRunner:Xr,$$animateAsyncRun:Qr,$browser:pt,$cacheFactory:ht,$controller:St,$document:Mt,$exceptionHandler:Tt,$filter:jn,$$forceReflow:sa,$interpolate:Rt,$interval:Vt,$http:Bt,$httpParamSerializer:wt,$httpParamSerializerJQLike:Ct,$httpBackend:Ot,$xhrFactory:Lt,$jsonpCallbacks:ga,$location:Xt,$log:en,$parse:bn,$rootScope:wn,$q:Sn,$$q:Mn,$sce:An,$sceDelegate:_n,$sniffer:In,$templateCache:dt,$templateRequest:En,$$testability:Bn,$timeout:Ln,$window:Rn,$$rAF:xn,$$jqLite:Ze,$$HashMap:Dr,$$cookieReader:Dn})}])}function Se(){return++Mr}function Me(e){return e.replace(wr,function(e,t,n,i){return i?n.toUpperCase():n}).replace(Cr,"Moz$1")}function Te(e){return!Ar.test(e)}function xe(e){var t=e.nodeType;return t===fr||!t||t===$r}function we(e){for(var t in Sr[e.ng339])return!0;return!1}function Ce(e){for(var t=0,n=e.length;t<n;t++)Le(e[t])}function Pe(e,t){var n,r,a,s,o=t.createDocumentFragment(),u=[];if(Te(e))u.push(t.createTextNode(e));else{for(n=o.appendChild(t.createElement("div")),r=(Ir.exec(e)||["",""])[1].toLowerCase(),a=Br[r]||Br._default,n.innerHTML=a[1]+e.replace(Er,"<$1></$2>")+a[2],s=a[0];s--;)n=n.lastChild;u=U(u,n.childNodes),n=o.firstChild,n.textContent=""}return o.textContent="",o.innerHTML="",i(u,function(e){o.appendChild(e)}),o}function ke(t,n){n=n||e.document;var i;return(i=_r.exec(t))?[n.createElement(i[1])]:(i=Pe(t,n))?i.childNodes:[]}function _e(e,t){var n=e.parentNode;n&&n.replaceChild(t,e),t.appendChild(e)}function Ae(e){if(e instanceof Ae)return e;var t;if(S(e)&&(e=ar(e),t=!0),!(this instanceof Ae)){if(t&&"<"!==e.charAt(0))throw kr("nosel","Looking up elements via selectors is not supported by jqLite! See: http://docs.angularjs.org/api/angular.element");return new Ae(e)}t?je(this,ke(e)):je(this,e)}function Ie(e){return e.cloneNode(!0)}function Ee(e,t){if(t||Le(e),e.querySelectorAll)for(var n=e.querySelectorAll("*"),i=0,r=n.length;i<r;i++)Le(n[i])}function Be(e,t,n,r){if($(r))throw kr("offargs","jqLite#off() does not support the `selector` argument");var a=Oe(e),s=a&&a.events,o=a&&a.handle;if(o)if(t){var u=function(t){var i=s[t];$(n)&&V(i||[],n),$(n)&&i&&i.length>0||(xr(e,t,o),delete s[t])};i(t.split(" "),function(e){u(e),Pr[e]&&u(Pr[e])})}else for(t in s)"$destroy"!==t&&xr(e,t,o),delete s[t]}function Le(e,t){var n=e.ng339,i=n&&Sr[n];if(i){if(t)return void delete i.data[t];i.handle&&(i.events.$destroy&&i.handle({},"$destroy"),Be(e)),delete Sr[n],e.ng339=void 0}}function Oe(e,t){var n=e.ng339,i=n&&Sr[n];return t&&!i&&(e.ng339=n=Se(),i=Sr[n]={events:{},data:{},handle:void 0}),i}function Ne(e,t,n){if(xe(e)){var i=$(n),r=!i&&t&&!y(t),a=!t,s=Oe(e,!r),o=s&&s.data;if(i)o[t]=n;else{if(a)return o;if(r)return o&&o[t];l(o,t)}}}function Re(e,t){return!!e.getAttribute&&(" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ").indexOf(" "+t+" ")>-1}function Ve(e,t){t&&e.setAttribute&&i(t.split(" "),function(t){e.setAttribute("class",ar((" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ").replace(" "+ar(t)+" "," ")))})}function De(e,t){if(t&&e.setAttribute){var n=(" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ");i(t.split(" "),function(e){e=ar(e),n.indexOf(" "+e+" ")===-1&&(n+=e+" ")}),e.setAttribute("class",ar(n))}}function je(e,t){if(t)if(t.nodeType)e[e.length++]=t;else{var n=t.length;if("number"==typeof n&&t.window!==t){if(n)for(var i=0;i<n;i++)e[e.length++]=t[i]}else e[e.length++]=t}}function Ue(e,t){return Fe(e,"$"+(t||"ngController")+"Controller")}function Fe(e,t,n){e.nodeType===$r&&(e=e.documentElement);for(var i=ir(t)?t:[t];e;){for(var r=0,a=i.length;r<a;r++)if($(n=Hi.data(e,i[r])))return n;e=e.parentNode||e.nodeType===yr&&e.host}}function qe(e){for(Ee(e,!0);e.firstChild;)e.removeChild(e.firstChild)}function Ge(e,t){t||Ee(e);var n=e.parentNode;n&&n.removeChild(e)}function He(t,n){n=n||e,"complete"===n.document.readyState?n.setTimeout(t):Hi(n).on("load",t)}function ze(e,t){var n=Nr[t.toLowerCase()];return n&&Rr[R(e)]&&n}function We(e){return Vr[e]}function Ye(e,t){var n=function(n,i){n.isDefaultPrevented=function(){return n.defaultPrevented};var r=t[i||n.type],a=r?r.length:0;if(a){if(v(n.immediatePropagationStopped)){var s=n.stopImmediatePropagation;n.stopImmediatePropagation=function(){n.immediatePropagationStopped=!0,n.stopPropagation&&n.stopPropagation(),s&&s.call(n)}}n.isImmediatePropagationStopped=function(){return n.immediatePropagationStopped===!0};var o=r.specialHandlerWrapper||Je;a>1&&(r=ve(r));for(var u=0;u<a;u++)n.isImmediatePropagationStopped()||o(e,n,r[u])}};return n.elem=e,n}function Je(e,t,n){n.call(e,t)}function Ke(e,t,n){var i=t.relatedTarget;i&&(i===e||Lr.call(e,i))||n.call(e,t)}function Ze(){this.$get=function(){return l(Ae,{hasClass:function(e,t){return e.attr&&(e=e[0]),Re(e,t)},addClass:function(e,t){return e.attr&&(e=e[0]),De(e,t)},removeClass:function(e,t){return e.attr&&(e=e[0]),Ve(e,t)}})}}function Qe(e,t){var n=e&&e.$$hashKey;if(n)return"function"==typeof n&&(n=e.$$hashKey()),n;var i=typeof e;return n="function"===i||"object"===i&&null!==e?e.$$hashKey=i+":"+(t||s)():i+":"+e}function Xe(e,t){if(t){var n=0;this.nextUid=function(){return++n}}i(e,this.put,this)}function et(e){return Function.prototype.toString.call(e)+" "}function tt(e){var t=et(e).replace(Gr,""),n=t.match(jr)||t.match(Ur);return n}function nt(e){var t=tt(e);return t?"function("+(t[1]||"").replace(/[\s\r\n]+/," ")+")":"fn"}function it(e,t,n){var r,a,s;if("function"==typeof e){if(!(r=e.$inject)){if(r=[],e.length){if(t)throw S(n)&&n||(n=e.name||nt(e)),Hr("strictdi","{0} is not using explicit annotation and cannot be invoked in strict mode",n);a=tt(e),i(a[1].split(Fr),function(e){e.replace(qr,function(e,t,n){r.push(n)})})}e.$inject=r}}else ir(e)?(s=e.length-1,pe(e[s],"fn"),r=e.slice(0,s)):pe(e,"fn",!0);return r}function rt(e,t){function n(e){return function(t,n){return y(t)?void i(t,a(e)):e(t,n)}}function r(e,t){if(he(e,"service"),(x(t)||ir(t))&&(t=T.instantiate(t)),!t.$get)throw Hr("pget","Provider '{0}' must define $get factory method.",e);return M[e+m]=t}function s(e,t){return function(){var n=P.invoke(t,this);if(v(n))throw Hr("undef","Provider '{0}' must return a value from $get factory method.",e);return n}}function o(e,t,n){return r(e,{$get:n!==!1?s(e,t):t})}function u(e,t){return o(e,["$injector",function(e){return e.instantiate(t)}])}function l(e,t){return o(e,g(t),!1)}function c(e,t){he(e,"constant"),M[e]=t,w[e]=t}function p(e,t){var n=T.get(e+m),i=n.$get;n.$get=function(){var e=P.invoke(i,n);return P.invoke(t,null,{$delegate:e})}}function h(e){ce(v(e)||ir(e),"modulesToLoad","not an array");var t,n=[];return i(e,function(e){function i(e){var t,n;for(t=0,n=e.length;t<n;t++){var i=e[t],r=T.get(i[0]);r[i[1]].apply(r,i[2])}}if(!b.get(e)){b.put(e,!0);try{S(e)?(t=Wi(e),n=n.concat(h(t.requires)).concat(t._runBlocks),i(t._invokeQueue),i(t._configBlocks)):x(e)?n.push(T.invoke(e)):ir(e)?n.push(T.invoke(e)):pe(e,"module")}catch(r){throw ir(e)&&(e=e[e.length-1]),r.message&&r.stack&&r.stack.indexOf(r.message)===-1&&(r=r.message+"\n"+r.stack),Hr("modulerr","Failed to instantiate module {0} due to:\n{1}",e,r.stack||r.message||r)}}}),n}function d(e,n){function i(t,i){if(e.hasOwnProperty(t)){if(e[t]===f)throw Hr("cdep","Circular dependency found: {0}",t+" <- "+$.join(" <- "));return e[t]}try{return $.unshift(t),e[t]=f,e[t]=n(t,i),e[t]}catch(r){throw e[t]===f&&delete e[t],r}finally{$.shift()}}function r(e,n,r){for(var a=[],s=rt.$$annotate(e,t,r),o=0,u=s.length;o<u;o++){var l=s[o];if("string"!=typeof l)throw Hr("itkn","Incorrect injection token! Expected service name as string, got {0}",l);a.push(n&&n.hasOwnProperty(l)?n[l]:i(l,r))}return a}function a(e){return!(Gi<=11)&&("function"==typeof e&&/^(?:class\b|constructor\()/.test(et(e)))}function s(e,t,n,i){"string"==typeof n&&(i=n,n=null);var s=r(e,n,i);return ir(e)&&(e=e[e.length-1]),a(e)?(s.unshift(null),new(Function.prototype.bind.apply(e,s))):e.apply(t,s)}function o(e,t,n){var i=ir(e)?e[e.length-1]:e,a=r(e,t,n);return a.unshift(null),new(Function.prototype.bind.apply(i,a))}return{invoke:s,instantiate:o,get:i,annotate:rt.$$annotate,has:function(t){return M.hasOwnProperty(t+m)||e.hasOwnProperty(t)}}}t=t===!0;var f={},m="Provider",$=[],b=new Xe([],(!0)),M={$provide:{provider:n(r),factory:n(o),service:n(u),value:n(l),constant:n(c),decorator:p}},T=M.$injector=d(M,function(e,t){throw er.isString(t)&&$.push(t),Hr("unpr","Unknown provider: {0}",$.join(" <- "))}),w={},C=d(w,function(e,t){var n=T.get(e+m,t);return P.invoke(n.$get,n,void 0,e)}),P=C;M["$injector"+m]={$get:g(C)};var k=h(e);return P=C.get("$injector"),P.strictDi=t,i(k,function(e){e&&P.invoke(e)}),P}function at(){var e=!0;this.disableAutoScrolling=function(){e=!1},this.$get=["$window","$location","$rootScope",function(t,n,i){function r(e){var t=null;return Array.prototype.some.call(e,function(e){if("a"===R(e))return t=e,!0}),t}function a(){var e=o.yOffset;if(x(e))e=e();else if(O(e)){var n=e[0],i=t.getComputedStyle(n);e="fixed"!==i.position?0:n.getBoundingClientRect().bottom}else M(e)||(e=0);return e}function s(e){if(e){e.scrollIntoView();var n=a();if(n){var i=e.getBoundingClientRect().top;t.scrollBy(0,i-n)}}else t.scrollTo(0,0)}function o(e){e=S(e)?e:M(e)?e.toString():n.hash();var t;e?(t=u.getElementById(e))?s(t):(t=r(u.getElementsByName(e)))?s(t):"top"===e&&s(null):s(null)}var u=t.document;return e&&i.$watch(function(){return n.hash()},function(e,t){e===t&&""===e||He(function(){i.$evalAsync(o)})}),o}]}function st(e,t){return e||t?e?t?(ir(e)&&(e=e.join(" ")),ir(t)&&(t=t.join(" ")),e+" "+t):e:t:""}function ot(e){for(var t=0;t<e.length;t++){var n=e[t];if(n.nodeType===Wr)return n}}function ut(e){S(e)&&(e=e.split(" "));var t=ge();return i(e,function(e){e.length&&(t[e]=!0)}),t}function lt(e){return y(e)?e:{}}function ct(e,t,n,r){function a(e){try{e.apply(null,F(arguments,1))}finally{if($--,0===$)for(;y.length;)try{y.pop()()}catch(t){n.error(t)}}}function s(e){var t=e.indexOf("#");return t===-1?"":e.substr(t)}function o(){x=null,u(),l()}function u(){b=w(),b=v(b)?null:b,j(b,k)&&(b=k),k=b}function l(){M===c.url()&&S===b||(M=c.url(),S=b,i(C,function(e){e(c.url(),b)}))}var c=this,p=e.location,h=e.history,f=e.setTimeout,g=e.clearTimeout,m={};c.isMock=!1;var $=0,y=[];c.$$completeOutstandingRequest=a,c.$$incOutstandingRequestCount=function(){$++},c.notifyWhenNoOutstandingRequests=function(e){0===$?e():y.push(e)};var b,S,M=p.href,T=t.find("base"),x=null,w=r.history?function(){try{return h.state}catch(e){}}:d;u(),S=b,c.url=function(t,n,i){if(v(i)&&(i=null),p!==e.location&&(p=e.location),h!==e.history&&(h=e.history),t){var a=S===i;if(M===t&&(!r.history||a))return c;var o=M&&Gt(M)===Gt(t);return M=t,S=i,!r.history||o&&a?(o||(x=t),n?p.replace(t):o?p.hash=s(t):p.href=t,p.href!==t&&(x=t)):(h[n?"replaceState":"pushState"](i,"",t),u(),S=b),x&&(x=t),c}return x||p.href.replace(/%27/g,"'")},c.state=function(){return b};var C=[],P=!1,k=null;c.onUrlChange=function(t){return P||(r.history&&Hi(e).on("popstate",o),Hi(e).on("hashchange",o),P=!0),C.push(t),t},c.$$applicationDestroyed=function(){Hi(e).off("hashchange popstate",o)},c.$$checkUrlChange=l,c.baseHref=function(){var e=T.attr("href");return e?e.replace(/^(https?:)?\/\/[^\/]*/,""):""},c.defer=function(e,t){var n;return $++,n=f(function(){delete m[n],a(e)},t||0),m[n]=!0,n},c.defer.cancel=function(e){return!!m[e]&&(delete m[e],g(e),a(d),!0)}}function pt(){this.$get=["$window","$log","$sniffer","$document",function(e,t,n,i){return new ct(e,i,t,n)}]}function ht(){this.$get=function(){function e(e,i){function r(e){e!==h&&(d?d===e&&(d=e.n):d=e,a(e.n,e.p),a(e,h),h=e,h.n=null)}function a(e,t){e!==t&&(e&&(e.p=t),t&&(t.n=e))}if(e in n)throw t("$cacheFactory")("iid","CacheId '{0}' is already taken!",e);var s=0,o=l({},i,{id:e}),u=ge(),c=i&&i.capacity||Number.MAX_VALUE,p=ge(),h=null,d=null;return n[e]={put:function(e,t){if(!v(t)){if(c<Number.MAX_VALUE){var n=p[e]||(p[e]={key:e});r(n)}return e in u||s++,u[e]=t,s>c&&this.remove(d.key),t}},get:function(e){if(c<Number.MAX_VALUE){var t=p[e];if(!t)return;r(t)}return u[e]},remove:function(e){if(c<Number.MAX_VALUE){var t=p[e];if(!t)return;t===h&&(h=t.p),t===d&&(d=t.n),a(t.n,t.p),delete p[e]}e in u&&(delete u[e],s--)},removeAll:function(){u=ge(),s=0,p=ge(),h=d=null},destroy:function(){u=null,o=null,p=null,delete n[e]},info:function(){return l({},o,{size:s})}}}var n={};return e.info=function(){var e={};return i(n,function(t,n){e[n]=t.info()}),e},e.get=function(e){return n[e]},e}}function dt(){this.$get=["$cacheFactory",function(e){return e("templates")}]}function ft(){}function gt(t,n){function r(e,t,n){var r=/^\s*([@&<]|=(\*?))(\??)\s*([\w$]*)\s*$/,a=ge();return i(e,function(e,i){if(e in k)return void(a[i]=k[e]);var s=e.match(r);if(!s)throw ta("iscp","Invalid {3} for directive '{0}'. Definition: {... {1}: '{2}' ...}",t,i,e,n?"controller bindings definition":"isolate scope definition");a[i]={mode:s[1][0],collection:"*"===s[2],optional:"?"===s[3],attrName:s[4]||i},s[4]&&(k[e]=a[i])}),a}function s(e,t){var n={isolateScope:null,bindToController:null};if(y(e.scope)&&(e.bindToController===!0?(n.bindToController=r(e.scope,t,!0),n.isolateScope={}):n.isolateScope=r(e.scope,t,!1)),y(e.bindToController)&&(n.bindToController=r(e.bindToController,t,!0)),n.bindToController&&!e.controller)throw ta("noctrl","Cannot bind to controller without directive '{0}'s controller.",t);return n}function o(e){var t=e.charAt(0);if(!t||t!==ji(t))throw ta("baddir","Directive/Component name '{0}' is invalid. The first character must be a lowercase letter",e);if(e!==e.trim())throw ta("baddir","Directive/Component name '{0}' is invalid. The name should not contain leading or trailing whitespaces",e)}function u(e){var t=e.require||e.controller&&e.name;return!ir(t)&&y(t)&&i(t,function(e,n){var i=e.match(w),r=e.substring(i[0].length);r||(t[n]=i[0]+n)}),t}function c(e,t){if(e&&(!S(e)||!/[EACM]/.test(e)))throw ta("badrestrict","Restrict property '{0}' of directive '{1}' is invalid",e,t);return e||"EA"}var p={},m="Directive",b=/^\s*directive:\s*([\w-]+)\s+(.*)$/,M=/(([\w-]+)(?::([^;]+))?;?)/,T=N("ngSrc,ngSrcset,src,srcset"),w=/^(?:(\^\^?)?(\?)?(\^\^?)?)?/,C=/^(on[a-z]+|formaction)$/,k=ge();this.directive=function O(e,n){return ce(e,"name"),he(e,"directive"),S(e)?(o(e),ce(n,"directiveFactory"),p.hasOwnProperty(e)||(p[e]=[],t.factory(e+m,["$injector","$exceptionHandler",function(t,n){var r=[];return i(p[e],function(i,a){try{var s=t.invoke(i);x(s)?s={compile:g(s)}:!s.compile&&s.link&&(s.compile=g(s.link)),s.priority=s.priority||0,s.index=a,s.name=s.name||e,s.require=u(s),s.restrict=c(s.restrict,e),s.$$moduleName=i.$$moduleName,r.push(s)}catch(o){n(o)}}),r}])),p[e].push(n)):i(e,a(O)),this},this.component=function(e,t){function n(e){function n(t){return x(t)||ir(t)?function(n,i){return e.invoke(t,this,{$element:n,$attrs:i})}:t}var a=t.template||t.templateUrl?t.template:"",s={controller:r,controllerAs:bt(t.controller)||t.controllerAs||"$ctrl",template:n(a),templateUrl:n(t.templateUrl),transclude:t.transclude,scope:{},bindToController:t.bindings||{},restrict:"E",require:t.require};return i(t,function(e,t){"$"===t.charAt(0)&&(s[t]=e)}),s}var r=t.controller||function(){};return i(t,function(e,t){"$"===t.charAt(0)&&(n[t]=e,x(r)&&(r[t]=e))}),n.$inject=["$injector"],this.directive(e,n)},this.aHrefSanitizationWhitelist=function(e){return $(e)?(n.aHrefSanitizationWhitelist(e),this):n.aHrefSanitizationWhitelist()},this.imgSrcSanitizationWhitelist=function(e){return $(e)?(n.imgSrcSanitizationWhitelist(e),this):n.imgSrcSanitizationWhitelist()};var _=!0;this.debugInfoEnabled=function(e){return $(e)?(_=e,this):_};var A=!0;this.preAssignBindingsEnabled=function(e){return $(e)?(A=e,this):A};var E=10;this.onChangesTtl=function(e){return arguments.length?(E=e,this):E};var B=!0;this.commentDirectivesEnabled=function(e){return arguments.length?(B=e,this):B};var L=!0;this.cssClassDirectivesEnabled=function(e){return arguments.length?(L=e,this):L},this.$get=["$injector","$interpolate","$exceptionHandler","$templateRequest","$parse","$controller","$rootScope","$sce","$animate","$$sanitizeUri",function(t,n,r,a,o,u,c,g,k,O){function N(){try{if(!--ke)throw Me=void 0,ta("infchng","{0} $onChanges() iterations reached. Aborting!\n",E);c.$apply(function(){for(var e=[],t=0,n=Me.length;t<n;++t)try{Me[t]()}catch(i){e.push(i)}if(Me=void 0,e.length)throw e})}finally{ke++}}function D(e,t){if(t){var n,i,r,a=Object.keys(t);for(n=0,i=a.length;n<i;n++)r=a[n],this[r]=t[r]}else this.$attr={};this.$$element=e}function U(e,t,n){we.innerHTML="<span "+t+">";var i=we.firstChild.attributes,r=i[0];i.removeNamedItem(r.name),r.value=n,e.attributes.setNamedItem(r)}function G(e,t){try{e.addClass(t)}catch(n){}}function H(t,n,i,r,a){t instanceof Hi||(t=Hi(t));for(var s=/\S+/,o=0,u=t.length;o<u;o++){var l=t[o];l.nodeType===mr&&l.nodeValue.match(s)&&_e(l,t[o]=e.document.createElement("span"))}var c=W(t,n,t,i,r,a);H.$$addScopeClass(t);var p=null;return function(e,n,i){ce(e,"scope"),a&&a.needsNewScope&&(e=e.$parent.$new()),i=i||{};var r=i.parentBoundTranscludeFn,s=i.transcludeControllers,o=i.futureParentElement;r&&r.$$boundTransclude&&(r=r.$$boundTransclude),p||(p=z(o));var u;if(u="html"!==p?Hi(fe(p,Hi("<div>").append(t).html())):n?Or.clone.call(t):t,s)for(var l in s)u.data("$"+l+"Controller",s[l].instance);return H.$$addScopeInfo(u,e),n&&n(u,e),c&&c(e,u,u,r),u}}function z(e){var t=e&&e[0];return t&&"foreignobject"!==R(t)&&Zi.call(t).match(/SVG/)?"svg":"html"}function W(e,t,n,i,r,a){function s(e,n,i,r){var a,s,o,u,l,c,p,h,g;if(d){var m=n.length;for(g=new Array(m),l=0;l<f.length;l+=3)p=f[l],g[p]=n[p]}else g=n;for(l=0,c=f.length;l<c;)o=g[f[l++]],a=f[l++],s=f[l++],a?(a.scope?(u=e.$new(),H.$$addScopeInfo(Hi(o),u)):u=e,h=a.transcludeOnThisElement?Y(e,a.transclude,r):!a.templateOnThisElement&&r?r:!r&&t?Y(e,t):null,a(s,u,o,i,h)):s&&s(e,o.childNodes,void 0,r)}for(var o,u,l,c,p,h,d,f=[],g=0;g<e.length;g++)o=new D,u=J(e[g],[],o,0===g?i:void 0,r),l=u.length?te(u,e[g],o,t,n,null,[],[],a):null,l&&l.scope&&H.$$addScopeClass(o.$$element),p=l&&l.terminal||!(c=e[g].childNodes)||!c.length?null:W(c,l?(l.transcludeOnThisElement||!l.templateOnThisElement)&&l.transclude:t),(l||p)&&(f.push(g,l,p),h=!0,d=d||l),a=null;return h?s:null}function Y(e,t,n){function i(i,r,a,s,o){return i||(i=e.$new(!1,o),i.$$transcluded=!0),t(i,r,{parentBoundTranscludeFn:n,transcludeControllers:a,futureParentElement:s})}var r=i.$$slots=ge();for(var a in t.$$slots)t.$$slots[a]?r[a]=Y(e,t.$$slots[a],n):r[a]=null;return i}function J(e,t,n,i,r){var a,s,o,u=e.nodeType,l=n.$attr;switch(u){case fr:s=R(e),ae(t,vt(s),"E",i,r);for(var c,p,h,d,f,g,m=e.attributes,v=0,$=m&&m.length;v<$;v++){var b=!1,T=!1;c=m[v],p=c.name,f=ar(c.value),d=vt(p),g=Le.test(d),
g&&(p=p.replace(ia,"").substr(8).replace(/_(.)/g,function(e,t){return t.toUpperCase()}));var x=d.match(Oe);x&&se(x[1])&&(b=p,T=p.substr(0,p.length-5)+"end",p=p.substr(0,p.length-6)),h=vt(p.toLowerCase()),l[h]=p,!g&&n.hasOwnProperty(h)||(n[h]=f,ze(e,h)&&(n[h]=!0)),ve(e,t,f,h,g),ae(t,h,"A",i,r,b,T)}if("input"===s&&"hidden"===e.getAttribute("type")&&e.setAttribute("autocomplete","off"),!Pe)break;if(o=e.className,y(o)&&(o=o.animVal),S(o)&&""!==o)for(;a=M.exec(o);)h=vt(a[2]),ae(t,h,"C",i,r)&&(n[h]=ar(a[3])),o=o.substr(a.index+a[0].length);break;case mr:if(11===Gi)for(;e.parentNode&&e.nextSibling&&e.nextSibling.nodeType===mr;)e.nodeValue=e.nodeValue+e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);de(t,e.nodeValue);break;case vr:if(!Ce)break;Z(e,t,n,i,r)}return t.sort(pe),t}function Z(e,t,n,i,r){try{var a=b.exec(e.nodeValue);if(a){var s=vt(a[1]);ae(t,s,"M",i,r)&&(n[s]=ar(a[2]))}}catch(o){}}function Q(e,t,n){var i=[],r=0;if(t&&e.hasAttribute&&e.hasAttribute(t)){do{if(!e)throw ta("uterdir","Unterminated attribute, found '{0}' but no matching '{1}' found.",t,n);e.nodeType===fr&&(e.hasAttribute(t)&&r++,e.hasAttribute(n)&&r--),i.push(e),e=e.nextSibling}while(r>0)}else i.push(e);return Hi(i)}function X(e,t,n){return function(i,r,a,s,o){return r=Q(r[0],t,n),e(i,r,a,s,o)}}function ee(e,t,n,i,r,a){var s;return e?H(t,n,i,r,a):function(){return s||(s=H(t,n,i,r,a),t=n=a=null),s.apply(this,arguments)}}function te(e,t,n,a,s,o,u,c,p){function h(e,t,n,i){e&&(n&&(e=X(e,n,i)),e.require=f.require,e.directiveName=g,(w===f||f.$$isolateScope)&&(e=ye(e,{isolateScope:!0})),u.push(e)),t&&(n&&(t=X(t,n,i)),t.require=f.require,t.directiveName=g,(w===f||f.$$isolateScope)&&(t=ye(t,{isolateScope:!0})),c.push(t))}function d(e,a,s,o,p){function h(e,t,n,i){var r;if(P(e)||(i=n,n=t,t=e,e=void 0),E&&(r=b),n||(n=E?k.parent():k),!i)return p(e,t,r,n,V);var a=p.$$slots[i];if(a)return a(e,t,r,n,V);if(v(a))throw ta("noslot",'No parent directive that requires a transclusion with slot name "{0}". Element: {1}',i,K(k))}var d,f,g,m,$,b,S,k,_,I;t===s?(_=n,k=n.$$element):(k=Hi(s),_=new D(k,n)),$=a,w?m=a.$new(!0):M&&($=a.$parent),p&&(S=h,S.$$boundTransclude=p,S.isSlotFilled=function(e){return!!p.$$slots[e]}),T&&(b=ie(k,_,S,T,m,a,w)),w&&(H.$$addScopeInfo(k,m,!0,!(C&&(C===w||C===w.$$originalDirective))),H.$$addScopeClass(k,!0),m.$$isolateBindings=w.$$isolateBindings,I=Se(a,_,m,m.$$isolateBindings,w),I.removeWatches&&m.$on("$destroy",I.removeWatches));for(var B in b){var L=T[B],O=b[B],N=L.$$bindings.bindToController;if(A){N?O.bindingInfo=Se($,_,O.instance,N,L):O.bindingInfo={};var R=O();R!==O.instance&&(O.instance=R,k.data("$"+L.name+"Controller",R),O.bindingInfo.removeWatches&&O.bindingInfo.removeWatches(),O.bindingInfo=Se($,_,O.instance,N,L))}else O.instance=O(),k.data("$"+L.name+"Controller",O.instance),O.bindingInfo=Se($,_,O.instance,N,L)}for(i(T,function(e,t){var n=e.require;e.bindToController&&!ir(n)&&y(n)&&l(b[t].instance,ne(t,n,k,b))}),i(b,function(e){var t=e.instance;if(x(t.$onChanges))try{t.$onChanges(e.bindingInfo.initialChanges)}catch(n){r(n)}if(x(t.$onInit))try{t.$onInit()}catch(n){r(n)}x(t.$doCheck)&&($.$watch(function(){t.$doCheck()}),t.$doCheck()),x(t.$onDestroy)&&$.$on("$destroy",function(){t.$onDestroy()})}),d=0,f=u.length;d<f;d++)g=u[d],be(g,g.isolateScope?m:a,k,_,g.require&&ne(g.directiveName,g.require,k,b),S);var V=a;for(w&&(w.template||null===w.templateUrl)&&(V=m),e&&e(V,s.childNodes,void 0,p),d=c.length-1;d>=0;d--)g=c[d],be(g,g.isolateScope?m:a,k,_,g.require&&ne(g.directiveName,g.require,k,b),S);i(b,function(e){var t=e.instance;x(t.$postLink)&&t.$postLink()})}p=p||{};for(var f,g,m,$,b,S=-Number.MAX_VALUE,M=p.newScopeDirective,T=p.controllerDirectives,w=p.newIsolateScopeDirective,C=p.templateDirective,k=p.nonTlbTranscludeDirective,_=!1,I=!1,E=p.hasElementTranscludeDirective,B=n.$$element=Hi(t),L=o,O=a,N=!1,V=!1,j=0,U=e.length;j<U;j++){f=e[j];var G=f.$$start,z=f.$$end;if(G&&(B=Q(t,G,z)),m=void 0,S>f.priority)break;if(b=f.scope,b&&(f.templateUrl||(y(b)?(he("new/isolated scope",w||M,f,B),w=f):he("new/isolated scope",w,f,B)),M=M||f),g=f.name,!N&&(f.replace&&(f.templateUrl||f.template)||f.transclude&&!f.$$tlb)){for(var W,Y=j+1;W=e[Y++];)if(W.transclude&&!W.$$tlb||W.replace&&(W.templateUrl||W.template)){V=!0;break}N=!0}if(!f.templateUrl&&f.controller&&(T=T||ge(),he("'"+g+"' controller",T[g],f,B),T[g]=f),b=f.transclude)if(_=!0,f.$$tlb||(he("transclusion",k,f,B),k=f),"element"===b)E=!0,S=f.priority,m=B,B=n.$$element=Hi(H.$$createComment(g,n[g])),t=B[0],$e(s,F(m),t),m[0].$$parentNode=m[0].parentNode,O=ee(V,m,a,S,L&&L.name,{nonTlbTranscludeDirective:k});else{var Z=ge();if(m=Hi(Ie(t)).contents(),y(b)){m=[];var te=ge(),ae=ge();i(b,function(e,t){var n="?"===e.charAt(0);e=n?e.substring(1):e,te[e]=t,Z[t]=null,ae[t]=n}),i(B.contents(),function(e){var t=te[vt(R(e))];t?(ae[t]=!0,Z[t]=Z[t]||[],Z[t].push(e)):m.push(e)}),i(ae,function(e,t){if(!e)throw ta("reqslot","Required transclusion slot `{0}` was not filled.",t)});for(var se in Z)Z[se]&&(Z[se]=ee(V,Z[se],a))}B.empty(),O=ee(V,m,a,void 0,void 0,{needsNewScope:f.$$isolateScope||f.$$newScope}),O.$$slots=Z}if(f.template)if(I=!0,he("template",C,f,B),C=f,b=x(f.template)?f.template(B,n):f.template,b=Be(b),f.replace){if(L=f,m=Te(b)?[]:yt(fe(f.templateNamespace,ar(b))),t=m[0],1!==m.length||t.nodeType!==fr)throw ta("tplrt","Template for directive '{0}' must have exactly one root element. {1}",g,"");$e(s,B,t);var ue={$attr:{}},ce=J(t,[],ue),pe=e.splice(j+1,e.length-(j+1));(w||M)&&re(ce,w,M),e=e.concat(ce).concat(pe),oe(n,ue),U=e.length}else B.html(b);if(f.templateUrl)I=!0,he("template",C,f,B),C=f,f.replace&&(L=f),d=le(e.splice(j,e.length-j),B,n,s,_&&O,u,c,{controllerDirectives:T,newScopeDirective:M!==f&&M,newIsolateScopeDirective:w,templateDirective:C,nonTlbTranscludeDirective:k}),U=e.length;else if(f.compile)try{$=f.compile(B,n,O);var de=f.$$originalDirective||f;x($)?h(null,q(de,$),G,z):$&&h(q(de,$.pre),q(de,$.post),G,z)}catch(me){r(me,K(B))}f.terminal&&(d.terminal=!0,S=Math.max(S,f.priority))}return d.scope=M&&M.scope===!0,d.transcludeOnThisElement=_,d.templateOnThisElement=I,d.transclude=O,p.hasElementTranscludeDirective=E,d}function ne(e,t,n,r){var a;if(S(t)){var s=t.match(w),o=t.substring(s[0].length),u=s[1]||s[3],l="?"===s[2];if("^^"===u?n=n.parent():(a=r&&r[o],a=a&&a.instance),!a){var c="$"+o+"Controller";a=u?n.inheritedData(c):n.data(c)}if(!a&&!l)throw ta("ctreq","Controller '{0}', required by directive '{1}', can't be found!",o,e)}else if(ir(t)){a=[];for(var p=0,h=t.length;p<h;p++)a[p]=ne(e,t[p],n,r)}else y(t)&&(a={},i(t,function(t,i){a[i]=ne(e,t,n,r)}));return a||null}function ie(e,t,n,i,r,a,s){var o=ge();for(var l in i){var c=i[l],p={$scope:c===s||c.$$isolateScope?r:a,$element:e,$attrs:t,$transclude:n},h=c.controller;"@"===h&&(h=t[c.name]);var d=u(h,p,!0,c.controllerAs);o[c.name]=d,e.data("$"+c.name+"Controller",d.instance)}return o}function re(e,t,n){for(var i=0,r=e.length;i<r;i++)e[i]=h(e[i],{$$isolateScope:t,$$newScope:n})}function ae(e,n,i,r,a,o,u){if(n===a)return null;var l=null;if(p.hasOwnProperty(n))for(var c,d=t.get(n+m),f=0,g=d.length;f<g;f++)if(c=d[f],(v(r)||r>c.priority)&&c.restrict.indexOf(i)!==-1){if(o&&(c=h(c,{$$start:o,$$end:u})),!c.$$bindings){var $=c.$$bindings=s(c,c.name);y($.isolateScope)&&(c.$$isolateBindings=$.isolateScope)}e.push(c),l=c}return l}function se(e){if(p.hasOwnProperty(e))for(var n,i=t.get(e+m),r=0,a=i.length;r<a;r++)if(n=i[r],n.multiElement)return!0;return!1}function oe(e,t){var n=t.$attr,r=e.$attr;i(e,function(i,r){"$"!==r.charAt(0)&&(t[r]&&t[r]!==i&&(i+=("style"===r?";":" ")+t[r]),e.$set(r,i,!0,n[r]))}),i(t,function(t,i){e.hasOwnProperty(i)||"$"===i.charAt(0)||(e[i]=t,"class"!==i&&"style"!==i&&(r[i]=n[i]))})}function le(e,t,n,r,s,o,u,l){var c,p,d=[],f=t[0],g=e.shift(),m=h(g,{templateUrl:null,transclude:null,replace:null,$$originalDirective:g}),v=x(g.templateUrl)?g.templateUrl(t,n):g.templateUrl,$=g.templateNamespace;return t.empty(),a(v).then(function(a){var h,b,S,M;if(a=Be(a),g.replace){if(S=Te(a)?[]:yt(fe($,ar(a))),h=S[0],1!==S.length||h.nodeType!==fr)throw ta("tplrt","Template for directive '{0}' must have exactly one root element. {1}",g.name,v);b={$attr:{}},$e(r,t,h);var T=J(h,[],b);y(g.scope)&&re(T,!0),e=T.concat(e),oe(n,b)}else h=f,t.html(a);for(e.unshift(m),c=te(e,h,n,s,t,g,o,u,l),i(r,function(e,n){e===h&&(r[n]=t[0])}),p=W(t[0].childNodes,s);d.length;){var x=d.shift(),w=d.shift(),C=d.shift(),P=d.shift(),k=t[0];if(!x.$$destroyed){if(w!==f){var _=w.className;l.hasElementTranscludeDirective&&g.replace||(k=Ie(h)),$e(C,Hi(w),k),G(Hi(k),_)}M=c.transcludeOnThisElement?Y(x,c.transclude,P):P,c(p,x,k,r,M)}}d=null}),function(e,t,n,i,r){var a=r;t.$$destroyed||(d?d.push(t,n,i,a):(c.transcludeOnThisElement&&(a=Y(t,c.transclude,r)),c(p,t,n,i,a)))}}function pe(e,t){var n=t.priority-e.priority;return 0!==n?n:e.name!==t.name?e.name<t.name?-1:1:e.index-t.index}function he(e,t,n,i){function r(e){return e?" (module: "+e+")":""}if(t)throw ta("multidir","Multiple directives [{0}{1}, {2}{3}] asking for {4} on: {5}",t.name,r(t.$$moduleName),n.name,r(n.$$moduleName),e,K(i))}function de(e,t){var i=n(t,!0);i&&e.push({priority:0,compile:function(e){var t=e.parent(),n=!!t.length;return n&&H.$$addBindingClass(t),function(e,t){var r=t.parent();n||H.$$addBindingClass(r),H.$$addBindingInfo(r,i.expressions),e.$watch(i,function(e){t[0].nodeValue=e})}}})}function fe(t,n){switch(t=ji(t||"html")){case"svg":case"math":var i=e.document.createElement("div");return i.innerHTML="<"+t+">"+n+"</"+t+">",i.childNodes[0].childNodes;default:return n}}function me(e,t){if("srcdoc"===t)return g.HTML;var n=R(e);if("src"===t||"ngSrc"===t){if(["img","video","audio","source","track"].indexOf(n)===-1)return g.RESOURCE_URL}else if("xlinkHref"===t||"form"===n&&"action"===t)return g.RESOURCE_URL}function ve(e,t,i,r,a){var s=me(e,r),o=!a,u=T[r]||a,l=n(i,o,s,u);if(l){if("multiple"===r&&"select"===R(e))throw ta("selmulti","Binding to the 'multiple' attribute is not supported. Element: {0}",K(e));t.push({priority:100,compile:function(){return{pre:function(e,t,a){var o=a.$$observers||(a.$$observers=ge());if(C.test(r))throw ta("nodomevents","Interpolations for HTML DOM event attributes are disallowed.  Please use the ng- versions (such as ng-click instead of onclick) instead.");var c=a[r];c!==i&&(l=c&&n(c,!0,s,u),i=c),l&&(a[r]=l(e),(o[r]||(o[r]=[])).$$inter=!0,(a.$$observers&&a.$$observers[r].$$scope||e).$watch(l,function(e,t){"class"===r&&e!==t?a.$updateClass(e,t):a.$set(r,e)}))}}}})}}function $e(t,n,i){var r,a,s=n[0],o=n.length,u=s.parentNode;if(t)for(r=0,a=t.length;r<a;r++)if(t[r]===s){t[r++]=i;for(var l=r,c=l+o-1,p=t.length;l<p;l++,c++)c<p?t[l]=t[c]:delete t[l];t.length-=o-1,t.context===s&&(t.context=i);break}u&&u.replaceChild(i,s);var h=e.document.createDocumentFragment();for(r=0;r<o;r++)h.appendChild(n[r]);for(Hi.hasData(s)&&(Hi.data(i,Hi.data(s)),Hi(s).off("$destroy")),Hi.cleanData(h.querySelectorAll("*")),r=1;r<o;r++)delete n[r];n[0]=i,n.length=1}function ye(e,t){return l(function(){return e.apply(null,arguments)},e,t)}function be(e,t,n,i,a,s){try{e(t,n,i,a,s)}catch(o){r(o,K(n))}}function Se(e,t,r,a,s){function u(t,n,i){!x(r.$onChanges)||n===i||n!==n&&i!==i||(Me||(e.$$postDigest(N),Me=[]),c||(c={},Me.push(l)),c[t]&&(i=c[t].previousValue),c[t]=new mt(i,n))}function l(){r.$onChanges(c),c=void 0}var c,p=[],h={};return i(a,function(i,a){var l,c,f,g,m,v=i.attrName,$=i.optional,y=i.mode;switch(y){case"@":$||Di.call(t,v)||(r[a]=t[v]=void 0),m=t.$observe(v,function(e){if(S(e)||I(e)){var t=r[a];u(a,e,t),r[a]=e}}),t.$$observers[v].$$scope=e,l=t[v],S(l)?r[a]=n(l)(e):I(l)&&(r[a]=l),h[a]=new mt(na,r[a]),p.push(m);break;case"=":if(!Di.call(t,v)){if($)break;t[v]=void 0}if($&&!t[v])break;c=o(t[v]),g=c.literal?j:function(e,t){return e===t||e!==e&&t!==t},f=c.assign||function(){throw l=r[a]=c(e),ta("nonassign","Expression '{0}' in attribute '{1}' used with directive '{2}' is non-assignable!",t[v],v,s.name)},l=r[a]=c(e);var b=function(t){return g(t,r[a])||(g(t,l)?f(e,t=r[a]):r[a]=t),l=t};b.$stateful=!0,m=i.collection?e.$watchCollection(t[v],b):e.$watch(o(t[v],b),null,c.literal),p.push(m);break;case"<":if(!Di.call(t,v)){if($)break;t[v]=void 0}if($&&!t[v])break;c=o(t[v]);var M=c.literal,T=r[a]=c(e);h[a]=new mt(na,r[a]),m=e.$watch(c,function(e,t){if(t===e){if(t===T||M&&j(t,T))return;t=T}u(a,e,t),r[a]=e},M),p.push(m);break;case"&":if(c=t.hasOwnProperty(v)?o(t[v]):d,c===d&&$)break;r[a]=function(t){return c(e,t)}}}),{initialChanges:h,removeWatches:p.length&&function(){for(var e=0,t=p.length;e<t;++e)p[e]()}}}var Me,xe=/^\w/,we=e.document.createElement("div"),Ce=B,Pe=L,ke=E;D.prototype={$normalize:vt,$addClass:function(e){e&&e.length>0&&k.addClass(this.$$element,e)},$removeClass:function(e){e&&e.length>0&&k.removeClass(this.$$element,e)},$updateClass:function(e,t){var n=$t(e,t);n&&n.length&&k.addClass(this.$$element,n);var i=$t(t,e);i&&i.length&&k.removeClass(this.$$element,i)},$set:function(e,t,n,a){var s,o=this.$$element[0],u=ze(o,e),l=We(e),c=e;if(u?(this.$$element.prop(e,t),a=u):l&&(this[l]=t,c=l),this[e]=t,a?this.$attr[e]=a:(a=this.$attr[e],a||(this.$attr[e]=a=ue(e,"-"))),s=R(this.$$element),"a"===s&&("href"===e||"xlinkHref"===e)||"img"===s&&"src"===e)this[e]=t=O(t,"src"===e);else if("img"===s&&"srcset"===e&&$(t)){for(var p="",h=ar(t),d=/(\s+\d+x\s*,|\s+\d+w\s*,|\s+,|,\s+)/,f=/\s/.test(h)?d:/(,)/,g=h.split(f),m=Math.floor(g.length/2),y=0;y<m;y++){var b=2*y;p+=O(ar(g[b]),!0),p+=" "+ar(g[b+1])}var S=ar(g[2*y]).split(/\s/);p+=O(ar(S[0]),!0),2===S.length&&(p+=" "+ar(S[1])),this[e]=t=p}n!==!1&&(null===t||v(t)?this.$$element.removeAttr(a):xe.test(a)?this.$$element.attr(a,t):U(this.$$element[0],a,t));var M=this.$$observers;M&&i(M[c],function(e){try{e(t)}catch(n){r(n)}})},$observe:function(e,t){var n=this,i=n.$$observers||(n.$$observers=ge()),r=i[e]||(i[e]=[]);return r.push(t),c.$evalAsync(function(){r.$$inter||!n.hasOwnProperty(e)||v(n[e])||t(n[e])}),function(){V(r,t)}}};var Ae=n.startSymbol(),Ee=n.endSymbol(),Be="{{"===Ae&&"}}"===Ee?f:function(e){return e.replace(/\{\{/g,Ae).replace(/}}/g,Ee)},Le=/^ngAttr[A-Z]/,Oe=/^(.+)Start$/;return H.$$addBindingInfo=_?function(e,t){var n=e.data("$binding")||[];ir(t)?n=n.concat(t):n.push(t),e.data("$binding",n)}:d,H.$$addBindingClass=_?function(e){G(e,"ng-binding")}:d,H.$$addScopeInfo=_?function(e,t,n,i){var r=n?i?"$isolateScopeNoTemplate":"$isolateScope":"$scope";e.data(r,t)}:d,H.$$addScopeClass=_?function(e,t){G(e,t?"ng-isolate-scope":"ng-scope")}:d,H.$$createComment=function(t,n){var i="";return _&&(i=" "+(t||"")+": ",n&&(i+=n+" ")),e.document.createComment(i)},H}]}function mt(e,t){this.previousValue=e,this.currentValue=t}function vt(e){return Me(e.replace(ia,""))}function $t(e,t){var n="",i=e.split(/\s+/),r=t.split(/\s+/);e:for(var a=0;a<i.length;a++){for(var s=i[a],o=0;o<r.length;o++)if(s===r[o])continue e;n+=(n.length>0?" ":"")+s}return n}function yt(e){e=Hi(e);var t=e.length;if(t<=1)return e;for(;t--;){var n=e[t];(n.nodeType===vr||n.nodeType===mr&&""===n.nodeValue.trim())&&Ji.call(e,t,1)}return e}function bt(e,t){if(t&&S(t))return t;if(S(e)){var n=aa.exec(e);if(n)return n[3]}}function St(){var e={},n=!1;this.has=function(t){return e.hasOwnProperty(t)},this.register=function(t,n){he(t,"controller"),y(t)?l(e,t):e[t]=n},this.allowGlobals=function(){n=!0},this.$get=["$injector","$window",function(i,r){function a(e,n,i,r){if(!e||!y(e.$scope))throw t("$controller")("noscp","Cannot export controller '{0}' as '{1}'! No $scope object provided via `locals`.",r,n);e.$scope[n]=i}return function(t,s,o,u){var c,p,h,d;if(o=o===!0,u&&S(u)&&(d=u),S(t)){if(p=t.match(aa),!p)throw ra("ctrlfmt","Badly formed controller string '{0}'. Must match `__name__ as __id__` or `__name__`.",t);if(h=p[1],d=d||p[3],t=e.hasOwnProperty(h)?e[h]:de(s.$scope,h,!0)||(n?de(r,h,!0):void 0),!t)throw ra("ctrlreg","The controller with the name '{0}' is not registered.",h);pe(t,h,!0)}if(o){var f=(ir(t)?t[t.length-1]:t).prototype;return c=Object.create(f||null),d&&a(s,d,c,h||t.name),l(function(){var e=i.invoke(t,c,s,h);return e!==c&&(y(e)||x(e))&&(c=e,d&&a(s,d,c,h||t.name)),c},{instance:c,identifier:d})}return c=i.instantiate(t,s,h),d&&a(s,d,c,h||t.name),c}}]}function Mt(){this.$get=["$window",function(e){return Hi(e.document)}]}function Tt(){this.$get=["$log",function(e){return function(t,n){e.error.apply(e,arguments)}}]}function xt(e){return y(e)?T(e)?e.toISOString():H(e):e}function wt(){this.$get=function(){return function(e){if(!e)return"";var t=[];return r(e,function(e,n){null===e||v(e)||(ir(e)?i(e,function(e){t.push(te(n)+"="+te(xt(e)))}):t.push(te(n)+"="+te(xt(e))))}),t.join("&")}}}function Ct(){this.$get=function(){return function(e){function t(e,a,s){null===e||v(e)||(ir(e)?i(e,function(e,n){t(e,a+"["+(y(e)?n:"")+"]")}):y(e)&&!T(e)?r(e,function(e,n){t(e,a+(s?"":"[")+n+(s?"":"]"))}):n.push(te(a)+"="+te(xt(e))))}if(!e)return"";var n=[];return t(e,"",!0),n.join("&")}}}function Pt(e,t){if(S(e)){var n=e.replace(pa,"").trim();if(n){var i=t("Content-Type");(i&&0===i.indexOf(oa)||kt(n))&&(e=z(n))}}return e}function kt(e){var t=e.match(la);return t&&ca[t[0]].test(e)}function _t(e){function t(e,t){e&&(r[e]=r[e]?r[e]+", "+t:t)}var n,r=ge();return S(e)?i(e.split("\n"),function(e){n=e.indexOf(":"),t(ji(ar(e.substr(0,n))),ar(e.substr(n+1)))}):y(e)&&i(e,function(e,n){t(ji(n),ar(e))}),r}function At(e){var t;return function(n){if(t||(t=_t(e)),n){var i=t[ji(n)];return void 0===i&&(i=null),i}return t}}function It(e,t,n,r){return x(r)?r(e,t,n):(i(r,function(i){e=i(e,t,n)}),e)}function Et(e){return 200<=e&&e<300}function Bt(){var e=this.defaults={transformResponse:[Pt],transformRequest:[function(e){return!y(e)||k(e)||A(e)||_(e)?e:H(e)}],headers:{common:{Accept:"application/json, text/plain, */*"},post:ve(ua),put:ve(ua),patch:ve(ua)},xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",paramSerializer:"$httpParamSerializer"},n=!1;this.useApplyAsync=function(e){return $(e)?(n=!!e,this):n};var r=!0;this.useLegacyPromiseExtensions=function(e){return $(e)?(r=!!e,this):r};var a=this.interceptors=[];this.$get=["$httpBackend","$$cookieReader","$cacheFactory","$rootScope","$q","$injector",function(s,o,u,c,p,h){function d(n){function a(e,t){for(var n=0,i=t.length;n<i;){var r=t[n++],a=t[n++];e=e.then(r,a)}return t.length=0,e}function s(e,t){var n,r={};return i(e,function(e,i){x(e)?(n=e(t),null!=n&&(r[i]=n)):r[i]=e}),r}function o(t){var n,i,r,a=e.headers,o=l({},t.headers);a=l({},a.common,a[ji(t.method)]);e:for(n in a){i=ji(n);for(r in o)if(ji(r)===i)continue e;o[n]=a[n]}return s(o,ve(t))}function u(t){var n=t.headers,r=It(t.data,At(n),void 0,t.transformRequest);return v(r)&&i(n,function(e,t){"content-type"===ji(t)&&delete n[t]}),v(t.withCredentials)&&!v(e.withCredentials)&&(t.withCredentials=e.withCredentials),m(t,r).then(c,c)}function c(e){var t=l({},e);return t.data=It(e.data,e.headers,e.status,d.transformResponse),Et(e.status)?t:p.reject(t)}if(!y(n))throw t("$http")("badreq","Http request configuration must be an object.  Received: {0}",n);if(!S(n.url))throw t("$http")("badreq","Http request configuration url must be a string.  Received: {0}",n.url);var d=l({method:"get",transformRequest:e.transformRequest,transformResponse:e.transformResponse,paramSerializer:e.paramSerializer},n);d.headers=o(n),d.method=Ui(d.method),d.paramSerializer=S(d.paramSerializer)?h.get(d.paramSerializer):d.paramSerializer;var f=[],g=[],$=p.when(d);return i(T,function(e){(e.request||e.requestError)&&f.unshift(e.request,e.requestError),(e.response||e.responseError)&&g.push(e.response,e.responseError)}),$=a($,f),$=$.then(u),$=a($,g),r?($.success=function(e){return pe(e,"fn"),$.then(function(t){e(t.data,t.status,t.headers,d)}),$},$.error=function(e){return pe(e,"fn"),$.then(null,function(t){e(t.data,t.status,t.headers,d)}),$}):($.success=da("success"),$.error=da("error")),$}function f(e){i(arguments,function(e){d[e]=function(t,n){return d(l({},n||{},{method:e,url:t}))}})}function g(e){i(arguments,function(e){d[e]=function(t,n,i){return d(l({},i||{},{method:e,url:t,data:n}))}})}function m(t,r){function a(e){if(e){var t={};return i(e,function(e,i){t[i]=function(t){function i(){e(t)}n?c.$applyAsync(i):c.$$phase?i():c.$apply(i)}}),t}}function u(e,t,i,r){function a(){l(t,e,i,r)}g&&(Et(e)?g.put(w,[e,t,_t(i),r]):g.remove(w)),n?c.$applyAsync(a):(a(),c.$$phase||c.$apply())}function l(e,n,i,r){n=n>=-1?n:0,(Et(n)?S.resolve:S.reject)({data:e,status:n,headers:At(i),config:t,statusText:r})}function h(e){l(e.data,e.status,ve(e.headers()),e.statusText)}function f(){var e=d.pendingRequests.indexOf(t);e!==-1&&d.pendingRequests.splice(e,1)}var g,m,S=p.defer(),T=S.promise,x=t.headers,w=b(t.url,t.paramSerializer(t.params));if(d.pendingRequests.push(t),T.then(f,f),!t.cache&&!e.cache||t.cache===!1||"GET"!==t.method&&"JSONP"!==t.method||(g=y(t.cache)?t.cache:y(e.cache)?e.cache:M),g&&(m=g.get(w),$(m)?E(m)?m.then(h,h):ir(m)?l(m[1],m[0],ve(m[2]),m[3]):l(m,200,{},"OK"):g.put(w,T)),v(m)){var C=Nn(t.url)?o()[t.xsrfCookieName||e.xsrfCookieName]:void 0;C&&(x[t.xsrfHeaderName||e.xsrfHeaderName]=C),s(t.method,w,r,u,x,t.timeout,t.withCredentials,t.responseType,a(t.eventHandlers),a(t.uploadEventHandlers))}return T}function b(e,t){return t.length>0&&(e+=(e.indexOf("?")===-1?"?":"&")+t),e}var M=u("$http");e.paramSerializer=S(e.paramSerializer)?h.get(e.paramSerializer):e.paramSerializer;var T=[];return i(a,function(e){T.unshift(S(e)?h.get(e):h.invoke(e))}),d.pendingRequests=[],f("get","delete","head","jsonp"),g("post","put","patch"),d.defaults=e,d}]}function Lt(){this.$get=function(){return function(){return new e.XMLHttpRequest}}}function Ot(){this.$get=["$browser","$jsonpCallbacks","$document","$xhrFactory",function(e,t,n,i){return Nt(e,i,e.defer,t,n[0])}]}function Nt(e,t,n,r,a){function s(e,t,n){e=e.replace("JSON_CALLBACK",t);var i=a.createElement("script"),s=null;return i.type="text/javascript",i.src=e,i.async=!0,s=function(e){xr(i,"load",s),xr(i,"error",s),a.body.removeChild(i),i=null;var o=-1,u="unknown";e&&("load"!==e.type||r.wasCalled(t)||(e={type:"error"}),u=e.type,o="error"===e.type?404:200),n&&n(o,u)},Tr(i,"load",s),Tr(i,"error",s),a.body.appendChild(i),s}return function(a,o,u,l,c,p,h,f,g,m){function y(){M&&M(),T&&T.abort()}function b(t,i,r,a,s){$(C)&&n.cancel(C),M=T=null,t(i,r,a,s),e.$$completeOutstandingRequest(d)}if(e.$$incOutstandingRequestCount(),o=o||e.url(),"jsonp"===ji(a))var S=r.createCallback(o),M=s(o,S,function(e,t){var n=200===e&&r.getResponse(S);b(l,e,n,"",t),r.removeCallback(S)});else{var T=t(a,o);T.open(a,o,!0),i(c,function(e,t){$(e)&&T.setRequestHeader(t,e)}),T.onload=function(){var e=T.statusText||"",t="response"in T?T.response:T.responseText,n=1223===T.status?204:T.status;0===n&&(n=t?200:"file"===On(o).protocol?404:0),b(l,n,t,T.getAllResponseHeaders(),e)};var x=function(){b(l,-1,null,null,"")};if(T.onerror=x,T.onabort=x,T.ontimeout=x,i(g,function(e,t){T.addEventListener(t,e)}),i(m,function(e,t){T.upload.addEventListener(t,e)}),h&&(T.withCredentials=!0),f)try{T.responseType=f}catch(w){if("json"!==f)throw w}T.send(v(u)?null:u)}if(p>0)var C=n(y,p);else E(p)&&p.then(y)}}function Rt(){var e="{{",t="}}";this.startSymbol=function(t){return t?(e=t,this):e},this.endSymbol=function(e){return e?(t=e,this):t},this.$get=["$parse","$exceptionHandler","$sce",function(n,i,r){function a(e){return"\\\\\\"+e}function s(n){return n.replace(d,e).replace(f,t)}function o(e){if(null==e)return"";switch(typeof e){case"string":break;case"number":e=""+e;break;default:e=H(e)}return e}function u(e,t,n,i){var r=e.$watch(function(e){return r(),i(e)},t,n);return r}function c(a,c,d,f){function m(e){try{return e=E(e),f&&!$(e)?e:o(e)}catch(t){i(fa.interr(a,t))}}if(!a.length||a.indexOf(e)===-1){var y;if(!c){var b=s(a);y=g(b),y.exp=a,y.expressions=[],y.$$watchDelegate=u}return y}f=!!f;for(var S,M,T,w=0,C=[],P=[],k=a.length,_=[],A=[];w<k;){if((S=a.indexOf(e,w))===-1||(M=a.indexOf(t,S+p))===-1){w!==k&&_.push(s(a.substring(w)));break}w!==S&&_.push(s(a.substring(w,S))),T=a.substring(S+p,M),C.push(T),P.push(n(T,m)),w=M+h,A.push(_.length),_.push("")}if(d&&_.length>1&&fa.throwNoconcat(a),!c||C.length){var I=function(e){for(var t=0,n=C.length;t<n;t++){if(f&&v(e[t]))return;_[A[t]]=e[t]}return _.join("")},E=function(e){return d?r.getTrusted(d,e):r.valueOf(e)};return l(function(e){var t=0,n=C.length,r=new Array(n);try{for(;t<n;t++)r[t]=P[t](e);return I(r)}catch(s){i(fa.interr(a,s))}},{exp:a,expressions:C,$$watchDelegate:function(e,t){var n;return e.$watchGroup(P,function(i,r){var a=I(i);x(t)&&t.call(this,a,i!==r?n:a,e),n=a})}})}}var p=e.length,h=t.length,d=new RegExp(e.replace(/./g,a),"g"),f=new RegExp(t.replace(/./g,a),"g");return c.startSymbol=function(){return e},c.endSymbol=function(){return t},c}]}function Vt(){this.$get=["$rootScope","$window","$q","$$q","$browser",function(e,t,n,i,r){function a(a,o,u,l){function c(){p?a.apply(null,h):a(g)}var p=arguments.length>4,h=p?F(arguments,4):[],d=t.setInterval,f=t.clearInterval,g=0,m=$(l)&&!l,v=(m?i:n).defer(),y=v.promise;return u=$(u)?u:0,y.$$intervalId=d(function(){m?r.defer(c):e.$evalAsync(c),v.notify(g++),u>0&&g>=u&&(v.resolve(g),f(y.$$intervalId),delete s[y.$$intervalId]),m||e.$apply()},o),s[y.$$intervalId]=v,y}var s={};return a.cancel=function(e){return!!(e&&e.$$intervalId in s)&&(s[e.$$intervalId].reject("canceled"),t.clearInterval(e.$$intervalId),delete s[e.$$intervalId],!0)},a}]}function Dt(e){for(var t=e.split("/"),n=t.length;n--;)t[n]=ee(t[n]);return t.join("/")}function jt(e,t){var n=On(e);t.$$protocol=n.protocol,t.$$host=n.hostname,t.$$port=p(n.port)||va[n.protocol]||null}function Ut(e,t){if(ya.test(e))throw $a("badpath",'Invalid url "{0}".',e);var n="/"!==e.charAt(0);n&&(e="/"+e);var i=On(e);t.$$path=decodeURIComponent(n&&"/"===i.pathname.charAt(0)?i.pathname.substring(1):i.pathname),t.$$search=Q(i.search),t.$$hash=decodeURIComponent(i.hash),t.$$path&&"/"!==t.$$path.charAt(0)&&(t.$$path="/"+t.$$path)}function Ft(e,t){return e.slice(0,t.length)===t}function qt(e,t){if(Ft(t,e))return t.substr(e.length)}function Gt(e){var t=e.indexOf("#");return t===-1?e:e.substr(0,t)}function Ht(e){return e.replace(/(#.+)|#$/,"$1")}function zt(e){return e.substr(0,Gt(e).lastIndexOf("/")+1)}function Wt(e){return e.substring(0,e.indexOf("/",e.indexOf("//")+2))}function Yt(e,t,n){this.$$html5=!0,n=n||"",jt(e,this),this.$$parse=function(e){var n=qt(t,e);if(!S(n))throw $a("ipthprfx",'Invalid url "{0}", missing path prefix "{1}".',e,t);Ut(n,this),this.$$path||(this.$$path="/"),this.$$compose()},this.$$compose=function(){var e=X(this.$$search),n=this.$$hash?"#"+ee(this.$$hash):"";this.$$url=Dt(this.$$path)+(e?"?"+e:"")+n,this.$$absUrl=t+this.$$url.substr(1)},this.$$parseLinkUrl=function(i,r){if(r&&"#"===r[0])return this.hash(r.slice(1)),!0;var a,s,o;return $(a=qt(e,i))?(s=a,o=n&&$(a=qt(n,a))?t+(qt("/",a)||a):e+s):$(a=qt(t,i))?o=t+a:t===i+"/"&&(o=t),o&&this.$$parse(o),!!o}}function Jt(e,t,n){jt(e,this),this.$$parse=function(i){function r(e,t,n){var i,r=/^\/[A-Z]:(\/.*)/;return Ft(t,n)&&(t=t.replace(n,"")),r.exec(t)?e:(i=r.exec(e),i?i[1]:e)}var a,s=qt(e,i)||qt(t,i);v(s)||"#"!==s.charAt(0)?this.$$html5?a=s:(a="",v(s)&&(e=i,this.replace())):(a=qt(n,s),v(a)&&(a=s)),Ut(a,this),this.$$path=r(this.$$path,a,e),this.$$compose()},this.$$compose=function(){var t=X(this.$$search),i=this.$$hash?"#"+ee(this.$$hash):"";this.$$url=Dt(this.$$path)+(t?"?"+t:"")+i,this.$$absUrl=e+(this.$$url?n+this.$$url:"")},this.$$parseLinkUrl=function(t,n){return Gt(e)===Gt(t)&&(this.$$parse(t),!0)}}function Kt(e,t,n){this.$$html5=!0,Jt.apply(this,arguments),this.$$parseLinkUrl=function(i,r){if(r&&"#"===r[0])return this.hash(r.slice(1)),!0;var a,s;return e===Gt(i)?a=i:(s=qt(t,i))?a=e+n+s:t===i+"/"&&(a=t),a&&this.$$parse(a),!!a},this.$$compose=function(){var t=X(this.$$search),i=this.$$hash?"#"+ee(this.$$hash):"";this.$$url=Dt(this.$$path)+(t?"?"+t:"")+i,this.$$absUrl=e+n+this.$$url}}function Zt(e){return function(){return this[e]}}function Qt(e,t){return function(n){return v(n)?this[e]:(this[e]=t(n),this.$$compose(),this)}}function Xt(){var e="",t={enabled:!1,requireBase:!0,rewriteLinks:!0};this.hashPrefix=function(t){return $(t)?(e=t,this):e},this.html5Mode=function(e){return I(e)?(t.enabled=e,this):y(e)?(I(e.enabled)&&(t.enabled=e.enabled),I(e.requireBase)&&(t.requireBase=e.requireBase),(I(e.rewriteLinks)||S(e.rewriteLinks))&&(t.rewriteLinks=e.rewriteLinks),this):t},this.$get=["$rootScope","$browser","$sniffer","$rootElement","$window",function(n,i,r,a,s){function o(e,t,n){var r=l.url(),a=l.$$state;try{i.url(e,t,n),l.$$state=i.state()}catch(s){throw l.url(r),l.$$state=a,s}}function u(e,t){n.$broadcast("$locationChangeSuccess",l.absUrl(),e,l.$$state,t)}var l,c,p,h=i.baseHref(),d=i.url();if(t.enabled){if(!h&&t.requireBase)throw $a("nobase","$location in HTML5 mode requires a <base> tag to be present!");p=Wt(d)+(h||"/"),c=r.history?Yt:Kt}else p=Gt(d),c=Jt;var f=zt(p);l=new c(p,f,"#"+e),l.$$parseLinkUrl(d,d),l.$$state=i.state();var g=/^\s*(javascript|mailto):/i;a.on("click",function(e){var r=t.rewriteLinks;if(r&&!e.ctrlKey&&!e.metaKey&&!e.shiftKey&&2!==e.which&&2!==e.button){for(var o=Hi(e.target);"a"!==R(o[0]);)if(o[0]===a[0]||!(o=o.parent())[0])return;if(!S(r)||!v(o.attr(r))){var u=o.prop("href"),c=o.attr("href")||o.attr("xlink:href");y(u)&&"[object SVGAnimatedString]"===u.toString()&&(u=On(u.animVal).href),g.test(u)||!u||o.attr("target")||e.isDefaultPrevented()||l.$$parseLinkUrl(u,c)&&(e.preventDefault(),l.absUrl()!==i.url()&&(n.$apply(),s.angular["ff-684208-preventDefault"]=!0))}}}),Ht(l.absUrl())!==Ht(d)&&i.url(l.absUrl(),!0);var m=!0;return i.onUrlChange(function(e,t){return v(qt(f,e))?void(s.location.href=e):(n.$evalAsync(function(){var i,r=l.absUrl(),a=l.$$state;e=Ht(e),l.$$parse(e),l.$$state=t,i=n.$broadcast("$locationChangeStart",e,r,t,a).defaultPrevented,l.absUrl()===e&&(i?(l.$$parse(r),l.$$state=a,o(r,!1,a)):(m=!1,u(r,a)))}),void(n.$$phase||n.$digest()))}),n.$watch(function(){var e=Ht(i.url()),t=Ht(l.absUrl()),a=i.state(),s=l.$$replace,c=e!==t||l.$$html5&&r.history&&a!==l.$$state;(m||c)&&(m=!1,n.$evalAsync(function(){var t=l.absUrl(),i=n.$broadcast("$locationChangeStart",t,e,l.$$state,a).defaultPrevented;l.absUrl()===t&&(i?(l.$$parse(e),l.$$state=a):(c&&o(t,s,a===l.$$state?null:l.$$state),u(e,a)))})),l.$$replace=!1}),l}]}function en(){var e=!0,t=this;this.debugEnabled=function(t){return $(t)?(e=t,this):e},this.$get=["$window",function(n){function r(e){return e instanceof Error&&(e.stack?e=e.message&&e.stack.indexOf(e.message)===-1?"Error: "+e.message+"\n"+e.stack:e.stack:e.sourceURL&&(e=e.message+"\n"+e.sourceURL+":"+e.line)),e}function a(e){var t=n.console||{},a=t[e]||t.log||d,s=!1;try{s=!!a.apply}catch(o){}return s?function(){var e=[];return i(arguments,function(t){e.push(r(t))}),a.apply(t,e)}:function(e,t){a(e,null==t?"":t)}}return{log:a("log"),info:a("info"),warn:a("warn"),error:a("error"),debug:function(){var n=a("debug");return function(){e&&n.apply(t,arguments)}}()}}]}function tn(e,t){if("__defineGetter__"===e||"__defineSetter__"===e||"__lookupGetter__"===e||"__lookupSetter__"===e||"__proto__"===e)throw Sa("isecfld","Attempting to access a disallowed field in Angular expressions! Expression: {0}",t);return e}function nn(e){return e+""}function rn(e,t){if(e){if(e.constructor===e)throw Sa("isecfn","Referencing Function in Angular expressions is disallowed! Expression: {0}",t);if(e.window===e)throw Sa("isecwindow","Referencing the Window in Angular expressions is disallowed! Expression: {0}",t);if(e.children&&(e.nodeName||e.prop&&e.attr&&e.find))throw Sa("isecdom","Referencing DOM nodes in Angular expressions is disallowed! Expression: {0}",t);if(e===Object)throw Sa("isecobj","Referencing Object in Angular expressions is disallowed! Expression: {0}",t)}return e}function an(e,t){if(e){if(e.constructor===e)throw Sa("isecfn","Referencing Function in Angular expressions is disallowed! Expression: {0}",t);if(e===La||e===Oa||e===Na)throw Sa("isecff","Referencing call, apply or bind in Angular expressions is disallowed! Expression: {0}",t)}}function sn(e,t){if(e&&(e===Ma||e===Ta||e===xa||e===wa||e===Ca||e===Pa||e===ka||e===_a||e===Aa||e===Ia||e===Ea||e===Ba))throw Sa("isecaf","Assigning to a constructor or its prototype is disallowed! Expression: {0}",t);
}function on(e,t){return"undefined"!=typeof e?e:t}function un(e,t){return"undefined"==typeof e?t:"undefined"==typeof t?e:e+t}function ln(e,t){var n=e(t);return!n.$stateful}function cn(e,t){var n,r,a;switch(e.type){case Ua.Program:n=!0,i(e.body,function(e){cn(e.expression,t),n=n&&e.expression.constant}),e.constant=n;break;case Ua.Literal:e.constant=!0,e.toWatch=[];break;case Ua.UnaryExpression:cn(e.argument,t),e.constant=e.argument.constant,e.toWatch=e.argument.toWatch;break;case Ua.BinaryExpression:cn(e.left,t),cn(e.right,t),e.constant=e.left.constant&&e.right.constant,e.toWatch=e.left.toWatch.concat(e.right.toWatch);break;case Ua.LogicalExpression:cn(e.left,t),cn(e.right,t),e.constant=e.left.constant&&e.right.constant,e.toWatch=e.constant?[]:[e];break;case Ua.ConditionalExpression:cn(e.test,t),cn(e.alternate,t),cn(e.consequent,t),e.constant=e.test.constant&&e.alternate.constant&&e.consequent.constant,e.toWatch=e.constant?[]:[e];break;case Ua.Identifier:e.constant=!1,e.toWatch=[e];break;case Ua.MemberExpression:cn(e.object,t),e.computed&&cn(e.property,t),e.constant=e.object.constant&&(!e.computed||e.property.constant),e.toWatch=[e];break;case Ua.CallExpression:a=!!e.filter&&ln(t,e.callee.name),n=a,r=[],i(e.arguments,function(e){cn(e,t),n=n&&e.constant,e.constant||r.push.apply(r,e.toWatch)}),e.constant=n,e.toWatch=a?r:[e];break;case Ua.AssignmentExpression:cn(e.left,t),cn(e.right,t),e.constant=e.left.constant&&e.right.constant,e.toWatch=[e];break;case Ua.ArrayExpression:n=!0,r=[],i(e.elements,function(e){cn(e,t),n=n&&e.constant,e.constant||r.push.apply(r,e.toWatch)}),e.constant=n,e.toWatch=r;break;case Ua.ObjectExpression:n=!0,r=[],i(e.properties,function(e){cn(e.value,t),n=n&&e.value.constant&&!e.computed,e.value.constant||r.push.apply(r,e.value.toWatch)}),e.constant=n,e.toWatch=r;break;case Ua.ThisExpression:e.constant=!1,e.toWatch=[];break;case Ua.LocalsExpression:e.constant=!1,e.toWatch=[]}}function pn(e){if(1===e.length){var t=e[0].expression,n=t.toWatch;return 1!==n.length?n:n[0]!==t?n:void 0}}function hn(e){return e.type===Ua.Identifier||e.type===Ua.MemberExpression}function dn(e){if(1===e.body.length&&hn(e.body[0].expression))return{type:Ua.AssignmentExpression,left:e.body[0].expression,right:{type:Ua.NGValueParameter},operator:"="}}function fn(e){return 0===e.body.length||1===e.body.length&&(e.body[0].expression.type===Ua.Literal||e.body[0].expression.type===Ua.ArrayExpression||e.body[0].expression.type===Ua.ObjectExpression)}function gn(e){return e.constant}function mn(e,t){this.astBuilder=e,this.$filter=t}function vn(e,t){this.astBuilder=e,this.$filter=t}function $n(e){return"constructor"===e}function yn(e){return x(e.valueOf)?e.valueOf():Ra.call(e)}function bn(){var e,t,n=ge(),r=ge(),a={"true":!0,"false":!1,"null":null,undefined:void 0};this.addLiteral=function(e,t){a[e]=t},this.setIdentifierFns=function(n,i){return e=n,t=i,this},this.$get=["$filter",function(s){function o(e,t,i){var a,o,l;switch(i=i||b,typeof e){case"string":e=e.trim(),l=e;var m=i?r:n;if(a=m[l],!a){":"===e.charAt(0)&&":"===e.charAt(1)&&(o=!0,e=e.substring(2));var $=i?y:v,S=new ja($),M=new Fa(S,s,$);a=M.parse(e),a.constant?a.$$watchDelegate=f:o?a.$$watchDelegate=a.literal?h:p:a.inputs&&(a.$$watchDelegate=c),i&&(a=u(a)),m[l]=a}return g(a,t);case"function":return g(e,t);default:return g(d,t)}}function u(e){function t(t,n,i,r){var a=b;b=!0;try{return e(t,n,i,r)}finally{b=a}}if(!e)return e;t.$$watchDelegate=e.$$watchDelegate,t.assign=u(e.assign),t.constant=e.constant,t.literal=e.literal;for(var n=0;e.inputs&&n<e.inputs.length;++n)e.inputs[n]=u(e.inputs[n]);return t.inputs=e.inputs,t}function l(e,t){return null==e||null==t?e===t:("object"!=typeof e||(e=yn(e),"object"!=typeof e))&&(e===t||e!==e&&t!==t)}function c(e,t,n,i,r){var a,s=i.inputs;if(1===s.length){var o=l;return s=s[0],e.$watch(function(e){var t=s(e);return l(t,o)||(a=i(e,void 0,void 0,[t]),o=t&&yn(t)),a},t,n,r)}for(var u=[],c=[],p=0,h=s.length;p<h;p++)u[p]=l,c[p]=null;return e.$watch(function(e){for(var t=!1,n=0,r=s.length;n<r;n++){var o=s[n](e);(t||(t=!l(o,u[n])))&&(c[n]=o,u[n]=o&&yn(o))}return t&&(a=i(e,void 0,void 0,c)),a},t,n,r)}function p(e,t,n,i){var r,a;return r=e.$watch(function(e){return i(e)},function(e,n,i){a=e,x(t)&&t.apply(this,arguments),$(e)&&i.$$postDigest(function(){$(a)&&r()})},n)}function h(e,t,n,r){function a(e){var t=!0;return i(e,function(e){$(e)||(t=!1)}),t}var s,o;return s=e.$watch(function(e){return r(e)},function(e,n,i){o=e,x(t)&&t.call(this,e,n,i),a(e)&&i.$$postDigest(function(){a(o)&&s()})},n)}function f(e,t,n,i){var r=e.$watch(function(e){return r(),i(e)},t,n);return r}function g(e,t){if(!t)return e;var n=e.$$watchDelegate,i=!1,r=n!==h&&n!==p,a=r?function(n,r,a,s){var o=i&&s?s[0]:e(n,r,a,s);return t(o,n,r)}:function(n,i,r,a){var s=e(n,i,r,a),o=t(s,n,i);return $(s)?o:s};return e.$$watchDelegate&&e.$$watchDelegate!==c?a.$$watchDelegate=e.$$watchDelegate:t.$stateful||(a.$$watchDelegate=c,i=!e.inputs,a.inputs=e.inputs?e.inputs:[e]),a}var m=or().noUnsafeEval,v={csp:m,expensiveChecks:!1,literals:D(a),isIdentifierStart:x(e)&&e,isIdentifierContinue:x(t)&&t},y={csp:m,expensiveChecks:!0,literals:D(a),isIdentifierStart:x(e)&&e,isIdentifierContinue:x(t)&&t},b=!1;return o.$$runningExpensiveChecks=function(){return b},o}]}function Sn(){this.$get=["$rootScope","$exceptionHandler",function(e,t){return Tn(function(t){e.$evalAsync(t)},t)}]}function Mn(){this.$get=["$browser","$exceptionHandler",function(e,t){return Tn(function(t){e.defer(t)},t)}]}function Tn(e,n){function r(){var e=new c;return e.resolve=s(e,e.resolve),e.reject=s(e,e.reject),e.notify=s(e,e.notify),e}function a(){this.$$state={status:0}}function s(e,t){return function(n){t.call(e,n)}}function o(e){var t,i,r;r=e.pending,e.processScheduled=!1,e.pending=void 0;for(var a=0,s=r.length;a<s;++a){i=r[a][0],t=r[a][e.status];try{x(t)?i.resolve(t(e.value)):1===e.status?i.resolve(e.value):i.reject(e.value)}catch(o){i.reject(o),n(o)}}}function u(t){!t.processScheduled&&t.pending&&(t.processScheduled=!0,e(function(){o(t)}))}function c(){this.promise=new a}function p(e){var t=new c;return t.reject(e),t.promise}function h(e,t,n){var i=null;try{x(n)&&(i=n())}catch(r){return p(r)}return E(i)?i.then(function(){return t(e)},p):t(e)}function d(e,t,n,i){var r=new c;return r.resolve(e),r.promise.then(t,n,i)}function f(e){var t=new c,n=0,r=ir(e)?[]:{};return i(e,function(e,i){n++,d(e).then(function(e){r[i]=e,--n||t.resolve(r)},function(e){t.reject(e)})}),0===n&&t.resolve(r),t.promise}function g(e){var t=r();return i(e,function(e){d(e).then(t.resolve,t.reject)}),t.promise}function m(e){function t(e){i.resolve(e)}function n(e){i.reject(e)}if(!x(e))throw $("norslvr","Expected resolverFn, got '{0}'",e);var i=new c;return e(t,n),i.promise}var $=t("$q",TypeError);l(a.prototype,{then:function(e,t,n){if(v(e)&&v(t)&&v(n))return this;var i=new c;return this.$$state.pending=this.$$state.pending||[],this.$$state.pending.push([i,e,t,n]),this.$$state.status>0&&u(this.$$state),i.promise},"catch":function(e){return this.then(null,e)},"finally":function(e,t){return this.then(function(t){return h(t,b,e)},function(t){return h(t,p,e)},t)}}),l(c.prototype,{resolve:function(e){this.promise.$$state.status||(e===this.promise?this.$$reject($("qcycle","Expected promise to be resolved with value other than itself '{0}'",e)):this.$$resolve(e))},$$resolve:function(e){function t(e){o||(o=!0,a.$$resolve(e))}function i(e){o||(o=!0,a.$$reject(e))}var r,a=this,o=!1;try{(y(e)||x(e))&&(r=e&&e.then),x(r)?(this.promise.$$state.status=-1,r.call(e,t,i,s(this,this.notify))):(this.promise.$$state.value=e,this.promise.$$state.status=1,u(this.promise.$$state))}catch(l){i(l),n(l)}},reject:function(e){this.promise.$$state.status||this.$$reject(e)},$$reject:function(e){this.promise.$$state.value=e,this.promise.$$state.status=2,u(this.promise.$$state)},notify:function(t){var i=this.promise.$$state.pending;this.promise.$$state.status<=0&&i&&i.length&&e(function(){for(var e,r,a=0,s=i.length;a<s;a++){r=i[a][0],e=i[a][3];try{r.notify(x(e)?e(t):t)}catch(o){n(o)}}})}});var b=d;return m.prototype=a.prototype,m.defer=r,m.reject=p,m.when=d,m.resolve=b,m.all=f,m.race=g,m}function xn(){this.$get=["$window","$timeout",function(e,t){var n=e.requestAnimationFrame||e.webkitRequestAnimationFrame,i=e.cancelAnimationFrame||e.webkitCancelAnimationFrame||e.webkitCancelRequestAnimationFrame,r=!!n,a=r?function(e){var t=n(e);return function(){i(t)}}:function(e){var n=t(e,16.66,!1);return function(){t.cancel(n)}};return a.supported=r,a}]}function wn(){function e(e){function t(){this.$$watchers=this.$$nextSibling=this.$$childHead=this.$$childTail=null,this.$$listeners={},this.$$listenerCount={},this.$$watchersCount=0,this.$id=s(),this.$$ChildScope=null}return t.prototype=e,t}var r=10,a=t("$rootScope"),o=null,u=null;this.digestTtl=function(e){return arguments.length&&(r=e),r},this.$get=["$exceptionHandler","$parse","$browser",function(t,l,c){function p(e){e.currentScope.$$destroyed=!0}function h(e){9===Gi&&(e.$$childHead&&h(e.$$childHead),e.$$nextSibling&&h(e.$$nextSibling)),e.$parent=e.$$nextSibling=e.$$prevSibling=e.$$childHead=e.$$childTail=e.$root=e.$$watchers=null}function f(){this.$id=s(),this.$$phase=this.$parent=this.$$watchers=this.$$nextSibling=this.$$prevSibling=this.$$childHead=this.$$childTail=null,this.$root=this,this.$$destroyed=!1,this.$$listeners={},this.$$listenerCount={},this.$$watchersCount=0,this.$$isolateBindings=null}function g(e){if(w.$$phase)throw a("inprog","{0} already in progress",w.$$phase);w.$$phase=e}function m(){w.$$phase=null}function $(e,t){do e.$$watchersCount+=t;while(e=e.$parent)}function b(e,t,n){do e.$$listenerCount[n]-=t,0===e.$$listenerCount[n]&&delete e.$$listenerCount[n];while(e=e.$parent)}function S(){}function M(){for(;k.length;)try{k.shift()()}catch(e){t(e)}u=null}function T(){null===u&&(u=c.defer(function(){w.$apply(M)}))}f.prototype={constructor:f,$new:function(t,n){var i;return n=n||this,t?(i=new f,i.$root=this.$root):(this.$$ChildScope||(this.$$ChildScope=e(this)),i=new this.$$ChildScope),i.$parent=n,i.$$prevSibling=n.$$childTail,n.$$childHead?(n.$$childTail.$$nextSibling=i,n.$$childTail=i):n.$$childHead=n.$$childTail=i,(t||n!==this)&&i.$on("$destroy",p),i},$watch:function(e,t,n,i){var r=l(e);if(r.$$watchDelegate)return r.$$watchDelegate(this,t,n,r,e);var a=this,s=a.$$watchers,u={fn:t,last:S,get:r,exp:i||e,eq:!!n};return o=null,x(t)||(u.fn=d),s||(s=a.$$watchers=[],s.$$digestWatchIndex=-1),s.unshift(u),s.$$digestWatchIndex++,$(this,1),function(){var e=V(s,u);e>=0&&($(a,-1),e<s.$$digestWatchIndex&&s.$$digestWatchIndex--),o=null}},$watchGroup:function(e,t){function n(){u=!1,l?(l=!1,t(a,a,o)):t(a,r,o)}var r=new Array(e.length),a=new Array(e.length),s=[],o=this,u=!1,l=!0;if(!e.length){var c=!0;return o.$evalAsync(function(){c&&t(a,a,o)}),function(){c=!1}}return 1===e.length?this.$watch(e[0],function(e,n,i){a[0]=e,r[0]=n,t(a,e===n?a:r,i)}):(i(e,function(e,t){var i=o.$watch(e,function(e,i){a[t]=e,r[t]=i,u||(u=!0,o.$evalAsync(n))});s.push(i)}),function(){for(;s.length;)s.shift()()})},$watchCollection:function(e,t){function i(e){a=e;var t,i,r,o,u;if(!v(a)){if(y(a))if(n(a)){s!==d&&(s=d,m=s.length=0,p++),t=a.length,m!==t&&(p++,s.length=m=t);for(var l=0;l<t;l++)u=s[l],o=a[l],r=u!==u&&o!==o,r||u===o||(p++,s[l]=o)}else{s!==f&&(s=f={},m=0,p++),t=0;for(i in a)Di.call(a,i)&&(t++,o=a[i],u=s[i],i in s?(r=u!==u&&o!==o,r||u===o||(p++,s[i]=o)):(m++,s[i]=o,p++));if(m>t){p++;for(i in s)Di.call(a,i)||(m--,delete s[i])}}else s!==a&&(s=a,p++);return p}}function r(){if(g?(g=!1,t(a,a,u)):t(a,o,u),c)if(y(a))if(n(a)){o=new Array(a.length);for(var e=0;e<a.length;e++)o[e]=a[e]}else{o={};for(var i in a)Di.call(a,i)&&(o[i]=a[i])}else o=a}i.$stateful=!0;var a,s,o,u=this,c=t.length>1,p=0,h=l(e,i),d=[],f={},g=!0,m=0;return this.$watch(h,r)},$digest:function(){var e,n,i,s,l,p,h,d,f,v,$,y=r,b=this,T=[];g("$digest"),c.$$checkUrlChange(),this===w&&null!==u&&(c.defer.cancel(u),M()),o=null;do{h=!1,f=b;for(var k=0;k<C.length;k++){try{$=C[k],$.scope.$eval($.expression,$.locals)}catch(A){t(A)}o=null}C.length=0;e:do{if(p=f.$$watchers)for(p.$$digestWatchIndex=p.length;p.$$digestWatchIndex--;)try{if(e=p[p.$$digestWatchIndex])if(l=e.get,(n=l(f))===(i=e.last)||(e.eq?j(n,i):nr(n)&&nr(i))){if(e===o){h=!1;break e}}else h=!0,o=e,e.last=e.eq?D(n,null):n,s=e.fn,s(n,i===S?n:i,f),y<5&&(v=4-y,T[v]||(T[v]=[]),T[v].push({msg:x(e.exp)?"fn: "+(e.exp.name||e.exp.toString()):e.exp,newVal:n,oldVal:i}))}catch(A){t(A)}if(!(d=f.$$watchersCount&&f.$$childHead||f!==b&&f.$$nextSibling))for(;f!==b&&!(d=f.$$nextSibling);)f=f.$parent}while(f=d);if((h||C.length)&&!y--)throw m(),a("infdig","{0} $digest() iterations reached. Aborting!\nWatchers fired in the last 5 iterations: {1}",r,T)}while(h||C.length);for(m();_<P.length;)try{P[_++]()}catch(A){t(A)}P.length=_=0},$destroy:function(){if(!this.$$destroyed){var e=this.$parent;this.$broadcast("$destroy"),this.$$destroyed=!0,this===w&&c.$$applicationDestroyed(),$(this,-this.$$watchersCount);for(var t in this.$$listenerCount)b(this,this.$$listenerCount[t],t);e&&e.$$childHead===this&&(e.$$childHead=this.$$nextSibling),e&&e.$$childTail===this&&(e.$$childTail=this.$$prevSibling),this.$$prevSibling&&(this.$$prevSibling.$$nextSibling=this.$$nextSibling),this.$$nextSibling&&(this.$$nextSibling.$$prevSibling=this.$$prevSibling),this.$destroy=this.$digest=this.$apply=this.$evalAsync=this.$applyAsync=d,this.$on=this.$watch=this.$watchGroup=function(){return d},this.$$listeners={},this.$$nextSibling=null,h(this)}},$eval:function(e,t){return l(e)(this,t)},$evalAsync:function(e,t){w.$$phase||C.length||c.defer(function(){C.length&&w.$digest()}),C.push({scope:this,expression:l(e),locals:t})},$$postDigest:function(e){P.push(e)},$apply:function(e){try{g("$apply");try{return this.$eval(e)}finally{m()}}catch(n){t(n)}finally{try{w.$digest()}catch(n){throw t(n),n}}},$applyAsync:function(e){function t(){n.$eval(e)}var n=this;e&&k.push(t),e=l(e),T()},$on:function(e,t){var n=this.$$listeners[e];n||(this.$$listeners[e]=n=[]),n.push(t);var i=this;do i.$$listenerCount[e]||(i.$$listenerCount[e]=0),i.$$listenerCount[e]++;while(i=i.$parent);var r=this;return function(){var i=n.indexOf(t);i!==-1&&(n[i]=null,b(r,1,e))}},$emit:function(e,n){var i,r,a,s=[],o=this,u=!1,l={name:e,targetScope:o,stopPropagation:function(){u=!0},preventDefault:function(){l.defaultPrevented=!0},defaultPrevented:!1},c=U([l],arguments,1);do{for(i=o.$$listeners[e]||s,l.currentScope=o,r=0,a=i.length;r<a;r++)if(i[r])try{i[r].apply(null,c)}catch(p){t(p)}else i.splice(r,1),r--,a--;if(u)return l.currentScope=null,l;o=o.$parent}while(o);return l.currentScope=null,l},$broadcast:function(e,n){var i=this,r=i,a=i,s={name:e,targetScope:i,preventDefault:function(){s.defaultPrevented=!0},defaultPrevented:!1};if(!i.$$listenerCount[e])return s;for(var o,u,l,c=U([s],arguments,1);r=a;){for(s.currentScope=r,o=r.$$listeners[e]||[],u=0,l=o.length;u<l;u++)if(o[u])try{o[u].apply(null,c)}catch(p){t(p)}else o.splice(u,1),u--,l--;if(!(a=r.$$listenerCount[e]&&r.$$childHead||r!==i&&r.$$nextSibling))for(;r!==i&&!(a=r.$$nextSibling);)r=r.$parent}return s.currentScope=null,s}};var w=new f,C=w.$$asyncQueue=[],P=w.$$postDigestQueue=[],k=w.$$applyAsyncQueue=[],_=0;return w}]}function Cn(){var e=/^\s*(https?|ftp|mailto|tel|file):/,t=/^\s*((https?|ftp|file|blob):|data:image\/)/;this.aHrefSanitizationWhitelist=function(t){return $(t)?(e=t,this):e},this.imgSrcSanitizationWhitelist=function(e){return $(e)?(t=e,this):t},this.$get=function(){return function(n,i){var r,a=i?t:e;return r=On(n).href,""===r||r.match(a)?n:"unsafe:"+r}}}function Pn(e){if("self"===e)return e;if(S(e)){if(e.indexOf("***")>-1)throw qa("iwcard","Illegal sequence *** in string matcher.  String: {0}",e);return e=sr(e).replace(/\\\*\\\*/g,".*").replace(/\\\*/g,"[^:/.?&;]*"),new RegExp("^"+e+"$")}if(w(e))return new RegExp("^"+e.source+"$");throw qa("imatcher",'Matchers may only be "self", string patterns or RegExp objects')}function kn(e){var t=[];return $(e)&&i(e,function(e){t.push(Pn(e))}),t}function _n(){this.SCE_CONTEXTS=Ga;var e=["self"],t=[];this.resourceUrlWhitelist=function(t){return arguments.length&&(e=kn(t)),e},this.resourceUrlBlacklist=function(e){return arguments.length&&(t=kn(e)),t},this.$get=["$injector",function(n){function i(e,t){return"self"===e?Nn(t):!!e.exec(t.href)}function r(n){var r,a,s=On(n.toString()),o=!1;for(r=0,a=e.length;r<a;r++)if(i(e[r],s)){o=!0;break}if(o)for(r=0,a=t.length;r<a;r++)if(i(t[r],s)){o=!1;break}return o}function a(e){var t=function(e){this.$$unwrapTrustedValue=function(){return e}};return e&&(t.prototype=new e),t.prototype.valueOf=function(){return this.$$unwrapTrustedValue()},t.prototype.toString=function(){return this.$$unwrapTrustedValue().toString()},t}function s(e,t){var n=p.hasOwnProperty(e)?p[e]:null;if(!n)throw qa("icontext","Attempted to trust a value in invalid context. Context: {0}; Value: {1}",e,t);if(null===t||v(t)||""===t)return t;if("string"!=typeof t)throw qa("itype","Attempted to trust a non-string value in a content requiring a string: Context: {0}",e);return new n(t)}function o(e){return e instanceof c?e.$$unwrapTrustedValue():e}function u(e,t){if(null===t||v(t)||""===t)return t;var n=p.hasOwnProperty(e)?p[e]:null;if(n&&t instanceof n)return t.$$unwrapTrustedValue();if(e===Ga.RESOURCE_URL){if(r(t))return t;throw qa("insecurl","Blocked loading resource from url not allowed by $sceDelegate policy.  URL: {0}",t.toString())}if(e===Ga.HTML)return l(t);throw qa("unsafe","Attempting to use an unsafe value in a safe context.")}var l=function(e){throw qa("unsafe","Attempting to use an unsafe value in a safe context.")};n.has("$sanitize")&&(l=n.get("$sanitize"));var c=a(),p={};return p[Ga.HTML]=a(c),p[Ga.CSS]=a(c),p[Ga.URL]=a(c),p[Ga.JS]=a(c),p[Ga.RESOURCE_URL]=a(p[Ga.URL]),{trustAs:s,getTrusted:u,valueOf:o}}]}function An(){var e=!0;this.enabled=function(t){return arguments.length&&(e=!!t),e},this.$get=["$parse","$sceDelegate",function(t,n){if(e&&Gi<8)throw qa("iequirks","Strict Contextual Escaping does not support Internet Explorer version < 11 in quirks mode.  You can fix this by adding the text <!doctype html> to the top of your HTML document.  See http://docs.angularjs.org/api/ng.$sce for more information.");var r=ve(Ga);r.isEnabled=function(){return e},r.trustAs=n.trustAs,r.getTrusted=n.getTrusted,r.valueOf=n.valueOf,e||(r.trustAs=r.getTrusted=function(e,t){return t},r.valueOf=f),r.parseAs=function(e,n){var i=t(n);return i.literal&&i.constant?i:t(n,function(t){return r.getTrusted(e,t)})};var a=r.parseAs,s=r.getTrusted,o=r.trustAs;return i(Ga,function(e,t){var n=ji(t);r[Me("parse_as_"+n)]=function(t){return a(e,t)},r[Me("get_trusted_"+n)]=function(t){return s(e,t)},r[Me("trust_as_"+n)]=function(t){return o(e,t)}}),r}]}function In(){this.$get=["$window","$document",function(e,t){var n,i,r={},a=e.chrome&&(e.chrome.app&&e.chrome.app.runtime||!e.chrome.app&&e.chrome.runtime&&e.chrome.runtime.id),s=!a&&e.history&&e.history.pushState,o=p((/android (\d+)/.exec(ji((e.navigator||{}).userAgent))||[])[1]),u=/Boxee/i.test((e.navigator||{}).userAgent),l=t[0]||{},c=/^(Moz|webkit|ms)(?=[A-Z])/,h=l.body&&l.body.style,d=!1,f=!1;if(h){for(var g in h)if(i=c.exec(g)){n=i[0],n=n[0].toUpperCase()+n.substr(1);break}n||(n="WebkitOpacity"in h&&"webkit"),d=!!("transition"in h||n+"Transition"in h),f=!!("animation"in h||n+"Animation"in h),!o||d&&f||(d=S(h.webkitTransition),f=S(h.webkitAnimation))}return{history:!(!s||o<4||u),hasEvent:function(e){if("input"===e&&Gi<=11)return!1;if(v(r[e])){var t=l.createElement("div");r[e]="on"+e in t}return r[e]},csp:or(),vendorPrefix:n,transitions:d,animations:f,android:o}}]}function En(){var e;this.httpOptions=function(t){return t?(e=t,this):e},this.$get=["$templateCache","$http","$q","$sce",function(t,n,i,r){function a(s,o){function u(e){if(!o)throw Ha("tpload","Failed to load template: {0} (HTTP status: {1} {2})",s,e.status,e.statusText);return i.reject(e)}a.totalPendingRequests++,S(s)&&!v(t.get(s))||(s=r.getTrustedResourceUrl(s));var c=n.defaults&&n.defaults.transformResponse;return ir(c)?c=c.filter(function(e){return e!==Pt}):c===Pt&&(c=null),n.get(s,l({cache:t,transformResponse:c},e))["finally"](function(){a.totalPendingRequests--}).then(function(e){return t.put(s,e.data),e.data},u)}return a.totalPendingRequests=0,a}]}function Bn(){this.$get=["$rootScope","$browser","$location",function(e,t,n){var r={};return r.findBindings=function(e,t,n){var r=e.getElementsByClassName("ng-binding"),a=[];return i(r,function(e){var r=er.element(e).data("$binding");r&&i(r,function(i){if(n){var r=new RegExp("(^|\\s)"+sr(t)+"(\\s|\\||$)");r.test(i)&&a.push(e)}else i.indexOf(t)!==-1&&a.push(e)})}),a},r.findModels=function(e,t,n){for(var i=["ng-","data-ng-","ng\\:"],r=0;r<i.length;++r){var a=n?"=":"*=",s="["+i[r]+"model"+a+'"'+t+'"]',o=e.querySelectorAll(s);if(o.length)return o}},r.getLocation=function(){return n.url()},r.setLocation=function(t){t!==n.url()&&(n.url(t),e.$digest())},r.whenStable=function(e){t.notifyWhenNoOutstandingRequests(e)},r}]}function Ln(){this.$get=["$rootScope","$browser","$q","$$q","$exceptionHandler",function(e,t,n,i,r){function a(a,o,u){x(a)||(u=o,o=a,a=d);var l,c=F(arguments,3),p=$(u)&&!u,h=(p?i:n).defer(),f=h.promise;return l=t.defer(function(){try{h.resolve(a.apply(null,c))}catch(t){h.reject(t),r(t)}finally{delete s[f.$$timeoutId]}p||e.$apply()},o),f.$$timeoutId=l,s[l]=h,f}var s={};return a.cancel=function(e){return!!(e&&e.$$timeoutId in s)&&(s[e.$$timeoutId].reject("canceled"),delete s[e.$$timeoutId],t.defer.cancel(e.$$timeoutId))},a}]}function On(e){var t=e;return Gi&&(za.setAttribute("href",t),t=za.href),za.setAttribute("href",t),{href:za.href,protocol:za.protocol?za.protocol.replace(/:$/,""):"",host:za.host,search:za.search?za.search.replace(/^\?/,""):"",hash:za.hash?za.hash.replace(/^#/,""):"",hostname:za.hostname,port:za.port,pathname:"/"===za.pathname.charAt(0)?za.pathname:"/"+za.pathname}}function Nn(e){var t=S(e)?On(e):e;return t.protocol===Wa.protocol&&t.host===Wa.host}function Rn(){this.$get=g(e)}function Vn(e){function t(e){try{return e.cookie||""}catch(t){return""}}function n(e){try{return decodeURIComponent(e)}catch(t){return e}}var i=e[0]||{},r={},a="";return function(){var e,s,o,u,l,c=t(i);if(c!==a)for(a=c,e=a.split("; "),r={},o=0;o<e.length;o++)s=e[o],u=s.indexOf("="),u>0&&(l=n(s.substring(0,u)),v(r[l])&&(r[l]=n(s.substring(u+1))));return r}}function Dn(){this.$get=Vn}function jn(e){function t(r,a){if(y(r)){var s={};return i(r,function(e,n){s[n]=t(n,e)}),s}return e.factory(r+n,a)}var n="Filter";this.register=t,this.$get=["$injector",function(e){return function(t){return e.get(t+n)}}],t("currency",Hn),t("date",si),t("filter",Un),t("json",oi),t("limitTo",ui),t("lowercase",es),t("number",zn),t("orderBy",ci),t("uppercase",ts)}function Un(){return function(e,i,r,a){if(!n(e)){if(null==e)return e;throw t("filter")("notarray","Expected array but received: {0}",e)}a=a||"$";var s,o,u=Gn(i);switch(u){case"function":s=i;break;case"boolean":case"null":case"number":case"string":o=!0;case"object":s=Fn(i,r,a,o);break;default:return e}return Array.prototype.filter.call(e,s)}}function Fn(e,t,n,i){var r,a=y(e)&&n in e;return t===!0?t=j:x(t)||(t=function(e,t){return!v(e)&&(null===e||null===t?e===t:!(y(t)||y(e)&&!m(e))&&(e=ji(""+e),t=ji(""+t),e.indexOf(t)!==-1))}),r=function(r){return a&&!y(r)?qn(r,e[n],t,n,!1):qn(r,e,t,n,i)}}function qn(e,t,n,i,r,a){var s=Gn(e),o=Gn(t);if("string"===o&&"!"===t.charAt(0))return!qn(e,t.substring(1),n,i,r);if(ir(e))return e.some(function(e){return qn(e,t,n,i,r)});switch(s){case"object":var u;if(r){for(u in e)if("$"!==u.charAt(0)&&qn(e[u],t,n,i,!0))return!0;return!a&&qn(e,t,n,i,!1)}if("object"===o){for(u in t){var l=t[u];if(!x(l)&&!v(l)){var c=u===i,p=c?e:e[u];if(!qn(p,l,n,i,c,c))return!1}}return!0}return n(e,t);case"function":return!1;default:return n(e,t)}}function Gn(e){return null===e?"null":typeof e}function Hn(e){var t=e.NUMBER_FORMATS;return function(e,n,i){return v(n)&&(n=t.CURRENCY_SYM),v(i)&&(i=t.PATTERNS[1].maxFrac),null==e?e:Jn(e,t.PATTERNS[1],t.GROUP_SEP,t.DECIMAL_SEP,i).replace(/\u00A4/g,n)}}function zn(e){var t=e.NUMBER_FORMATS;return function(e,n){return null==e?e:Jn(e,t.PATTERNS[0],t.GROUP_SEP,t.DECIMAL_SEP,n)}}function Wn(e){var t,n,i,r,a,s=0;for((n=e.indexOf(Ja))>-1&&(e=e.replace(Ja,"")),(i=e.search(/e/i))>0?(n<0&&(n=i),n+=+e.slice(i+1),e=e.substring(0,i)):n<0&&(n=e.length),i=0;e.charAt(i)===Ka;i++);if(i===(a=e.length))t=[0],n=1;else{for(a--;e.charAt(a)===Ka;)a--;for(n-=i,t=[],r=0;i<=a;i++,r++)t[r]=+e.charAt(i)}return n>Ya&&(t=t.splice(0,Ya-1),s=n-1,n=1),{d:t,e:s,i:n}}function Yn(e,t,n,i){var r=e.d,a=r.length-e.i;t=v(t)?Math.min(Math.max(n,a),i):+t;var s=t+e.i,o=r[s];if(s>0){r.splice(Math.max(e.i,s));for(var u=s;u<r.length;u++)r[u]=0}else{a=Math.max(0,a),e.i=1,r.length=Math.max(1,s=t+1),r[0]=0;for(var l=1;l<s;l++)r[l]=0}if(o>=5)if(s-1<0){for(var c=0;c>s;c--)r.unshift(0),e.i++;r.unshift(1),e.i++}else r[s-1]++;for(;a<Math.max(0,t);a++)r.push(0);var p=r.reduceRight(function(e,t,n,i){return t+=e,i[n]=t%10,Math.floor(t/10)},0);p&&(r.unshift(p),e.i++)}function Jn(e,t,n,i,r){if(!S(e)&&!M(e)||isNaN(e))return"";var a,s=!isFinite(e),o=!1,u=Math.abs(e)+"",l="";if(s)l="∞";else{a=Wn(u),Yn(a,r,t.minFrac,t.maxFrac);var c=a.d,p=a.i,h=a.e,d=[];for(o=c.reduce(function(e,t){return e&&!t},!0);p<0;)c.unshift(0),p++;p>0?d=c.splice(p,c.length):(d=c,c=[0]);var f=[];for(c.length>=t.lgSize&&f.unshift(c.splice(-t.lgSize,c.length).join(""));c.length>t.gSize;)f.unshift(c.splice(-t.gSize,c.length).join(""));c.length&&f.unshift(c.join("")),l=f.join(n),d.length&&(l+=i+d.join("")),h&&(l+="e+"+h)}return e<0&&!o?t.negPre+l+t.negSuf:t.posPre+l+t.posSuf}function Kn(e,t,n,i){var r="";for((e<0||i&&e<=0)&&(i?e=-e+1:(e=-e,r="-")),e=""+e;e.length<t;)e=Ka+e;return n&&(e=e.substr(e.length-t)),r+e}function Zn(e,t,n,i,r){return n=n||0,function(a){var s=a["get"+e]();return(n>0||s>-n)&&(s+=n),0===s&&n===-12&&(s=12),Kn(s,t,i,r)}}function Qn(e,t,n){return function(i,r){var a=i["get"+e](),s=(n?"STANDALONE":"")+(t?"SHORT":""),o=Ui(s+e);return r[o][a]}}function Xn(e,t,n){var i=-1*n,r=i>=0?"+":"";return r+=Kn(Math[i>0?"floor":"ceil"](i/60),2)+Kn(Math.abs(i%60),2)}function ei(e){var t=new Date(e,0,1).getDay();return new Date(e,0,(t<=4?5:12)-t)}function ti(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate()+(4-e.getDay()))}function ni(e){return function(t){var n=ei(t.getFullYear()),i=ti(t),r=+i-+n,a=1+Math.round(r/6048e5);return Kn(a,e)}}function ii(e,t){return e.getHours()<12?t.AMPMS[0]:t.AMPMS[1]}function ri(e,t){return e.getFullYear()<=0?t.ERAS[0]:t.ERAS[1]}function ai(e,t){return e.getFullYear()<=0?t.ERANAMES[0]:t.ERANAMES[1]}function si(e){function t(e){var t;if(t=e.match(n)){var i=new Date(0),r=0,a=0,s=t[8]?i.setUTCFullYear:i.setFullYear,o=t[8]?i.setUTCHours:i.setHours;t[9]&&(r=p(t[9]+t[10]),a=p(t[9]+t[11])),s.call(i,p(t[1]),p(t[2])-1,p(t[3]));var u=p(t[4]||0)-r,l=p(t[5]||0)-a,c=p(t[6]||0),h=Math.round(1e3*parseFloat("0."+(t[7]||0)));return o.call(i,u,l,c,h),i}return e}var n=/^(\d{4})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/;return function(n,r,a){var s,o,u="",l=[];if(r=r||"mediumDate",r=e.DATETIME_FORMATS[r]||r,S(n)&&(n=Xa.test(n)?p(n):t(n)),M(n)&&(n=new Date(n)),!T(n)||!isFinite(n.getTime()))return n;for(;r;)o=Qa.exec(r),o?(l=U(l,o,1),r=l.pop()):(l.push(r),r=null);var c=n.getTimezoneOffset();return a&&(c=W(a,c),n=J(n,a,!0)),i(l,function(t){s=Za[t],u+=s?s(n,e.DATETIME_FORMATS,c):"''"===t?"'":t.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}}function oi(){return function(e,t){return v(t)&&(t=2),H(e,t)}}function ui(){return function(e,t,i){return t=Math.abs(Number(t))===1/0?Number(t):p(t),nr(t)?e:(M(e)&&(e=e.toString()),n(e)?(i=!i||isNaN(i)?0:p(i),i=i<0?Math.max(0,e.length+i):i,t>=0?li(e,i,i+t):0===i?li(e,t,e.length):li(e,Math.max(0,i+t),i)):e)}}function li(e,t,n){return S(e)?e.slice(t,n):Yi.call(e,t,n)}function ci(e){function i(t){return t.map(function(t){var n=1,i=f;if(x(t))i=t;else if(S(t)&&("+"!==t.charAt(0)&&"-"!==t.charAt(0)||(n="-"===t.charAt(0)?-1:1,t=t.substring(1)),""!==t&&(i=e(t),i.constant))){var r=i();i=function(e){return e[r]}}return{get:i,descending:n}})}function r(e){switch(typeof e){case"number":case"boolean":case"string":return!0;default:return!1}}function a(e){return x(e.valueOf)&&(e=e.valueOf(),r(e))?e:m(e)&&(e=e.toString(),r(e))?e:e}function s(e,t){var n=typeof e;return null===e?(n="string",e="null"):"object"===n&&(e=a(e)),{value:e,type:n,index:t}}function o(e,t){var n=0,i=e.type,r=t.type;if(i===r){var a=e.value,s=t.value;"string"===i?(a=a.toLowerCase(),s=s.toLowerCase()):"object"===i&&(y(a)&&(a=e.index),y(s)&&(s=t.index)),a!==s&&(n=a<s?-1:1)}else n=i<r?-1:1;return n}return function(e,r,a,u){function l(e,t){return{value:e,tieBreaker:{value:t,type:"number",index:t},predicateValues:p.map(function(n){return s(n.get(e),t)})}}function c(e,t){for(var n=0,i=p.length;n<i;n++){var r=d(e.predicateValues[n],t.predicateValues[n]);if(r)return r*p[n].descending*h}return d(e.tieBreaker,t.tieBreaker)*h}if(null==e)return e;if(!n(e))throw t("orderBy")("notarray","Expected array but received: {0}",e);ir(r)||(r=[r]),0===r.length&&(r=["+"]);var p=i(r),h=a?-1:1,d=x(u)?u:o,f=Array.prototype.map.call(e,l);return f.sort(c),e=f.map(function(e){return e.value})}}function pi(e){return x(e)&&(e={link:e}),e.restrict=e.restrict||"AC",g(e)}function hi(e,t){e.$name=t}function di(e,t,n,r,a){var s=this,o=[];s.$error={},s.$$success={},s.$pending=void 0,s.$name=a(t.name||t.ngForm||"")(n),s.$dirty=!1,s.$pristine=!0,s.$valid=!0,s.$invalid=!1,s.$submitted=!1,s.$$parentForm=rs,s.$rollbackViewValue=function(){i(o,function(e){e.$rollbackViewValue()})},s.$commitViewValue=function(){i(o,function(e){e.$commitViewValue()})},s.$addControl=function(e){he(e.$name,"input"),o.push(e),e.$name&&(s[e.$name]=e),e.$$parentForm=s},s.$$renameControl=function(e,t){var n=e.$name;s[n]===e&&delete s[n],s[t]=e,e.$name=t},s.$removeControl=function(e){e.$name&&s[e.$name]===e&&delete s[e.$name],i(s.$pending,function(t,n){s.$setValidity(n,null,e)}),i(s.$error,function(t,n){s.$setValidity(n,null,e)}),i(s.$$success,function(t,n){s.$setValidity(n,null,e)}),V(o,e),e.$$parentForm=rs},Li({ctrl:this,$element:e,set:function(e,t,n){var i=e[t];if(i){var r=i.indexOf(n);r===-1&&i.push(n)}else e[t]=[n]},unset:function(e,t,n){var i=e[t];i&&(V(i,n),0===i.length&&delete e[t])},$animate:r}),s.$setDirty=function(){r.removeClass(e,Fs),r.addClass(e,qs),s.$dirty=!0,s.$pristine=!1,s.$$parentForm.$setDirty()},s.$setPristine=function(){r.setClass(e,Fs,qs+" "+as),s.$dirty=!1,s.$pristine=!0,s.$submitted=!1,i(o,function(e){e.$setPristine()})},s.$setUntouched=function(){i(o,function(e){e.$setUntouched()})},s.$setSubmitted=function(){r.addClass(e,as),s.$submitted=!0,s.$$parentForm.$setSubmitted()}}function fi(e){e.$formatters.push(function(t){return e.$isEmpty(t)?t:t.toString()})}function gi(e,t,n,i,r,a){mi(e,t,n,i,r,a),fi(i)}function mi(e,t,n,i,r,a){var s=ji(t[0].type);if(!r.android){var o=!1;t.on("compositionstart",function(){o=!0}),t.on("compositionend",function(){o=!1,l()})}var u,l=function(e){if(u&&(a.defer.cancel(u),u=null),!o){var r=t.val(),l=e&&e.type;"password"===s||n.ngTrim&&"false"===n.ngTrim||(r=ar(r)),(i.$viewValue!==r||""===r&&i.$$hasNativeValidators)&&i.$setViewValue(r,l)}};if(r.hasEvent("input"))t.on("input",l);else{var c=function(e,t,n){u||(u=a.defer(function(){u=null,t&&t.value===n||l(e)}))};t.on("keydown",function(e){var t=e.keyCode;91===t||15<t&&t<19||37<=t&&t<=40||c(e,this,this.value)}),r.hasEvent("paste")&&t.on("paste cut",c)}t.on("change",l),ys[s]&&i.$$hasNativeValidators&&s===n.type&&t.on($s,function(e){if(!u){var t=this[Vi],n=t.badInput,i=t.typeMismatch;u=a.defer(function(){u=null,t.badInput===n&&t.typeMismatch===i||l(e)})}}),i.$render=function(){var e=i.$isEmpty(i.$viewValue)?"":i.$viewValue;t.val()!==e&&t.val(e)}}function vi(e,t){if(T(e))return e;if(S(e)){gs.lastIndex=0;var n=gs.exec(e);if(n){var i=+n[1],r=+n[2],a=0,s=0,o=0,u=0,l=ei(i),c=7*(r-1);return t&&(a=t.getHours(),s=t.getMinutes(),o=t.getSeconds(),
u=t.getMilliseconds()),new Date(i,0,l.getDate()+c,a,s,o,u)}}return NaN}function $i(e,t){return function(n,r){var a,s;if(T(n))return n;if(S(n)){if('"'===n.charAt(0)&&'"'===n.charAt(n.length-1)&&(n=n.substring(1,n.length-1)),ls.test(n))return new Date(n);if(e.lastIndex=0,a=e.exec(n))return a.shift(),s=r?{yyyy:r.getFullYear(),MM:r.getMonth()+1,dd:r.getDate(),HH:r.getHours(),mm:r.getMinutes(),ss:r.getSeconds(),sss:r.getMilliseconds()/1e3}:{yyyy:1970,MM:1,dd:1,HH:0,mm:0,ss:0,sss:0},i(a,function(e,n){n<t.length&&(s[t[n]]=+e)}),new Date(s.yyyy,s.MM-1,s.dd,s.HH,s.mm,s.ss||0,1e3*s.sss||0)}return NaN}}function yi(e,t,n,i){return function(r,a,s,o,u,l,c){function p(e){return e&&!(e.getTime&&e.getTime()!==e.getTime())}function h(e){return $(e)&&!T(e)?n(e)||void 0:e}bi(r,a,s,o),mi(r,a,s,o,u,l);var d,f=o&&o.$options&&o.$options.timezone;if(o.$$parserName=e,o.$parsers.push(function(e){if(o.$isEmpty(e))return null;if(t.test(e)){var i=n(e,d);return f&&(i=J(i,f)),i}}),o.$formatters.push(function(e){if(e&&!T(e))throw Js("datefmt","Expected `{0}` to be a date",e);return p(e)?(d=e,d&&f&&(d=J(d,f,!0)),c("date")(e,i,f)):(d=null,"")}),$(s.min)||s.ngMin){var g;o.$validators.min=function(e){return!p(e)||v(g)||n(e)>=g},s.$observe("min",function(e){g=h(e),o.$validate()})}if($(s.max)||s.ngMax){var m;o.$validators.max=function(e){return!p(e)||v(m)||n(e)<=m},s.$observe("max",function(e){m=h(e),o.$validate()})}}}function bi(e,t,n,i){var r=t[0],a=i.$$hasNativeValidators=y(r.validity);a&&i.$parsers.push(function(e){var n=t.prop(Vi)||{};return n.badInput||n.typeMismatch?void 0:e})}function Si(e){e.$$parserName="number",e.$parsers.push(function(t){return e.$isEmpty(t)?null:hs.test(t)?parseFloat(t):void 0}),e.$formatters.push(function(t){if(!e.$isEmpty(t)){if(!M(t))throw Js("numfmt","Expected `{0}` to be a number",t);t=t.toString()}return t})}function Mi(e){return $(e)&&!M(e)&&(e=parseFloat(e)),nr(e)?void 0:e}function Ti(e){return(0|e)===e}function xi(e){var t=e.toString(),n=t.indexOf(".");if(n===-1){if(-1<e&&e<1){var i=/e-(\d+)$/.exec(t);if(i)return Number(i[1])}return 0}return t.length-n-1}function wi(e,t,n){var i=Number(e);if(!Ti(i)||!Ti(t)||!Ti(n)){var r=Math.max(xi(i),xi(t),xi(n)),a=Math.pow(10,r);i*=a,t*=a,n*=a}return(i-t)%n===0}function Ci(e,t,n,i,r,a){bi(e,t,n,i),mi(e,t,n,i,r,a),Si(i);var s,o;($(n.min)||n.ngMin)&&(i.$validators.min=function(e){return i.$isEmpty(e)||v(s)||e>=s},n.$observe("min",function(e){s=Mi(e),i.$validate()})),($(n.max)||n.ngMax)&&(i.$validators.max=function(e){return i.$isEmpty(e)||v(o)||e<=o},n.$observe("max",function(e){o=Mi(e),i.$validate()}))}function Pi(e,t,n,i,r,a){function s(e,i){t.attr(e,n[e]),n.$observe(e,i)}function o(e){if(p=Mi(e),!nr(i.$modelValue))if(c){var n=t.val();p>n&&(n=p,t.val(n)),i.$setViewValue(n)}else i.$validate()}function u(e){if(h=Mi(e),!nr(i.$modelValue))if(c){var n=t.val();h<n&&(t.val(h),n=h<p?p:h),i.$setViewValue(n)}else i.$validate()}function l(e){d=Mi(e),nr(i.$modelValue)||(c&&i.$viewValue!==t.val()?i.$setViewValue(t.val()):i.$validate())}bi(e,t,n,i),Si(i),mi(e,t,n,i,r,a);var c=i.$$hasNativeValidators&&"range"===t[0].type,p=c?0:void 0,h=c?100:void 0,d=c?1:void 0,f=t[0].validity,g=$(n.min),m=$(n.max),y=$(n.step),b=i.$render;i.$render=c&&$(f.rangeUnderflow)&&$(f.rangeOverflow)?function(){b(),i.$setViewValue(t.val())}:b,g&&(i.$validators.min=c?function(){return!0}:function(e,t){return i.$isEmpty(t)||v(p)||t>=p},s("min",o)),m&&(i.$validators.max=c?function(){return!0}:function(e,t){return i.$isEmpty(t)||v(h)||t<=h},s("max",u)),y&&(i.$validators.step=c?function(){return!f.stepMismatch}:function(e,t){return i.$isEmpty(t)||v(d)||wi(t,p||0,d)},s("step",l))}function ki(e,t,n,i,r,a){mi(e,t,n,i,r,a),fi(i),i.$$parserName="url",i.$validators.url=function(e,t){var n=e||t;return i.$isEmpty(n)||cs.test(n)}}function _i(e,t,n,i,r,a){mi(e,t,n,i,r,a),fi(i),i.$$parserName="email",i.$validators.email=function(e,t){var n=e||t;return i.$isEmpty(n)||ps.test(n)}}function Ai(e,t,n,i){v(n.name)&&t.attr("name",s());var r=function(e){t[0].checked&&i.$setViewValue(n.value,e&&e.type)};t.on("click",r),i.$render=function(){var e=n.value;t[0].checked=e==i.$viewValue},n.$observe("value",i.$render)}function Ii(e,t,n,i,r){var a;if($(i)){if(a=e(i),!a.constant)throw Js("constexpr","Expected constant expression for `{0}`, but saw `{1}`.",n,i);return a(t)}return r}function Ei(e,t,n,i,r,a,s,o){var u=Ii(o,e,"ngTrueValue",n.ngTrueValue,!0),l=Ii(o,e,"ngFalseValue",n.ngFalseValue,!1),c=function(e){i.$setViewValue(t[0].checked,e&&e.type)};t.on("click",c),i.$render=function(){t[0].checked=i.$viewValue},i.$isEmpty=function(e){return e===!1},i.$formatters.push(function(e){return j(e,u)}),i.$parsers.push(function(e){return e?u:l})}function Bi(e,t){return e="ngClass"+e,["$animate",function(n){function r(e,t){var n=[];e:for(var i=0;i<e.length;i++){for(var r=e[i],a=0;a<t.length;a++)if(r===t[a])continue e;n.push(r)}return n}function a(e){var t=[];return ir(e)?(i(e,function(e){t=t.concat(a(e))}),t):S(e)?e.split(" "):y(e)?(i(e,function(e,n){e&&(t=t.concat(n.split(" ")))}),t):e}return{restrict:"AC",link:function(s,o,u){function l(e){var t=p(e,1);u.$addClass(t)}function c(e){var t=p(e,-1);u.$removeClass(t)}function p(e,t){var n=o.data("$classCounts")||ge(),r=[];return i(e,function(e){(t>0||n[e])&&(n[e]=(n[e]||0)+t,n[e]===+(t>0)&&r.push(e))}),o.data("$classCounts",n),r.join(" ")}function h(e,t){var i=r(t,e),a=r(e,t);i=p(i,1),a=p(a,-1),i&&i.length&&n.addClass(o,i),a&&a.length&&n.removeClass(o,a)}function d(e){if(t===!0||(1&s.$index)===t){var n=a(e||[]);if(f){if(!j(e,f)){var i=a(f);h(i,n)}}else l(n)}f=ir(e)?e.map(function(e){return ve(e)}):ve(e)}var f;u.$observe("class",function(t){d(s.$eval(u[e]))}),"ngClass"!==e&&s.$watch("$index",function(e,n){var i=1&e;if(i!==(1&n)){var r=a(f);i===t?l(r):c(r)}}),s.$watch(u[e],d,!0)}}}]}function Li(e){function t(e,t,o){v(t)?n("$pending",e,o):i("$pending",e,o),I(t)?t?(c(s.$error,e,o),l(s.$$success,e,o)):(l(s.$error,e,o),c(s.$$success,e,o)):(c(s.$error,e,o),c(s.$$success,e,o)),s.$pending?(r(zs,!0),s.$valid=s.$invalid=void 0,a("",null)):(r(zs,!1),s.$valid=Oi(s.$error),s.$invalid=!s.$valid,a("",s.$valid));var u;u=s.$pending&&s.$pending[e]?void 0:!s.$error[e]&&(!!s.$$success[e]||null),a(e,u),s.$$parentForm.$setValidity(e,u,s)}function n(e,t,n){s[e]||(s[e]={}),l(s[e],t,n)}function i(e,t,n){s[e]&&c(s[e],t,n),Oi(s[e])&&(s[e]=void 0)}function r(e,t){t&&!u[e]?(p.addClass(o,e),u[e]=!0):!t&&u[e]&&(p.removeClass(o,e),u[e]=!1)}function a(e,t){e=e?"-"+ue(e,"-"):"",r(js+e,t===!0),r(Us+e,t===!1)}var s=e.ctrl,o=e.$element,u={},l=e.set,c=e.unset,p=e.$animate;u[Us]=!(u[js]=o.hasClass(js)),s.$setValidity=t}function Oi(e){if(e)for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}function Ni(e){e[0].hasAttribute("selected")&&(e[0].selected=!0)}var Ri=/^\/(.+)\/([a-z]*)$/,Vi="validity",Di=Object.prototype.hasOwnProperty,ji=function(e){return S(e)?e.toLowerCase():e},Ui=function(e){return S(e)?e.toUpperCase():e},Fi=function(e){return S(e)?e.replace(/[A-Z]/g,function(e){return String.fromCharCode(32|e.charCodeAt(0))}):e},qi=function(e){return S(e)?e.replace(/[a-z]/g,function(e){return String.fromCharCode(e.charCodeAt(0)&-33)}):e};"i"!=="I".toLowerCase()&&(ji=Fi,Ui=qi);var Gi,Hi,zi,Wi,Yi=[].slice,Ji=[].splice,Ki=[].push,Zi=Object.prototype.toString,Qi=Object.getPrototypeOf,Xi=t("ng"),er=e.angular||(e.angular={}),tr=0;Gi=e.document.documentMode;var nr=Number.isNaN||function(e){return e!==e};d.$inject=[],f.$inject=[];var ir=Array.isArray,rr=/^\[object (?:Uint8|Uint8Clamped|Uint16|Uint32|Int8|Int16|Int32|Float32|Float64)Array]$/,ar=function(e){return S(e)?e.trim():e},sr=function(e){return e.replace(/([-()[\]{}+?*.$^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")},or=function(){function t(){try{return new Function(""),!1}catch(e){return!0}}if(!$(or.rules)){var n=e.document.querySelector("[ng-csp]")||e.document.querySelector("[data-ng-csp]");if(n){var i=n.getAttribute("ng-csp")||n.getAttribute("data-ng-csp");or.rules={noUnsafeEval:!i||i.indexOf("no-unsafe-eval")!==-1,noInlineStyle:!i||i.indexOf("no-inline-style")!==-1}}else or.rules={noUnsafeEval:t(),noInlineStyle:!1}}return or.rules},ur=function(){if($(ur.name_))return ur.name_;var t,n,i,r,a=cr.length;for(n=0;n<a;++n)if(i=cr[n],t=e.document.querySelector("["+i.replace(":","\\:")+"jq]")){r=t.getAttribute(i+"jq");break}return ur.name_=r},lr=/:/g,cr=["ng-","data-ng-","ng:","x-ng-"],pr=ie(e.document),hr=/[A-Z]/g,dr=!1,fr=1,gr=2,mr=3,vr=8,$r=9,yr=11,br={full:"1.5.11",major:1,minor:5,dot:11,codeName:"princely-quest"};Ae.expando="ng339";var Sr=Ae.cache={},Mr=1,Tr=function(e,t,n){e.addEventListener(t,n,!1)},xr=function(e,t,n){e.removeEventListener(t,n,!1)};Ae._data=function(e){return this.cache[e[this.expando]]||{}};var wr=/([:\-_]+(.))/g,Cr=/^moz([A-Z])/,Pr={mouseleave:"mouseout",mouseenter:"mouseover"},kr=t("jqLite"),_r=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,Ar=/<|&#?\w+;/,Ir=/<([\w:-]+)/,Er=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,Br={option:[1,'<select multiple="multiple">',"</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};Br.optgroup=Br.option,Br.tbody=Br.tfoot=Br.colgroup=Br.caption=Br.thead,Br.th=Br.td;var Lr=e.Node.prototype.contains||function(e){return!!(16&this.compareDocumentPosition(e))},Or=Ae.prototype={ready:function(t){function n(){i||(i=!0,t())}var i=!1;"complete"===e.document.readyState?e.setTimeout(n):(this.on("DOMContentLoaded",n),Ae(e).on("load",n))},toString:function(){var e=[];return i(this,function(t){e.push(""+t)}),"["+e.join(", ")+"]"},eq:function(e){return Hi(e>=0?this[e]:this[this.length+e])},length:0,push:Ki,sort:[].sort,splice:[].splice},Nr={};i("multiple,selected,checked,disabled,readOnly,required,open".split(","),function(e){Nr[ji(e)]=e});var Rr={};i("input,select,option,textarea,button,form,details".split(","),function(e){Rr[e]=!0});var Vr={ngMinlength:"minlength",ngMaxlength:"maxlength",ngMin:"min",ngMax:"max",ngPattern:"pattern"};i({data:Ne,removeData:Le,hasData:we,cleanData:Ce},function(e,t){Ae[t]=e}),i({data:Ne,inheritedData:Fe,scope:function(e){return Hi.data(e,"$scope")||Fe(e.parentNode||e,["$isolateScope","$scope"])},isolateScope:function(e){return Hi.data(e,"$isolateScope")||Hi.data(e,"$isolateScopeNoTemplate")},controller:Ue,injector:function(e){return Fe(e,"$injector")},removeAttr:function(e,t){e.removeAttribute(t)},hasClass:Re,css:function(e,t,n){return t=Me(t),$(n)?void(e.style[t]=n):e.style[t]},attr:function(e,t,n){var i=e.nodeType;if(i!==mr&&i!==gr&&i!==vr){var r=ji(t);if(Nr[r]){if(!$(n))return e[t]||(e.attributes.getNamedItem(t)||d).specified?r:void 0;n?(e[t]=!0,e.setAttribute(t,r)):(e[t]=!1,e.removeAttribute(r))}else if($(n))e.setAttribute(t,n);else if(e.getAttribute){var a=e.getAttribute(t,2);return null===a?void 0:a}}},prop:function(e,t,n){return $(n)?void(e[t]=n):e[t]},text:function(){function e(e,t){if(v(t)){var n=e.nodeType;return n===fr||n===mr?e.textContent:""}e.textContent=t}return e.$dv="",e}(),val:function(e,t){if(v(t)){if(e.multiple&&"select"===R(e)){var n=[];return i(e.options,function(e){e.selected&&n.push(e.value||e.text)}),0===n.length?null:n}return e.value}e.value=t},html:function(e,t){return v(t)?e.innerHTML:(Ee(e,!0),void(e.innerHTML=t))},empty:qe},function(e,t){Ae.prototype[t]=function(t,n){var i,r,a=this.length;if(e!==qe&&v(2===e.length&&e!==Re&&e!==Ue?t:n)){if(y(t)){for(i=0;i<a;i++)if(e===Ne)e(this[i],t);else for(r in t)e(this[i],r,t[r]);return this}for(var s=e.$dv,o=v(s)?Math.min(a,1):a,u=0;u<o;u++){var l=e(this[u],t,n);s=s?s+l:l}return s}for(i=0;i<a;i++)e(this[i],t,n);return this}}),i({removeData:Le,on:function(e,t,n,i){if($(i))throw kr("onargs","jqLite#on() does not support the `selector` or `eventData` parameters");if(xe(e)){var r=Oe(e,!0),a=r.events,s=r.handle;s||(s=r.handle=Ye(e,a));for(var o=t.indexOf(" ")>=0?t.split(" "):[t],u=o.length,l=function(t,i,r){var o=a[t];o||(o=a[t]=[],o.specialHandlerWrapper=i,"$destroy"===t||r||Tr(e,t,s)),o.push(n)};u--;)t=o[u],Pr[t]?(l(Pr[t],Ke),l(t,void 0,!0)):l(t)}},off:Be,one:function(e,t,n){e=Hi(e),e.on(t,function i(){e.off(t,n),e.off(t,i)}),e.on(t,n)},replaceWith:function(e,t){var n,r=e.parentNode;Ee(e),i(new Ae(t),function(t){n?r.insertBefore(t,n.nextSibling):r.replaceChild(t,e),n=t})},children:function(e){var t=[];return i(e.childNodes,function(e){e.nodeType===fr&&t.push(e)}),t},contents:function(e){return e.contentDocument||e.childNodes||[]},append:function(e,t){var n=e.nodeType;if(n===fr||n===yr){t=new Ae(t);for(var i=0,r=t.length;i<r;i++){var a=t[i];e.appendChild(a)}}},prepend:function(e,t){if(e.nodeType===fr){var n=e.firstChild;i(new Ae(t),function(t){e.insertBefore(t,n)})}},wrap:function(e,t){_e(e,Hi(t).eq(0).clone()[0])},remove:Ge,detach:function(e){Ge(e,!0)},after:function(e,t){var n=e,i=e.parentNode;if(i){t=new Ae(t);for(var r=0,a=t.length;r<a;r++){var s=t[r];i.insertBefore(s,n.nextSibling),n=s}}},addClass:De,removeClass:Ve,toggleClass:function(e,t,n){t&&i(t.split(" "),function(t){var i=n;v(i)&&(i=!Re(e,t)),(i?De:Ve)(e,t)})},parent:function(e){var t=e.parentNode;return t&&t.nodeType!==yr?t:null},next:function(e){return e.nextElementSibling},find:function(e,t){return e.getElementsByTagName?e.getElementsByTagName(t):[]},clone:Ie,triggerHandler:function(e,t,n){var r,a,s,o=t.type||t,u=Oe(e),c=u&&u.events,p=c&&c[o];p&&(r={preventDefault:function(){this.defaultPrevented=!0},isDefaultPrevented:function(){return this.defaultPrevented===!0},stopImmediatePropagation:function(){this.immediatePropagationStopped=!0},isImmediatePropagationStopped:function(){return this.immediatePropagationStopped===!0},stopPropagation:d,type:o,target:e},t.type&&(r=l(r,t)),a=ve(p),s=n?[r].concat(n):[r],i(a,function(t){r.isImmediatePropagationStopped()||t.apply(e,s)}))}},function(e,t){Ae.prototype[t]=function(t,n,i){for(var r,a=0,s=this.length;a<s;a++)v(r)?(r=e(this[a],t,n,i),$(r)&&(r=Hi(r))):je(r,e(this[a],t,n,i));return $(r)?r:this}}),Ae.prototype.bind=Ae.prototype.on,Ae.prototype.unbind=Ae.prototype.off,Xe.prototype={put:function(e,t){this[Qe(e,this.nextUid)]=t},get:function(e){return this[Qe(e,this.nextUid)]},remove:function(e){var t=this[e=Qe(e,this.nextUid)];return delete this[e],t}};var Dr=[function(){this.$get=[function(){return Xe}]}],jr=/^([^(]+?)=>/,Ur=/^[^(]*\(\s*([^)]*)\)/m,Fr=/,/,qr=/^\s*(_?)(\S+?)\1\s*$/,Gr=/((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm,Hr=t("$injector");rt.$$annotate=it;var zr=t("$animate"),Wr=1,Yr="ng-animate",Jr=function(){this.$get=d},Kr=function(){var e=new Xe,t=[];this.$get=["$$AnimateRunner","$rootScope",function(n,r){function a(e,t,n){var r=!1;return t&&(t=S(t)?t.split(" "):ir(t)?t:[],i(t,function(t){t&&(r=!0,e[t]=n)})),r}function s(){i(t,function(t){var n=e.get(t);if(n){var r=ut(t.attr("class")),a="",s="";i(n,function(e,t){var n=!!r[t];e!==n&&(e?a+=(a.length?" ":"")+t:s+=(s.length?" ":"")+t)}),i(t,function(e){a&&De(e,a),s&&Ve(e,s)}),e.remove(t)}}),t.length=0}function o(n,i,o){var u=e.get(n)||{},l=a(u,i,!0),c=a(u,o,!1);(l||c)&&(e.put(n,u),t.push(n),1===t.length&&r.$$postDigest(s))}return{enabled:d,on:d,off:d,pin:d,push:function(e,t,i,r){r&&r(),i=i||{},i.from&&e.css(i.from),i.to&&e.css(i.to),(i.addClass||i.removeClass)&&o(e,i.addClass,i.removeClass);var a=new n;return a.complete(),a}}}]},Zr=["$provide",function(e){var t=this;this.$$registeredAnimations=Object.create(null),this.register=function(n,i){if(n&&"."!==n.charAt(0))throw zr("notcsel","Expecting class selector starting with '.' got '{0}'.",n);var r=n+"-animation";t.$$registeredAnimations[n.substr(1)]=r,e.factory(r,i)},this.classNameFilter=function(e){if(1===arguments.length&&(this.$$classNameFilter=e instanceof RegExp?e:null,this.$$classNameFilter)){var t=new RegExp("(\\s+|\\/)"+Yr+"(\\s+|\\/)");if(t.test(this.$$classNameFilter.toString()))throw zr("nongcls",'$animateProvider.classNameFilter(regex) prohibits accepting a regex value which matches/contains the "{0}" CSS class.',Yr)}return this.$$classNameFilter},this.$get=["$$animateQueue",function(e){function t(e,t,n){if(n){var i=ot(n);!i||i.parentNode||i.previousElementSibling||(n=null)}n?n.after(e):t.prepend(e)}return{on:e.on,off:e.off,pin:e.pin,enabled:e.enabled,cancel:function(e){e.end&&e.end()},enter:function(n,i,r,a){return i=i&&Hi(i),r=r&&Hi(r),i=i||r.parent(),t(n,i,r),e.push(n,"enter",lt(a))},move:function(n,i,r,a){return i=i&&Hi(i),r=r&&Hi(r),i=i||r.parent(),t(n,i,r),e.push(n,"move",lt(a))},leave:function(t,n){return e.push(t,"leave",lt(n),function(){t.remove()})},addClass:function(t,n,i){return i=lt(i),i.addClass=st(i.addclass,n),e.push(t,"addClass",i)},removeClass:function(t,n,i){return i=lt(i),i.removeClass=st(i.removeClass,n),e.push(t,"removeClass",i)},setClass:function(t,n,i,r){return r=lt(r),r.addClass=st(r.addClass,n),r.removeClass=st(r.removeClass,i),e.push(t,"setClass",r)},animate:function(t,n,i,r,a){return a=lt(a),a.from=a.from?l(a.from,n):n,a.to=a.to?l(a.to,i):i,r=r||"ng-inline-animate",a.tempClasses=st(a.tempClasses,r),e.push(t,"animate",a)}}}]}],Qr=function(){this.$get=["$$rAF",function(e){function t(t){n.push(t),n.length>1||e(function(){for(var e=0;e<n.length;e++)n[e]();n=[]})}var n=[];return function(){var e=!1;return t(function(){e=!0}),function(n){e?n():t(n)}}}]},Xr=function(){this.$get=["$q","$sniffer","$$animateAsyncRun","$document","$timeout",function(e,t,n,r,a){function s(e){this.setHost(e);var t=n(),i=function(e){a(e,0,!1)};this._doneCallbacks=[],this._tick=function(e){var n=r[0];n&&n.hidden?i(e):t(e)},this._state=0}var o=0,u=1,l=2;return s.chain=function(e,t){function n(){return i===e.length?void t(!0):void e[i](function(e){return e===!1?void t(!1):(i++,void n())})}var i=0;n()},s.all=function(e,t){function n(n){a=a&&n,++r===e.length&&t(a)}var r=0,a=!0;i(e,function(e){e.done(n)})},s.prototype={setHost:function(e){this.host=e||{}},done:function(e){this._state===l?e():this._doneCallbacks.push(e)},progress:d,getPromise:function(){if(!this.promise){var t=this;this.promise=e(function(e,n){t.done(function(t){t===!1?n():e()})})}return this.promise},then:function(e,t){return this.getPromise().then(e,t)},"catch":function(e){return this.getPromise()["catch"](e)},"finally":function(e){return this.getPromise()["finally"](e)},pause:function(){this.host.pause&&this.host.pause()},resume:function(){this.host.resume&&this.host.resume()},end:function(){this.host.end&&this.host.end(),this._resolve(!0)},cancel:function(){this.host.cancel&&this.host.cancel(),this._resolve(!1)},complete:function(e){var t=this;t._state===o&&(t._state=u,t._tick(function(){t._resolve(e)}))},_resolve:function(e){this._state!==l&&(i(this._doneCallbacks,function(t){t(e)}),this._doneCallbacks.length=0,this._state=l)}},s}]},ea=function(){this.$get=["$$rAF","$q","$$AnimateRunner",function(e,t,n){return function(t,i){function r(){return e(function(){a(),o||u.complete(),o=!0}),u}function a(){s.addClass&&(t.addClass(s.addClass),s.addClass=null),s.removeClass&&(t.removeClass(s.removeClass),s.removeClass=null),s.to&&(t.css(s.to),s.to=null)}var s=i||{};s.$$prepared||(s=D(s)),s.cleanupStyles&&(s.from=s.to=null),s.from&&(t.css(s.from),s.from=null);var o,u=new n;return{start:r,end:r}}}]},ta=t("$compile"),na=new ft;gt.$inject=["$provide","$$sanitizeUriProvider"],mt.prototype.isFirstChange=function(){return this.previousValue===na};var ia=/^((?:x|data)[:\-_])/i,ra=t("$controller"),aa=/^(\S+)(\s+as\s+([\w$]+))?$/,sa=function(){this.$get=["$document",function(e){return function(t){return t?!t.nodeType&&t instanceof Hi&&(t=t[0]):t=e[0].body,t.offsetWidth+1}}]},oa="application/json",ua={"Content-Type":oa+";charset=utf-8"},la=/^\[|^\{(?!\{)/,ca={"[":/]$/,"{":/}$/},pa=/^\)]\}',?\n/,ha=t("$http"),da=function(e){return function(){throw ha("legacy","The method `{0}` on the promise returned from `$http` has been disabled.",e)}},fa=er.$interpolateMinErr=t("$interpolate");fa.throwNoconcat=function(e){throw fa("noconcat","Error while interpolating: {0}\nStrict Contextual Escaping disallows interpolations that concatenate multiple expressions when a trusted value is required.  See http://docs.angularjs.org/api/ng.$sce",e)},fa.interr=function(e,t){return fa("interr","Can't interpolate: {0}\n{1}",e,t.toString())};var ga=function(){this.$get=["$window",function(e){function t(e){var t=function(e){t.data=e,t.called=!0};return t.id=e,t}var n=e.angular.callbacks,i={};return{createCallback:function(e){var r="_"+(n.$$counter++).toString(36),a="angular.callbacks."+r,s=t(r);return i[a]=n[r]=s,a},wasCalled:function(e){return i[e].called},getResponse:function(e){return i[e].data},removeCallback:function(e){var t=i[e];delete n[t.id],delete i[e]}}}]},ma=/^([^?#]*)(\?([^#]*))?(#(.*))?$/,va={http:80,https:443,ftp:21},$a=t("$location"),ya=/^\s*[\\\/]{2,}/,ba={$$absUrl:"",$$html5:!1,$$replace:!1,absUrl:Zt("$$absUrl"),url:function(e){if(v(e))return this.$$url;var t=ma.exec(e);return(t[1]||""===e)&&this.path(decodeURIComponent(t[1])),(t[2]||t[1]||""===e)&&this.search(t[3]||""),this.hash(t[5]||""),this},protocol:Zt("$$protocol"),host:Zt("$$host"),port:Zt("$$port"),path:Qt("$$path",function(e){return e=null!==e?e.toString():"","/"===e.charAt(0)?e:"/"+e}),search:function(e,t){switch(arguments.length){case 0:return this.$$search;case 1:if(S(e)||M(e))e=e.toString(),this.$$search=Q(e);else{if(!y(e))throw $a("isrcharg","The first argument of the `$location#search()` call must be a string or an object.");e=D(e,{}),i(e,function(t,n){null==t&&delete e[n]}),this.$$search=e}break;default:v(t)||null===t?delete this.$$search[e]:this.$$search[e]=t}return this.$$compose(),this},hash:Qt("$$hash",function(e){return null!==e?e.toString():""}),replace:function(){return this.$$replace=!0,this}};i([Kt,Jt,Yt],function(e){e.prototype=Object.create(ba),e.prototype.state=function(t){if(!arguments.length)return this.$$state;if(e!==Yt||!this.$$html5)throw $a("nostate","History API state support is available only in HTML5 mode and only in browsers supporting HTML5 History API");return this.$$state=v(t)?null:t,this}});var Sa=t("$parse"),Ma=[].constructor,Ta=(!1).constructor,xa=Function.constructor,wa=(0).constructor,Ca={}.constructor,Pa="".constructor,ka=Ma.prototype,_a=Ta.prototype,Aa=xa.prototype,Ia=wa.prototype,Ea=Ca.prototype,Ba=Pa.prototype,La=Aa.call,Oa=Aa.apply,Na=Aa.bind,Ra=Ea.valueOf,Va=ge();i("+ - * / % === !== == != < > <= >= && || ! = |".split(" "),function(e){Va[e]=!0});var Da={n:"\n",f:"\f",r:"\r",t:"\t",v:"\x0B","'":"'",'"':'"'},ja=function(e){this.options=e};ja.prototype={constructor:ja,lex:function(e){for(this.text=e,this.index=0,this.tokens=[];this.index<this.text.length;){var t=this.text.charAt(this.index);if('"'===t||"'"===t)this.readString(t);else if(this.isNumber(t)||"."===t&&this.isNumber(this.peek()))this.readNumber();else if(this.isIdentifierStart(this.peekMultichar()))this.readIdent();else if(this.is(t,"(){}[].,;:?"))this.tokens.push({index:this.index,text:t}),this.index++;else if(this.isWhitespace(t))this.index++;else{var n=t+this.peek(),i=n+this.peek(2),r=Va[t],a=Va[n],s=Va[i];if(r||a||s){var o=s?i:a?n:t;this.tokens.push({index:this.index,text:o,operator:!0}),this.index+=o.length}else this.throwError("Unexpected next character ",this.index,this.index+1)}}return this.tokens},is:function(e,t){return t.indexOf(e)!==-1},peek:function(e){var t=e||1;return this.index+t<this.text.length&&this.text.charAt(this.index+t)},isNumber:function(e){return"0"<=e&&e<="9"&&"string"==typeof e},isWhitespace:function(e){return" "===e||"\r"===e||"\t"===e||"\n"===e||"\x0B"===e||" "===e},isIdentifierStart:function(e){return this.options.isIdentifierStart?this.options.isIdentifierStart(e,this.codePointAt(e)):this.isValidIdentifierStart(e)},isValidIdentifierStart:function(e){return"a"<=e&&e<="z"||"A"<=e&&e<="Z"||"_"===e||"$"===e},isIdentifierContinue:function(e){return this.options.isIdentifierContinue?this.options.isIdentifierContinue(e,this.codePointAt(e)):this.isValidIdentifierContinue(e)},isValidIdentifierContinue:function(e,t){return this.isValidIdentifierStart(e,t)||this.isNumber(e)},codePointAt:function(e){return 1===e.length?e.charCodeAt(0):(e.charCodeAt(0)<<10)+e.charCodeAt(1)-56613888},peekMultichar:function(){var e=this.text.charAt(this.index),t=this.peek();if(!t)return e;var n=e.charCodeAt(0),i=t.charCodeAt(0);return n>=55296&&n<=56319&&i>=56320&&i<=57343?e+t:e},isExpOperator:function(e){return"-"===e||"+"===e||this.isNumber(e)},throwError:function(e,t,n){n=n||this.index;var i=$(t)?"s "+t+"-"+this.index+" ["+this.text.substring(t,n)+"]":" "+n;throw Sa("lexerr","Lexer Error: {0} at column{1} in expression [{2}].",e,i,this.text)},readNumber:function(){for(var e="",t=this.index;this.index<this.text.length;){var n=ji(this.text.charAt(this.index));if("."===n||this.isNumber(n))e+=n;else{var i=this.peek();if("e"===n&&this.isExpOperator(i))e+=n;else if(this.isExpOperator(n)&&i&&this.isNumber(i)&&"e"===e.charAt(e.length-1))e+=n;else{if(!this.isExpOperator(n)||i&&this.isNumber(i)||"e"!==e.charAt(e.length-1))break;this.throwError("Invalid exponent")}}this.index++}this.tokens.push({index:t,text:e,constant:!0,value:Number(e)})},readIdent:function(){var e=this.index;for(this.index+=this.peekMultichar().length;this.index<this.text.length;){var t=this.peekMultichar();if(!this.isIdentifierContinue(t))break;this.index+=t.length}this.tokens.push({index:e,text:this.text.slice(e,this.index),identifier:!0})},readString:function(e){var t=this.index;this.index++;for(var n="",i=e,r=!1;this.index<this.text.length;){var a=this.text.charAt(this.index);if(i+=a,r){if("u"===a){var s=this.text.substring(this.index+1,this.index+5);s.match(/[\da-f]{4}/i)||this.throwError("Invalid unicode escape [\\u"+s+"]"),this.index+=4,n+=String.fromCharCode(parseInt(s,16))}else{var o=Da[a];n+=o||a}r=!1}else if("\\"===a)r=!0;else{if(a===e)return this.index++,void this.tokens.push({index:t,text:i,constant:!0,value:n});n+=a}this.index++}this.throwError("Unterminated quote",t)}};var Ua=function(e,t){this.lexer=e,this.options=t};Ua.Program="Program",Ua.ExpressionStatement="ExpressionStatement",Ua.AssignmentExpression="AssignmentExpression",Ua.ConditionalExpression="ConditionalExpression",Ua.LogicalExpression="LogicalExpression",Ua.BinaryExpression="BinaryExpression",Ua.UnaryExpression="UnaryExpression",Ua.CallExpression="CallExpression",Ua.MemberExpression="MemberExpression",Ua.Identifier="Identifier",Ua.Literal="Literal",Ua.ArrayExpression="ArrayExpression",Ua.Property="Property",Ua.ObjectExpression="ObjectExpression",Ua.ThisExpression="ThisExpression",Ua.LocalsExpression="LocalsExpression",Ua.NGValueParameter="NGValueParameter",Ua.prototype={ast:function(e){this.text=e,this.tokens=this.lexer.lex(e);var t=this.program();return 0!==this.tokens.length&&this.throwError("is an unexpected token",this.tokens[0]),t},program:function(){for(var e=[];;)if(this.tokens.length>0&&!this.peek("}",")",";","]")&&e.push(this.expressionStatement()),!this.expect(";"))return{type:Ua.Program,body:e}},expressionStatement:function(){return{type:Ua.ExpressionStatement,expression:this.filterChain()}},filterChain:function(){for(var e=this.expression();this.expect("|");)e=this.filter(e);return e},expression:function(){return this.assignment()},assignment:function(){var e=this.ternary();if(this.expect("=")){if(!hn(e))throw Sa("lval","Trying to assign a value to a non l-value");e={type:Ua.AssignmentExpression,left:e,right:this.assignment(),operator:"="}}return e},ternary:function(){var e,t,n=this.logicalOR();return this.expect("?")&&(e=this.expression(),this.consume(":"))?(t=this.expression(),{type:Ua.ConditionalExpression,test:n,alternate:e,consequent:t}):n},logicalOR:function(){for(var e=this.logicalAND();this.expect("||");)e={type:Ua.LogicalExpression,operator:"||",left:e,right:this.logicalAND()};return e},logicalAND:function(){for(var e=this.equality();this.expect("&&");)e={type:Ua.LogicalExpression,operator:"&&",left:e,right:this.equality()};return e},equality:function(){for(var e,t=this.relational();e=this.expect("==","!=","===","!==");)t={type:Ua.BinaryExpression,operator:e.text,left:t,right:this.relational()};return t},relational:function(){for(var e,t=this.additive();e=this.expect("<",">","<=",">=");)t={type:Ua.BinaryExpression,operator:e.text,left:t,right:this.additive()};return t},additive:function(){for(var e,t=this.multiplicative();e=this.expect("+","-");)t={type:Ua.BinaryExpression,operator:e.text,left:t,right:this.multiplicative()};return t},multiplicative:function(){for(var e,t=this.unary();e=this.expect("*","/","%");)t={type:Ua.BinaryExpression,operator:e.text,left:t,right:this.unary()};return t},unary:function(){var e;return(e=this.expect("+","-","!"))?{type:Ua.UnaryExpression,operator:e.text,prefix:!0,argument:this.unary()}:this.primary()},primary:function(){var e;this.expect("(")?(e=this.filterChain(),this.consume(")")):this.expect("[")?e=this.arrayDeclaration():this.expect("{")?e=this.object():this.selfReferential.hasOwnProperty(this.peek().text)?e=D(this.selfReferential[this.consume().text]):this.options.literals.hasOwnProperty(this.peek().text)?e={type:Ua.Literal,value:this.options.literals[this.consume().text]}:this.peek().identifier?e=this.identifier():this.peek().constant?e=this.constant():this.throwError("not a primary expression",this.peek());for(var t;t=this.expect("(","[",".");)"("===t.text?(e={type:Ua.CallExpression,callee:e,arguments:this.parseArguments()},this.consume(")")):"["===t.text?(e={type:Ua.MemberExpression,object:e,property:this.expression(),computed:!0},this.consume("]")):"."===t.text?e={type:Ua.MemberExpression,object:e,property:this.identifier(),computed:!1}:this.throwError("IMPOSSIBLE");return e},filter:function(e){for(var t=[e],n={type:Ua.CallExpression,callee:this.identifier(),arguments:t,filter:!0};this.expect(":");)t.push(this.expression());return n},parseArguments:function(){var e=[];if(")"!==this.peekToken().text)do e.push(this.filterChain());while(this.expect(","));return e},identifier:function(){var e=this.consume();return e.identifier||this.throwError("is not a valid identifier",e),{type:Ua.Identifier,name:e.text}},constant:function(){return{type:Ua.Literal,value:this.consume().value}},arrayDeclaration:function(){var e=[];if("]"!==this.peekToken().text)do{if(this.peek("]"))break;e.push(this.expression())}while(this.expect(","));return this.consume("]"),{type:Ua.ArrayExpression,elements:e}},object:function(){var e,t=[];if("}"!==this.peekToken().text)do{if(this.peek("}"))break;e={type:Ua.Property,kind:"init"},this.peek().constant?(e.key=this.constant(),e.computed=!1,this.consume(":"),e.value=this.expression()):this.peek().identifier?(e.key=this.identifier(),e.computed=!1,this.peek(":")?(this.consume(":"),e.value=this.expression()):e.value=e.key):this.peek("[")?(this.consume("["),e.key=this.expression(),this.consume("]"),e.computed=!0,this.consume(":"),e.value=this.expression()):this.throwError("invalid key",this.peek()),t.push(e)}while(this.expect(","));return this.consume("}"),{type:Ua.ObjectExpression,properties:t}},throwError:function(e,t){throw Sa("syntax","Syntax Error: Token '{0}' {1} at column {2} of the expression [{3}] starting at [{4}].",t.text,e,t.index+1,this.text,this.text.substring(t.index))},consume:function(e){if(0===this.tokens.length)throw Sa("ueoe","Unexpected end of expression: {0}",this.text);var t=this.expect(e);return t||this.throwError("is unexpected, expecting ["+e+"]",this.peek()),t},peekToken:function(){if(0===this.tokens.length)throw Sa("ueoe","Unexpected end of expression: {0}",this.text);return this.tokens[0]},peek:function(e,t,n,i){return this.peekAhead(0,e,t,n,i)},peekAhead:function(e,t,n,i,r){if(this.tokens.length>e){var a=this.tokens[e],s=a.text;if(s===t||s===n||s===i||s===r||!t&&!n&&!i&&!r)return a}return!1},expect:function(e,t,n,i){var r=this.peek(e,t,n,i);return!!r&&(this.tokens.shift(),r)},selfReferential:{"this":{type:Ua.ThisExpression},$locals:{type:Ua.LocalsExpression}}},mn.prototype={compile:function(e,t){var n=this,r=this.astBuilder.ast(e);this.state={nextId:0,filters:{},expensiveChecks:t,fn:{vars:[],body:[],own:{}},assign:{vars:[],body:[],
own:{}},inputs:[]},cn(r,n.$filter);var a,s="";if(this.stage="assign",a=dn(r)){this.state.computing="assign";var o=this.nextId();this.recurse(a,o),this.return_(o),s="fn.assign="+this.generateFunction("assign","s,v,l")}var u=pn(r.body);n.stage="inputs",i(u,function(e,t){var i="fn"+t;n.state[i]={vars:[],body:[],own:{}},n.state.computing=i;var r=n.nextId();n.recurse(e,r),n.return_(r),n.state.inputs.push(i),e.watchId=t}),this.state.computing="fn",this.stage="main",this.recurse(r);var l='"'+this.USE+" "+this.STRICT+'";\n'+this.filterPrefix()+"var fn="+this.generateFunction("fn","s,l,a,i")+s+this.watchFns()+"return fn;",c=new Function("$filter","ensureSafeMemberName","ensureSafeObject","ensureSafeFunction","getStringValue","ensureSafeAssignContext","ifDefined","plus","text",l)(this.$filter,tn,rn,an,nn,sn,on,un,e);return this.state=this.stage=void 0,c.literal=fn(r),c.constant=gn(r),c},USE:"use",STRICT:"strict",watchFns:function(){var e=[],t=this.state.inputs,n=this;return i(t,function(t){e.push("var "+t+"="+n.generateFunction(t,"s"))}),t.length&&e.push("fn.inputs=["+t.join(",")+"];"),e.join("")},generateFunction:function(e,t){return"function("+t+"){"+this.varsPrefix(e)+this.body(e)+"};"},filterPrefix:function(){var e=[],t=this;return i(this.state.filters,function(n,i){e.push(n+"=$filter("+t.escape(i)+")")}),e.length?"var "+e.join(",")+";":""},varsPrefix:function(e){return this.state[e].vars.length?"var "+this.state[e].vars.join(",")+";":""},body:function(e){return this.state[e].body.join("")},recurse:function(e,t,n,r,a,s){var o,u,l,c,p,h=this;if(r=r||d,!s&&$(e.watchId))return t=t||this.nextId(),void this.if_("i",this.lazyAssign(t,this.computedMember("i",e.watchId)),this.lazyRecurse(e,t,n,r,a,!0));switch(e.type){case Ua.Program:i(e.body,function(t,n){h.recurse(t.expression,void 0,void 0,function(e){u=e}),n!==e.body.length-1?h.current().body.push(u,";"):h.return_(u)});break;case Ua.Literal:c=this.escape(e.value),this.assign(t,c),r(c);break;case Ua.UnaryExpression:this.recurse(e.argument,void 0,void 0,function(e){u=e}),c=e.operator+"("+this.ifDefined(u,0)+")",this.assign(t,c),r(c);break;case Ua.BinaryExpression:this.recurse(e.left,void 0,void 0,function(e){o=e}),this.recurse(e.right,void 0,void 0,function(e){u=e}),c="+"===e.operator?this.plus(o,u):"-"===e.operator?this.ifDefined(o,0)+e.operator+this.ifDefined(u,0):"("+o+")"+e.operator+"("+u+")",this.assign(t,c),r(c);break;case Ua.LogicalExpression:t=t||this.nextId(),h.recurse(e.left,t),h.if_("&&"===e.operator?t:h.not(t),h.lazyRecurse(e.right,t)),r(t);break;case Ua.ConditionalExpression:t=t||this.nextId(),h.recurse(e.test,t),h.if_(t,h.lazyRecurse(e.alternate,t),h.lazyRecurse(e.consequent,t)),r(t);break;case Ua.Identifier:t=t||this.nextId(),n&&(n.context="inputs"===h.stage?"s":this.assign(this.nextId(),this.getHasOwnProperty("l",e.name)+"?l:s"),n.computed=!1,n.name=e.name),tn(e.name),h.if_("inputs"===h.stage||h.not(h.getHasOwnProperty("l",e.name)),function(){h.if_("inputs"===h.stage||"s",function(){a&&1!==a&&h.if_(h.not(h.nonComputedMember("s",e.name)),h.lazyAssign(h.nonComputedMember("s",e.name),"{}")),h.assign(t,h.nonComputedMember("s",e.name))})},t&&h.lazyAssign(t,h.nonComputedMember("l",e.name))),(h.state.expensiveChecks||$n(e.name))&&h.addEnsureSafeObject(t),r(t);break;case Ua.MemberExpression:o=n&&(n.context=this.nextId())||this.nextId(),t=t||this.nextId(),h.recurse(e.object,o,void 0,function(){h.if_(h.notNull(o),function(){a&&1!==a&&h.addEnsureSafeAssignContext(o),e.computed?(u=h.nextId(),h.recurse(e.property,u),h.getStringValue(u),h.addEnsureSafeMemberName(u),a&&1!==a&&h.if_(h.not(h.computedMember(o,u)),h.lazyAssign(h.computedMember(o,u),"{}")),c=h.ensureSafeObject(h.computedMember(o,u)),h.assign(t,c),n&&(n.computed=!0,n.name=u)):(tn(e.property.name),a&&1!==a&&h.if_(h.not(h.nonComputedMember(o,e.property.name)),h.lazyAssign(h.nonComputedMember(o,e.property.name),"{}")),c=h.nonComputedMember(o,e.property.name),(h.state.expensiveChecks||$n(e.property.name))&&(c=h.ensureSafeObject(c)),h.assign(t,c),n&&(n.computed=!1,n.name=e.property.name))},function(){h.assign(t,"undefined")}),r(t)},!!a);break;case Ua.CallExpression:t=t||this.nextId(),e.filter?(u=h.filter(e.callee.name),l=[],i(e.arguments,function(e){var t=h.nextId();h.recurse(e,t),l.push(t)}),c=u+"("+l.join(",")+")",h.assign(t,c),r(t)):(u=h.nextId(),o={},l=[],h.recurse(e.callee,u,o,function(){h.if_(h.notNull(u),function(){h.addEnsureSafeFunction(u),i(e.arguments,function(e){h.recurse(e,h.nextId(),void 0,function(e){l.push(h.ensureSafeObject(e))})}),o.name?(h.state.expensiveChecks||h.addEnsureSafeObject(o.context),c=h.member(o.context,o.name,o.computed)+"("+l.join(",")+")"):c=u+"("+l.join(",")+")",c=h.ensureSafeObject(c),h.assign(t,c)},function(){h.assign(t,"undefined")}),r(t)}));break;case Ua.AssignmentExpression:u=this.nextId(),o={},this.recurse(e.left,void 0,o,function(){h.if_(h.notNull(o.context),function(){h.recurse(e.right,u),h.addEnsureSafeObject(h.member(o.context,o.name,o.computed)),h.addEnsureSafeAssignContext(o.context),c=h.member(o.context,o.name,o.computed)+e.operator+u,h.assign(t,c),r(t||c)})},1);break;case Ua.ArrayExpression:l=[],i(e.elements,function(e){h.recurse(e,h.nextId(),void 0,function(e){l.push(e)})}),c="["+l.join(",")+"]",this.assign(t,c),r(c);break;case Ua.ObjectExpression:l=[],p=!1,i(e.properties,function(e){e.computed&&(p=!0)}),p?(t=t||this.nextId(),this.assign(t,"{}"),i(e.properties,function(e){e.computed?(o=h.nextId(),h.recurse(e.key,o)):o=e.key.type===Ua.Identifier?e.key.name:""+e.key.value,u=h.nextId(),h.recurse(e.value,u),h.assign(h.member(t,o,e.computed),u)})):(i(e.properties,function(t){h.recurse(t.value,e.constant?void 0:h.nextId(),void 0,function(e){l.push(h.escape(t.key.type===Ua.Identifier?t.key.name:""+t.key.value)+":"+e)})}),c="{"+l.join(",")+"}",this.assign(t,c)),r(t||c);break;case Ua.ThisExpression:this.assign(t,"s"),r("s");break;case Ua.LocalsExpression:this.assign(t,"l"),r("l");break;case Ua.NGValueParameter:this.assign(t,"v"),r("v")}},getHasOwnProperty:function(e,t){var n=e+"."+t,i=this.current().own;return i.hasOwnProperty(n)||(i[n]=this.nextId(!1,e+"&&("+this.escape(t)+" in "+e+")")),i[n]},assign:function(e,t){if(e)return this.current().body.push(e,"=",t,";"),e},filter:function(e){return this.state.filters.hasOwnProperty(e)||(this.state.filters[e]=this.nextId(!0)),this.state.filters[e]},ifDefined:function(e,t){return"ifDefined("+e+","+this.escape(t)+")"},plus:function(e,t){return"plus("+e+","+t+")"},return_:function(e){this.current().body.push("return ",e,";")},if_:function(e,t,n){if(e===!0)t();else{var i=this.current().body;i.push("if(",e,"){"),t(),i.push("}"),n&&(i.push("else{"),n(),i.push("}"))}},not:function(e){return"!("+e+")"},notNull:function(e){return e+"!=null"},nonComputedMember:function(e,t){var n=/^[$_a-zA-Z][$_a-zA-Z0-9]*$/,i=/[^$_a-zA-Z0-9]/g;return n.test(t)?e+"."+t:e+'["'+t.replace(i,this.stringEscapeFn)+'"]'},computedMember:function(e,t){return e+"["+t+"]"},member:function(e,t,n){return n?this.computedMember(e,t):this.nonComputedMember(e,t)},addEnsureSafeObject:function(e){this.current().body.push(this.ensureSafeObject(e),";")},addEnsureSafeMemberName:function(e){this.current().body.push(this.ensureSafeMemberName(e),";")},addEnsureSafeFunction:function(e){this.current().body.push(this.ensureSafeFunction(e),";")},addEnsureSafeAssignContext:function(e){this.current().body.push(this.ensureSafeAssignContext(e),";")},ensureSafeObject:function(e){return"ensureSafeObject("+e+",text)"},ensureSafeMemberName:function(e){return"ensureSafeMemberName("+e+",text)"},ensureSafeFunction:function(e){return"ensureSafeFunction("+e+",text)"},getStringValue:function(e){this.assign(e,"getStringValue("+e+")")},ensureSafeAssignContext:function(e){return"ensureSafeAssignContext("+e+",text)"},lazyRecurse:function(e,t,n,i,r,a){var s=this;return function(){s.recurse(e,t,n,i,r,a)}},lazyAssign:function(e,t){var n=this;return function(){n.assign(e,t)}},stringEscapeRegex:/[^ a-zA-Z0-9]/g,stringEscapeFn:function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)},escape:function(e){if(S(e))return"'"+e.replace(this.stringEscapeRegex,this.stringEscapeFn)+"'";if(M(e))return e.toString();if(e===!0)return"true";if(e===!1)return"false";if(null===e)return"null";if("undefined"==typeof e)return"undefined";throw Sa("esc","IMPOSSIBLE")},nextId:function(e,t){var n="v"+this.state.nextId++;return e||this.current().vars.push(n+(t?"="+t:"")),n},current:function(){return this.state[this.state.computing]}},vn.prototype={compile:function(e,t){var n=this,r=this.astBuilder.ast(e);this.expression=e,this.expensiveChecks=t,cn(r,n.$filter);var a,s;(a=dn(r))&&(s=this.recurse(a));var o,u=pn(r.body);u&&(o=[],i(u,function(e,t){var i=n.recurse(e);e.input=i,o.push(i),e.watchId=t}));var l=[];i(r.body,function(e){l.push(n.recurse(e.expression))});var c=0===r.body.length?d:1===r.body.length?l[0]:function(e,t){var n;return i(l,function(i){n=i(e,t)}),n};return s&&(c.assign=function(e,t,n){return s(e,n,t)}),o&&(c.inputs=o),c.literal=fn(r),c.constant=gn(r),c},recurse:function(e,t,n){var r,a,s,o=this;if(e.input)return this.inputs(e.input,e.watchId);switch(e.type){case Ua.Literal:return this.value(e.value,t);case Ua.UnaryExpression:return a=this.recurse(e.argument),this["unary"+e.operator](a,t);case Ua.BinaryExpression:return r=this.recurse(e.left),a=this.recurse(e.right),this["binary"+e.operator](r,a,t);case Ua.LogicalExpression:return r=this.recurse(e.left),a=this.recurse(e.right),this["binary"+e.operator](r,a,t);case Ua.ConditionalExpression:return this["ternary?:"](this.recurse(e.test),this.recurse(e.alternate),this.recurse(e.consequent),t);case Ua.Identifier:return tn(e.name,o.expression),o.identifier(e.name,o.expensiveChecks||$n(e.name),t,n,o.expression);case Ua.MemberExpression:return r=this.recurse(e.object,!1,!!n),e.computed||(tn(e.property.name,o.expression),a=e.property.name),e.computed&&(a=this.recurse(e.property)),e.computed?this.computedMember(r,a,t,n,o.expression):this.nonComputedMember(r,a,o.expensiveChecks,t,n,o.expression);case Ua.CallExpression:return s=[],i(e.arguments,function(e){s.push(o.recurse(e))}),e.filter&&(a=this.$filter(e.callee.name)),e.filter||(a=this.recurse(e.callee,!0)),e.filter?function(e,n,i,r){for(var o=[],u=0;u<s.length;++u)o.push(s[u](e,n,i,r));var l=a.apply(void 0,o,r);return t?{context:void 0,name:void 0,value:l}:l}:function(e,n,i,r){var u,l=a(e,n,i,r);if(null!=l.value){rn(l.context,o.expression),an(l.value,o.expression);for(var c=[],p=0;p<s.length;++p)c.push(rn(s[p](e,n,i,r),o.expression));u=rn(l.value.apply(l.context,c),o.expression)}return t?{value:u}:u};case Ua.AssignmentExpression:return r=this.recurse(e.left,!0,1),a=this.recurse(e.right),function(e,n,i,s){var u=r(e,n,i,s),l=a(e,n,i,s);return rn(u.value,o.expression),sn(u.context),u.context[u.name]=l,t?{value:l}:l};case Ua.ArrayExpression:return s=[],i(e.elements,function(e){s.push(o.recurse(e))}),function(e,n,i,r){for(var a=[],o=0;o<s.length;++o)a.push(s[o](e,n,i,r));return t?{value:a}:a};case Ua.ObjectExpression:return s=[],i(e.properties,function(e){e.computed?s.push({key:o.recurse(e.key),computed:!0,value:o.recurse(e.value)}):s.push({key:e.key.type===Ua.Identifier?e.key.name:""+e.key.value,computed:!1,value:o.recurse(e.value)})}),function(e,n,i,r){for(var a={},o=0;o<s.length;++o)s[o].computed?a[s[o].key(e,n,i,r)]=s[o].value(e,n,i,r):a[s[o].key]=s[o].value(e,n,i,r);return t?{value:a}:a};case Ua.ThisExpression:return function(e){return t?{value:e}:e};case Ua.LocalsExpression:return function(e,n){return t?{value:n}:n};case Ua.NGValueParameter:return function(e,n,i){return t?{value:i}:i}}},"unary+":function(e,t){return function(n,i,r,a){var s=e(n,i,r,a);return s=$(s)?+s:0,t?{value:s}:s}},"unary-":function(e,t){return function(n,i,r,a){var s=e(n,i,r,a);return s=$(s)?-s:0,t?{value:s}:s}},"unary!":function(e,t){return function(n,i,r,a){var s=!e(n,i,r,a);return t?{value:s}:s}},"binary+":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s),u=t(i,r,a,s),l=un(o,u);return n?{value:l}:l}},"binary-":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s),u=t(i,r,a,s),l=($(o)?o:0)-($(u)?u:0);return n?{value:l}:l}},"binary*":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)*t(i,r,a,s);return n?{value:o}:o}},"binary/":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)/t(i,r,a,s);return n?{value:o}:o}},"binary%":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)%t(i,r,a,s);return n?{value:o}:o}},"binary===":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)===t(i,r,a,s);return n?{value:o}:o}},"binary!==":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)!==t(i,r,a,s);return n?{value:o}:o}},"binary==":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)==t(i,r,a,s);return n?{value:o}:o}},"binary!=":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)!=t(i,r,a,s);return n?{value:o}:o}},"binary<":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)<t(i,r,a,s);return n?{value:o}:o}},"binary>":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)>t(i,r,a,s);return n?{value:o}:o}},"binary<=":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)<=t(i,r,a,s);return n?{value:o}:o}},"binary>=":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)>=t(i,r,a,s);return n?{value:o}:o}},"binary&&":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)&&t(i,r,a,s);return n?{value:o}:o}},"binary||":function(e,t,n){return function(i,r,a,s){var o=e(i,r,a,s)||t(i,r,a,s);return n?{value:o}:o}},"ternary?:":function(e,t,n,i){return function(r,a,s,o){var u=e(r,a,s,o)?t(r,a,s,o):n(r,a,s,o);return i?{value:u}:u}},value:function(e,t){return function(){return t?{context:void 0,name:void 0,value:e}:e}},identifier:function(e,t,n,i,r){return function(a,s,o,u){var l=s&&e in s?s:a;i&&1!==i&&l&&!l[e]&&(l[e]={});var c=l?l[e]:void 0;return t&&rn(c,r),n?{context:l,name:e,value:c}:c}},computedMember:function(e,t,n,i,r){return function(a,s,o,u){var l,c,p=e(a,s,o,u);return null!=p&&(l=t(a,s,o,u),l=nn(l),tn(l,r),i&&1!==i&&(sn(p),p&&!p[l]&&(p[l]={})),c=p[l],rn(c,r)),n?{context:p,name:l,value:c}:c}},nonComputedMember:function(e,t,n,i,r,a){return function(s,o,u,l){var c=e(s,o,u,l);r&&1!==r&&(sn(c),c&&!c[t]&&(c[t]={}));var p=null!=c?c[t]:void 0;return(n||$n(t))&&rn(p,a),i?{context:c,name:t,value:p}:p}},inputs:function(e,t){return function(n,i,r,a){return a?a[t]:e(n,i,r)}}};var Fa=function(e,t,n){this.lexer=e,this.$filter=t,this.options=n,this.ast=new Ua(e,n),this.astCompiler=n.csp?new vn(this.ast,t):new mn(this.ast,t)};Fa.prototype={constructor:Fa,parse:function(e){return this.astCompiler.compile(e,this.options.expensiveChecks)}};var qa=t("$sce"),Ga={HTML:"html",CSS:"css",URL:"url",RESOURCE_URL:"resourceUrl",JS:"js"},Ha=t("$compile"),za=e.document.createElement("a"),Wa=On(e.location.href);Vn.$inject=["$document"],jn.$inject=["$provide"];var Ya=22,Ja=".",Ka="0";Hn.$inject=["$locale"],zn.$inject=["$locale"];var Za={yyyy:Zn("FullYear",4,0,!1,!0),yy:Zn("FullYear",2,0,!0,!0),y:Zn("FullYear",1,0,!1,!0),MMMM:Qn("Month"),MMM:Qn("Month",!0),MM:Zn("Month",2,1),M:Zn("Month",1,1),LLLL:Qn("Month",!1,!0),dd:Zn("Date",2),d:Zn("Date",1),HH:Zn("Hours",2),H:Zn("Hours",1),hh:Zn("Hours",2,-12),h:Zn("Hours",1,-12),mm:Zn("Minutes",2),m:Zn("Minutes",1),ss:Zn("Seconds",2),s:Zn("Seconds",1),sss:Zn("Milliseconds",3),EEEE:Qn("Day"),EEE:Qn("Day",!0),a:ii,Z:Xn,ww:ni(2),w:ni(1),G:ri,GG:ri,GGG:ri,GGGG:ai},Qa=/((?:[^yMLdHhmsaZEwG']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|m+|s+|a|Z|G+|w+))(.*)/,Xa=/^-?\d+$/;si.$inject=["$locale"];var es=g(ji),ts=g(Ui);ci.$inject=["$parse"];var ns=g({restrict:"E",compile:function(e,t){if(!t.href&&!t.xlinkHref)return function(e,t){if("a"===t[0].nodeName.toLowerCase()){var n="[object SVGAnimatedString]"===Zi.call(t.prop("href"))?"xlink:href":"href";t.on("click",function(e){t.attr(n)||e.preventDefault()})}}}}),is={};i(Nr,function(e,t){function n(e,n,r){e.$watch(r[i],function(e){r.$set(t,!!e)})}if("multiple"!==e){var i=vt("ng-"+t),r=n;"checked"===e&&(r=function(e,t,r){r.ngModel!==r[i]&&n(e,t,r)}),is[i]=function(){return{restrict:"A",priority:100,link:r}}}}),i(Vr,function(e,t){is[t]=function(){return{priority:100,link:function(e,n,i){if("ngPattern"===t&&"/"===i.ngPattern.charAt(0)){var r=i.ngPattern.match(Ri);if(r)return void i.$set("ngPattern",new RegExp(r[1],r[2]))}e.$watch(i[t],function(e){i.$set(t,e)})}}}}),i(["src","srcset","href"],function(e){var t=vt("ng-"+e);is[t]=function(){return{priority:99,link:function(n,i,r){var a=e,s=e;"href"===e&&"[object SVGAnimatedString]"===Zi.call(i.prop("href"))&&(s="xlinkHref",r.$attr[s]="xlink:href",a=null),r.$observe(t,function(t){return t?(r.$set(s,t),void(Gi&&a&&i.prop(a,r[s]))):void("href"===e&&r.$set(s,null))})}}}});var rs={$addControl:d,$$renameControl:hi,$removeControl:d,$setValidity:d,$setDirty:d,$setPristine:d,$setSubmitted:d},as="ng-submitted";di.$inject=["$element","$attrs","$scope","$animate","$interpolate"];var ss=function(e){return["$timeout","$parse",function(t,n){function i(e){return""===e?n('this[""]').assign:n(e).assign||d}var r={name:"form",restrict:e?"EAC":"E",require:["form","^^?form"],controller:di,compile:function(n,r){n.addClass(Fs).addClass(js);var a=r.name?"name":!(!e||!r.ngForm)&&"ngForm";return{pre:function(e,n,r,s){var o=s[0];if(!("action"in r)){var u=function(t){e.$apply(function(){o.$commitViewValue(),o.$setSubmitted()}),t.preventDefault()};Tr(n[0],"submit",u),n.on("$destroy",function(){t(function(){xr(n[0],"submit",u)},0,!1)})}var c=s[1]||o.$$parentForm;c.$addControl(o);var p=a?i(o.$name):d;a&&(p(e,o),r.$observe(a,function(t){o.$name!==t&&(p(e,void 0),o.$$parentForm.$$renameControl(o,t),(p=i(o.$name))(e,o))})),n.on("$destroy",function(){o.$$parentForm.$removeControl(o),p(e,void 0),l(o,rs)})}}}};return r}]},os=ss(),us=ss(!0),ls=/^\d{4,}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+(?:[+-][0-2]\d:[0-5]\d|Z)$/,cs=/^[a-z][a-z\d.+-]*:\/*(?:[^:@]+(?::[^@]+)?@)?(?:[^\s:\/?#]+|\[[a-f\d:]+])(?::\d+)?(?:\/[^?#]*)?(?:\?[^#]*)?(?:#.*)?$/i,ps=/^(?=.{1,254}$)(?=.{1,64}@)[-!#$%&'*+\/0-9=?A-Z^_`a-z{|}~]+(\.[-!#$%&'*+\/0-9=?A-Z^_`a-z{|}~]+)*@[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?(\.[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*$/,hs=/^\s*(-|\+)?(\d+|(\d*(\.\d*)))([eE][+-]?\d+)?\s*$/,ds=/^(\d{4,})-(\d{2})-(\d{2})$/,fs=/^(\d{4,})-(\d\d)-(\d\d)T(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,gs=/^(\d{4,})-W(\d\d)$/,ms=/^(\d{4,})-(\d\d)$/,vs=/^(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,$s="keydown wheel mousedown",ys=ge();i("date,datetime-local,month,time,week".split(","),function(e){ys[e]=!0});var bs={text:gi,date:yi("date",ds,$i(ds,["yyyy","MM","dd"]),"yyyy-MM-dd"),"datetime-local":yi("datetimelocal",fs,$i(fs,["yyyy","MM","dd","HH","mm","ss","sss"]),"yyyy-MM-ddTHH:mm:ss.sss"),time:yi("time",vs,$i(vs,["HH","mm","ss","sss"]),"HH:mm:ss.sss"),week:yi("week",gs,vi,"yyyy-Www"),month:yi("month",ms,$i(ms,["yyyy","MM"]),"yyyy-MM"),number:Ci,url:ki,email:_i,radio:Ai,range:Pi,checkbox:Ei,hidden:d,button:d,submit:d,reset:d,file:d},Ss=["$browser","$sniffer","$filter","$parse",function(e,t,n,i){return{restrict:"E",require:["?ngModel"],link:{pre:function(r,a,s,o){if(o[0]){var u=ji(s.type);"range"!==u||s.hasOwnProperty("ngInputRange")||(u="text"),(bs[u]||bs.text)(r,a,s,o[0],t,e,n,i)}}}}}],Ms=/^(true|false|\d+)$/,Ts=function(){return{restrict:"A",priority:100,compile:function(e,t){return Ms.test(t.ngValue)?function(e,t,n){n.$set("value",e.$eval(n.ngValue))}:function(e,t,n){e.$watch(n.ngValue,function(e){n.$set("value",e)})}}}},xs=["$compile",function(e){return{restrict:"AC",compile:function(t){return e.$$addBindingClass(t),function(t,n,i){e.$$addBindingInfo(n,i.ngBind),n=n[0],t.$watch(i.ngBind,function(e){n.textContent=v(e)?"":e})}}}}],ws=["$interpolate","$compile",function(e,t){return{compile:function(n){return t.$$addBindingClass(n),function(n,i,r){var a=e(i.attr(r.$attr.ngBindTemplate));t.$$addBindingInfo(i,a.expressions),i=i[0],r.$observe("ngBindTemplate",function(e){i.textContent=v(e)?"":e})}}}}],Cs=["$sce","$parse","$compile",function(e,t,n){return{restrict:"A",compile:function(i,r){var a=t(r.ngBindHtml),s=t(r.ngBindHtml,function(t){return e.valueOf(t)});return n.$$addBindingClass(i),function(t,i,r){n.$$addBindingInfo(i,r.ngBindHtml),t.$watch(s,function(){var n=a(t);i.html(e.getTrustedHtml(n)||"")})}}}}],Ps=g({restrict:"A",require:"ngModel",link:function(e,t,n,i){i.$viewChangeListeners.push(function(){e.$eval(n.ngChange)})}}),ks=Bi("",!0),_s=Bi("Odd",0),As=Bi("Even",1),Is=pi({compile:function(e,t){t.$set("ngCloak",void 0),e.removeClass("ng-cloak")}}),Es=[function(){return{restrict:"A",scope:!0,controller:"@",priority:500}}],Bs={},Ls={blur:!0,focus:!0};i("click dblclick mousedown mouseup mouseover mouseout mousemove mouseenter mouseleave keydown keyup keypress submit focus blur copy cut paste".split(" "),function(e){var t=vt("ng-"+e);Bs[t]=["$parse","$rootScope",function(n,i){return{restrict:"A",compile:function(r,a){var s=n(a[t],null,!0);return function(t,n){n.on(e,function(n){var r=function(){s(t,{$event:n})};Ls[e]&&i.$$phase?t.$evalAsync(r):t.$apply(r)})}}}}]});var Os=["$animate","$compile",function(e,t){return{multiElement:!0,transclude:"element",priority:600,terminal:!0,restrict:"A",$$tlb:!0,link:function(n,i,r,a,s){var o,u,l;n.$watch(r.ngIf,function(n){n?u||s(function(n,a){u=a,n[n.length++]=t.$$createComment("end ngIf",r.ngIf),o={clone:n},e.enter(n,i.parent(),i)}):(l&&(l.remove(),l=null),u&&(u.$destroy(),u=null),o&&(l=fe(o.clone),e.leave(l).done(function(e){e!==!1&&(l=null)}),o=null))})}}}],Ns=["$templateRequest","$anchorScroll","$animate",function(e,t,n){return{restrict:"ECA",priority:400,terminal:!0,transclude:"element",controller:er.noop,compile:function(i,r){var a=r.ngInclude||r.src,s=r.onload||"",o=r.autoscroll;return function(i,r,u,l,c){var p,h,d,f=0,g=function(){h&&(h.remove(),h=null),p&&(p.$destroy(),p=null),d&&(n.leave(d).done(function(e){e!==!1&&(h=null)}),h=d,d=null)};i.$watch(a,function(a){var u=function(e){e===!1||!$(o)||o&&!i.$eval(o)||t()},h=++f;a?(e(a,!0).then(function(e){if(!i.$$destroyed&&h===f){var t=i.$new();l.template=e;var o=c(t,function(e){g(),n.enter(e,null,r).done(u)});p=t,d=o,p.$emit("$includeContentLoaded",a),i.$eval(s)}},function(){i.$$destroyed||h===f&&(g(),i.$emit("$includeContentError",a))}),i.$emit("$includeContentRequested",a)):(g(),l.template=null)})}}}}],Rs=["$compile",function(t){return{restrict:"ECA",priority:-400,require:"ngInclude",link:function(n,i,r,a){return Zi.call(i[0]).match(/SVG/)?(i.empty(),void t(Pe(a.template,e.document).childNodes)(n,function(e){i.append(e)},{futureParentElement:i})):(i.html(a.template),void t(i.contents())(n))}}}],Vs=pi({priority:450,compile:function(){return{pre:function(e,t,n){e.$eval(n.ngInit)}}}}),Ds=function(){return{restrict:"A",priority:100,require:"ngModel",link:function(e,t,n,r){var a=t.attr(n.$attr.ngList)||", ",s="false"!==n.ngTrim,o=s?ar(a):a,u=function(e){if(!v(e)){var t=[];return e&&i(e.split(o),function(e){e&&t.push(s?ar(e):e)}),t}};r.$parsers.push(u),r.$formatters.push(function(e){if(ir(e))return e.join(a)}),r.$isEmpty=function(e){return!e||!e.length}}}},js="ng-valid",Us="ng-invalid",Fs="ng-pristine",qs="ng-dirty",Gs="ng-untouched",Hs="ng-touched",zs="ng-pending",Ws="ng-empty",Ys="ng-not-empty",Js=t("ngModel"),Ks=["$scope","$exceptionHandler","$attrs","$element","$parse","$animate","$timeout","$rootScope","$q","$interpolate",function(e,t,n,r,a,s,o,u,l,c){this.$viewValue=Number.NaN,this.$modelValue=Number.NaN,this.$$rawModelValue=void 0,this.$validators={},this.$asyncValidators={},this.$parsers=[],this.$formatters=[],this.$viewChangeListeners=[],this.$untouched=!0,this.$touched=!1,this.$pristine=!0,this.$dirty=!1,this.$valid=!0,this.$invalid=!1,this.$error={},this.$$success={},this.$pending=void 0,this.$name=c(n.name||"",!1)(e),this.$$parentForm=rs;var p,h=a(n.ngModel),f=h.assign,g=h,m=f,y=null,b=this;this.$$setOptions=function(e){if(b.$options=e,e&&e.getterSetter){var t=a(n.ngModel+"()"),i=a(n.ngModel+"($$$p)");g=function(e){var n=h(e);return x(n)&&(n=t(e)),n},m=function(e,t){x(h(e))?i(e,{$$$p:t}):f(e,t)}}else if(!h.assign)throw Js("nonassign","Expression '{0}' is non-assignable. Element: {1}",n.ngModel,K(r))},this.$render=d,this.$isEmpty=function(e){return v(e)||""===e||null===e||e!==e},this.$$updateEmptyClasses=function(e){b.$isEmpty(e)?(s.removeClass(r,Ys),s.addClass(r,Ws)):(s.removeClass(r,Ws),s.addClass(r,Ys))};var S=0;Li({ctrl:this,$element:r,set:function(e,t){e[t]=!0},unset:function(e,t){delete e[t]},$animate:s}),this.$setPristine=function(){b.$dirty=!1,b.$pristine=!0,s.removeClass(r,qs),s.addClass(r,Fs)},this.$setDirty=function(){b.$dirty=!0,b.$pristine=!1,s.removeClass(r,Fs),s.addClass(r,qs),b.$$parentForm.$setDirty()},this.$setUntouched=function(){b.$touched=!1,b.$untouched=!0,s.setClass(r,Gs,Hs)},this.$setTouched=function(){b.$touched=!0,b.$untouched=!1,s.setClass(r,Hs,Gs)},this.$rollbackViewValue=function(){o.cancel(y),b.$viewValue=b.$$lastCommittedViewValue,b.$render()},this.$validate=function(){if(!nr(b.$modelValue)){var e=b.$$lastCommittedViewValue,t=b.$$rawModelValue,n=b.$valid,i=b.$modelValue,r=b.$options&&b.$options.allowInvalid;b.$$runValidators(t,e,function(e){r||n===e||(b.$modelValue=e?t:void 0,b.$modelValue!==i&&b.$$writeModelToScope())})}},this.$$runValidators=function(e,t,n){function r(){var e=b.$$parserName||"parse";return v(p)?(o(e,null),!0):(p||(i(b.$validators,function(e,t){o(t,null)}),i(b.$asyncValidators,function(e,t){o(t,null)})),o(e,p),p)}function a(){var n=!0;return i(b.$validators,function(i,r){var a=i(e,t);n=n&&a,o(r,a)}),!!n||(i(b.$asyncValidators,function(e,t){o(t,null)}),!1)}function s(){var n=[],r=!0;i(b.$asyncValidators,function(i,a){var s=i(e,t);if(!E(s))throw Js("nopromise","Expected asynchronous validator to return a promise but got '{0}' instead.",s);o(a,void 0),n.push(s.then(function(){o(a,!0)},function(){r=!1,o(a,!1)}))}),n.length?l.all(n).then(function(){u(r)},d):u(!0)}function o(e,t){c===S&&b.$setValidity(e,t)}function u(e){c===S&&n(e)}S++;var c=S;return r()&&a()?void s():void u(!1)},this.$commitViewValue=function(){var e=b.$viewValue;o.cancel(y),(b.$$lastCommittedViewValue!==e||""===e&&b.$$hasNativeValidators)&&(b.$$updateEmptyClasses(e),b.$$lastCommittedViewValue=e,b.$pristine&&this.$setDirty(),this.$$parseAndValidate())},this.$$parseAndValidate=function(){function t(){b.$modelValue!==a&&b.$$writeModelToScope()}var n=b.$$lastCommittedViewValue,i=n;if(p=!v(i)||void 0)for(var r=0;r<b.$parsers.length;r++)if(i=b.$parsers[r](i),v(i)){p=!1;break}nr(b.$modelValue)&&(b.$modelValue=g(e));var a=b.$modelValue,s=b.$options&&b.$options.allowInvalid;b.$$rawModelValue=i,s&&(b.$modelValue=i,t()),b.$$runValidators(i,b.$$lastCommittedViewValue,function(e){s||(b.$modelValue=e?i:void 0,t())})},this.$$writeModelToScope=function(){m(e,b.$modelValue),i(b.$viewChangeListeners,function(e){try{e()}catch(n){t(n)}})},this.$setViewValue=function(e,t){b.$viewValue=e,b.$options&&!b.$options.updateOnDefault||b.$$debounceViewValueCommit(t)},this.$$debounceViewValueCommit=function(t){var n,i=0,r=b.$options;r&&$(r.debounce)&&(n=r.debounce,M(n)?i=n:M(n[t])?i=n[t]:M(n["default"])&&(i=n["default"])),o.cancel(y),i?y=o(function(){b.$commitViewValue()},i):u.$$phase?b.$commitViewValue():e.$apply(function(){b.$commitViewValue()})},e.$watch(function(){var t=g(e);if(t!==b.$modelValue&&(b.$modelValue===b.$modelValue||t===t)){b.$modelValue=b.$$rawModelValue=t,p=void 0;for(var n=b.$formatters,i=n.length,r=t;i--;)r=n[i](r);b.$viewValue!==r&&(b.$$updateEmptyClasses(r),b.$viewValue=b.$$lastCommittedViewValue=r,b.$render(),b.$$runValidators(b.$modelValue,b.$viewValue,d))}return t})}],Zs=["$rootScope",function(e){return{restrict:"A",require:["ngModel","^?form","^?ngModelOptions"],controller:Ks,priority:1,compile:function(t){return t.addClass(Fs).addClass(Gs).addClass(js),{pre:function(e,t,n,i){var r=i[0],a=i[1]||r.$$parentForm;r.$$setOptions(i[2]&&i[2].$options),a.$addControl(r),n.$observe("name",function(e){r.$name!==e&&r.$$parentForm.$$renameControl(r,e)}),e.$on("$destroy",function(){r.$$parentForm.$removeControl(r)})},post:function(t,n,i,r){var a=r[0];a.$options&&a.$options.updateOn&&n.on(a.$options.updateOn,function(e){a.$$debounceViewValueCommit(e&&e.type)}),n.on("blur",function(){a.$touched||(e.$$phase?t.$evalAsync(a.$setTouched):t.$apply(a.$setTouched))})}}}}}],Qs=/(\s+|^)default(\s+|$)/,Xs=function(){return{restrict:"A",controller:["$scope","$attrs",function(e,t){var n=this;this.$options=D(e.$eval(t.ngModelOptions)),$(this.$options.updateOn)?(this.$options.updateOnDefault=!1,this.$options.updateOn=ar(this.$options.updateOn.replace(Qs,function(){return n.$options.updateOnDefault=!0," "}))):this.$options.updateOnDefault=!0}]}},eo=pi({terminal:!0,priority:1e3}),to=t("ngOptions"),no=/^\s*([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+group\s+by\s+([\s\S]+?))?(?:\s+disable\s+when\s+([\s\S]+?))?\s+for\s+(?:([$\w][$\w]*)|(?:\(\s*([$\w][$\w]*)\s*,\s*([$\w][$\w]*)\s*\)))\s+in\s+([\s\S]+?)(?:\s+track\s+by\s+([\s\S]+?))?$/,io=["$compile","$document","$parse",function(t,r,a){function s(e,t,i){function r(e,t,n,i,r){this.selectValue=e,this.viewValue=t,this.label=n,this.group=i,this.disabled=r}function s(e){var t;if(!l&&n(e))t=e;else{t=[];for(var i in e)e.hasOwnProperty(i)&&"$"!==i.charAt(0)&&t.push(i)}return t}var o=e.match(no);if(!o)throw to("iexp","Expected expression in form of '_select_ (as _label_)? for (_key_,)?_value_ in _collection_' but got '{0}'. Element: {1}",e,K(t));var u=o[5]||o[7],l=o[6],c=/ as /.test(o[0])&&o[1],p=o[9],h=a(o[2]?o[1]:u),d=c&&a(c),f=d||h,g=p&&a(p),m=p?function(e,t){return g(i,t)}:function(e){return Qe(e)},v=function(e,t){return m(e,T(e,t))},$=a(o[2]||o[1]),y=a(o[3]||""),b=a(o[4]||""),S=a(o[8]),M={},T=l?function(e,t){return M[l]=t,M[u]=e,M}:function(e){return M[u]=e,M};return{trackBy:p,getTrackByValue:v,getWatchables:a(S,function(e){var t=[];e=e||[];for(var n=s(e),r=n.length,a=0;a<r;a++){var u=e===n?a:n[a],l=e[u],c=T(l,u),p=m(l,c);if(t.push(p),o[2]||o[1]){var h=$(i,c);t.push(h)}if(o[4]){var d=b(i,c);t.push(d)}}return t}),getOptions:function(){for(var e=[],t={},n=S(i)||[],a=s(n),o=a.length,u=0;u<o;u++){var l=n===a?u:a[u],c=n[l],h=T(c,l),d=f(i,h),g=m(d,h),M=$(i,h),x=y(i,h),w=b(i,h),C=new r(g,d,M,x,w);e.push(C),t[g]=C}return{items:e,selectValueMap:t,getOptionFromViewValue:function(e){return t[v(e)]},getViewValueFromOption:function(e){return p?D(e.viewValue):e.viewValue}}}}}function o(e,n,a,o){function c(e,t){var n=u.cloneNode(!1);t.appendChild(n),p(e,n)}function p(e,t){e.element=t,t.disabled=e.disabled,e.label!==t.label&&(t.label=e.label,t.textContent=e.label),t.value=e.selectValue}function h(){var e=x&&f.readValue();if(x)for(var t=x.items.length-1;t>=0;t--){var i=x.items[t];Ge($(i.group)?i.element.parentNode:i.element)}x=w.getOptions();var r={};if(S&&n.prepend(d),x.items.forEach(function(e){var t;$(e.group)?(t=r[e.group],t||(t=l.cloneNode(!1),C.appendChild(t),t.label=null===e.group?"null":e.group,r[e.group]=t),c(e,t)):c(e,C)}),n[0].appendChild(C),g.$render(),!g.$isEmpty(e)){var a=f.readValue(),s=w.trackBy||m;(s?j(e,a):e===a)||(g.$setViewValue(a),g.$render())}}for(var d,f=o[0],g=o[1],m=a.multiple,v=0,y=n.children(),b=y.length;v<b;v++)if(""===y[v].value){d=y.eq(v);break}var S=!!d,M=!1,T=Hi(u.cloneNode(!1));T.val("?");var x,w=s(a.ngOptions,n,e),C=r[0].createDocumentFragment(),P=function(){S||n.prepend(d),n.val(""),M&&(d.prop("selected",!0),d.attr("selected",!0))},k=function(){S?M&&d.removeAttr("selected"):d.remove()},_=function(){n.prepend(T),n.val("?"),T.prop("selected",!0),T.attr("selected",!0)},A=function(){T.remove()};m?(g.$isEmpty=function(e){return!e||0===e.length},f.writeValue=function(e){x.items.forEach(function(e){e.element.selected=!1}),e&&e.forEach(function(e){var t=x.getOptionFromViewValue(e);t&&(t.element.selected=!0)})},f.readValue=function(){var e=n.val()||[],t=[];return i(e,function(e){var n=x.selectValueMap[e];n&&!n.disabled&&t.push(x.getViewValueFromOption(n))}),t},w.trackBy&&e.$watchCollection(function(){if(ir(g.$viewValue))return g.$viewValue.map(function(e){return w.getTrackByValue(e)})},function(){g.$render()})):(f.writeValue=function(e){
var t=x.selectValueMap[n.val()],i=x.getOptionFromViewValue(e);t&&t.element.removeAttribute("selected"),i?(n[0].value!==i.selectValue&&(A(),k(),n[0].value=i.selectValue,i.element.selected=!0),i.element.setAttribute("selected","selected")):null===e||S?(A(),P()):(k(),_())},f.readValue=function(){var e=x.selectValueMap[n.val()];return e&&!e.disabled?(k(),A(),x.getViewValueFromOption(e)):null},w.trackBy&&e.$watch(function(){return w.getTrackByValue(g.$viewValue)},function(){g.$render()})),S?(d.remove(),t(d)(e),d[0].nodeType===vr?(M=!1,f.registerOption=function(e,t){""===t.val()&&(M=!0,d=t,d.removeClass("ng-scope"),g.$render(),t.on("$destroy",function(){d=void 0,M=!1}))}):(d.removeClass("ng-scope"),M=!0)):d=Hi(u.cloneNode(!1)),n.empty(),h(),e.$watchCollection(w.getWatchables,h)}var u=e.document.createElement("option"),l=e.document.createElement("optgroup");return{restrict:"A",terminal:!0,require:["select","ngModel"],link:{pre:function(e,t,n,i){i[0].registerOption=d},post:o}}}],ro=["$locale","$interpolate","$log",function(e,t,n){var r=/{}/g,a=/^when(Minus)?(.+)$/;return{link:function(s,o,u){function l(e){o.text(e||"")}var c,p=u.count,h=u.$attr.when&&o.attr(u.$attr.when),f=u.offset||0,g=s.$eval(h)||{},m={},$=t.startSymbol(),y=t.endSymbol(),b=$+p+"-"+f+y,S=er.noop;i(u,function(e,t){var n=a.exec(t);if(n){var i=(n[1]?"-":"")+ji(n[2]);g[i]=o.attr(u.$attr[t])}}),i(g,function(e,n){m[n]=t(e.replace(r,b))}),s.$watch(p,function(t){var i=parseFloat(t),r=nr(i);if(r||i in g||(i=e.pluralCat(i-f)),!(i===c||r&&nr(c))){S();var a=m[i];v(a)?(null!=t&&n.debug("ngPluralize: no rule defined for '"+i+"' in "+h),S=d,l()):S=s.$watch(a,l),c=i}})}}}],ao=["$parse","$animate","$compile",function(e,r,a){var s="$$NG_REMOVED",o=t("ngRepeat"),u=function(e,t,n,i,r,a,s){e[n]=i,r&&(e[r]=a),e.$index=t,e.$first=0===t,e.$last=t===s-1,e.$middle=!(e.$first||e.$last),e.$odd=!(e.$even=0===(1&t))},l=function(e){return e.clone[0]},c=function(e){return e.clone[e.clone.length-1]};return{restrict:"A",multiElement:!0,transclude:"element",priority:1e3,terminal:!0,$$tlb:!0,compile:function(t,p){var h=p.ngRepeat,d=a.$$createComment("end ngRepeat",h),f=h.match(/^\s*([\s\S]+?)\s+in\s+([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+track\s+by\s+([\s\S]+?))?\s*$/);if(!f)throw o("iexp","Expected expression in form of '_item_ in _collection_[ track by _id_]' but got '{0}'.",h);var g=f[1],m=f[2],v=f[3],$=f[4];if(f=g.match(/^(?:(\s*[$\w]+)|\(\s*([$\w]+)\s*,\s*([$\w]+)\s*\))$/),!f)throw o("iidexp","'_item_' in '_item_ in _collection_' should be an identifier or '(_key_, _value_)' expression, but got '{0}'.",g);var y=f[3]||f[1],b=f[2];if(v&&(!/^[$a-zA-Z_][$a-zA-Z0-9_]*$/.test(v)||/^(null|undefined|this|\$index|\$first|\$middle|\$last|\$even|\$odd|\$parent|\$root|\$id)$/.test(v)))throw o("badident","alias '{0}' is invalid --- must be a valid JS identifier which is not a reserved name.",v);var S,M,T,x,w={$id:Qe};return $?S=e($):(T=function(e,t){return Qe(t)},x=function(e){return e}),function(e,t,a,p,f){S&&(M=function(t,n,i){return b&&(w[b]=t),w[y]=n,w.$index=i,S(e,w)});var g=ge();e.$watchCollection(m,function(a){var p,m,$,S,w,C,P,k,_,A,I,E,B=t[0],L=ge();if(v&&(e[v]=a),n(a))_=a,k=M||T;else{k=M||x,_=[];for(var O in a)Di.call(a,O)&&"$"!==O.charAt(0)&&_.push(O)}for(S=_.length,I=new Array(S),p=0;p<S;p++)if(w=a===_?p:_[p],C=a[w],P=k(w,C,p),g[P])A=g[P],delete g[P],L[P]=A,I[p]=A;else{if(L[P])throw i(I,function(e){e&&e.scope&&(g[e.id]=e)}),o("dupes","Duplicates in a repeater are not allowed. Use 'track by' expression to specify unique keys. Repeater: {0}, Duplicate key: {1}, Duplicate value: {2}",h,P,C);I[p]={id:P,scope:void 0,clone:void 0},L[P]=!0}for(var N in g){if(A=g[N],E=fe(A.clone),r.leave(E),E[0].parentNode)for(p=0,m=E.length;p<m;p++)E[p][s]=!0;A.scope.$destroy()}for(p=0;p<S;p++)if(w=a===_?p:_[p],C=a[w],A=I[p],A.scope){$=B;do $=$.nextSibling;while($&&$[s]);l(A)!==$&&r.move(fe(A.clone),null,B),B=c(A),u(A.scope,p,y,C,b,w,S)}else f(function(e,t){A.scope=t;var n=d.cloneNode(!1);e[e.length++]=n,r.enter(e,null,B),B=n,A.clone=e,L[A.id]=A,u(A.scope,p,y,C,b,w,S)});g=L})}}}}],so="ng-hide",oo="ng-hide-animate",uo=["$animate",function(e){return{restrict:"A",multiElement:!0,link:function(t,n,i){t.$watch(i.ngShow,function(t){e[t?"removeClass":"addClass"](n,so,{tempClasses:oo})})}}}],lo=["$animate",function(e){return{restrict:"A",multiElement:!0,link:function(t,n,i){t.$watch(i.ngHide,function(t){e[t?"addClass":"removeClass"](n,so,{tempClasses:oo})})}}}],co=pi(function(e,t,n){e.$watch(n.ngStyle,function(e,n){n&&e!==n&&i(n,function(e,n){t.css(n,"")}),e&&t.css(e)},!0)}),po=["$animate","$compile",function(e,t){return{require:"ngSwitch",controller:["$scope",function(){this.cases={}}],link:function(n,r,a,s){var o=a.ngSwitch||a.on,u=[],l=[],c=[],p=[],h=function(e,t){return function(n){n!==!1&&e.splice(t,1)}};n.$watch(o,function(n){for(var r,a;c.length;)e.cancel(c.pop());for(r=0,a=p.length;r<a;++r){var o=fe(l[r].clone);p[r].$destroy();var d=c[r]=e.leave(o);d.done(h(c,r))}l.length=0,p.length=0,(u=s.cases["!"+n]||s.cases["?"])&&i(u,function(n){n.transclude(function(i,r){p.push(r);var a=n.element;i[i.length++]=t.$$createComment("end ngSwitchWhen");var s={clone:i};l.push(s),e.enter(i,a.parent(),a)})})})}}}],ho=pi({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(e,t,n,r,a){var s=n.ngSwitchWhen.split(n.ngSwitchWhenSeparator).sort().filter(function(e,t,n){return n[t-1]!==e});i(s,function(e){r.cases["!"+e]=r.cases["!"+e]||[],r.cases["!"+e].push({transclude:a,element:t})})}}),fo=pi({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(e,t,n,i,r){i.cases["?"]=i.cases["?"]||[],i.cases["?"].push({transclude:r,element:t})}}),go=t("ngTransclude"),mo=["$compile",function(e){return{restrict:"EAC",terminal:!0,compile:function(t){var n=e(t.contents());return t.empty(),function(e,t,i,r,a){function s(e,n){e.length?t.append(e):(o(),n.$destroy())}function o(){n(e,function(e){t.append(e)})}if(!a)throw go("orphan","Illegal use of ngTransclude directive in the template! No parent directive that requires a transclusion found. Element: {0}",K(t));i.ngTransclude===i.$attr.ngTransclude&&(i.ngTransclude="");var u=i.ngTransclude||i.ngTranscludeSlot;a(s,null,u),u&&!a.isSlotFilled(u)&&o()}}}}],vo=["$templateCache",function(e){return{restrict:"E",terminal:!0,compile:function(t,n){if("text/ng-template"===n.type){var i=n.id,r=t[0].text;e.put(i,r)}}}}],$o={$setViewValue:d,$render:d},yo=["$element","$scope",function(t,n){var i=this,r=new Xe;i.ngModelCtrl=$o,i.unknownOption=Hi(e.document.createElement("option")),i.renderUnknownOption=function(e){var n="? "+Qe(e)+" ?";i.unknownOption.val(n),t.prepend(i.unknownOption),t.val(n)},n.$on("$destroy",function(){i.renderUnknownOption=d}),i.removeUnknownOption=function(){i.unknownOption.parent()&&i.unknownOption.remove()},i.readValue=function(){return i.removeUnknownOption(),t.val()},i.writeValue=function(e){i.hasOption(e)?(i.removeUnknownOption(),t.val(e),""===e&&i.emptyOption.prop("selected",!0)):null==e&&i.emptyOption?(i.removeUnknownOption(),t.val("")):i.renderUnknownOption(e)},i.addOption=function(e,t){if(t[0].nodeType!==vr){he(e,'"option value"'),""===e&&(i.emptyOption=t);var n=r.get(e)||0;r.put(e,n+1),i.ngModelCtrl.$render(),Ni(t)}},i.removeOption=function(e){var t=r.get(e);t&&(1===t?(r.remove(e),""===e&&(i.emptyOption=void 0)):r.put(e,t-1))},i.hasOption=function(e){return!!r.get(e)},i.registerOption=function(e,t,n,r,a){if(r){var s;n.$observe("value",function(e){$(s)&&i.removeOption(s),s=e,i.addOption(e,t)})}else a?e.$watch(a,function(e,r){n.$set("value",e),r!==e&&i.removeOption(r),i.addOption(e,t)}):i.addOption(n.value,t);t.on("$destroy",function(){i.removeOption(n.value),i.ngModelCtrl.$render()})}}],bo=function(){function e(e,t,n,r){var a=r[1];if(a){var s=r[0];if(s.ngModelCtrl=a,t.on("change",function(){e.$apply(function(){a.$setViewValue(s.readValue())})}),n.multiple){s.readValue=function(){var e=[];return i(t.find("option"),function(t){t.selected&&e.push(t.value)}),e},s.writeValue=function(e){var n=new Xe(e);i(t.find("option"),function(e){e.selected=$(n.get(e.value))})};var o,u=NaN;e.$watch(function(){u!==a.$viewValue||j(o,a.$viewValue)||(o=ve(a.$viewValue),a.$render()),u=a.$viewValue}),a.$isEmpty=function(e){return!e||0===e.length}}}}function t(e,t,n,i){var r=i[1];if(r){var a=i[0];r.$render=function(){a.writeValue(r.$viewValue)}}}return{restrict:"E",require:["select","?ngModel"],controller:yo,priority:1,link:{pre:e,post:t}}},So=["$interpolate",function(e){return{restrict:"E",priority:100,compile:function(t,n){var i,r;return $(n.ngValue)?i=!0:$(n.value)?i=e(n.value,!0):(r=e(t.text(),!0),r||n.$set("value",t.text())),function(e,t,n){var a="$selectController",s=t.parent(),o=s.data(a)||s.parent().data(a);o&&o.registerOption(e,t,n,i,r)}}}}],Mo=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,i){i&&(n.required=!0,i.$validators.required=function(e,t){return!n.required||!i.$isEmpty(t)},n.$observe("required",function(){i.$validate()}))}}},To=function(){return{restrict:"A",require:"?ngModel",link:function(e,n,i,r){if(r){var a,s=i.ngPattern||i.pattern;i.$observe("pattern",function(e){if(S(e)&&e.length>0&&(e=new RegExp("^"+e+"$")),e&&!e.test)throw t("ngPattern")("noregexp","Expected {0} to be a RegExp but was {1}. Element: {2}",s,e,K(n));a=e||void 0,r.$validate()}),r.$validators.pattern=function(e,t){return r.$isEmpty(t)||v(a)||a.test(t)}}}}},xo=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,i){if(i){var r=-1;n.$observe("maxlength",function(e){var t=p(e);r=nr(t)?-1:t,i.$validate()}),i.$validators.maxlength=function(e,t){return r<0||i.$isEmpty(t)||t.length<=r}}}}},wo=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,i){if(i){var r=0;n.$observe("minlength",function(e){r=p(e)||0,i.$validate()}),i.$validators.minlength=function(e,t){return i.$isEmpty(t)||t.length>=r}}}}};return e.angular.bootstrap?void(e.console&&console.log("WARNING: Tried to load angular more than once.")):(le(),be(er),er.module("ngLocale",[],["$provide",function(e){function t(e){e+="";var t=e.indexOf(".");return t==-1?0:e.length-t-1}function n(e,n){var i=n;void 0===i&&(i=Math.min(t(e),3));var r=Math.pow(10,i),a=(e*r|0)%r;return{v:i,f:a}}var i={ZERO:"zero",ONE:"one",TWO:"two",FEW:"few",MANY:"many",OTHER:"other"};e.value("$locale",{DATETIME_FORMATS:{AMPMS:["AM","PM"],DAY:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],ERANAMES:["Before Christ","Anno Domini"],ERAS:["BC","AD"],FIRSTDAYOFWEEK:6,MONTH:["January","February","March","April","May","June","July","August","September","October","November","December"],SHORTDAY:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],SHORTMONTH:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],STANDALONEMONTH:["January","February","March","April","May","June","July","August","September","October","November","December"],WEEKENDRANGE:[5,6],fullDate:"EEEE, MMMM d, y",longDate:"MMMM d, y",medium:"MMM d, y h:mm:ss a",mediumDate:"MMM d, y",mediumTime:"h:mm:ss a","short":"M/d/yy h:mm a",shortDate:"M/d/yy",shortTime:"h:mm a"},NUMBER_FORMATS:{CURRENCY_SYM:"$",DECIMAL_SEP:".",GROUP_SEP:",",PATTERNS:[{gSize:3,lgSize:3,maxFrac:3,minFrac:0,minInt:1,negPre:"-",negSuf:"",posPre:"",posSuf:""},{gSize:3,lgSize:3,maxFrac:2,minFrac:2,minInt:1,negPre:"-¤",negSuf:"",posPre:"¤",posSuf:""}]},id:"en-us",localeID:"en_US",pluralCat:function(e,t){var r=0|e,a=n(e,t);return 1==r&&0==a.v?i.ONE:i.OTHER}})}]),void Hi(e.document).ready(function(){re(e.document,ae)}))}(window),!window.angular.$$csp().noInlineStyle&&window.angular.element(document.head).prepend('<style type="text/css">@charset "UTF-8";[ng\\:cloak],[ng-cloak],[data-ng-cloak],[x-ng-cloak],.ng-cloak,.x-ng-cloak,.ng-hide:not(.ng-hide-animate){display:none !important;}ng\\:form{display:block;}.ng-animate-shim{visibility:hidden;}.ng-anchor{position:absolute;}</style>');var CustomerSetting;!function(e){"use strict";function t(e,t){var n,i,r,a,s=function(){var o=(new Date).getTime()-i;o>0&&o<t?n=setTimeout(s,t-o):(n=null,e.apply(r,a),r=null,a=null)};return r=this,a=arguments,function(){i=(new Date).getTime(),n||(n=setTimeout(s,t))}}var n=function(){function e(){}return e.format=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return e.replace(/{(\d+)}/g,function(e,n){return void 0===t[n]||null===t[n]?e:t[n]})},e}();e.StringFormater=n;var i=function(){function e(){}return e.join2DimensionalArray=function(e){var t=new Array;return e&&e.length>0&&e.forEach(function(e){e instanceof Array?t.push.apply(t,e):t.push.apply(t,[e])}),t},e}();e.ArrayHelpers=i,e.debounce=t}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";!function(e){e[e.product=1]="product",e[e.sportType=2]="sportType",e[e.betType=3]="betType",e[e.sportTypeBetType=4]="sportTypeBetType"}(e.SettingLevel||(e.SettingLevel={}));e.SettingLevel;!function(e){e[e.success=0]="success",e[e.warning=1]="warning",e[e.error=2]="error"}(e.MessageType||(e.MessageType={}));e.MessageType;!function(e){e[e.editable=1]="editable",e[e.readOnly=2]="readOnly",e[e.hidden=3]="hidden"}(e.DisplayMode||(e.DisplayMode={}));e.DisplayMode;!function(e){e[e.query=0]="query",e[e.update=1]="update",e[e.multipleUpdate=2]="multipleUpdate"}(e.ActionType||(e.ActionType={}));e.ActionType;!function(e){e[e.none=0]="none",e[e.success=1]="success",e[e.warning=2]="warning",e[e.error=3]="error"}(e.NotificationType||(e.NotificationType={}));e.NotificationType;!function(e){e[e.relative=1]="relative",e[e.absolute=2]="absolute",e[e.fixed=3]="fixed"}(e.PositionModel||(e.PositionModel={}));var t=(e.PositionModel,function(){function e(){}return e.isY7=function(e){return this.y7Alpha===e||this.y7InternalAlpha===e||this.y7ExternalNap===e||this.y7ExternalNapBetSetting===e},e.nap=1,e.alpha=2,e.internalAlpha=3,e.externalNap=4,e.externalNapBetSetting=5,e.y7Alpha=6,e.y7InternalAlpha=7,e.y7ExternalNap=8,e.y7ExternalNapBetSetting=9,e}());e.System=t;var n=function(){function t(){}return t.notImplemented=function(t){return e.StringFormater.format("{0} is not implemented",t)},t.negativeParam=function(t){return e.StringFormater.format("{0} must be greater than zero.",t)},t.invalidParams=function(){return"The request parameters have get some invalid values."},t.invalidParam=function(t){return e.StringFormater.format("{0} has taken an invalid value.",t)},t.wrongCustomerLevel=function(){return"Wrong customer level."},t.customerIdsNotMatchCustomerNames=function(){return"The number of customer IDs does not match with the number of customer names."},t}();e.Exceptions=n;var i=function(){function e(){}return Object.defineProperty(e,"system",{get:function(){return 1},enumerable:!0,configurable:!0}),Object.defineProperty(e,"language",{get:function(){return"en-US"},enumerable:!0,configurable:!0}),Object.defineProperty(e,"percentageDivider",{get:function(){return 100},enumerable:!0,configurable:!0}),Object.defineProperty(e,"displayingMsgTime",{get:function(){return 3e3},enumerable:!0,configurable:!0}),e}();e.Defaults=i;var r=function(){function e(e,t){this.key=e,this.value=t}return e}();e.KeyValuePair=r}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";e.moduleName="customerSetting";var t=angular.module(e.moduleName,[]),n=function(){function e(){}return e.controller=function(e,n){t.controller(e,n)},e.service=function(e,n){t.service(e,n)},e.factory=function(e,n){t.factory(e,n)},e.filter=function(e,n){t.filter(e,n)},e.directive=function(e,n){var i=function(){var e=Array.prototype.concat.apply([null],arguments);return new(Function.prototype.bind.apply(n,e))};i.$inject=n.$inject,t.directive(e,i)},e.value=function(e,n){t.value(e,n)},e}();e.ModuleRegister=n,t.run(["$http","appConfigService",function(e,t){e.defaults.headers.common.__RequestVerificationToken=t.requestVerificationToken()}])}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";!function(e){e[e.company=5]="company",e[e["super"]=4]="super",e[e.master=3]="master",e[e.agent=2]="agent",e[e.member=1]="member"}(e.Level||(e.Level={}));var t=e.Level,n=function(){function e(e){e&&(this.userName=e.userName,this.firstName=e.firstName,this.lastName=e.lastName,this.phone=e.phone,this.mobilePhone=e.mobilePhone,this.fax=e.fax,this.email=e.email,this.currency=e.currency,this.isSupportPT3DecimalSites=e.isSupportPT3DecimalSites,this.isY7Sites=e.isY7Sites)}return e}();e.CustomerInfo=n;var i=function(){function t(e){e&&(this.sportTypeId=e.sportTypeId,this.sportTypeName=e.sportTypeName,this.betTypeId=e.betTypeId,this.betTypeName=e.betTypeName,this.typeName=e.typeName,this.tooltipMessage=e.tooltipMessage,this.showIconTooltipMessage=e.showIconTooltipMessage,void 0!==e.isLive&&(this.isLive=e.isLive)),this.validationState=h.normal,this.highlightState={}}return t.prototype.isChanged=function(){return this.sportTypeId!==this.oldSetting.sportTypeId||this.sportTypeName!==this.oldSetting.sportTypeName||this.betTypeId!==this.oldSetting.betTypeId||this.betTypeName!==this.oldSetting.betTypeName},t.prototype.copySettingTo=function(t){var n=this,i=0,r=!0;return t&&(i=t.filter(function(t){var i=!0;return(n.sportTypeId!==t.sportTypeId||n.betTypeId!==t.betTypeId||n.isLive!==t.isLive)&&(i=t.copySettingFrom(n),r=r?i:r,i?t.hasHighlightField(e.NotificationType.success):i)}).length),{numberOfCopiedItems:i,status:r}},t.prototype.copySettingFrom=function(t){throw e.Exceptions.notImplemented("copyFrom")},t.prototype.removeCopyDisabled=function(t){throw e.Exceptions.notImplemented("removeCopyDisabled")},t.prototype.validate=function(){return!0},t.prototype.toJson=function(){var e=this,t={},n=["oldSetting","validationState","highlightState"];return Object.keys(this).forEach(function(i){void 0!==e[i]&&null!==e[i]&&n.indexOf(i)<0&&"$"!==i.charAt(0)&&(e[i].toJson?t[i]=e[i].toJson():t[i]=e[i])}),t},t.prototype.changeFieldHighlightState=function(t,n){void 0===n&&(n=e.NotificationType.none),t&&(this.highlightState[t]=n)},t.prototype.resetValidationState=function(){this.validationState=u.normal},t.prototype.highlightCopiedFields=function(t){if(t)for(var n in t)this[n]!==t[n]&&this.changeFieldHighlightState(n,e.NotificationType.success)},t.prototype.highlightUnablyCopiedField=function(){this.resetHighlightState(),this.changeFieldHighlightState(this.validationState.fieldName,e.NotificationType.error),this.resetValidationState()},t.prototype.hasHighlightField=function(e){for(var t in this.highlightState)if(this.highlightState[t]===e)return!0;return!1},t.prototype.resetHighlightState=function(){this.highlightState={}},t.prototype.resetFieldHighlightState=function(e){this.changeFieldHighlightState(e)},t}();e.DefaultSettingItem=i;var r=function(e){function t(t,n){void 0===n&&(n=!1),e.call(this,t),this.isRowCopied=!1,this.isMultiple=!!n,this.isCheckedUpdateRow=!n,this.isCheckedUpdateMinPtOnly=!n}return __extends(t,e),t.prototype.removeCopyDisabled=function(e){e.forEach(function(e){e.isRowCopied=!1})},t.prototype.copySettingTo=function(e){var t=this,n=0,i=!0;return e&&(n=e.filter(function(e){var n=!1;return(t.sportTypeId!==e.sportTypeId||t.betTypeId!==e.betTypeId||t.isLive!==e.isLive)&&(n=e.copySettingFrom(t),i=i?n:i,e.isRowCopied)}).length),{numberOfCopiedItems:n,status:i}},t.prototype.toJson=function(){if(this.isMultiple&&!this.isCheckedUpdateRow&&!this.isCheckedUpdateMinPtOnly)return null;var t={};return t=e.prototype.toJson.call(this),t.isUpdateMinPtOnly=this.isCheckedUpdateMinPtOnly&&!this.isCheckedUpdateRow,t},t}(i);e.CopyWithCheckboxSettingItem=r;var a=function(){function e(e){e&&e.errorMessage&&(this.errorMessage=e.errorMessage,this.typeName=e.typeName)}return e.prototype.isChanged=function(){return!1},e.prototype.validate=function(){return!0},e.prototype.toJson=function(){return{errorMessage:this.errorMessage,typeName:this.typeName}},e}();e.ErrorSettingItem=a;var s=function(){function t(e){e&&(this.id=e.id,this.name=e.name,this.settingLevel=e.settingLevel,this.settingItems=e.settingItems,this.typeName=e.typeName,this.displayOrder=e.displayOrder,this.displayMode=e.displayMode,this.isError=e.isError,this.extraData=this.getExtraData(e))}return t.prototype.getChanges=function(){var e,n=this.settingItems.filter(function(e){return!!e.isChanged&&e.isChanged()});return n&&n.length>0&&(e=new t,e.id=this.id,e.name=this.name,e.settingLevel=this.settingLevel,e.settingItems=n,e.typeName=this.typeName,e.displayOrder=this.displayOrder,e.displayMode=this.displayMode,e.isError=this.isError,e.extraData=this.extraData),e},t.prototype.validate=function(){var e=this.settingItems.every(function(e){return!e.validate||e.validate()});return this.id>0&&this.settingLevel>0&&void 0!==this.typeName&&e},t.prototype.toJson=function(){var e={id:this.id,name:this.name,settingLevel:this.settingLevel,settingItems:[],typeName:this.typeName,displayOrder:this.displayOrder,displayMode:this.displayMode,isError:this.isError};return this.settingItems.forEach(function(t){var n=t.toJson();null!=n&&e.settingItems.push(t.toJson())}),this.extraData&&this.extraData.length>0&&this.extraData.forEach(function(t){e[t.key]=t.value}),e},t.prototype.getExtraData=function(t){var n=new Array,i=Object.keys(this).filter(function(e){return"extraData"!==e});return n=Object.keys(t).filter(function(e){return i.indexOf(e)<0}).map(function(n){return new e.KeyValuePair(n,t[n])})},t}();e.SettingType=s;var o=function(){function i(e){e&&(this.customerId=e.customerId,this.customerInfo=new n(e.customerInfo),this.customerLevel=e.customerLevel.value,this.productId=e.productId,this.productName=e.productName,this.isNormalMode=null===e.isNormalMode||void 0===e.isNormalMode||e.isNormalMode,e.warningMessages&&this.initializeWarningMessages(e.warningMessages),e.settingTypes?this.settingTypes=e.settingTypes.map(function(e){return new s(e)}):this.settingTypes=new Array)}return i.prototype.getEditableSettingTypes=function(t){var n,r;return r=t?this.settingTypes.filter(function(n){return n.id===t&&n.displayMode===e.DisplayMode.editable}):this.getListOfEditableSettingTypes(),r&&r.length>0&&(n=new i,n.customerId=this.customerId,n.customerInfo=this.customerInfo,n.customerLevel=this.customerLevel,n.productId=this.productId,n.productName=this.productName,n.settingTypes=r),n},i.prototype.getChanges=function(e){var t,n,r=new Array;return n=e?this.settingTypes.filter(function(t){return t.id===e}):this.getListOfEditableSettingTypes(),n&&n.length>0&&n.forEach(function(e){var t=e.getChanges();t&&r.push(t)}),r.length>0&&(t=new i,t.customerId=this.customerId,t.customerInfo=this.customerInfo,t.customerLevel=this.customerLevel,t.productId=this.productId,t.productName=this.productName,t.settingTypes=r),t},i.prototype.validate=function(){var e=this.getListOfEditableSettingTypes(),n=e.every(function(e){return e.validate()});return this.customerLevel<t.company&&this.customerId>0&&this.productId>0&&n},i.prototype.toJson=function(){var e={customerId:this.customerId,customerInfo:this.customerInfo,customerLevel:{value:this.customerLevel},productId:this.productId,productName:this.productName,settingTypes:[]};return this.settingTypes.forEach(function(t){e.settingTypes.push(t.toJson())}),e},i.prototype.getListOfEditableSettingTypes=function(){return this.settingTypes.filter(function(t){return t.displayMode===e.DisplayMode.editable})},i.prototype.initializeWarningMessages=function(t){this.warningMessages=new p(t),this.warningMessages.msgType=e.MessageType.warning,this.warningMessages.subResults&&this.warningMessages.subResults.forEach(function(t){t.settingResults.forEach(function(t){t.msgType=e.MessageType.warning})})},i}();e.Setting=o;var u=function(){function t(t,n,i){this.code=t,this.message=n,i?this.msgType=i:0!==this.code?this.msgType=e.MessageType.error:this.msgType=e.MessageType.success}return Object.defineProperty(t.prototype,"isSuccessful",{get:function(){return 0===this.code},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"msgTypeName",{get:function(){return e.MessageType[this.msgType.toString()]},enumerable:!0,configurable:!0}),Object.defineProperty(t,"normal",{get:function(){return new t(0,"",e.MessageType.success)},enumerable:!0,configurable:!0}),Object.defineProperty(t,"systemError",{get:function(){return new t((-1),"SystemError",e.MessageType.error)},enumerable:!0,configurable:!0}),Object.defineProperty(t,"notAuthorizedError",{get:function(){return new t((-2),"YouAreNotAuthorized",e.MessageType.error)},enumerable:!0,configurable:!0}),Object.defineProperty(t,"noChangeToUpdate",{get:function(){return new t((-3),"NoChangeToUpdate",e.MessageType.error)},enumerable:!0,configurable:!0}),Object.defineProperty(t,"invalidParameters",{get:function(){return new t((-4),e.Exceptions.invalidParams(),e.MessageType.error)},enumerable:!0,configurable:!0}),t}();e.Result=u;var l=function(e){function t(t,n,i,r,a,s){e.call(this,t,n,i),this.settingTypeId=r,this.settingTypeName=a,this.messages=s}return __extends(t,e),t}(u);e.SettingResult=l;var c=function(){function t(e){e&&(this.customerId=e.customerId,this.customerName=e.customerName,this.settingResults=new Array,this.settingResults.push(new l(e.code,e.message,this.detechMsgType(e),e.settingTypeId,e.settingTypeName,e.subMessages)))}return Object.defineProperty(t.prototype,"isSuccessful",{get:function(){return!this.settingResults||this.settingResults.every(function(e){return e.isSuccessful})},enumerable:!0,configurable:!0}),t.prototype.pushSettingResult=function(e){e&&this.settingResults.push(new l(e.code,e.message,this.detechMsgType(e),e.settingTypeId,e.settingTypeName,e.subMessages))},t.prototype.removeSuccessResults=function(){return this.settingResults&&(this.settingResults=this.settingResults.filter(function(e){return!e.isSuccessful})),this.settingResults||new Array},t.prototype.removeDuplicatedMessages=function(){var e=new Array;this.settingResults.forEach(function(t){if(!t.messages&&e.length>0){var n=e.some(function(e){return!e.messages&&t.code===e.code&&t.message===e.message});n||e.push(t)}else e.push(t)}),this.settingResults=e},t.prototype.detechMsgType=function(t){return t.isSuccessful?e.MessageType.success:0!==t.msgType?e.MessageType.error:e.MessageType.warning},t}();e.CustomerSettingResult=c;var p=function(e){function t(t){var n=this;t&&(e.call(this,t.code,t.message,t.msgType),t.subResults&&(this.subResults=new Array,t.subResults.forEach(function(e){var t=n.subResults.filter(function(t){return t.customerId===e.customerId})[0];t?t.pushSettingResult(e):n.subResults.push(new c(e))}),this.subResults.forEach(function(e){e.removeDuplicatedMessages()})))}return __extends(t,e),Object.defineProperty(t.prototype,"isSuccessful",{get:function(){return!this.subResults||this.subResults.every(function(e){return e.isSuccessful})},enumerable:!0,configurable:!0}),t.prototype.removeSuccessResults=function(){return this.subResults&&(this.subResults=this.subResults.filter(function(e){return!e.isSuccessful&&e.removeSuccessResults().length>0})),this.subResults||new Array},t}(u);e.UpdatingSettingResult=p;var h=function(t){function n(n,i,r){t.call(this,-8e3,n,e.MessageType.error),this.fieldName=i,this.msgParams=r}return __extends(n,t),n}(u);e.ValidationResult=h}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(){function t(e,t,n){this.productId=e,this.uplineLevel=t,this.customerLevel=n}return Object.defineProperty(t.prototype,"productId",{get:function(){return this.prodId},set:function(t){if(t<=0)throw e.Exceptions.negativeParam("productId");this.prodId=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"uplineLevel",{get:function(){return this.uplineLv},set:function(t){if(t<e.Level.agent)throw e.Exceptions.invalidParam("uplineLevel");t===e.Level.company&&(t=null),this.uplineLv=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"customerLevel",{get:function(){return this.custLv},set:function(t){if(t<e.Level.member||t>e.Level["super"])throw e.Exceptions.wrongCustomerLevel;this.custLv=t},enumerable:!0,configurable:!0}),t}();e.AbstractSettingQuery=t;var n=function(t){function n(e,n,i,r){t.call(this,e,n,i),this.customerId=r}return __extends(n,t),Object.defineProperty(n.prototype,"customerId",{get:function(){return this.custId},set:function(t){if(t<=0)throw e.Exceptions.negativeParam("customerId");this.custId=t},enumerable:!0,configurable:!0}),n.prototype.toJson=function(){var e={customerId:this.customerId,customerLevel:this.customerLevel,productId:this.productId};return this.uplineLevel&&(e.uplineLevel=this.uplineLevel),e},n}(t);e.SettingQuery=n;var i=function(t){function i(e,n,i,r,a){t.call(this,e,n,i),this.customerIds=r,this.customerNames=a}return __extends(i,t),Object.defineProperty(i.prototype,"customerIds",{get:function(){return this.custIds.join()},set:function(t){if(!t)throw e.Exceptions.invalidParam("customerIds");this.custIds=t.split(",").map(function(e){return parseInt(e.trim(),10)})},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"customerNames",{get:function(){return this.custNames.join(", ")},set:function(t){if(!t)throw e.Exceptions.invalidParam("customerNames");this.custNames=t.split(",").map(function(e){return e.trim()})},enumerable:!0,configurable:!0}),i.prototype.getFirstCustomerSettingQuery=function(){var e;return this.custIds.length>0&&this.validate()&&(e=new n(this.productId,this.uplineLevel,this.customerLevel,this.custIds[0])),e},i.prototype.validate=function(){return this.custIds.length===this.custNames.length},i}(t);e.MultipleSettingQuery=i;var r=function(){function t(e,t){this.uplineLevel=e,this.setting=t}return t.prototype.validate=function(){return this.uplineLevel&&(this.uplineLevel<=e.Level.member||this.uplineLevel>=e.Level.company)||!this.setting?e.Result.invalidParameters:e.Result.normal},t.prototype.toJson=function(){return{uplineLevel:this.uplineLevel,setting:JSON.stringify(this.setting.toJson())}},t}();e.SettingCommand=r;var a=function(t){function n(e,n,i,r){t.call(this,i,r),this.customerIds=e,this.customerNames=n}return __extends(n,t),n.prototype.validate=function(){var n=this;return t.prototype.validate.call(this)||function(){return n.customerIds&&n.customerNames?e.Result.normal:e.Result.invalidParameters}()},n.prototype.toJson=function(){return{uplineLevel:this.uplineLevel,customerIds:this.customerIds,customerNames:this.customerNames,setting:JSON.stringify(this.setting.toJson())}},n}(r);e.MultipleSettingCommand=a;var s=function(){function e(e,t,n,i,r){this.uplineLevel=e,this.customerLevel=t,this.productId=n,this.productName=i,this.settingType=r}return e}();e.SettingPartialCommand=s;var o=function(){function e(e,t,n,i,r,a,s,o){this.customerId=e,this.customerLevel=t,this.productId=n,this.uplineLevel=i,this.settingTypeId=r,this.actionId=a,this.actionType=s,this.jsonParameters=o}return e.prototype.toJson=function(){return{customerId:this.customerId,customerLevel:this.customerLevel,productId:this.productId,uplineLevel:this.uplineLevel,settingTypeId:this.settingTypeId,actionId:this.actionId,actionType:this.actionType,jsonParameters:JSON.stringify(this.jsonParameters)}},e}();e.RedirectCommand=o}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";function t(e){var t=e("number");return function(e,n){return n=n||e.toString().indexOf(".")>0?2:0,t(e,n)}}function n(){return function(e){return e||"---"}}function i(){return function(e){return Math.min.apply(Math,e)}}function r(){return function(e){return Math.max.apply(Math,e)}}function a(t){return function(n){return t.canUpdate()&&n&&n.displayMode===e.DisplayMode.editable}}function s(t){return function(n){var i=!1;return n&&n.settingTypes&&(i=n.settingTypes.some(function(t){
return t.displayMode===e.DisplayMode.editable})),t.canUpdate()&&i}}function o(){return function(e){return!e.isError}}function u(e){return function(t){return e.trustAsHtml(t)}}var l=function(){function e(e){var t;this.$window=e,t=this.$window.appConfig||{},t&&(this.applicationPath=t.applicationPath,this.antiForgeryToken=t.antiForgeryToken,this.editable=t.editable,this.applicationPath&&"/"!==this.applicationPath[this.applicationPath.length-1]&&(this.applicationPath+="/"))}return Object.defineProperty(e.prototype,"thousandSeparator",{get:function(){return","},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"decimalSeparator",{get:function(){return"."},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"defaultPrecision",{get:function(){return 2},enumerable:!0,configurable:!0}),e.prototype.getQueryPath=function(){return this.applicationPath+"admin/customer/viewCustomerSettingquerySetting?"},e.prototype.getUpdatePath=function(){return this.applicationPath+"updateCustomerSetting/updateSetting"},e.prototype.getUpdateMultiplePath=function(){return this.applicationPath+"updateCustomerSetting/updateMultipleSetting"},e.prototype.getSettingTypeActionPath=function(){return this.applicationPath+"route/redirect"},e.prototype.requestVerificationToken=function(){return this.antiForgeryToken},e.prototype.canUpdate=function(){return this.editable},e.$inject=["$window"],e}();e.AppConfigService=l,e.ModuleRegister.service("appConfigService",l);var c=function(){function t(e){this.$window=e,this.fetchParams()}return Object.defineProperty(t.prototype,"systemId",{get:function(){return this.system},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"language",{get:function(){return this.lang},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"customerNames",{get:function(){return this.query.customerNames},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"uplineLevel",{get:function(){return this.query.uplineLevel},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"forcingMode",{get:function(){return this.isForcingMode},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"multipleUpdates",{get:function(){return this.isMultipleUpdates},enumerable:!0,configurable:!0}),t.prototype.getSettingQuery=function(){if(this.isMultipleUpdates){var t=this.query;if(!t.validate())throw e.Exceptions.customerIdsNotMatchCustomerNames();return t.getFirstCustomerSettingQuery()}return this.query},t.prototype.createSettingCommand=function(t){if(this.isMultipleUpdates){var n=this.query;if(!n.validate())throw e.Exceptions.customerIdsNotMatchCustomerNames();return new e.MultipleSettingCommand(n.customerIds,n.customerNames,n.uplineLevel,t)}return new e.SettingCommand(this.query.uplineLevel,t)},t.prototype.fetchParams=function(){var t=this.$window.params;if(t){var n=t.uplineLevel?t.uplineLevel:void 0,i=t.customerLevel?t.customerLevel:void 0;this.isMultipleUpdates=t.isMultiple||!1,this.lang=t.language||e.Defaults.language,this.system=t.system||e.Defaults.system,this.isForcingMode=t.isForcingMode||!1,this.isMultipleUpdates?this.query=new e.MultipleSettingQuery(t.productId,n,i,t.customerIds,t.customerNames):this.query=new e.SettingQuery(t.productId,n,i,t.customerId)}},t.$inject=["$window"],t}();e.ParametersService=c,e.ModuleRegister.service("parametersService",c);var p=function(){function t(e){this.$q=e,this.promises=new Array}return t.prototype.reset=function(){this.promises=new Array},t.prototype.register=function(t,n){var i=this.promises.filter(function(e){return t===e.key}).length<=0;i&&this.promises.push(new e.KeyValuePair(t,n))},t.prototype.execute=function(t){var n=this.$q.defer();if(t){var i;if(this.promises.forEach(function(n){var r=t.setting.settingTypes.filter(function(e){return n.key===e.id});if(r.length>0){var a=new e.SettingPartialCommand(t.uplineLevel,t.setting.customerLevel,t.setting.productId,t.setting.productName,r[0]);i=i?i.then(function(){return n.value(a)}):n.value(a)}}),i)return i}return n.resolve(),n.promise},t.$inject=["$q"],t}();e.UpdatingSettingTypePromiseChain=p,e.ModuleRegister.service("updatingSettingTypePromiseChain",p);var h=function(){function t(e){this.parametersService=e,this.systemId=e.systemId,this.multipleUpdates=e.multipleUpdates}return t.prototype.register=function(t,n){this.uplineLevel=n,t&&(this.customerId=t.customerId,this.customerLevel=t.customerLevel,this.productId=t.productId,this.productName=t.productName,this.normalMode=t.isNormalMode,this.isSupportPT3DecimalSites=this.systemId==e.System.nap||t.customerInfo.isSupportPT3DecimalSites,this.isY7Sites=t.customerInfo.isY7Sites,this.parametersService.forcingMode&&(this.normalMode=!1))},t.$inject=["parametersService"],t}();e.SettingTypesSharedValuesService=h,e.ModuleRegister.service("settingTypesSharedValuesService",h);var d=function(){function t(){}return t.prototype.generateRanges=function(t,n,i,r,a){var s=new Array;for(n=Math.max(0,n),i=Math.max(0,i),t>i&&s.push(new e.KeyValuePair(t,t));i>=n;)s.push(new e.KeyValuePair(i,i)),i-=r,i=Math.round(i*a)/a;return t<n&&s.push(new e.KeyValuePair(t,t)),s},t}();e.ValueRangeService=d,e.ModuleRegister.service("valueRangeService",d),t.$inject=["$filter"],e.ModuleRegister.filter("formatNumber",t),e.ModuleRegister.filter("naValue",n),e.ModuleRegister.filter("minValue",i),e.ModuleRegister.filter("maxValue",r),a.$inject=["appConfigService"],e.ModuleRegister.filter("editableSettingType",a),s.$inject=["appConfigService"],e.ModuleRegister.filter("editableForm",s),e.ModuleRegister.filter("validSettingType",o),u.$inject=["$sce"],e.ModuleRegister.filter("unsafe",u)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";function t(e){return function(t){return e.getLabel(t)}}function n(e){return function(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];return n.unshift(t),e.getFormattedMessage.apply(e,n)}}var i=function(){function t(t,n){this.resource=t,this.setLanguage(n.language||e.Defaults.language)}return t.prototype.getLabel=function(e){var t=this.label[e];return angular.isUndefined(t)&&(t=this.defaultLabel[e]||e),t},t.prototype.getFormattedLabel=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=this.getLabel(e);return i&&(i=i.replace(/{(\d+)}/g,function(e,n){return void 0===t[n]||null===t[n]?e:t[n]})),i},t.prototype.getMessage=function(e){var t=this.message[e];return angular.isUndefined(t)&&(t=this.defaultMessage[e]||e),t},t.prototype.getFormattedMessage=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=this.getMessage(e);return i&&(i=i.replace(/{(\d+)}/g,function(e,n){return void 0===t[n]||null===t[n]?e:t[n]})),i},t.prototype.setLanguage=function(t){var n=e.Defaults.language.toLowerCase(),i=this.resource.label||{},r=this.resource.message||{};t=t?t.toLowerCase():n,this.defaultLabel=i[n]||{},this.defaultMessage=r[n]||{},this.label=i[t]||this.defaultLabel,this.message=r[t]||this.defaultMessage},t.$inject=["customerSettingResource","parametersService"],t}();e.ResourceService=i,e.ModuleRegister.service("resourceService",i),t.$inject=["resourceService"],e.ModuleRegister.filter("label",t),n.$inject=["resourceService"],e.ModuleRegister.filter("message",n)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function e(){this.replace=!0,this.templateUrl="BlockUI/block-ui.htm",this.controller=["$scope","blockUiService",function(e,t){t.setup(e)}]}return e}();e.ModuleRegister.directive("blockUi",t);var n=function(){function e(){}return e.prototype.setup=function(e){this.scope=e},e.prototype.blockUi=function(){this.scope.isLoading=!0},e.prototype.unblockUi=function(){this.scope.isLoading=!1},e}();e.BlockUiService=n,e.ModuleRegister.service("blockUiService",n)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(e,t,n,i){this.$http=e,this.$window=t,this.resourceService=n,this.blockUiService=i}return t.prototype.registerSharedScope=function(e){this.sharedScope=e},t.prototype.request=function(t,n,i){var r=this;this.blockUiService.blockUi(),this.$http.post(t,n).then(function(e){"string"==typeof e.data?r.$window.document.write(e.data):e.data.code===-302&&e.data.redirect?r.$window.location.href=r.$window.location.origin+e.data.redirect:i(e)},function(t){var n;t&&(401===t.status?(n=e.Result.notAuthorizedError,n.message=r.resourceService.getMessage(n.message)):t.data?n=new e.Result(t.status,t.data.message,e.MessageType.error):(n=e.Result.systemError,n.message=r.resourceService.getMessage(n.message))),r.sharedScope.requestMsgResult=n})["finally"](function(){r.blockUiService.unblockUi()})},t.$inject=["$http","$window","resourceService","blockUiService"],t}();e.RequestService=t,e.ModuleRegister.service("requestService",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(e,t,n,i,r,a,s,o,u){var l=this;this.$window=e,this.$rootScope=t,this.$timeout=n,this.appConfigService=i,this.parametersService=r,this.resourceService=a,this.requestService=s,this.updatingSettingTypePromiseChain=o,this.settingTypesSharedValuesService=u,this.successfulUpdatingCallback=this.$window.parent.successfulUpdatingCallback,this.systemId=this.parametersService.systemId,this.customerNames=this.parametersService.customerNames,this.requestService.registerSharedScope(this),this.checkChangedInMultipleModeSettingTypes=[18,19],angular.element(this.$window.document).ready(function(){l.querySetting()})}return t.prototype.update=function(t){var n,i,r=this,a=!!this.customerNames,s=a?this.appConfigService.getUpdateMultiplePath():this.appConfigService.getUpdatePath(),o=this.setting.getEditableSettingTypes(t),u=a||!this.settingTypesSharedValuesService.normalMode?o:this.setting.getChanges(t);if(this.$rootScope.$emit("updateSetting",t),this.requestMsgResult=void 0,a&&this.hasCheckChangedInMultipleModeSettingTypes()&&(u.settingTypes=u.settingTypes.filter(function(e){return!(r.checkChangedInMultipleModeSettingTypes.indexOf(e.id)>-1)||!!r.setting.getChanges(e.id)})),u&&u.settingTypes.length>0)o.validate()&&(i=this.parametersService.createSettingCommand(u),n=i.validate(),n.isSuccessful?this.updatingSettingTypePromiseChain.execute(i).then(function(){r.updateSetting(s,i,!!t,a)}):this.requestMsgResult=n);else{var l=e.Result.noChangeToUpdate;l.message=this.resourceService.getMessage(l.message),this.requestMsgResult=l}},t.prototype.hasCheckChangedInMultipleModeSettingTypes=function(){var e=this;return this.setting.settingTypes.filter(function(t){return e.checkChangedInMultipleModeSettingTypes.indexOf(t.id)>-1}).length>0},t.prototype.updateSetting=function(t,n,i,r){var a=this;this.requestService.request(t,n.toJson(),function(t){a.requestMsgResult=new e.UpdatingSettingResult(t.data),a.querySetting(),a.$timeout(function(){var e=a.requestMsgResult;e&&e.removeSuccessResults&&0===e.removeSuccessResults().length&&(a.requestMsgResult=void 0,a.successfulUpdatingCallback&&a.successfulUpdatingCallback(i,r))},e.Defaults.displayingMsgTime)})},t.prototype.querySetting=function(){var t=this,n=this.parametersService.getSettingQuery();n&&this.requestService.request(this.appConfigService.getQueryPath(),n.toJson(),function(n){t.setting=new e.Setting(n.data),t.warningMessages=t.setting.warningMessages,t.settingTypesSharedValuesService.register(t.setting,t.parametersService.uplineLevel),t.$rootScope.$emit("queriedSetting")})},t.$inject=["$window","$rootScope","$timeout","appConfigService","parametersService","resourceService","requestService","updatingSettingTypePromiseChain","settingTypesSharedValuesService"],t}();e.ModuleRegister.controller("customerSettingController",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";function t(){return function(t,n,i){return n===e.SettingLevel.product?i:n===e.SettingLevel.sportType?t.sportTypeName:t.betTypeName}}var n=function(){function t(){var t=this;this.replace=!0,this.templateUrl="CopyWithCheckBoxSettingBlock/copy-with-checkbox-setting-block.htm",this.controller=["$scope","$timeout",function(n,i){n.avaiSettingLevels=e.SettingLevel,n.$watch("settingType",function(i){i&&angular.isArray(i.settingItems)&&(i.settingLevel===e.SettingLevel.sportTypeBetType?(n.sportGroups=t.groupSettingItemsBySportType(i.settingItems),n.togglableSettingBlocks&&(n.togglableSharedStates=new e.TogglableSettingBlockSharedStates(n.sportGroups.length))):n.sportGroups=[i.settingItems])}),n.isAutoChanged=function(e,t,r){e.resetFieldHighlightState(t),i(function(){r&&e.isCheckedUpdateRow&&n.$emit("copySettingTypeWithCheckbox"+n.settingType.id)},100)}}]}return t.prototype.groupSettingItemsBySportType=function(e){var t,n=-1,i=new Array;return e.forEach(function(e){var r=e;n!==r.sportTypeId&&(t&&i.push(t),n=r.sportTypeId,t=new Array),t.push(r)}),t&&i.push(t),i},t}();e.CopyWithCheckboxSettingTypeDirective=n,e.ModuleRegister.directive("copyWithCheckboxSettingType",n),e.ModuleRegister.filter("showingName",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function e(){this.replace=!0,this.templateUrl="CustomerInfo/customer-info.htm"}return Object.defineProperty(e.prototype,"scope",{get:function(){return{info:"=",names:"="}},enumerable:!0,configurable:!0}),e}();e.ModuleRegister.directive("customerInfo",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(t){var n=this;this.replace=!0,this.controller=["$scope","settingTypesSharedValuesService",function(t,i){t.customerId=i.customerId,t.uplineLevel=i.uplineLevel,t.customerLevel=i.customerLevel,t.productId=i.productId,t.productName=i.productName,t.avaiCustomerLevels=e.Level,t.$watch("settingType",function(r){t.normalMode=i.normalMode,r&&angular.isArray(r.settingItems)&&(r.isError?r.settingItems.length>0&&(r.settingItems[0]=new e.ErrorSettingItem(r.settingItems[0])):r.settingItems.forEach(function(e,a){e&&(r.settingItems[a]=n.settingItemType(e,!0,t.customerLevel,t.uplineLevel,r.extraData,i.multipleUpdates,i.isSupportPT3DecimalSites))}))})}],this.settingItemType=t}return Object.defineProperty(t.prototype,"scope",{get:function(){return{settingType:"=",update:"&"}},enumerable:!0,configurable:!0}),t}();e.SettingTypeDirective=t}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function e(e){if(e>0){this.toggleStates=[],this.numberofExpandedBlocks=1;for(var t=0;t<e;t++)this.toggleStates[t]=0===t}}return e.prototype.isExpanded=function(e){return this.toggleStates[e]},e.prototype.isExpandedAll=function(){return this.numberofExpandedBlocks===this.toggleStates.length},e.prototype.collapseBlock=function(e){this.toggleStates[e]&&(this.toggleStates[e]=!1,this.numberofExpandedBlocks--)},e.prototype.expandBlock=function(e){this.toggleStates[e]||(this.toggleStates[e]=!0,this.numberofExpandedBlocks++)},e.prototype.collapseAllBlocks=function(){for(var e=0;e<this.toggleStates.length;e++)this.toggleStates[e]=!1;this.numberofExpandedBlocks=0},e.prototype.expandAllBlocks=function(){for(var e=0;e<this.toggleStates.length;e++)this.toggleStates[e]=!0;this.numberofExpandedBlocks=this.toggleStates.length},e}();e.TogglableSettingBlockSharedStates=t}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";function t(){return function(t,n,i){return n===e.SettingLevel.product?i:n===e.SettingLevel.sportType?t.sportTypeName:t.betTypeName}}var n=function(){function t(){var t=this;this.replace=!0,this.templateUrl="DefaultSettingBlock/default-setting-block.htm",this.controller=["$scope",function(n){n.avaiSettingLevels=e.SettingLevel,n.$watch("settingType",function(i){i&&angular.isArray(i.settingItems)&&(i.settingLevel===e.SettingLevel.sportTypeBetType?(n.sportGroups=t.groupSettingItemsBySportType(i.settingItems),n.togglableSettingBlocks&&(n.togglableSharedStates=new e.TogglableSettingBlockSharedStates(n.sportGroups.length))):n.sportGroups=[i.settingItems])})}]}return t.prototype.groupSettingItemsBySportType=function(e){var t,n=-1,i=new Array;return e.forEach(function(e){var r=e;n!==r.sportTypeId&&(t&&i.push(t),n=r.sportTypeId,t=new Array),t.push(r)}),t&&i.push(t),i},t}();e.DefaultSettingTypeGroupingDirective=n,e.ModuleRegister.directive("defaultSettingTypeGrouping",n),e.ModuleRegister.filter("showingName",t)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){var t=function(){function e(e,t){var n=this;this.restrict="A",this.require="ngModel",this.link=function(e,t){var i,r,a=angular.element(t[0]),s=a[0],o=parseInt(a.attr("maxlength"),10),u=a.controller("ngModel");null!==e.precision&&void 0!==e.precision||(e.precision=n.defaultPrecision),a.attr("inputmode","numeric"),a.on("focus",function(){a.data("focused",!0)}),a.on("focusout",function(){a.data("focused",!1),e.strictlyFormat&&e.precision>0&&(i=u.$modelValue,r=n.numberFilter(u.$modelValue,e.precision),a.val(r))}),a.on("mouseup",function(){a.data("focused")&&(s.select(),a.data("focused",!1))}),a.on("keydown",function(e){var t=a.val(),i=s.selectionStart;return 8===e.keyCode?void(s.selectionStart===s.selectionEnd&&i>0&&t[i-1]===n.thousandSeparator&&(s.selectionStart=i-1,s.selectionEnd=i-1)):46===e.keyCode?void(s.selectionStart===s.selectionEnd&&i>0&&t[i]===n.thousandSeparator&&(s.selectionStart=i+1,s.selectionEnd=i+1)):69===e.keyCode?void e.preventDefault():void 0}),u.$formatters.unshift(function(){return i=u.$modelValue,r=n.formatModelValue(i,e)}),u.$parsers.unshift(function(t){if(u.$isEmpty(t))return i=null,r=t,i;if(r===t)return i;var a=s.selectionStart,l=n.parseAndFormatViewValue(t,e.precision,e.roundingAllowed),c=l.formattedValue,p=l.value;if(!isNaN(p)&&(e.negativeAllowed||p>=0)){var h=p,d=c,f=o;e.strictlyFormat&&e.precision>0&&d.indexOf(n.decimalSeparator)<0&&(f=o-e.precision-1),d.length>f&&(h=i,d=r),i=h,r=d}return u.$setViewValue(r),u.$render(),n.setCaretPosition(s,a,t,r),i})},this.numberFilter=e("number"),this.thousandSeparator=t.thousandSeparator,this.decimalSeparator=t.decimalSeparator,this.defaultPrecision=t.defaultPrecision}return e.prototype.formatModelValue=function(e,t){return t.strictlyFormat?this.numberFilter(e,t.precision):this.numberFilter(e)},e.prototype.parseAndFormatViewValue=function(e,t,n){var i,r="",a=e?e.split(this.decimalSeparator):[];if(a.length>1){if(t>0&&!/[\,\.]/.test(a[1])){var s=a[0].replace(/[\,]/g,""),o=n?a[1]:a[1].substring(0,t);i=+(s+this.decimalSeparator+o),isNaN(i)||(r=n?this.numberFilter(i,t):this.numberFilter(s)+this.decimalSeparator+o)}}else if(a.length>0){var u=a[0].replace(/[\,]/g,"");i=+u,isNaN(i)||(r=this.numberFilter(i))}return{formattedValue:r,value:i}},e.prototype.setCaretPosition=function(e,t,n,i){var r=t;if(n.length!==r){var a=n.length-i.length;r=Math.max(0,r-a),e.selectionStart===e.selectionEnd&&(e.selectionEnd=r),e.selectionStart=r}},e.$inject=["$filter","appConfigService"],e}();e.NumericInputDirective=t,e.ModuleRegister.directive("numericInput",t);var n=function(e){function t(){e.apply(this,arguments)}return __extends(t,e),Object.defineProperty(t.prototype,"scope",{get:function(){return{precision:"=?numericInput",strictlyFormat:"=?",roundingAllowed:"=?",negativeAllowed:"=?"}},enumerable:!0,configurable:!0}),t}(t);e.ModuleRegister.directive("customizableNumericInput",n);var i=function(){function n(){}return n.extend=function(n,i){if(null===this.numericInput){var r=angular.injector(["ng"]).get("$filter"),a=angular.injector(["ng",e.moduleName]).get("appConfigService");this.numericInput=new t(r,a)}this.numericInput.link(i,n,null,null,null)},n.numericInput=null,n}();e.NumericInputExtension=i}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function e(){this.replace=!0,this.templateUrl="SettingResult/setting-result.htm",this.controller=["$rootScope","$scope","$window",function(e,t,n){t.$watch("result",function(t){e.$emit("page-height-changed"),t&&n.scrollTo(0,0)})}]}return Object.defineProperty(e.prototype,"scope",{get:function(){return{result:"="}},enumerable:!0,configurable:!0}),e}();e.ModuleRegister.directive("settingResult",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(e){var t=this;this.replace=!0,this.link=function(e,n){e.$watch("setting.settingTypes",function(){t.generateNodes(e,n)})},this.$compile=e,this.loadedSettingTypes=new Array}return Object.defineProperty(t.prototype,"scope",{get:function(){return{setting:"=",update:"&"}},enumerable:!0,configurable:!0}),t.prototype.generateNodes=function(t,n){var i=this;t.setting&&t.setting.settingTypes&&angular.isArray(t.setting.settingTypes)&&t.setting.settingTypes.forEach(function(r,a){var s=i.loadedSettingTypes.some(function(e){return e===r.id});if(!s){var o=e.StringFormater.format("settingtype{0}",r.id),u=["<",o,' data-setting-type="setting.settingTypes[',a,']"',' data-update="update({ settingTypeId: settingTypeId})"',"></",o,">"],l=angular.element(u.join(""));a===t.setting.settingTypes.length-1&&l.addClass("last-setting-type"),n.append(l),i.$compile(l)(t),i.loadedSettingTypes.push(r.id)}})},t.$inject=["$compile"],t}();e.ModuleRegister.directive("settingTypeLoader",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(t,n){void 0===n&&(n=5),this.positionModel=t||e.PositionModel.relative,this.displayingSeconds=n}return t.prototype.isShowingMessage=function(){return!!this.messageKey||"none"===this.type},t.prototype.succeed=function(){this.messageKey="CopiedSuccessfully",this.type="success"},t.prototype.fail=function(){this.messageKey="CopyingFailed",this.type="error"},t.prototype.none=function(){this.messageKey=null,this.type="none"},t}();e.CopyingWithCheckboxState=t,function(e){e[e.minPt=1]="minPt",e[e.row=2]="row",e[e.group=3]="group"}(e.CopyMode||(e.CopyMode={}));var n=(e.CopyMode,function(){function n(n,i){var r=this;this.replace=!0,this.templateUrl="CopyWithCheckBoxSettingBlock/CopySettingWithCheckbox/copy-setting-with-checkbox.htm",this.controller=["$scope",function(n){n.copyingState=new t,r.copyingSettingWithCheckboxService.register(n),n.isIconActive=!1,r.$rootScope.$on("copySettingTypeWithCheckbox"+n.settingTypeId,function(){n.copy(!0)}),n.copy=function(t){if(void 0===t&&(t=!1),n.settingItems&&n.settingItems.length>1){var i=e.ArrayHelpers.join2DimensionalArray(n.settingItems),a=i[0],s=i.slice(1);if(!a.isCheckedUpdateRow||a.isRowCopied&&!n.isIconActive||t&&!a.isRowCopied)return;if(t||!a.isRowCopied){if(a.validate()){r.copyingSettingWithCheckboxService.hideTooltips(n);var o=a.copySettingTo(s);if(!(o.numberOfCopiedItems>0))return void n.copyingState.none();a.isRowCopied=!0,n.isIconActive=!0,o.status?n.copyingState.succeed():n.copyingState.fail()}}else a.isRowCopied=!1,n.isIconActive=!1,a.removeCopyDisabled(s),r.copyingSettingWithCheckboxService.hideTooltips(n)}},n.hideTooltip=function(){n.copyingState.none()}}],this.copyingSettingWithCheckboxService=i,this.$rootScope=n}return Object.defineProperty(n.prototype,"scope",{get:function(){return{settingTypeId:"=",settingItems:"="}},enumerable:!0,configurable:!0}),n.$inject=["$rootScope","copyingSettingWithCheckboxService"],n}());e.CopySettingWithCheckboxDirective=n,e.ModuleRegister.directive("copySettingWithCheckbox",n);var i=function(){function t(t){var n=this;this.$rootScope=t,this.scopes=new Array,this.$rootScope.$on("updateSetting",function(){n.scopes.forEach(function(t){if(t.copyingState.isShowingMessage()){var n=e.ArrayHelpers.join2DimensionalArray(t.settingItems);t.copyingState.none(),n.forEach(function(e){e.resetHighlightState()})}})})}return t.prototype.register=function(e){var t=this.scopes.filter(function(t){return t.$id===e.$id}).length>0;t||this.scopes.push(e)},t.prototype.hideTooltips=function(t){var n=t.settingTypeId;this.scopes.forEach(function(t){if(t.copyingState.isShowingMessage()){var i=t.settingTypeId;if(i===n){var r=e.ArrayHelpers.join2DimensionalArray(t.settingItems);t.copyingState.none(),r.forEach(function(e){e.resetHighlightState()})}}})},t.$inject=["$rootScope"],t}();e.ModuleRegister.service("copyingSettingWithCheckboxService",i)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(t,n){void 0===n&&(n=5),this.positionModel=t||e.PositionModel.relative,this.displayingSeconds=n}return t.prototype.isShowingMessage=function(){return!!this.messageKey||"none"===this.type},t.prototype.succeed=function(){this.messageKey="CopiedSuccessfully",this.type="success"},t.prototype.fail=function(){this.messageKey="CopyingFailed",this.type="error"},t.prototype.none=function(){this.messageKey=null,this.type="none"},t}();e.CopyingState=t;var n=function(){function n(n){var i=this;this.replace=!0,this.templateUrl="DefaultSettingBlock/CopySetting/copy-setting.htm",this.controller=["$scope",function(n){n.copyingState=new t,i.copyingSettingService.register(n),n.copy=function(){if(n.settingItems&&n.settingItems.length>1){var t=e.ArrayHelpers.join2DimensionalArray(n.settingItems),r=t[0],a=t.slice(1);if(r.validate()){i.copyingSettingService.hideTooltips(n);var s=r.copySettingTo(a);s.status?s.numberOfCopiedItems>0?n.copyingState.succeed():n.copyingState.none():n.copyingState.fail()}}},n.hideTooltip=function(){n.copyingState.none()}}],this.copyingSettingService=n}return Object.defineProperty(n.prototype,"scope",{get:function(){return{settingTypeId:"=",settingItems:"="}},enumerable:!0,configurable:!0}),n.$inject=["copyingSettingService"],n}();e.CopySettingDirective=n,e.ModuleRegister.directive("copySetting",n);var i=function(){function t(t){var n=this;this.$rootScope=t,this.scopes=new Array,this.$rootScope.$on("updateSetting",function(){n.scopes.forEach(function(t){if(t.copyingState.isShowingMessage()){var n=e.ArrayHelpers.join2DimensionalArray(t.settingItems);t.copyingState.none(),n.forEach(function(e){e.resetHighlightState()})}})})}return t.prototype.register=function(e){var t=this.scopes.filter(function(t){return t.$id===e.$id}).length>0;t||this.scopes.push(e)},t.prototype.hideTooltips=function(t){var n=t.settingTypeId;this.scopes.forEach(function(t){if(t.copyingState.isShowingMessage()){var i=t.settingTypeId;if(i===n){var r=e.ArrayHelpers.join2DimensionalArray(t.settingItems);t.copyingState.none(),r.forEach(function(e){e.resetHighlightState()})}}})},t.$inject=["$rootScope"],t}();e.ModuleRegister.service("copyingSettingService",i)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";function t(e,t){return function(n,i,r,a){void 0===a&&(a=null);var s=null;if(n&&n.message&&(!r||r&&r===n.fieldName)){var o=e.get(i),u=(n.msgParams||[]).map(function(e){if("number"==typeof e){var n=t("number")(e);return a&&(n=t(a)(n)),n}return e});o&&(u.unshift(n.message),s=o.getFormattedMessage.apply(o,u))}return s}}var n=function(){function t(t,n,i,r,a){var s=this;this.restrict="A",this.priority=100,this.link=function(t,n){var i=s.$templateCache.get("DefaultSettingBlock/HighlightField/tooltip.htm"),r=angular.element(i),a=e.debounce(function(){t.tooltipMessage&&s.calculateTooltipPosition(n,t.tooltipPositionModel)},100);s.$compile(r)(t),n.parent().prepend(r),t.$watch("tooltipMessage",function(){var i=t.fieldName,r=t.highlightState;t.highlightType=null,t.tooltipMessage&&(s.calculateTooltipPosition(n,t.tooltipPositionModel),t.highlightType=t.tooltipType||"error",t.tooltipDisplayingSeconds&&t.displayingTimeoutCallback&&s.$timeout(function(){t.displayingTimeoutCallback()},1e3*+t.tooltipDisplayingSeconds)),r&&i&&(r[i]=e.NotificationType[t.highlightType||"none"])}),t.highlightState&&t.$watch("highlightState",function(i){var r=(i||{})[t.fieldName],a=r&&r!==e.NotificationType.none?e.NotificationType[r]:null;s.resetElementBorder(n),a&&n.addClass("setting-field--"+a)},!0),n.on("change",function(){if(!t.tooltipMessage&&t.highlightState){var n=t.highlightState[t.fieldName];n&&n!==e.NotificationType.none&&(t.highlightState[t.fieldName]=e.NotificationType.none)}t.$apply()}),angular.element(s.$window).on("resize",a),s.$rootScope.$on("page-height-changed",function(){t.tooltipMessage&&s.calculateTooltipPosition(n,t.tooltipPositionModel)})},this.$window=t,this.$compile=n,this.$timeout=i,this.$templateCache=r,this.$rootScope=a}return Object.defineProperty(t.prototype,"scope",{get:function(){return{tooltipMessage:"@",tooltipType:"@",tooltipPositionModel:"@",tooltipDisplayingSeconds:"@",displayingTimeoutCallback:"&",fieldName:"@",highlightState:"="}},enumerable:!0,configurable:!0}),t.prototype.calculateTooltipPosition=function(t,n){var i=this;this.$timeout(function(){var r=angular.element(t[0].previousElementSibling);if(r&&0!==r.length){r.removeClass("tooltip--right tooltip--left"),r.css({left:"0px",visibility:"hidden"});var a=t[0].getBoundingClientRect(),s=r[0].getBoundingClientRect(),o=0,u=0,l=5;if((+n||e.PositionModel.relative)===e.PositionModel.relative){var c=i.$window.document.body.getBoundingClientRect();o=c.top,u=c.left}var p=Math.max(0,a.top-o-s.height+l),h=a.left+(a.width-s.width)/2-u;i.$window.innerWidth<h+s.width&&(p=a.top-o-(s.height-a.height)/2+l,h=a.left-s.width-l,r.addClass("tooltip--right")),h<0&&(p=a.top-o-(s.height-a.height)/2+l,h=a.left+a.width,r.addClass("tooltip--left")),r.css({top:p+"px",left:h+"px",visibility:"visible"})}})},t.prototype.resetElementBorder=function(e){e.hasClass("setting-field--error")&&e.removeClass("setting-field--error"),e.hasClass("setting-field--warning")&&e.removeClass("setting-field--success"),e.hasClass("setting-field--success")&&e.removeClass("setting-field--success")},t.$inject=["$window","$compile","$timeout","$templateCache","$rootScope"],t}();e.ModuleRegister.directive("fieldHighlighting",n),t.$inject=["$injector","$filter"],e.ModuleRegister.filter("tooltipMessage",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(t,n){var i=this;this.restrict="A",this.link=function(t,n){var r=angular.element(n.parent()),a=e.debounce(function(){n.hasClass("ng-hide")||i.calculateHeight(n,r)},100);i.calculateHeight(n,r),r.css({height:"auto"}),angular.element(i.$window).on("resize",a)},this.$window=t,this.$timeout=n}return t.prototype.calculateHeight=function(e,t){var n=this;this.$timeout(function(){var i=n.$window.innerHeight,r=.6*i,a=.2*i-35;e.css({"max-height":r+"px"}),t.css({"margin-top":a+"px"})})},t.$inject=["$window","$timeout"],t}();e.ModuleRegister.directive("detectMaxHeight",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function e(e){var t=this;this.restrict="A",this.link=function(e,n){function i(){if(p){var e=p.getBoundingClientRect();u<e.left?u=e.left:u>e.right-r&&(u=e.right-r),l<e.top?l=e.top:l>e.bottom-a&&(l=e.bottom-a)}n.css({"margin-top":l+"px","margin-left":u+"px"})}var r,a,s,o,u,l,c=angular.element(n[0].querySelector(e.draggableBanner)),p=angular.element(n.parent())[0];c.css({cursor:"move"}),c.on("mousedown",function(e){s=e.clientX-n[0].offsetLeft,o=e.clientY-n[0].offsetTop,r=n[0].offsetWidth,a=n[0].offsetHeight,t.$document.on("mousemove",h),t.$document.on("mouseup",d)});var h=function(e){u=e.clientX-s,l=e.clientY-o,i()},d=function(e){t.$document.off("mousemove",h),t.$document.off("mouseup",d)}},this.$document=e}return Object.defineProperty(e.prototype,"scope",{get:function(){return{draggableBanner:"=draggable"}},enumerable:!0,configurable:!0}),e.$inject=["$document"],e}();e.ModuleRegister.directive("draggable",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function e(){this.replace=!0,this.templateUrl="DefaultSettingBlock/NoteIcon/note-icon-tooltip.htm",this.controller=["$scope",function(e){e.showTooltip=function(){e.tooltipType="warning",
e.tooltipMessage=e.tooltipMessageBackup},e.removeMessage=function(){e.tooltipType="warning",e.tooltipMessage=null}}]}return Object.defineProperty(e.prototype,"scope",{get:function(){return{tooltipMessage:"@?",tooltipType:"@?",tooltipMessageBackup:"@?"}},enumerable:!0,configurable:!0}),e}();e.ModuleRegister.directive("noteIconTooltip",t)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function e(e,t){var n=this;this.initializing=!0,this.link=function(e,t){var i=angular.element(t.parent());i.addClass("togglable-bar"),i.on("click",function(t){var i=angular.element(t.target);if(i.hasClass("togglable-bar"))return n.toggle(e),!1}),n.isToggleAllDirective(e)||(e.$watch("settingData",function(){n.initializing?n.$timeout(function(){return n.initializing=!1}):e.sharedStates.isExpanded(e.blockIndex)||(e.sharedStates.expandBlock(e.blockIndex),n.triggerPageHeightChangedEvent())},!0),n.$rootScope.$on("updateSetting",function(t,i){i&&i!==e.settingTypeId||!e.settingData||!e.settingData.some(function(e){return!e.validationState.isSuccessful})||e.sharedStates.isExpanded(e.blockIndex)||(e.sharedStates.expandBlock(e.blockIndex),n.triggerPageHeightChangedEvent())}),n.$rootScope.$on("queriedSetting",function(){n.initializing=!0}))},this.$rootScope=e,this.$timeout=t}return Object.defineProperty(e.prototype,"scope",{get:function(){return{blockIndex:"=",sharedStates:"=",settingData:"=",settingTypeId:"="}},enumerable:!0,configurable:!0}),e.prototype.toggle=function(e){this.isToggleAllDirective(e)?e.sharedStates.isExpandedAll()?e.sharedStates.collapseAllBlocks():e.sharedStates.expandAllBlocks():e.sharedStates.isExpanded(e.blockIndex)?e.sharedStates.collapseBlock(e.blockIndex):e.sharedStates.expandBlock(e.blockIndex),e.$apply(),this.triggerPageHeightChangedEvent()},e.prototype.isToggleAllDirective=function(e){return null===e.blockIndex||void 0===e.blockIndex},e.prototype.triggerPageHeightChangedEvent=function(){this.$rootScope.$emit("page-height-changed")},e.$inject=["$rootScope","$timeout"],e}();e.ToggleSettingBlockDirective=t,e.ModuleRegister.directive("toggleSettingBlock",t)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(t){function n(i,r,a,s){void 0===r&&(r=!1),void 0===a&&(a=null),void 0===s&&(s=0),t.call(this,i),this.systemId=s,this.customerLevel=a,i&&(this.isDisabled=i.isDisabled,this.readonly=!1,i.upline&&(this.upline=new n(i.upline),this.readonly=i.upline.isDisabled),this.systemId!==e.System.alpha&&this.systemId!==e.System.internalAlpha||this.customerLevel===e.Level.member||(this.readonly=this.readonly||this.isChecked)),r&&(this.oldSetting=new n(this))}return __extends(n,t),Object.defineProperty(n.prototype,"isChecked",{get:function(){return!this.isDisabled},set:function(e){this.isDisabled=!e},enumerable:!0,configurable:!0}),n.prototype.isChanged=function(){var e=t.prototype.isChanged.call(this),n=this.oldSetting;return e||this.isDisabled!==n.isDisabled},n.prototype.copySettingFrom=function(e){var t=!0;if(e){var n=this.isChecked;this.readonly||(this.isChecked=e.isChecked),t=this.validate(),t?this.highlightCopiedFields({isDisabled:!n}):(this.isChecked=n,this.highlightUnablyCopiedField())}return t},n.prototype.validate=function(){return this.upline&&this.upline.isDisabled&&!this.isDisabled?(this.validationState=new e.ValidationResult("DownlineCannotBeEnabledWhenUplineIsDisabled","isDisabled"),!1):(this.resetValidationState(),!0)},n.prototype.toJson=function(){var e={sportTypeId:this.sportTypeId,sportTypeName:this.sportTypeName,betTypeId:this.betTypeId,betTypeName:this.betTypeName,typeName:this.typeName,isDisabled:this.isDisabled};return this.upline&&(e.upline=this.upline.toJson()),e},n}(e.DefaultSettingItem);e.SettingType13Item=t}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";function t(e){return function(t){return e.getLabel(t)}}function n(e){return function(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];return n.unshift(t),e.getFormattedMessage.apply(e,n)}}var i=function(e){function t(t,n){e.call(this,t,n)}return __extends(t,e),t.$inject=["settingType13Resources","parametersService"],t}(e.ResourceService);e.SettingType13ResourceService=i,e.ModuleRegister.service("settingType13ResourceService",i),t.$inject=["settingType13ResourceService"],e.ModuleRegister.filter("settingType13Label",t),n.$inject=["settingType13ResourceService"],e.ModuleRegister.filter("settingType13Message",n)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(t){function n(n,i){var r=this;t.call(this,function(t,n,r){return new e.SettingType13Item(t,n,r,i.systemId)}),this.templateUrl="settingtype13.htm",this.link=function(e){e.valueTemplateUrl="settingtype13-value.htm",e.settingTypeName=r.settingType13ResourceService.getLabel("ProductStatus")},this.settingType13ResourceService=n}return __extends(n,t),n.$inject=["settingType13ResourceService","parametersService"],n}(e.SettingTypeDirective);e.ModuleRegister.directive("settingtype13",t)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(t){function n(i,r,a,s){void 0===r&&(r=!1),void 0===a&&(a=0),void 0===s&&(s=0),t.call(this,i),i&&(this.minBet=i.minBet,this.maxBet=i.maxBet,this.maxPerMatch=i.maxPerMatch,i.upline&&(this.upline=new n(i.upline))),r&&(this.oldSetting=new n(this)),this.$specialCasesAllowed=!e.System.isY7(a);var o=this.$specialCasesAllowed?0:1,u=this.upline?this.upline.minBet:0;this.$minConstraint=Math.max(o,u)}return __extends(n,t),n.prototype.isChanged=function(){var e=t.prototype.isChanged.call(this),n=this.oldSetting;return e||this.minBet!==n.minBet||this.maxBet!==n.maxBet||this.maxPerMatch!==n.maxPerMatch},n.prototype.copySettingFrom=function(e){var t,n,i,r=!0;return e&&(t=this.minBet,n=this.maxBet,i=this.maxPerMatch,this.minBet=e.minBet,this.maxBet=e.maxBet,this.maxPerMatch=e.maxPerMatch,r=this.validate(),r?this.highlightCopiedFields({minBet:t,maxBet:n,maxPerMatch:i}):(this.minBet=t,this.maxBet=n,this.maxPerMatch=i,this.highlightUnablyCopiedField())),r},n.prototype.validate=function(){return!!this.validateRequiredFields()&&(!!this.validateMinBet()&&(!!this.validateMaxBet()&&!!this.validateMaxPerMatch()))},n.prototype.validateRequiredFields=function(){return this.resetValidationState(),null===this.minBet?(this.validationState=new e.ValidationResult("RequiredField","minBet"),!1):null===this.maxBet?(this.validationState=new e.ValidationResult("RequiredField","maxBet"),!1):null!==this.maxPerMatch||(this.validationState=new e.ValidationResult("RequiredField","maxPerMatch"),!1)},n.prototype.validateSpecialCase=function(){return!!(this.$specialCasesAllowed&&0===this.minBet&&this.maxBet>=0&&this.maxBet<=this.maxPerMatch&&this.maxPerMatch<1)},n.prototype.validateMinBet=function(){if(this.resetValidationState(),null!==this.minBet&&!this.validateSpecialCase()){if(this.minBet<this.$minConstraint)return this.validationState=new e.ValidationResult("MinBetMoreThanOrEqualTo","minBet",[this.$minConstraint]),!1;if(this.minBet>this.maxBet)return this.validationState=new e.ValidationResult("MinBetLessThanOrEqualMaxBet","minBet"),!1}return!0},n.prototype.validateMaxBet=function(){if(this.resetValidationState(),null!==this.maxBet&&!this.validateSpecialCase()){if(this.upline.maxBet>0&&this.maxBet>this.upline.maxBet)return this.validationState=new e.ValidationResult("MaxBetLessThanOrEqualTo","maxBet",[this.upline.maxBet]),!1;if(this.maxBet>this.maxPerMatch)return this.validationState=new e.ValidationResult("MaxBetLessThanOrEqualMaxPerMatch","maxBet"),!1;if(this.maxBet<this.minBet)return this.validationState=new e.ValidationResult("MaxBetMoreThanOrEqualMinBet","maxBet"),!1}return!0},n.prototype.validateMaxPerMatch=function(){if(this.resetValidationState(),null!==this.maxPerMatch&&!this.validateSpecialCase()){if(this.upline.maxPerMatch>0&&this.maxPerMatch>this.upline.maxPerMatch)return this.validationState=new e.ValidationResult("MaxPerMatchLessThanOrEqualTo","maxPerMatch",[this.upline.maxPerMatch]),!1;if(this.maxBet>this.maxPerMatch)return this.validationState=new e.ValidationResult("MaxPerMatchMoreThanOrEqualMaxBet","maxPerMatch"),!1}return!0},n}(e.DefaultSettingItem);e.SettingType2Item=t}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";var t=function(){function t(t){var n=this;this.replace=!0,this.templateUrl="bet-setting-input.htm",this.controller=["$scope",function(e){e.updateValue=function(){"minBet"===e.fieldName?e.settingItem.validateMinBet():"maxBet"===e.fieldName?e.settingItem.validateMaxBet():"maxPerMatch"===e.fieldName&&e.settingItem.validateMaxPerMatch()}}],this.link=function(t,i){var r=angular.element(i.children()[0]);r.attr("data-ng-model","settingItem."+t.fieldName),r.attr("data-ng-change","updateValue()"),r.attr("data-numeric-input",""),(t.customerLevel!==e.Level["super"]||"minBet"===t.fieldName&&t.settingItem.upline.minBet>0)&&r.addClass("setting-field--has-constraint"),"minBet"===t.fieldName?i.append(e.StringFormater.format('<span data-ng-if="settingItem.upline.{0} > 0"> >={{settingItem.upline.{0} | formatNumber}}</span>',t.fieldName)):i.append(e.StringFormater.format('<span data-ng-if="settingItem.upline.{0} > 0"> <={{settingItem.upline.{0} | formatNumber}}</span>',t.fieldName)),n.$compile(i)(t)},this.$compile=t}return Object.defineProperty(t.prototype,"scope",{get:function(){return{settingItem:"=",customerLevel:"=",uplineLevel:"=",fieldName:"@"}},enumerable:!0,configurable:!0}),t.$inject=["$compile"],t}();e.ModuleRegister.directive("betSettingInput",t)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";function t(e){return function(t){return e.getLabel(t)}}function n(e){return function(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];return n.unshift(t),e.getFormattedMessage.apply(e,n)}}var i=function(e){function t(t,n){e.call(this,t,n)}return __extends(t,e),t.$inject=["betSettingResources","parametersService"],t}(e.ResourceService);e.BetSettingResourceService=i,e.ModuleRegister.service("betSettingResourceService",i),t.$inject=["betSettingResourceService"],e.ModuleRegister.filter("betSettingLabel",t),n.$inject=["betSettingResourceService"],e.ModuleRegister.filter("betSettingMessage",n)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(t){function n(n,i){var r=this;t.call(this,function(t,n){return new e.SettingType2Item(t,n,i.systemId,i.productId)}),this.templateUrl="settingtype2.htm",this.link=function(e){e.headerTemplateUrl="settingtype2-header.htm",e.valueTemplateUrl="settingtype2-value.htm",e.settingTypeName=r.betSettingResourceService.getLabel("BetSetting")},this.betSettingResourceService=n}return __extends(n,t),n.$inject=["betSettingResourceService","settingTypesSharedValuesService"],n}(e.SettingTypeDirective);e.ModuleRegister.directive("settingtype2",t)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(){function e(){}return e.step=function(e){return e?.5:1},e.roundingFactor=function(e){return 100},e}();e.SettingType1Config=t;var n=function(){function t(){}return t.factory=function(t,n,l,c,p,h){return l===e.Level["super"]?new i(t,n):l===e.Level.master?new r(t,n,h):l===e.Level.agent?new a(t,n,h):l===e.Level.member&&c===e.Level["super"]?new s(t,n,h):l===e.Level.member&&c===e.Level.master?new o(t,n,p,h):l===e.Level.member&&c===e.Level.agent?new u(t,n):void 0},t.fetch=function(e,t){var n=["sportTypeId","sportTypeName","betTypeId","betTypeName","typeName","isLive"];e&&t&&Object.keys(e).filter(function(e){return n.indexOf(e)<0}).forEach(function(n){t[n]=e[n]})},t.detechChanges=function(e){var t=["oldSetting","validationState","highlightState"];return!!e&&Object.keys(e).some(function(n){return t.indexOf(n)<0&&void 0!==e.oldSetting[n]&&e[n]!==e.oldSetting[n]})},t}();e.SettingType1Item=n;var i=function(i){function r(e,t,a){void 0===t&&(t=!1),void 0===a&&(a=!1),i.call(this,e),n.fetch(e,this),this.isSupportPT3DecimalSites=a,t&&(this.oldSetting=new r(this,(!1),a))}return __extends(r,i),r.prototype.isChanged=function(){return n.detechChanges(this)},r.prototype.copySettingFrom=function(e){var t,n,i,r,a=!0;return e&&(t=this.s4,n=this.m4,i=this.a4,r=this.n4,this.s4=e.s4,this.m4=e.m4,this.a4=e.a4,this.n4=e.n4,a=this.validate(),a?this.highlightCopiedFields({s4:t,m4:n,a4:i,n4:r}):(this.s4=t,this.m4=n,this.a4=i,this.n4=r,this.highlightUnablyCopiedField())),a},r.prototype.validate=function(){return this.s4>this.defaultMaxPT?(this.validationState=new e.ValidationResult("SuperMaxPTCannotBeGreaterThan","s4",[this.defaultMaxPT/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1):this.m4>this.s4?(this.validationState=new e.ValidationResult("MasterMaxPTCannotBeGreaterThanSuperMaxPT","m4"),!1):this.a4>this.m4?(this.validationState=new e.ValidationResult("AgentMaxPTCannotBeGreaterThanMasterMaxPT","a4"),!1):this.n4>this.s4?(this.validationState=new e.ValidationResult("SuperMinPTCannotBeGreaterThanSuperMaxPT","n4"),!1):(this.resetValidationState(),!0)},r}(e.DefaultSettingItem);e.SuperPositionTaking=i;var r=function(i){function r(e,t,a){void 0===t&&(t=!1),void 0===a&&(a=!1),i.call(this,e),n.fetch(e,this),this.isSupportPT3DecimalSites=a,t&&(this.oldSetting=new r(this,(!1),a))}return __extends(r,i),r.prototype.isChanged=function(){var e=n.detechChanges(this);return e&&this.correctAutoPtValues(),e},r.prototype.copySettingFrom=function(e){var t,n,i,r,a,s=!0;return e&&(t=this.iS3,n=this.n3,i=this.m3,r=this.uS3,a=this.s3,this.iS3=e.iS3,this.n3=e.n3,this.m3=e.m3,this.uS3=e.uS3,this.s3=e.s3,s=this.validate(),s?this.highlightCopiedFields({iS3:t,n3:n,m3:i,uS3:r,s3:a}):(this.iS3&&!t&&"uS3"===this.validationState.fieldName?this.validationState.fieldName="s3":!this.iS3&&t&&"s3"===this.validationState.fieldName&&(this.validationState.fieldName="uS3"),this.iS3=t,this.n3=n,this.m3=i,this.uS3=r,this.s3=a,this.highlightUnablyCopiedField())),s},r.prototype.copyPtOnMembersFrom=function(e){var t,n=!0;return e&&(t=this.superPTOnMembers,this.superPTOnMembers=e.superPTOnMembers,n=this.validatePtOnMembers(),n?this.highlightCopiedFields({superPTOnMembers:t}):(this.superPTOnMembers=t,this.highlightUnablyCopiedField())),n},r.prototype.validate=function(){if(this.iS3){if(!this.validateAutoPt())return!1}else if(!this.validateManualPt())return!1;return this.n3>this.m3?(this.validationState=new e.ValidationResult("MasterMinPTCannotBeGreaterThanMasterMaxPT","n3"),!1):(this.resetValidationState(),!0)},r.prototype.validateAutoPt=function(){var n=this.n4-this.uS3,i=Math.min(this.s4,this.m4);return this.uS3>this.s4?(this.validationState=new e.ValidationResult("SuperAutoPTCannotBeGreaterThanSuperMaxPT","uS3"),!1):this.m3<n?(this.validationState=new e.ValidationResult("MasterMaxPTCannotBeLessThan","m3",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1):!(this.m3>i)||(this.validationState=new e.ValidationResult("MasterMaxPTCannotBeGreaterThan","m3",[i/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1)},r.prototype.validateManualPt=function(){var n=Math.max(0,this.n4-this.m4),i=Math.max(0,this.n4-this.s3),r=Math.min(this.s4-this.s3,this.m4);return this.s3<n?(this.validationState=new e.ValidationResult("SuperPTCannotBeLessThan","s3",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1):this.s3>this.s4?(this.validationState=new e.ValidationResult("SuperPTCannotBeGreaterThanSuperMaxPT","s3"),!1):this.m3<i?(this.validationState=new e.ValidationResult("MasterMaxPTCannotBeLessThan","m3",[i/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1):!(this.m3>r)||(this.validationState=new e.ValidationResult("MasterMaxPTCannotBeGreaterThan","m3",[r/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1)},r.prototype.validatePtOnMembers=function(){var n=Math.max(0,this.s4-this.m3);return this.superPTOnMembers<0?(this.validationState=new e.ValidationResult("SuperPTOnMemberCannotBeLessThan","superPTOnMembers",[0]),!1):!(this.superPTOnMembers>n)||(this.validationState=new e.ValidationResult("SuperPTOnMemberCannotBeGreaterThan","superPTOnMembers",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1)},r.prototype.correctAutoPtValues=function(){this.iS3&&this.s3>0&&(this.s3=0),!this.iS3&&this.uS3>0&&(this.uS3=0)},r}(e.DefaultSettingItem);e.MasterPositionTaking=r;var a=function(i){function r(e,t,a){void 0===t&&(t=!1),void 0===a&&(a=!1),i.call(this,e),n.fetch(e,this),this.isSupportPT3DecimalSites=a,t&&(this.oldSetting=new r(this,(!1),a))}return __extends(r,i),r.prototype.isChanged=function(){var e=n.detechChanges(this);return e&&this.correctAutoPtValues(),e},r.prototype.copySettingFrom=function(e){var t,n,i,r,a,s=!0;return e&&(t=this.iM2,n=this.n2,i=this.a2,r=this.uM2,a=this.m2,this.iM2=e.iM2,this.n2=e.n2,this.a2=e.a2,this.uM2=e.uM2,this.m2=e.m2,s=this.validate(),s?this.highlightCopiedFields({iM2:t,n2:n,a2:i,uM2:r,m2:a}):(this.iM2&&!t&&"uM2"===this.validationState.fieldName?this.validationState.fieldName="m2":!this.iM2&&t&&"m2"===this.validationState.fieldName&&(this.validationState.fieldName="uM2"),this.iM2=t,this.n2=n,this.a2=i,this.uM2=r,this.m2=a,this.highlightUnablyCopiedField())),s},r.prototype.copyPtOnMembersFrom=function(e){var t,n=!0;return e&&(t=this.masterPTOnMembers,this.masterPTOnMembers=e.masterPTOnMembers,n=this.validatePtOnMembers(),n?this.highlightCopiedFields({masterPTOnMembers:t}):(this.masterPTOnMembers=t,this.highlightUnablyCopiedField())),n},r.prototype.validate=function(){if(this.iM2){if(!this.validateAutoPt())return!1}else if(!this.validateManualPt())return!1;return this.n2>this.a2?(this.validationState=new e.ValidationResult("AgentMinPTCannotBeGreaterThanAgentMaxPT","n2"),!1):(this.resetValidationState(),!0)},r.prototype.validateAutoPt=function(){var n=this.n3-this.uM2,i=Math.min(this.m3,this.a4);return this.uM2>this.m3?(this.validationState=new e.ValidationResult("MasterAutoPTCannotBeGreaterThanMasterMaxPT","uM2"),!1):this.a2<n?(this.validationState=new e.ValidationResult("AgentMaxPTCannotBeLessThan","a2",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1):!(this.a2>i)||(this.validationState=new e.ValidationResult("AgentMaxPTCannotBeGreaterThan","a2",[i/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1)},r.prototype.validateManualPt=function(){var n=Math.max(0,this.n3-this.a4),i=Math.max(0,this.n3-this.m2),r=Math.min(this.m3-this.m2,this.a4);return this.m2<n?(this.validationState=new e.ValidationResult("MasterPTCannotBeLessThan","m2",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1):this.m2>this.m3?(this.validationState=new e.ValidationResult("MasterPTCannotBeGreaterThanMasterMaxPT","m2"),!1):this.a2<i?(this.validationState=new e.ValidationResult("AgentMaxPTCannotBeLessThan","a2",[i/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1):!(this.a2>r)||(this.validationState=new e.ValidationResult("AgentMaxPTCannotBeGreaterThan","a2",[r/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1)},r.prototype.validatePtOnMembers=function(){var n=Math.max(0,this.m3-this.a2);return this.masterPTOnMembers<0?(this.validationState=new e.ValidationResult("MasterPTOnMemberCannotBeLessThan","masterPTOnMembers",[0]),!1):!(this.masterPTOnMembers>n)||(this.validationState=new e.ValidationResult("MasterPTOnMemberCannotBeGreaterThan","masterPTOnMembers",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1)},r.prototype.correctAutoPtValues=function(){this.iM2&&this.m2>0&&(this.m2=0),!this.iM2&&this.uM2>0&&(this.uM2=0)},r}(e.DefaultSettingItem);e.AgentPositionTaking=a;var s=function(i){function r(e,t,a){void 0===t&&(t=!1),void 0===a&&(a=!1),i.call(this,e),n.fetch(e,this),this.isSupportPT3DecimalSites=a,t&&(this.oldSetting=new r(this,(!1),a))}return __extends(r,i),r.prototype.isChanged=function(){var e=n.detechChanges(this);return e&&this.correctAutoPtValues(),e},r.prototype.copySettingFrom=function(e){var t,n,i,r=!0;return e&&(t=this.iS1,n=this.uS1,i=this.s1,this.iS1=e.iS1,this.iS1?this.uS1=e.uS1:this.iS3||(this.s1=e.s1),r=this.validate(),r?this.highlightCopiedFields({iS1:t,uS1:n,s1:i}):(this.iS1&&!t&&"uS1"===this.validationState.fieldName?this.validationState.fieldName="s1":!this.iS1&&t&&"s1"===this.validationState.fieldName&&(this.validationState.fieldName="uS1"),this.iS1=t,this.uS1=n,this.s1=i,this.highlightUnablyCopiedField())),r},r.prototype.validate=function(){if(this.iS1){var n=Math.max(0,this.n4-this.m1-this.a1);if(this.uS1<n)return this.validationState=new e.ValidationResult("SuperAutoPTOnMemberCannotBeLessThan","uS1",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1;if(this.uS1>this.s4)return this.validationState=new e.ValidationResult("SuperAutoPTOnMemberCannotBeGreaterThanSuperMaxPT","uS1"),!1}else if(!this.iS3){var i=Math.max(0,this.n4-this.m1-this.a1),r=Math.max(this.n4-this.m1-this.a1,this.s4-this.m3);if(this.s1<i)return this.validationState=new e.ValidationResult("SuperPTOnMemberCannotBeLessThan","s1",[i/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1;if(this.s1>r)return this.validationState=new e.ValidationResult("SuperPTOnMemberCannotBeGreaterThan","s1",[r/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1}return this.resetValidationState(),!0},r.prototype.correctAutoPtValues=function(){this.iS1&&this.s1>0&&(this.s1=0),!this.iS1&&this.uS1>0&&(this.uS1=0)},r}(e.DefaultSettingItem);e.SuperPositionTakingOnMember=s;var o=function(i){function r(e,t,a,s){if(void 0===t&&(t=!1),void 0===s&&(s=!1),i.call(this,e),n.fetch(e,this),this.isSupportPT3DecimalSites=s,t&&(this.oldSetting=new r(this,(!1),null,s)),a){var o=a.filter(function(e){return"isDisableMasterAutoPT"===e.key});o.length>0&&(this.isDisableMasterAutoPT=!!o[0].value)}}return __extends(r,i),r.prototype.isChanged=function(){var e=n.detechChanges(this);return e&&this.correctAutoPtValues(),e},r.prototype.copySettingFrom=function(e){var t,n,i,r=!0;return e&&(t=this.iM1,n=this.uM1,i=this.m1,this.iM1=e.iM1,this.iM1?this.uM1=e.uM1:this.iM2||(this.m1=e.m1),r=this.validate(),r?this.highlightCopiedFields({iM1:t,uM1:n,m1:i}):(this.iM1&&!t&&"uM1"===this.validationState.fieldName?this.validationState.fieldName="m1":!this.iM1&&t&&"m1"===this.validationState.fieldName&&(this.validationState.fieldName="uM1"),this.iM1=t,this.uM1=n,this.m1=i,this.highlightUnablyCopiedField())),r},r.prototype.validate=function(){if(this.iM1){var n=Math.max(0,this.n3-this.a1);if(this.uM1<n)return this.validationState=new e.ValidationResult("MasterAutoPTOnMemberCannotBeLessThan","uM1",[n/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1;if(this.uM1>this.m3)return this.validationState=new e.ValidationResult("MasterAutoPTOnMemberCannotBeGreaterThanMasterMaxPT","uM1"),!1}else if(!this.iM2||this.isDisableMasterAutoPT){var i=Math.max(0,this.n3-this.a1),r=this.m3-this.a1;if(this.m1<i)return this.validationState=new e.ValidationResult("MasterPTOnMemberCannotBeLessThan","m1",[i/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1;if(this.m1>r)return this.validationState=new e.ValidationResult("MasterPTOnMemberCannotBeGreaterThan","m1",[r/t.roundingFactor(this.isSupportPT3DecimalSites)]),!1}return this.resetValidationState(),!0},r.prototype.correctAutoPtValues=function(){this.iM1&&this.m1>0&&(this.m1=0),!this.iM1&&this.uM1>0&&(this.uM1=0)},r}(e.DefaultSettingItem);e.MasterPositionTakingOnMember=o;var u=function(t){function i(e,r){void 0===r&&(r=!1),t.call(this,e),n.fetch(e,this),r&&(this.oldSetting=new i(this))}return __extends(i,t),i.prototype.isChanged=function(){return n.detechChanges(this)},i.prototype.copySettingFrom=function(e){var t,n=!0;return e&&(t=this.a1,this.a1=e.a1,n=this.validate(),n?this.highlightCopiedFields({a1:t}):(this.a1=t,this.highlightUnablyCopiedField())),n},i.prototype.validate=function(){return this.a1<this.n2?(this.validationState=new e.ValidationResult("AgentPTCannotBeLessThanAgentMinPT","a1"),!1):this.a1>this.a2?(this.validationState=new e.ValidationResult("AgentPTCannotBeGreaterThanAgentMaxPT","a1"),!1):(this.resetValidationState(),!0)},i}(e.DefaultSettingItem);e.AgentPositionTakingOnMember=u}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(){function e(e,t,n){var i=this;this.controller=["$scope",function(e){i.updatingSettingTypePromiseChain.register(e.settingType.id,function(e){return i.updatePositionTakingOnMembers(e)})}],this.$q=e,this.updatingSettingTypePromiseChain=t,this.adjustMemberPtModalService=n}return e.prototype.updatePositionTakingOnMembers=function(e){var t=this.$q.defer();return this.adjustMemberPtModalService.show(e.settingType.settingLevel,e.customerLevel,e.settingType.id,e.settingType.settingItems,function(){t.resolve()}),t.promise},e.$inject=["$q","updatingSettingTypePromiseChain","adjustMemberPtModalService"],e}();e.ModuleRegister.directive("positionTakingOnMember",t);var n=function(){function t(t){var n=this;this.replace=!0,this.templateUrl="adjust-member-pt-modal.htm",this.link=function(t){n.adjustMemberPtModalService.setup(t),t.avaiSettingLevels=e.SettingLevel,t.avaiCustomerLevels=e.Level,t.$watch("settingItems",function(i){angular.isArray(i)&&(t.settingLevel===e.SettingLevel.sportTypeBetType?t.sportGroups=n.groupSettingItemsBySportType(i):t.sportGroups=[i])})},this.adjustMemberPtModalService=t}return Object.defineProperty(t.prototype,"scope",{get:function(){return{}},enumerable:!0,configurable:!0}),t.prototype.groupSettingItemsBySportType=function(e){var t,n=-1,i=new Array;return e.forEach(function(e){var r=e;n!==r.sportTypeId&&(t&&i.push(t),n=r.sportTypeId,t=new Array),t.push(r)}),t&&i.push(t),i},t.$inject=["adjustMemberPtModalService"],t}();e.ModuleRegister.directive("adjustMemberPtModal",n);var i=function(){function t(e,t,n){this.$rootScope=e,this.valueRangeService=t,this.settingTypesSharedValuesService=n}return t.prototype.setup=function(t){var n=this;if(angular.isUndefined(t))throw"Scope of the modal must not be undefied";this.modalScope=t,this.modalScope.divisor=e.SettingType1Config.roundingFactor(this.settingTypesSharedValuesService.isSupportPT3DecimalSites),this.modalScope.cancel=function(){n.modalScope.isShown=!1,n.resolvePromise()},this.modalScope.ok=function(){n.modalScope.isShown=!1,n.modalScope.customerLevel===e.Level.master?n.modalScope.settingItems.forEach(function(e){e.isUpdateSuperPTOnMembers=!0}):n.modalScope.customerLevel===e.Level.agent&&n.modalScope.settingItems.forEach(function(e){e.isUpdateMasterPTOnMembers=!0}),n.resolvePromise()}},t.prototype.show=function(e,t,n,i,r){this.modalScope.isShown&&(this.modalScope.isShown=!1),this.modalScope.submitChanges=r,this.convertData(t,i)?(this.modalScope.settingLevel=e,this.modalScope.customerLevel=t,this.modalScope.settingTypeId=n,this.modalScope.isShown=!0):this.modalScope.submitChanges&&this.modalScope.submitChanges()},t.prototype.convertData=function(t,n){var i=this,r=!1,a=new Array,s=this.settingTypesSharedValuesService.isSupportPT3DecimalSites;return this.modalScope.ranges=new Array,t===e.Level.master?n.forEach(function(t){var n=t;n.iS3||n.iS3===n.oldSetting.iS3||(a.push(n),i.modalScope.ranges.push(i.valueRangeService.generateRanges(n.superPTOnMembers,0,n.s4-n.m3,e.SettingType1Config.step(s),e.SettingType1Config.roundingFactor(s))))}):t===e.Level.agent&&n.forEach(function(t){var n=t;n.iM2||n.iM2===n.oldSetting.iM2||(a.push(n),i.modalScope.ranges.push(i.valueRangeService.generateRanges(n.masterPTOnMembers,0,n.m3-n.a2,e.SettingType1Config.step(s),e.SettingType1Config.roundingFactor(s))))}),a.length>0&&(this.modalScope.settingItems=a,r=!0),r},t.prototype.resolvePromise=function(){this.$rootScope.$emit("updateSetting"),this.modalScope.submitChanges&&this.modalScope.submitChanges()},t.$inject=["$rootScope","valueRangeService","settingTypesSharedValuesService"],t}();e.ModuleRegister.service("adjustMemberPtModalService",i);var r=function(t){function n(){var n=this;t.apply(this,arguments),this.controller=["$scope",function(t){n.copyingSettingService.register(t),t.copyingState=new e.CopyingState(e.PositionModel.fixed),t.copyingState.none(),t.copy=function(){if(n.copyingSettingService.hideTooltips(t),t.settingItems&&t.settingItems.length>1){var i=e.ArrayHelpers.join2DimensionalArray(t.settingItems),r=i[0],a=i.slice(1),s=0,o=!0;t.customerLevel===e.Level.master?s=a.filter(function(t){var n=t.copyPtOnMembersFrom(r);return o=o?n:o,n?t.hasHighlightField(e.NotificationType.success):n}).length:t.customerLevel===e.Level.agent&&(s=a.filter(function(t){var n=t.copyPtOnMembersFrom(r);return o=o?n:o,n?t.hasHighlightField(e.NotificationType.success):n}).length),o?s>0?t.copyingState.succeed():t.copyingState.none():t.copyingState.fail()}},t.hideTooltip=function(){t.copyingState.none()}}]}return __extends(n,t),Object.defineProperty(n.prototype,"scope",{get:function(){return{customerLevel:"=",settingTypeId:"=",settingItems:"="}},enumerable:!0,configurable:!0}),n}(e.CopySettingDirective);e.ModuleRegister.directive("copyPositionTakingOnMember",r)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";function t(e){return function(t){return e.getLabel(t)}}function n(e){return function(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];return n.unshift(t),e.getFormattedMessage.apply(e,n)}}var i=function(e){function t(t,n){e.call(this,t,n)}return __extends(t,e),t.$inject=["positionTakingResources","parametersService"],t}(e.ResourceService);e.PositionTakingResourceService=i,e.ModuleRegister.service("positionTakingResourceService",i),t.$inject=["positionTakingResourceService"],e.ModuleRegister.filter("positionTakingLabel",t),n.$inject=["positionTakingResourceService"],e.ModuleRegister.filter("positionTakingMessage",n)}(CustomerSetting||(CustomerSetting={}));var CustomerSetting;!function(e){"use strict";function t(){return function(t,n){return t/e.SettingType1Config.roundingFactor(n)}}function n(){return function(e){return e.extraData.some(function(e){return"isDisableMasterAutoPT"===e.key&&e.value})}}var i=function(){function t(t,n){var i=this;this.replace=!0,this.templateUrl="position-taking-select.htm",this.controller=["$scope","valueRangeService",function(t,n){var r=i.settingTypesSharedValuesService.isSupportPT3DecimalSites;t.divisor=e.SettingType1Config.roundingFactor(r),t.$watchGroup(["min","max"],function(i,a){t.settingItem&&(angular.equals(a,i)||(t.settingItem[t.fieldName]=Math.max(t.min,Math.min(t.max,t.settingItem[t.fieldName]))),
t.range=n.generateRanges(t.settingItem[t.fieldName],t.min,t.max,e.SettingType1Config.step(r),e.SettingType1Config.roundingFactor(r)))}),t.resetState=function(e){var n=t.settingItem;n.validationState&&!n.validationState.isSuccessful?n.validationState.fieldName===e&&n.resetValidationState():n.resetHighlightState()}}],this.link=function(e,t){var n=t.find("select"),r=i.settingTypesSharedValuesService.isSupportPT3DecimalSites;n.attr("data-ng-change","resetState(fieldName)"),n.attr("data-ng-model","settingItem."+e.fieldName),n.attr("data-ng-options","item.key as (item.value | ptFormat:"+r+") for item in range"),n.attr("data-field-highlighting",""),"true"===e.disabled&&n.attr("disabled","disabled"),i.$compile(t)(e)},this.$compile=t,this.settingTypesSharedValuesService=n}return Object.defineProperty(t.prototype,"scope",{get:function(){return{settingItem:"=",fieldName:"@",min:"@",max:"@",disabled:"@"}},enumerable:!0,configurable:!0}),t.$inject=["$compile","settingTypesSharedValuesService"],t}();e.ModuleRegister.directive("positionTakingSelect",i),e.ModuleRegister.filter("ptFormat",t),e.ModuleRegister.filter("hideIsAutoColumn",n)}(CustomerSetting||(CustomerSetting={}));var __extends=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},CustomerSetting;!function(e){"use strict";var t=function(t){function n(n){var i=this;t.call(this,e.SettingType1Item.factory),this.templateUrl="settingtype1.htm",this.link=function(e){e.headerTemplateUrl="settingtype1-header.htm",e.valueTemplateUrl="settingtype1-value.htm",e.settingTypeName=i.positionTakingResourceService.getLabel("PositionTaking")},this.positionTakingResourceService=n}return __extends(n,t),n.$inject=["positionTakingResourceService"],n}(e.SettingTypeDirective);e.ModuleRegister.directive("settingtype1",t)}(CustomerSetting||(CustomerSetting={})),angular.module("customerSetting").run(["$templateCache",function(e){e.put("BlockUI/block-ui.htm",'<div class="block-ui-overlay" data-ng-show="isLoading"><div class="block-ui-loading" data-ng-show="isLoading"></div></div>'),e.put("CopyWithCheckBoxSettingBlock/copy-with-checkbox-setting-block.htm",'<div class="block" data-ng-show="settingType" data-ng-class="{ \'block--error\': !(settingType | validSettingType) }"><div class="block__header" data-setting-type="{{settingType.id}}" data-ng-class="{ \'block__header--error\': !(settingType | validSettingType) }">{{settingTypeName}}<div class="right-btn-area right-btn-area--seperate" title="{{\'Update\' | label}}" data-ng-if="(settingType | editableSettingType) && (settingType | validSettingType) && normalMode" data-ng-click="update({ settingTypeId: settingType.id })"><span class="icon icon--update"></span></div><toggle-setting-block data-ng-if="togglableSharedStates && togglableSharedStates.toggleStates.length > 1" data-shared-states="togglableSharedStates" data-setting-type-id="settingType.id"></toggle-setting-block></div><div class="block__item-group" data-ng-if="settingType | validSettingType" data-ng-repeat="group in sportGroups"><div class="block__sub-header" data-ng-if="settingType.settingLevel === avaiSettingLevels.sportTypeBetType" data-ng-class="{ \'block__sub-header--border-top\': ($index > 0), \'block__sub-header--no-border-bottom\': togglableSharedStates && !togglableSharedStates.toggleStates[$index] }" data-sport-type="{{group[0].sportTypeId}}">{{group[0].sportTypeName}}<toggle-setting-block data-ng-if="togglableSharedStates && togglableSharedStates.toggleStates.length > 1" data-shared-states="togglableSharedStates" data-block-index="$index" data-setting-data="group" data-setting-type-id="settingType.id"></toggle-setting-block><note-icon-tooltip data-ng-if="group[0].showIconTooltipMessage" data-tooltip-message data-tooltip-message-backup="{{group[0].tooltipMessage}}" data-tooltip-type><copy-setting-with-checkbox data-ng-if="(settingType | editableSettingType) && sportGroups.length > 1 && $first && $parent.$parent.$first" data-setting-type-id="settingType.id" data-setting-items="sportGroups" data-is-multiple="isMultiple" data-copy-mode="copyMode.group"></copy-setting-with-checkbox></note-icon-tooltip></div><div class="block__lbl-group" data-ng-include="headerTemplateUrl" data-ng-hide="togglableSharedStates && !togglableSharedStates.toggleStates[$index]"></div><div class="block__item" data-ng-repeat="item in group" data-ng-hide="togglableSharedStates && !togglableSharedStates.toggleStates[$parent.$index]" data-ng-class="{ \'block__item--before-extra-item\': $last && group.length % 2 === 1 && group.length > 1, \'block__item--last\': $last, \'block__item--near-last\': $index === group.length - 2 && $index % 2 === 0, \'block__item--odd\': $odd, \'block__item--full-width\': group.length === 1 }"><div class="block__lbl" data-product="{{productId}}" data-sport-type="{{item.sportTypeId}}" data-bet-type="{{item.betTypeId}}"><div class="w15-percent block__lbl--no-border" data-ng-if="(settingType | editableSettingType)"><copy-setting-with-checkbox data-ng-if="(settingType | editableSettingType) && group.length > 1 && $first && $parent.$parent.$first" data-setting-type-id="settingType.id" data-setting-items="group" data-is-multiple="isMultiple"></copy-setting-with-checkbox></div><div class="w15-percent block__lbl" data-ng-if="(settingType | editableSettingType)"><update-data-checkbox data-setting-item="item" data-is-min-pt="false" data-is-first="$first && $parent.$parent.$first"></update-data-checkbox></div><div data-ng-class="{\'w70-percent block__lbl block__lbl--no-border\': (settingType | editableSettingType)}"><span class="showing-name">{{item | showingName:settingType.settingLevel:productName }}{{item.isLive ? \'(\' + (\'Live\' | label) + \')\' : \'\'}}</span></div></div><div class="block__value" data-ng-include="valueTemplateUrl"></div></div><div class="block__item block__item--last block__item--extra" data-ng-if="group.length % 2 === 1 && group.length > 1" data-ng-hide="togglableSharedStates && !togglableSharedStates.toggleStates[$index]"><div class="block__lbl"></div><div class="block__value"></div></div></div><div class="block__item block__item--error" data-ng-if="!(settingType | validSettingType)">{{sportGroups[0][0].errorMessage}}</div></div>'),e.put("CustomerInfo/customer-info.htm",'<div class="customer-info"><div class="block" data-ng-if="names"><div class="block__header block__header--no-border">{{names}}</div></div><div class="block" data-ng-if="!names" data-ng-show="info"><div class="block__header">{{::("GeneralInformation" | label)}}</div><div class="block__item"><div class="block__lbl block__lbl--white">{{::("UserName" | label)}}</div><div class="block__value text-field-wrapper">{{info.userName | naValue}}</div></div><div class="block__item block__item--even"><div class="block__lbl block__lbl--white">{{::("Phone" | label)}}</div><div class="block__value text-field-wrapper">{{info.phone | naValue}}</div></div><div class="block__item"><div class="block__lbl block__lbl--white">{{::("FirstName" | label)}}</div><div class="block__value text-field-wrapper">{{info.firstName | naValue}}</div></div><div class="block__item block__item--even"><div class="block__lbl block__lbl--white">{{::("MobilePhone" | label)}}</div><div class="block__value text-field-wrapper">{{info.mobilePhone | naValue}}</div></div><div class="block__item block__item--near-last"><div class="block__lbl block__lbl--white">{{::("LastName" | label)}}</div><div class="block__value text-field-wrapper">{{info.lastName | naValue}}</div></div><div class="block__item block__item--last"><div class="block__lbl block__lbl--white">{{::("Fax" | label)}}</div><div class="block__value text-field-wrapper">{{info.fax | naValue}}</div></div></div></div>'),e.put("DefaultSettingBlock/default-setting-block.htm",'<div class="block" data-ng-show="settingType" data-ng-class="{ \'block--error\': !(settingType | validSettingType) }"><div class="block__header" data-setting-type="{{settingType.id}}" data-ng-class="{ \'block__header--error\': !(settingType | validSettingType) }">{{settingTypeName}}<div class="right-btn-area right-btn-area--seperate" title="{{\'Update\' | label}}" data-ng-if="(settingType | editableSettingType) && (settingType | validSettingType) && normalMode" data-ng-click="update({ settingTypeId: settingType.id })"><span class="icon icon--update"></span></div><toggle-setting-block data-ng-if="togglableSharedStates && togglableSharedStates.toggleStates.length > 1" data-shared-states="togglableSharedStates" data-setting-type-id="settingType.id"></toggle-setting-block></div><div class="block__item-group" data-ng-if="settingType | validSettingType" data-ng-repeat="group in sportGroups"><div class="block__sub-header" data-ng-if="settingType.settingLevel === avaiSettingLevels.sportTypeBetType" data-ng-class="{ \'block__sub-header--border-top\': ($index > 0), \'block__sub-header--no-border-bottom\': togglableSharedStates && !togglableSharedStates.toggleStates[$index] }" data-sport-type="{{group[0].sportTypeId}}">{{group[0].sportTypeName}}<toggle-setting-block data-ng-if="togglableSharedStates && togglableSharedStates.toggleStates.length > 1" data-shared-states="togglableSharedStates" data-block-index="$index" data-setting-data="group" data-setting-type-id="settingType.id"></toggle-setting-block><note-icon-tooltip data-ng-if="group[0].showIconTooltipMessage" data-tooltip-message data-tooltip-message-backup="{{group[0].tooltipMessage}}" data-tooltip-type><copy-setting data-ng-if="(settingType | editableSettingType) && sportGroups.length > 1 && $index === 0" data-setting-type-id="settingType.id" data-setting-items="sportGroups"></copy-setting></note-icon-tooltip></div><div class="block__lbl-group" data-ng-include="headerTemplateUrl" data-ng-hide="togglableSharedStates && !togglableSharedStates.toggleStates[$index]"></div><div class="block__item" data-ng-repeat="item in group" data-ng-hide="togglableSharedStates && !togglableSharedStates.toggleStates[$parent.$index]" data-ng-class="{ \'block__item--before-extra-item\': $last && group.length % 2 === 1 && group.length > 1, \'block__item--last\': $last, \'block__item--near-last\': $index === group.length - 2 && $index % 2 === 0, \'block__item--odd\': $odd, \'block__item--full-width\': group.length === 1 }"><div class="block__lbl" data-product="{{productId}}" data-sport-type="{{item.sportTypeId}}" data-bet-type="{{item.betTypeId}}"><span class="showing-name">{{item | showingName:settingType.settingLevel:productName }}{{item.isLive ? \'(\' + (\'Live\' | label) + \')\' : \'\'}}</span><copy-setting data-ng-if="(settingType | editableSettingType) && group.length > 1 && $index === 0" data-setting-type-id="settingType.id" data-setting-items="group"></copy-setting></div><div class="block__value" data-ng-include="valueTemplateUrl"></div></div><div class="block__item block__item--last block__item--extra" data-ng-if="group.length % 2 === 1 && group.length > 1" data-ng-hide="togglableSharedStates && !togglableSharedStates.toggleStates[$index]"><div class="block__lbl"></div><div class="block__value"></div></div></div><div class="block__item block__item--error" data-ng-if="!(settingType | validSettingType)">{{sportGroups[0][0].errorMessage}}</div></div>'),e.put("SettingResult/setting-result.htm",'<div data-ng-show="result"><div class="msg msg--{{result.msgTypeName}}" data-ng-if="!result.subResults"><div class="icon msg__icon"></div><div class="msg__content" data-ng-bind-html="result.message | unsafe"></div></div><div data-ng-if="result.subResults" data-ng-repeat="subResult in result.subResults"><div class="msg__customer-name" data-ng-if="subResult.customerName">{{subResult.customerName}}</div><div class="msg msg--{{subResult.msgTypeName}}" data-ng-if="!subResult.settingResults"><div class="icon msg__icon"></div><div class="msg__content" data-ng-bind-html="subResult.message | unsafe"></div></div><div class="msg msg--{{settingResult.msgTypeName}}" data-ng-if="subResult.settingResults" data-ng-repeat="settingResult in subResult.settingResults"><div class="icon msg__icon"></div><div class="msg__content"><div class="msg__main-message" data-ng-bind-html="settingResult.message | unsafe"></div><div class="msg__sub-message" data-ng-if="settingResult.messages" data-ng-repeat="message in settingResult.messages" data-ng-bind-html="message | unsafe"></div></div></div></div></div>'),e.put("CopyWithCheckBoxSettingBlock/CopySettingWithCheckbox/copy-setting-with-checkbox.htm",'<div class="right-btn-area"><div title="{{\'CopyTooltip\' | label}}" data-field-highlighting data-tooltip-type="{{copyingState.type}}" data-tooltip-message="{{copyingState.messageKey | message}}" data-tooltip-position-model="{{copyingState.positionModel}}" data-tooltip-displaying-seconds="{{copyingState.displayingSeconds}}" data-displaying-timeout-callback="hideTooltip();" data-ng-click="copy()"><span class="icon icon--copy" data-ng-class="{ \'icon--copy--active\' : isIconActive }"></span></div></div>'),e.put("DefaultSettingBlock/CopySetting/copy-setting.htm",'<div class="right-btn-area"><div title="{{\'CopyTooltip\' | label}}" data-field-highlighting data-tooltip-type="{{copyingState.type}}" data-tooltip-message="{{copyingState.messageKey | message}}" data-tooltip-position-model="{{copyingState.positionModel}}" data-tooltip-displaying-seconds="{{copyingState.displayingSeconds}}" data-displaying-timeout-callback="hideTooltip();" data-ng-click="copy()"><span class="icon icon--copy"></span></div></div>'),e.put("DefaultSettingBlock/HighlightField/tooltip.htm",'<div class="tooltip tooltip--{{highlightType}}" data-ng-if="tooltipMessage"><div class="tooltip__content">{{tooltipMessage}}</div></div>'),e.put("DefaultSettingBlock/NoteIcon/note-icon-tooltip.htm",'<parent><div class="icon icon--warning icon--warning--pointer--margintop setting-field--checkfield__check-ctrl tab" data-tooltip-message="{{tooltipMessage}}" data-tooltip-type="{{tooltipType}}" data-field-highlighting data-ng-mouseenter="showTooltip()" data-ng-mouseleave="removeMessage()"></div></parent>'),e.put("settingtype13-value.htm",'<div class="setting-field-wrapper"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" data-ng-if="settingType | editableSettingType" data-field-highlighting data-field-name="isDisabled" data-highlight-state="item.highlightState" data-tooltip-message="{{item.validationState | tooltipMessage:\'settingType13ResourceService\':\'isDisabled\'}}" data-ng-model="item.isChecked" data-ng-disabled="item.readonly"> <input type="checkbox" class="setting-field--checkfield__check-ctrl" disabled data-ng-if="!(settingType | editableSettingType)" data-ng-model="item.isChecked"> <span data-ng-if="customerLevel === avaiCustomerLevels.super">{{\'Super\' | label}}</span> <span data-ng-if="customerLevel === avaiCustomerLevels.master">{{\'Master\' | label}}</span> <span data-ng-if="customerLevel === avaiCustomerLevels.agent">{{\'Agent\' | label}}</span> <span data-ng-if="customerLevel === avaiCustomerLevels.member">{{\'Member\' | label}}</span></label></div>'),e.put("settingtype13.htm","<div><default-setting-type-grouping></default-setting-type-grouping></div>"),e.put("bet-setting-input.htm",'<div><input type="text" maxlength="14" class="setting-field setting-field--fullwidth setting-field--number" data-field-highlighting data-field-name="{{fieldName}}" data-highlight-state="settingItem.highlightState" data-tooltip-message="{{settingItem.validationState | tooltipMessage:\'betSettingResourceService\':fieldName}}"></div>'),e.put("settingtype2-header.htm",'<div class="block__lbl"></div><div class="w80-percent"><div class="block__lbl w33-percent">{{::(\'MinBet\' | betSettingLabel)}}</div><div class="block__lbl w33-percent">{{::(\'MaxBet\' | betSettingLabel)}}</div><div class="block__lbl block__lbl--no-border w33-percent w33-percent--last">{{::(\'MaxPerMatch\' | betSettingLabel)}}</div></div>'),e.put("settingtype2-value.htm",'<div class="block__cell-group" data-ng-if="settingType | editableSettingType"><bet-setting-input class="block__cell w33-percent setting-field-wrapper" data-setting-item="item" data-upline-level="uplineLevel" data-customer-level="customerLevel" data-field-name="minBet"></bet-setting-input><bet-setting-input class="block__cell w33-percent setting-field-wrapper" data-setting-item="item" data-upline-level="uplineLevel" data-customer-level="customerLevel" data-field-name="maxBet"></bet-setting-input><bet-setting-input class="block__cell block__cell--no-border w33-percent w33-percent--last setting-field-wrapper" data-setting-item="item" data-upline-level="uplineLevel" data-customer-level="customerLevel" data-field-name="maxPerMatch"></bet-setting-input></div><div class="block__cell-group" data-ng-if="!(settingType | editableSettingType)"><div class="block__cell w33-percent align-center">{{item.minBet | formatNumber}}</div><div class="block__cell w33-percent align-center">{{item.maxBet | formatNumber}}</div><div class="block__cell block__cell--no-border w33-percent w33-percent--last align-center">{{item.maxPerMatch | formatNumber}}</div></div>'),e.put("settingtype2.htm",'<div><default-setting-type-grouping class="full-width"></default-setting-type-grouping></div>'),e.put("adjust-member-pt-modal.htm",'<div class="modal-overlay" data-ng-show="isShown"><div class="modal" data-draggable="\'.modal__title\'"><div class="modal__title"><div data-ng-if="customerLevel === avaiCustomerLevels.master">{{\'DisableMasterAutoPt\' | positionTakingLabel}}</div><div data-ng-if="customerLevel === avaiCustomerLevels.agent">{{\'DisableAgentAutoPt\' | positionTakingLabel}}</div></div><div class="modal__content" data-detect-max-height><div class="question-block" data-ng-bind-html="\'UpdateMemberPtQuestion\' | positionTakingLabel | unsafe"></div><div class="block full-width"><div class="block__item-group" data-ng-repeat="group in sportGroups"><div class="block__sub-header" data-ng-if="settingLevel === avaiSettingLevels.sportTypeBetType" data-ng-class="{ \'block__sub-header--border-top\': ($index > 0) }" data-sport-type="{{group[0].sportTypeId}}">{{group[0].sportTypeName}}<copy-position-taking-on-member data-ng-if="sportGroups.length > 1 && $index === 0" data-customer-level="customerLevel" data-setting-type-id="settingTypeId" data-setting-items="sportGroups"></copy-position-taking-on-member></div><div class="block__item" data-ng-repeat="item in group" data-ng-class="{ \'block__item--last\': $last }"><div class="block__lbl block__lbl--half-width" data-sport-type="{{item.sportTypeId}}" data-bet-type="{{item.betTypeId}}"><span class="showing-name">{{item | showingName:settingLevel:productName }} {{item.isLive ? \'(\' + (\'Live\' | label) + \')\' : \'\'}}</span><copy-position-taking-on-member data-ng-if="group.length > 1 && $index === 0" data-customer-level="customerLevel" data-setting-type-id="settingTypeId" data-setting-items="group"></copy-position-taking-on-member></div><div class="block__value block__value--half-width"><div class="setting-field-wrapper"><select class="setting-field" data-ng-if="customerLevel === avaiCustomerLevels.master" data-ng-model="item.superPTOnMembers" data-field-highlighting data-field-name="superPTOnMembers" data-highlight-state="item.highlightState" data-ng-options="item.key as (item.value | ptFormat) for item in ranges[$index]"></select><select class="setting-field" data-ng-if="customerLevel === avaiCustomerLevels.agent" data-ng-model="item.masterPTOnMembers" data-field-highlighting data-field-name="masterPTOnMembers" data-highlight-state="item.highlightState" data-ng-options="item.key as (item.value | ptFormat) for item in ranges[$index]"></select></div></div></div></div></div></div><div class="modal__footer"><button class="btn btn--small" data-ng-click="ok()">{{\'Yes\' | label}}</button> <button class="btn btn--small" data-ng-click="cancel()">{{\'No\' | label}}</button></div></div></div>'),e.put("position-taking-select.htm",'<div><div class="setting-field-wrapper"><select class="setting-field" data-field-name="{{fieldName}}" data-highlight-state="settingItem.highlightState" data-tooltip-message="{{settingItem.validationState | tooltipMessage:\'positionTakingResourceService\':fieldName}}"></select></div></div>'),e.put("settingtype1-header.htm",'<div class="block__lbl"></div><div class="w80-percent" data-ng-if="customerLevel === avaiCustomerLevels.super"><div class="block__lbl w25-percent">{{::(\'SuperMaxPT\' | positionTakingLabel)}}</div><div class="block__lbl w25-percent">{{::(\'MasterMaxPT\' | positionTakingLabel)}}</div><div class="block__lbl w25-percent">{{::(\'AgentMaxPT\' | positionTakingLabel)}}</div><div class="block__lbl block__lbl--no-border w25-percent">{{::(\'SuperMinPT\' | positionTakingLabel)}}</div></div><div class="w80-percent" data-ng-if="customerLevel === avaiCustomerLevels.master"><div class="block__lbl w28-percent">{{::(\'SuperPT\' | positionTakingLabel)}}</div><div class="block__lbl w16-percent">{{::(\'IsAuto\' | positionTakingLabel)}}</div><div class="block__lbl w28-percent">{{::(\'MasterMaxPT\' | positionTakingLabel)}}</div><div class="block__lbl block__lbl--no-border w28-percent">{{::(\'MasterMinPT\' | positionTakingLabel)}}</div></div><div class="w80-percent" data-ng-if="customerLevel === avaiCustomerLevels.agent"><div class="block__lbl w28-percent" data-ng-class="{ \'w33-percent\': (settingType | hideIsAutoColumn) }">{{::(\'MasterPT\' | positionTakingLabel)}}</div><div class="block__lbl w16-percent" data-ng-if="!(settingType | hideIsAutoColumn)">{{::(\'IsAuto\' | positionTakingLabel)}}</div><div class="block__lbl w28-percent" data-ng-class="{ \'w33-percent\': (settingType | hideIsAutoColumn) }">{{::(\'AgentMaxPT\' | positionTakingLabel)}}</div><div class="block__lbl block__lbl--no-border w28-percent" data-ng-class="{ \'w33-percent--last\': (settingType | hideIsAutoColumn) }">{{::(\'AgentMinPT\' | positionTakingLabel)}}</div></div><div class="w80-percent" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.super"><div class="block__lbl w28-percent">{{::(\'SuperPT\' | positionTakingLabel)}}</div><div class="block__lbl w16-percent">{{::(\'IsAuto\' | positionTakingLabel)}}</div><div class="block__lbl w28-percent">{{::(\'MasterPT\' | positionTakingLabel)}}</div><div class="block__lbl block__lbl--no-border w28-percent">{{::(\'AgentPT\' | positionTakingLabel)}}</div></div><div class="w80-percent" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.master"><div class="block__lbl w42-percent" data-ng-class="{ \'w50-percent\': (settingType | hideIsAutoColumn) }">{{::(\'MasterPT\' | positionTakingLabel)}}</div><div class="block__lbl w16-percent" data-ng-if="!(settingType | hideIsAutoColumn)">{{::(\'IsAuto\' | positionTakingLabel)}}</div><div class="block__lbl block__lbl--no-border w42-percent" data-ng-class="{ \'w50-percent\': (settingType | hideIsAutoColumn) }">{{::(\'AgentPT\' | positionTakingLabel)}}</div></div><div class="block__lbl block__lbl--no-border w80-percent" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.agent">{{::(\'AgentPT\' | positionTakingLabel)}}</div>'),e.put("settingtype1-value.htm",'<div class="block__cell-group" data-ng-if="settingType | editableSettingType"><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.super"><div class="block__cell w25-percent align-center"><position-taking-select data-setting-item="item" data-field-name="s4" data-min="{{0}}" data-max="{{item.defaultMaxPT}}"></position-taking-select></div><div class="block__cell w25-percent align-center"><position-taking-select data-setting-item="item" data-field-name="m4" data-min="{{0}}" data-max="{{item.s4}}"></position-taking-select></div><div class="block__cell w25-percent align-center"><position-taking-select data-setting-item="item" data-field-name="a4" data-min="{{0}}" data-max="{{item.m4}}"></position-taking-select></div><div class="block__cell block__cell--no-border w25-percent align-center"><position-taking-select data-setting-item="item" data-field-name="n4" data-min="{{0}}" data-max="{{item.s4}}"></position-taking-select></div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.master" data-position-taking-on-member><div class="block__cell w28-percent align-center" data-ng-show="item.iS3"><position-taking-select data-setting-item="item" data-field-name="uS3" data-min="{{0}}" data-max="{{item.s4}}"></position-taking-select></div><div class="block__cell w28-percent align-center" data-ng-show="!item.iS3"><position-taking-select data-setting-item="item" data-field-name="s3" data-min="{{[0, item.n4 - item.m4] | maxValue}}" data-max="{{item.s4}}"></position-taking-select></div><div class="block__cell w16-percent align-center"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" data-field-highlighting data-field-name="iS3" data-highlight-state="item.highlightState" data-ng-change="item.resetFieldHighlightState(\'iS3\');" data-ng-model="item.iS3"></label></div><div class="block__cell w28-percent align-center"><position-taking-select data-setting-item="item" data-field-name="m3" data-min="{{item.iS3 ? item.n4 - item.uS3 : ([0, item.n4 - item.s3] | maxValue)}}" data-max="{{item.iS3 ? ([item.s4, item.m4] | minValue) : ([item.s4 - item.s3, item.m4] | minValue)}}"></position-taking-select></div><div class="block__cell block__cell--no-border w28-percent align-center"><position-taking-select data-setting-item="item" data-field-name="n3" data-min="{{0}}" data-max="{{item.m3}}"></position-taking-select></div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.agent" data-position-taking-on-member><div class="block__cell w28-percent align-center" data-ng-show="item.iM2 && !(settingType | hideIsAutoColumn)"><position-taking-select data-setting-item="item" data-field-name="uM2" data-min="{{0}}" data-max="{{item.m3}}"></position-taking-select></div><div class="block__cell w28-percent align-center" data-ng-show="!item.iM2 || (settingType | hideIsAutoColumn)" data-ng-class="{ \'w33-percent\': (settingType | hideIsAutoColumn) }"><position-taking-select data-setting-item="item" data-field-name="m2" data-min="{{[0, item.n3 - item.a4] | maxValue}}" data-max="{{item.m3}}"></position-taking-select></div><div class="block__cell w16-percent align-center" data-ng-if="!(settingType | hideIsAutoColumn)"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" data-field-highlighting data-field-name="iM2" data-highlight-state="item.highlightState" data-ng-change="item.resetFieldHighlightState(\'iM2\');" data-ng-model="item.iM2"></label></div><div class="block__cell w28-percent align-center" data-ng-class="{ \'w33-percent\': (settingType | hideIsAutoColumn) }"><position-taking-select data-setting-item="item" data-field-name="a2" data-min="{{item.iM2 ? item.n3 - item.uM2 : ([0, item.n3 - item.m2] | maxValue)}}" data-max="{{item.iM2 ? ([item.m3, item.a4] | minValue) : ([item.m3 - item.m2, item.a4] | minValue)}}"></position-taking-select></div><div class="block__cell block__cell--no-border w28-percent align-center" data-ng-class="{ \'w33-percent--last\': (settingType | hideIsAutoColumn) }"><position-taking-select data-setting-item="item" data-field-name="n2" data-min="{{0}}" data-max="{{item.a2}}"></position-taking-select></div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.super"><div class="block__cell w28-percent align-center" data-ng-show="item.iS1"><position-taking-select data-setting-item="item" data-field-name="uS1" data-min="{{[0, item.n4 - item.m1 - item.a1] | maxValue}}" data-max="{{item.s4}}"></position-taking-select></div><div class="block__cell w28-percent align-center" data-ng-show="!item.iS1"><position-taking-select data-setting-item="item" data-field-name="s1" data-min="{{[0, item.n4 - item.m1 - item.a1] | maxValue}}" data-max="{{[item.n4 - item.m1 - item.a1, item.s4 - item.m3] | maxValue}}" data-disabled="{{item.iS3}}"></position-taking-select></div><div class="block__cell w16-percent align-center"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" data-field-highlighting data-field-name="iS1" data-highlight-state="item.highlightState" data-ng-change="item.resetFieldHighlightState(\'iS1\');" data-ng-model="item.iS1"></label></div><div class="block__cell w28-percent align-center">{{item.m1 | ptFormat}}</div><div class="block__cell block__cell--no-border w28-percent align-center">{{item.a1 | ptFormat}}</div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.master"><div class="block__cell w42-percent align-center" data-ng-show="item.iM1 && !(settingType | hideIsAutoColumn)"><position-taking-select data-setting-item="item" data-field-name="uM1" data-min="{{[0, item.n3 - item.a1] | maxValue}}" data-max="{{item.m3}}"></position-taking-select></div><div class="block__cell w42-percent align-center" data-ng-show="!item.iM1 || (settingType | hideIsAutoColumn)" data-ng-class="{ \'w50-percent\': (settingType | hideIsAutoColumn) }"><position-taking-select data-setting-item="item" data-field-name="m1" data-min="{{[0, item.n3 - item.a1] | maxValue}}" data-max="{{item.m3 - item.a1}}" data-disabled="{{item.iM2 && !(settingType | hideIsAutoColumn)}}"></position-taking-select></div><div class="block__cell w16-percent align-center" data-ng-if="!(settingType | hideIsAutoColumn)"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" data-field-highlighting data-field-name="iM1" data-highlight-state="item.highlightState" data-ng-change="item.resetFieldHighlightState(\'iM1\');" data-ng-model="item.iM1"></label></div><div class="block__cell block__cell--no-border w42-percent align-center" data-ng-class="{ \'w50-percent\': (settingType | hideIsAutoColumn) }">{{item.a1 | ptFormat}}</div></div><div class="block__cell-group align-center" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.agent"><div><position-taking-select data-setting-item="item" data-field-name="a1" data-min="{{item.n2}}" data-max="{{item.a2}}"></position-taking-select></div></div></div><div class="block__cell-group" data-ng-if="!(settingType | editableSettingType)"><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.super"><div class="block__cell w25-percent align-center">{{item.s4 | ptFormat}}</div><div class="block__cell w25-percent align-center">{{item.m4 | ptFormat}}</div><div class="block__cell w25-percent align-center">{{item.a4 | ptFormat}}</div><div class="block__cell block__cell--no-border w25-percent align-center">{{item.n4 | ptFormat}}</div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.master"><div class="block__cell w28-percent align-center" data-ng-if="item.iS3">{{item.uS3 | ptFormat}}</div><div class="block__cell w28-percent align-center" data-ng-if="!item.iS3">{{item.s3 | ptFormat}}</div><div class="block__cell w16-percent align-center"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" disabled data-ng-model="item.iS3"></label></div><div class="block__cell w28-percent align-center">{{item.m3 | ptFormat}}</div><div class="block__cell block__cell--no-border w28-percent align-center">{{item.n3 | ptFormat}}</div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.agent"><div class="block__cell w28-percent align-center" data-ng-if="item.iM2 && !(settingType | hideIsAutoColumn)">{{item.uM2 | ptFormat}}</div><div class="block__cell w28-percent align-center" data-ng-if="!item.iM2 || (settingType | hideIsAutoColumn)" data-ng-class="{ \'w33-percent\': (settingType | hideIsAutoColumn) }">{{item.m2 | ptFormat}}</div><div class="block__cell w16-percent align-center" data-ng-if="!(settingType | hideIsAutoColumn)"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" disabled data-ng-model="item.iM2"></label></div><div class="block__cell w28-percent align-center" data-ng-class="{ \'w33-percent\': (settingType | hideIsAutoColumn) }">{{item.a2 | ptFormat}}</div><div class="block__cell block__cell--no-border w28-percent align-center" data-ng-class="{ \'w33-percent--last\': (settingType | hideIsAutoColumn) }">{{item.n2 | ptFormat}}</div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.super"><div class="block__cell w28-percent align-center" data-ng-if="item.iS1">{{item.uS1 | ptFormat}}</div><div class="block__cell w28-percent align-center" data-ng-if="!item.iS1">{{item.s1 | ptFormat}}</div><div class="block__cell w16-percent align-center"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" disabled data-ng-model="item.iS1"></label></div><div class="block__cell w28-percent align-center">{{item.m1 | ptFormat}}</div><div class="block__cell block__cell--no-border w28-percent align-center">{{item.a1 | ptFormat}}</div></div><div class="block__cell-group" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.master"><div class="block__cell w42-percent align-center" data-ng-if="item.iM1 && !(settingType | hideIsAutoColumn)">{{item.uM1 | ptFormat}}</div><div class="block__cell w42-percent align-center" data-ng-if="!item.iM1 || (settingType | hideIsAutoColumn)" data-ng-class="{ \'w50-percent\': (settingType | hideIsAutoColumn) }">{{item.m1 | ptFormat}}</div><div class="block__cell w16-percent align-center" data-ng-if="!(settingType | hideIsAutoColumn)"><label class="setting-field--checkfield"><input type="checkbox" class="setting-field--checkfield__check-ctrl" disabled data-ng-model="item.iM1"></label></div><div class="block__cell block__cell--no-border w42-percent align-center" data-ng-class="{ \'w50-percent\': (settingType | hideIsAutoColumn) }">{{item.a1 | ptFormat}}</div></div><div class="block__cell-group align-center" data-ng-if="customerLevel === avaiCustomerLevels.member && uplineLevel === avaiCustomerLevels.agent">{{item.a1 | ptFormat}}</div></div>'),
e.put("settingtype1.htm",'<div class="setting-type-1"><default-setting-type-grouping class="full-width"></default-setting-type-grouping><adjust-member-pt-modal data-ng-if="settingType | editableSettingType"></adjust-member-pt-modal></div>')}]),function(){"use strict";var e={};e.label=e.label||{},e.label["en-us"]={Agent:"Agent",Agents:"Agents",CopyTooltip:"Click to duplicate the values",CustomerSetting:"Customer Setting",Fax:"Fax",FirstName:"First Name",GeneralInformation:"General Information",LastName:"Last Name",Live:"Live",Master:"Master",Masters:"Masters",Member:"Member",Members:"Members",MobilePhone:"Mobile Phone",Nickname:"Nickname",No:"No",Phone:"Phone",Super:"Super",Update:"Update",UserName:"Username",UserNames:"Usernames",Yes:"Yes"},e.label=e.label||{},e.label["zh-cn"]={Agent:"代理",Agents:"代理",CopyTooltip:"点击以复制数值",CustomerSetting:"会员设定",Fax:"传真",FirstName:"名",GeneralInformation:"基本信息",LastName:"姓",Live:"现场",Master:"总代理",Masters:"总代理",Member:"会员",Members:"会员",MobilePhone:"手机",Nickname:"昵称",No:"无",Phone:"电话",Super:"超级总代理",Update:"更改",UserName:"用户名",UserNames:"用户名",Yes:"是"},e.label=e.label||{},e.label["zh-tw"]={Agent:"代理",Agents:"代理",CopyTooltip:"點擊以復制數值",CustomerSetting:"會員設定",Fax:"傳真",FirstName:"名",GeneralInformation:"基本信息",LastName:"姓",Live:"現場",Master:"總代理",Masters:"總代理",Member:"會員",Members:"會員",MobilePhone:"手機",Nickname:"暱稱",No:"無",Phone:"電話",Super:"超級總代理",Update:"更改",UserName:"用戶名",UserNames:"用戶名",Yes:"是"},e.label=e.label||{},e.label["ko-kr"]={Agents:"에이전트",CustomerSetting:"Customer Setting",Fax:"팩스",FirstName:"이름",GeneralInformation:"일반 정보",LastName:"성",Live:"라이브",Masters:"마스터",Members:"회원",MobilePhone:"핸드폰",Nickname:"닉네임",No:"아니오",Phone:"전화",Update:"업데이트",UserName:"사용자 이름",Yes:"Yes"},e.label=e.label||{},e.label["ja-jp"]={Fax:"ファックス",FirstName:"名",GeneralInformation:"General Information",LastName:"氏",Live:"ライブ",MobilePhone:"携帯電話番号",Nickname:"ニックネーム",No:"No",Phone:"電話番号",Update:"更新",UserName:"ユーザー名",Yes:"Yes"},e.label=e.label||{},e.label["th-th"]={Agent:"เอเย่นต์",Agents:"เอเจนท์",CopyTooltip:"คลิ๊กค่าที่จะซ้ำกัน",CustomerSetting:"ตั้งค่าสมาชิก",Fax:"แฟกซ์",FirstName:"ชื่อ",GeneralInformation:"ข้อมูลทั่วไป",LastName:"นามสกุล",Live:"สด",Master:"มาสเตอร์",Masters:"มาสเตอร์ต่าง ๆ",Member:"สมาชิก",Members:"เมมเบอร์ต่าง ๆ",MobilePhone:"โทรศัพท์มือถือ",Nickname:"ชื่อเล่น",No:"ไม่",Phone:"โทรศัพท์",Super:"ซุปเปอร์",Update:"ล่าสุด",UserName:"ชื่อผู้ใช้ระบบ",UserNames:"ยูเซอร์เนม",Yes:"ตกลง"},e.label=e.label||{},e.label["vi-vn"]={Agents:"Agents",CustomerSetting:"Thiết lập",Fax:"Fax",FirstName:"Tên",GeneralInformation:"Thông tin Chung",LastName:"Họ",Live:"Trực Tuyến",Masters:"Masters",Member:"Người chơi",Members:"Members",MobilePhone:"Điện thoại di động",Nickname:"Biệt danh",No:"Không",Phone:"Điện thoại",Update:"Cập nhật",UserName:"Tài khoản",Yes:"Có"},e.message=e.message||{},e.message["en-us"]={AccessDeniedMsg:"Sorry, You don&apos;t have permission to access this page.",AccountWasDisabled:"This account was disabled.",CopiedSuccessfully:"All fields in green are copied.",CopyingFailed:"Unable to copy to the fields in red.",CustomersAreNotSameLine:"Customers are not in the same line!",DataIsNotValid:"Data is not valid.",DuplicateValuesAlertMsg:"Out of range for highlighted values.",Msg_90001:"Update process is unsuccessful !",NoChangeToUpdate:"There is no change to update.",ProductNameMissing:"Product Id {0} does not have name in {1}",SettingTypeError:"Unable to load the setting type due to a technical problem. Please try again later or notify system administrator!",SettingTypeHasNoSettingItem:"Setting Type {0} does not have any setting item.",SportTypeOrBetTypeIsInvalid:"Sport Type or Bet Type is invalid!",SubAccountIdShouldBeZeroWhenSystemIsNotAgency:"Sub Account Id Should Be Zero When System Is Not Agency.",SystemError:"Oops... you are here because of a system error, please notify system administrator.",UplineLevelCannotBeNullIncaseOfEdittingMember:"Upline Level cannot be null incase of editting member.",UplineLevelIsNotValid:"Upline Level is not valid.",UplineLevelShouldBeNull:"Upline Level should be null.",YouAreNotAuthorized:"You are not authorized on this function!"},e.message=e.message||{},e.message["zh-cn"]={AccessDeniedMsg:"对不起,您不被允许进入此网页.",AccountWasDisabled:"这个帐号禁用",CopiedSuccessfully:"绿色的所有字段已被复制。",CopyingFailed:"红色字段无法复制。",CustomersAreNotSameLine:"会员不同线！",DuplicateValuesAlertMsg:"突显值已超出范围。",Msg_90001:"更新程序失败！",NoChangeToUpdate:"没有任何的变化更新。",SettingTypeError:"由于技术故障导致加载设置类型失败。请稍后再试或联系系统管理员！",SystemError:"哎哟..因为系统故障所以您会身在此处，请向管理层联系吧",YouAreNotAuthorized:"您没有使用此功能的权限"},e.message=e.message||{},e.message["zh-tw"]={AccessDeniedMsg:"對不起, 您不被允許進入此網頁.",AccountWasDisabled:"這個帳號禁用",CopiedSuccessfully:"綠色的所有字段已被複製。",CopyingFailed:"紅色字段無法複製。",CustomersAreNotSameLine:"會員不同線！",DuplicateValuesAlertMsg:"突顯值已超出範圍。",Msg_90001:"更新程序失敗！",NoChangeToUpdate:"沒有任何的變化更新。",SettingTypeError:"由於技術故障導致加載設置類型失敗。請稍後再試或聯系系統管理員！",SystemError:"哎喲..因為系統故障所以您會身在此處，請向管理層聯繫吧",YouAreNotAuthorized:"您沒有使用此功能的權限"},e.message=e.message||{},e.message["ko-kr"]={AccessDeniedMsg:"죄송합니다. 이페이지를 엑서스 할 수 있는 권한이 없습니다.",AccountWasDisabled:"이 계정은 실행하실수없는 계정입니다.",SystemError:"죄송합니다. 시스템에러가 발생했습니다. 관리자에게 문의 바랍니다.",YouAreNotAuthorized:"해당 기능에 대한 권한이 없습니다."},e.message=e.message||{},e.message["ja-jp"]={AccessDeniedMsg:"Sorry, You don&apos;t have permission to access this page.",AccountWasDisabled:"This account was disabled.",SystemError:"Oops... you are here because of a system error, please notify the administrators"},e.message=e.message||{},e.message["th-th"]={AccessDeniedMsg:"ขออภัยคุณไม่ได้รับอนุญาติให้เข้าหน้านี้",AccountWasDisabled:"บัญชีนี้ได้ถูกปิดไปแล้ว",CopiedSuccessfully:"ช่องสีเขียวทั้งหมดจะถูกคัดลอก",CopyingFailed:"ไม่สามารถคัดลอกไปยังช่องสีแดง",CustomersAreNotSameLine:"ลูกเค้าไม่ได้อยู่ในสายเดียวกัน",DuplicateValuesAlertMsg:"ตัวเลขที่ถูกไฮไลท์ไม่ได้อยู่ในขอบเขต",Msg_90001:"กระบวนการอัพเดทสำเร็จแล้ว",NoChangeToUpdate:"ไม่มีการเปลี่ยนแปลงการอัพเดท",SettingTypeError:"ไม่สามารถโหลดการตั้งค่าต่าง ๆ เนื่องจากปัญหาทางด้านเทคนิค รบกวนลองใหม่อีกครั้งหรือจนกว่าจะได้รับคำประกาศจากทางผู้ดูแลระบบ",SystemError:"อุ้ย...คุณอยู่ที่นี่เนื่องจากระบบผิดพลาด กรุณาแจ้งผู้ให้บริการ",YouAreNotAuthorized:"คุณไม่มีอำนาจในการเข้าใช้งานฟังก์ชันนี้"},e.message=e.message||{},e.message["vi-vn"]={AccessDeniedMsg:"Xin lỗi, bạn không được phép truy cập vào trang này",AccountWasDisabled:"Tài khoản bị vô hiệu hóa",CopiedSuccessfully:"Tất cả những ô được gán giá trị sao chép sẽ được đánh dấu xanh.",CopyingFailed:"Không thể gán giá trị được sao chép vào những ô được đánh dấu đỏ.",NoChangeToUpdate:"Không có thay đổi mới được cập nhật.",SystemError:"Oh …hệ thống đang bi lỗi,Vui lòng báo với người quản lý",YouAreNotAuthorized:"Bạn không được cho phép sử dụng chức năng này"},angular.module("customerSetting").value("customerSettingResource",e)}(),function(){"use strict";var e={};e.label=e.label||{},e.label["en-us"]={ProductStatus:"Product Status"},e.label=e.label||{},e.label["zh-cn"]={ProductStatus:"产品状态"},e.label=e.label||{},e.label["zh-tw"]={ProductStatus:"產品狀態"},e.label=e.label||{},e.label["th-th"]={ProductStatus:"สถานะผลิตภันฑ์"},e.label=e.label||{},e.label["vi-vn"]={ProductStatus:"Trạng thái trò chơi"},e.message=e.message||{},e.message["en-us"]={DownlineCannotBeEnabledWhenUplineIsDisabled:"Downline cannot be enabled when Upline is disabled.",Msg_60033:"Unable to edit downline&apos;s Product Status because the account disabled for this product.",Msg_60035:"You are not allowed to disable downline&apos;s product.",Msg_90012:"You are not allowed to perform this action.",UpdateProductStatusSuccessfully:"Product Status is updated successfully."},e.message=e.message||{},e.message["zh-cn"]={DownlineCannotBeEnabledWhenUplineIsDisabled:"当上线被禁用下线同时也禁用。",Msg_60033:"由于账号的此产品已关闭导致无法更改下线产品状态。",Msg_60035:"您不能禁用下线的产品。",Msg_90012:"您不能执行此操作。",UpdateProductStatusSuccessfully:"产品状态更新成功"},e.message=e.message||{},e.message["zh-tw"]={DownlineCannotBeEnabledWhenUplineIsDisabled:"當上線被禁用下線同時也禁用。",Msg_60033:"由於賬號的此產品已關閉導致無法更改下線產品狀態。",Msg_60035:"您不能禁用下線的產品。",Msg_90012:"您不能執行此操作。",UpdateProductStatusSuccessfully:"產品狀態更新成功"},e.message=e.message||{},e.message["th-th"]={DownlineCannotBeEnabledWhenUplineIsDisabled:"เมื่อต้นสายมีการปิดการใช้งาน ดาวน์ไลนก็จะถูกปิดการใช้งานด้วย",Msg_60033:"ไม่สามารถแก้ไขสถานะผลิตภัณฑ์ของดาวน์ไลน์ได้ เนื่องจากไม่ได้เปิดการใช้งานผลิตภัณฑ์นี้",Msg_60035:"คุณไม่สามารถปิดการใช้งานสินค้าให้กับดาวไลน์",Msg_90012:"คุณไม่สามารถดำเนินการนี้",UpdateProductStatusSuccessfully:"สถานะผลิตภันฑ์ อัพเดทสำเร็จแล้ว"},e.message=e.message||{},e.message["vi-vn"]={DownlineCannotBeEnabledWhenUplineIsDisabled:"Thành viên cấp dưới sẽ không được phép chơi trong trường hợp thành viên cấp trên bị cấm chơi.",Msg_60033:"Không thể tùy chỉnh trạng thái trò chơi của thành viên cấp dưới bởi vì tài khoản hiện tại không được phép chơi trò này.",Msg_60035:"Bạn không được phép vô hiệu hoá sản phẩm cược của cấp dưới.",Msg_90012:"Bạn không thể thực thi hành động này.",UpdateProductStatusSuccessfully:"Trạng thái trò chơi được cập nhật thành công."},angular.module("customerSetting").value("settingType13Resources",e)}(),function(){"use strict";var e={};e.label=e.label||{},e.label["en-us"]={BetSetting:"Bet Setting",MaxBet:"Max Bet",MaxPerMatch:"Max Per Match",MinBet:"Min Bet"},e.label=e.label||{},e.label["zh-cn"]={BetSetting:"投注设定",MaxBet:"最高投注",MaxPerMatch:"单场限额",MinBet:"最低投注"},e.label=e.label||{},e.label["zh-tw"]={BetSetting:"投注設定",MaxBet:"最高投注",MaxPerMatch:"單場限額",MinBet:"最低投注"},e.label=e.label||{},e.label["ko-kr"]={BetSetting:"배팅 설정",MaxBet:"최대 배팅",MaxPerMatch:"경기당 최대 값",MinBet:"최소 배팅"},e.label=e.label||{},e.label["ja-jp"]={MaxBet:"最大ベット",MaxPerMatch:"1 試合毎の最大"},e.label=e.label||{},e.label["th-th"]={BetSetting:"การกำหนดการวางเดิมพัน",MaxBet:"ค่าเดิมพันสูงสุด",MaxPerMatch:"ค่าเดิมพันสูงสุดต่อคู่การแข่งขัน",MinBet:"ค่าเดิมพันต้ำสุด"},e.label=e.label||{},e.label["vi-vn"]={BetSetting:"Tiền Cược",MaxBet:"Cược lớn nhất",MaxPerMatch:"Cược tối đa cho 1 trận",MinBet:"Cược nhỏ nhất"},e.message=e.message||{},e.message["en-us"]={BetLimitCannotBeGreaterThanMaxValue:"BetLimit cannot be greater than Max Value.",MaxBetCannotBeGreaterThanMaxPerMatch:"Max Bet cannot be greater than Max Per Match.",MaxBetCannotBeNegative:"Max Bet cannot be negative.",MaxBetLessThanOrEqualMaxPerMatch:"The Max Bet should be less than or equal to Max Per Match",MaxBetLessThanOrEqualTo:"Max Bet must be less than or equal to {0}.",MaxBetMoreThanOrEqualMinBet:"Max Bet must be greater than or equal to Min Bet.",MaxPerMatchCannotBeNegative:"Max Per Match cannot be negative.",MaxPerMatchLessThanOrEqualTo:"Max Per Match must be less than or equal to {0}.",MaxPerMatchMoreThanOrEqualMaxBet:"Max Per Match must be greater than or equal to Max Bet.",MinBetCannotBeGreaterThanMaxBet:"Min Bet cannot be greater than Max Bet.",MinBetCannotBeNegative:"Min Bet cannot be negative.",MinBetLessThanOrEqualMaxBet:"The Min Bet should be less than or equal to Max Bet.",MinBetMoreThanOrEqualTo:"Min Bet must be greater than or equal to {0}.",Msg_10003:"Invalid Min bet/ Max bet/ Max per match.",Msg_10005:"Min bet must be less than or equal to max bet and max bet must be less than or equal to max bet per match.",Msg_10006:"Min bet must be greater than or equal to Agent min bet.",Msg_10007:"Max bet must be less than or equal to Agent max bet.",Msg_10008:"Max Per Match must be less than or equal to Agent Max Per Match.",Msg_10016:"Min bet must be greater than or equal to Super Agent min bet.",Msg_10017:"Max bet must be less than or equal to Super Agent max bet.",Msg_10018:"Max Per Match must be less than or equal to Super Agent Max Per Match.",Msg_10019:"Min bet must be greater than or equal to Master Agent min bet.",Msg_10020:"Max bet must be less than or equal to Master Agent max bet.",Msg_10021:"Max Per Match must be less than or equal to Master Agent Max Per Match.",RequiredField:"This field is required.",UpdateBetSettingSuccessfully:"Bet Setting is updated successfully."},e.message=e.message||{},e.message["zh-cn"]={MaxBetCannotBeGreaterThanMaxPerMatch:"最高投注不可高于当场最高投注",MaxBetLessThanOrEqualMaxPerMatch:"最高投注必须少于或相等于单场限额",MaxBetLessThanOrEqualTo:"最高投注不得多于或相等于 {0}",MaxBetMoreThanOrEqualMinBet:"最高投注限额必须大于或相等于最低投注限额",MaxPerMatchLessThanOrEqualTo:"单场最高投注需低于或相等于 {0}",MaxPerMatchMoreThanOrEqualMaxBet:"单场投注限额必须大于或相等于最高投注限额",MinBetCannotBeGreaterThanMaxBet:"最低投注不可高于最低投注",MinBetLessThanOrEqualMaxBet:"最低投注必须少于或相等于最高投注",MinBetMoreThanOrEqualTo:"最大投注额必须大于或等于 {0}",Msg_10003:"最低投注/最高投注/单场限额无效",Msg_10005:"最低投注必须少于或相等于最高投注以及最高投注必须小于或相等于单场投注限额",Msg_10006:"最低投注必须大于或相等于代理最低投注限额",Msg_10007:"最高投注必须少于或相等于代理最高投注限额",Msg_10008:"单场限额必须少于或相等于代理单场投注限额",Msg_10016:"最低投注大于或相等于超级总代理最低投注限额",Msg_10017:"最高投注必须少于或相等于超级总代理最高投注限额",Msg_10018:"单场限额必须少于或相等于超级总代理单场投注限额",Msg_10019:"最低投注大于或相等于总代理最低投注限额",Msg_10020:"最高投注必须少于或相等于总代理最高投注限额",Msg_10021:"单场限额必须少于或相等于总代理单场投注限额",RequiredField:"必填栏",UpdateBetSettingSuccessfully:"投注设定已更新成功."},e.message=e.message||{},e.message["zh-tw"]={MaxBetCannotBeGreaterThanMaxPerMatch:"最高投注不可高於單場最高投注",MaxBetLessThanOrEqualMaxPerMatch:"最高投注必須少於或相等於單場限額",MaxBetLessThanOrEqualTo:"最高投注不得多於或相等於 {0}",MaxBetMoreThanOrEqualMinBet:"最高投註限額必須大於或相等於最低投註限額",MaxPerMatchLessThanOrEqualTo:"單場最高投注需底於或等於 {0}",MaxPerMatchMoreThanOrEqualMaxBet:"單場投註限額必須大於或相等於最高投註限額",MinBetCannotBeGreaterThanMaxBet:"最低投注不可高於最高投注",MinBetLessThanOrEqualMaxBet:"最低投注必須少於或相等於最高投注",MinBetMoreThanOrEqualTo:"最大投注額必須大於或等於 {0}",Msg_10003:"最低投註/最高投註/單場限額無效",Msg_10005:"最低投註必須少於或相等於最高投註以及最高投註必須小於或相等於單場投註限額",Msg_10006:"最低投註必須大於或相等於代理最低投註限額",Msg_10007:"最高投註必須少於或相等於代理最高投註限額",Msg_10008:"單場限額必須少於或相等於代理單場投註限額",Msg_10016:"最低投註大於或相等於超級總代理最低投註限額",Msg_10017:"最高投註必須少於或相等於超級總代理最高投註限額",Msg_10018:"單場限額必須少於或相等於超級總代理單場投註限額",Msg_10019:"最低投註大於或相等於總代理最低投註限額",Msg_10020:"最高投註必須少於或相等於總代理最高投註限額",Msg_10021:"單場限額必須少於或相等於總代理單場投註限額",RequiredField:"必填欄",UpdateBetSettingSuccessfully:"投注設定已更新成功."},e.message=e.message||{},e.message["ko-kr"]={MaxBetCannotBeGreaterThanMaxPerMatch:"최대 배팅은 경기당최대 값보다 클수 없습니다",MaxBetLessThanOrEqualMaxPerMatch:"최대베팅은 경기 당 최대치와 같거나 이하이어야합니다",MaxBetLessThanOrEqualTo:"최대 배팅보다 이하이여야 합니다 {0}.",MaxPerMatchLessThanOrEqualTo:"경기당 최대 값은 {0}과 같거나 작아야 합니다.",MinBetCannotBeGreaterThanMaxBet:"최소 배팅은 최대 배팅보다 클수 없습니다.",MinBetLessThanOrEqualMaxBet:"최소베팅은 최대 베팅과 같거나  이하이어야합니다.",UpdateBetSettingSuccessfully:"베팅 설정이 성공적으로 업데이트되었습니다."},e.message=e.message||{},e.message["ja-jp"]={MaxBetCannotBeGreaterThanMaxPerMatch:"Max Bet cannot be greater than Max Per Match",MaxBetLessThanOrEqualMaxPerMatch:"The Max Bet should be less than or equal to Max Per Match",MaxBetLessThanOrEqualTo:"一回当たりのマックスベットポイントは表示されていますので、再確認してください {0}.",MaxPerMatchLessThanOrEqualTo:"Max Per Match must be less than or equal to {0}.",MinBetCannotBeGreaterThanMaxBet:"Min Bet cannot be greater than Max Bet.",MinBetLessThanOrEqualMaxBet:"The Min Bet should be less than or equal to Max Bet",UpdateBetSettingSuccessfully:"Bet Setting is updated successfully."},e.message=e.message||{},e.message["th-th"]={MaxBetCannotBeGreaterThanMaxPerMatch:"จำนวนเงินสูงสุดที่สามารถเล่นได้ต้องไม่มากกว่าจำนวนเงินสูงสุดต่อรายการที่เล่น",MaxBetLessThanOrEqualMaxPerMatch:"ค่าเดิมพันสูงสุดควรจะน้อยกว่า หรือ เท่ากับค่าเดิมพันสูงสุดต่อการแข่งขัน",MaxBetLessThanOrEqualTo:"จำนวนการเดิมพันสูงสุดต้องน้อยกว่า หรือเท่ากับ {0}.",MaxBetMoreThanOrEqualMinBet:"ค่าการเดิมพันขั้นสูงสุดต้องมากกว่าหรือเท่ากับค่าการเดิมพันขั้นต่ำ",MaxPerMatchLessThanOrEqualTo:"จำนวนการเดิมพันสูงสุงต่อรายการต้องน้อยกว่า หรือเท่ากับ {0}",MaxPerMatchMoreThanOrEqualMaxBet:"ค่าการเดิมพันสูงสุดต่อแมทช์ต้องมากกว่าหรือเท่ากับค่าการเดิมพันขั้นสูงสุด",MinBetCannotBeGreaterThanMaxBet:"จำนวนเงินต่ำสุดที่สามารถเล่นได้ ต้องไม่มากกว่า จำนวนเงินสูงสุดที่สามารถเล่นได้",MinBetLessThanOrEqualMaxBet:"ค่าเดิมพันขั้นต่ำควรจะน้อยกว่า หรือ เท่ากับค่าเดิมพันสูงสุด.",MinBetMoreThanOrEqualTo:"จำนวนการเดิมพันต่ำสุดต้องมากกว่า หรือเท่ากับ {0}.",Msg_10003:"ค่า เดิมพันขั้นต่ำ/ เดิมพันขั้นสูดสุด/ เดิมพันต่อเกมสูงสุด ไม่ถูกต้อง",Msg_10005:"ค่าการเดิมพันขั้นต่ำต้องน้อยกว่าหรือเท่ากับค่าเดิมพันขั้นสูงสุด และ ค่าเดิมพันสูงสุดต้องน้อยกว่าหรือเท่ากับ ค่าเดิมพันสูงสุดต่อแมทซ์",Msg_10006:"ค่าวางเดิมพันขั้นต่ำต้องมากกว่าหรือเท่ากับ ค่าการเดิมพันต่ำสุดของเอเจนท์",Msg_10007:"ค่าวางเดิมพันขั้นสุงสุดต้องน้อยกว่าหรือเท่ากับ ค่าการเดิมพันสูงสุดของเอเจนท์",Msg_10008:"ค่าวางเดิมพันต่อแมทซ์สูงสุดต้องน้อยกว่าหรือเท่ากับ ค่าการเดิมพันสูงสุดต่อแมทซ์ของเอเจนท์",Msg_10016:"ค่าวางเดิมพันขั้นต่ำต้องมากกว่าหรือเท่ากับ ค่าการเดิมพันขั้นต่ำของซูเปอร์เอเจนท์",Msg_10017:"ค่าวางเดิมพันขั้นสูงสุดต้องน้อยกว่าหรือเท่ากับ ค่าการเดิมพันขั้นสูงสุดของซูเปอร์เอเจนท์",Msg_10018:"ค่าวางเดิมพันขั้นสูงสุดต่อแมทซ์ต้องน้อยกว่าหรือเท่ากับ ค่าการเดิมพันขั้นสูงสุดของซูเปอร์เอเจนท์ต่อแมทซ์",Msg_10019:"ค่าวางเดิมพันขั้นต่ำต้องมากกว่าหรือเท่ากับ ค่าการเดิมพันขั้นต่ำของมาสเตอร์เอเจนท์",Msg_10020:"ค่าวางเดิมพันขั้นสูงสุดต้องน้อยกว่าหรือเท่ากับ ค่าการเดิมพันขั้นสูงสุดของมาสเตอร์เอเจนท์",Msg_10021:"ค่าวางเดิมพันต่อแมทซ์สูงสุดต้องน้อยกว่าหรือเท่ากับ ค่าการเดิมพันขั้นสูงสุดต่อแมทซ์ของมาสเตอร์เอเจนท์",RequiredField:"จำเป็นต้องใส่ข้อมูลนี้",UpdateBetSettingSuccessfully:"การตั้งค่าเดิมพัน ได้อัพเดทเรียบร้อยแล้ว."},e.message=e.message||{},e.message["vi-vn"]={MaxBetCannotBeGreaterThanMaxPerMatch:"Tiền cược lớn nhất không thể lớn hơn Tiền cược tối đa của mỗi trận đấu",MaxBetLessThanOrEqualMaxPerMatch:"Tiền cược lớn nhất phải nhỏ hơn hoặc bằng Tiền cược tối đa của mỗi trận đấu",MaxBetLessThanOrEqualTo:"Tiền cược lớn nhất phải nhỏ hơn hoặc bằng {0}",MaxPerMatchLessThanOrEqualTo:"Tiền cược tối đa cho mỗi trận phài ít hơn hoặc bằng {0}",MinBetCannotBeGreaterThanMaxBet:"Tiền cược nhỏ nhất không thể lớn hơn Tiền cược lớn nhất",MinBetLessThanOrEqualMaxBet:"Tiền cược nhỏ nhất phải nhỏ hơn hoặc bằng Tiền cược lớn nhất.",MinBetMoreThanOrEqualTo:"Cược Nhỏ Nhất phải lớn hơn hay bằng {0}.",RequiredField:"Thông tin này bắt buộc phải nhập.",UpdateBetSettingSuccessfully:"Thiết lập về tiền cược được cập nhật thành công"},angular.module("customerSetting").value("betSettingResources",e)}(),function(){"use strict";var e={};e.label=e.label||{},e.label["en-us"]={AgentMaxPT:"Agent Max PT",AgentMinPT:"Agent Min PT",AgentPT:"Agent PT",DisableAgentAutoPt:"You have changed this agent not to take auto position taking.",DisableMasterAutoPt:"You have changed this master not to take auto position taking.",IsAuto:"Is Auto",Live:"Live",MasterMaxPT:"Master Max PT",MasterMinPT:"Master Min PT",MasterPT:"Master PT",Max:"Max",MaxLive:"Max Live",Min:"Min",MinLive:"Min Live",PositionTaking:"Position Taking",SuperMaxPT:"Super Max PT",SuperMinPT:"Super Min PT",SuperPT:"Super PT",UpdateMemberPtInfo:"You have changed your position taking.",UpdateMemberPtQuestion:"Do you want to update all member&apos;s position taking to:",UpdateMemberPtTitle:"Confirm Update Members&apos;s PT"},e.label=e.label||{},e.label["zh-cn"]={AgentMaxPT:"代理最高占成",AgentMinPT:"代理最低占成",AgentPT:"代理占成数",DisableAgentAutoPt:"您已更改于此代理不使用自动占成",DisableMasterAutoPt:"您已更改此总代理不再自动占成.",IsAuto:"是自动",LastName:"姓",Live:"现场",MasterMaxPT:"总代理最高占成",MasterMinPT:"总代理最低占成数",MasterPT:"总代理占成数",Max:"最高",MaxLive:"Max Live",Min:"最低",MinLive:"Min Live",PositionTaking:"占成数",SuperMaxPT:"超级总代理最高占成",SuperMinPT:"超级总代理最低占成",SuperPT:"超级总代理占成数",UpdateMemberPtInfo:"您已更改您的占成数",UpdateMemberPtQuestion:"您是否要把所有会员的占成数更改为:",UpdateMemberPtTitle:"确认更新会员的占成数"},e.label=e.label||{},e.label["zh-tw"]={AgentMaxPT:"代理最高佔成",AgentMinPT:"代理最低佔成",AgentPT:"代理占成数",DisableAgentAutoPt:"You have changed this agent not to take auto position taking",DisableMasterAutoPt:"You have changed this master not to take auto position taking.",IsAuto:"是自動",Live:"現場",MasterMaxPT:"總代理最高佔成",MasterMinPT:"总代理最低占成数",MasterPT:"总代理占成数",Max:"最高",Members:"會員",Min:"最低",MinLive:"Min Live",PositionTaking:"佔成數",SuperMaxPT:"超級總代理最高佔成",SuperMinPT:"超級總代理最低佔成",SuperPT:"超级总代理占成数",UpdateMemberPtInfo:"您已更改您的占成数",UpdateMemberPtQuestion:"您是否要把所有會員佔成數更改為:",UpdateMemberPtTitle:"確認更新會員的佔成數"},e.label=e.label||{},e.label["ko-kr"]={AgentMaxPT:"에이전트 최대 PT",AgentMinPT:"에이전트 최소 PT",AgentPT:"에이전트 PT",DisableAgentAutoPt:"해당 에이전트는 자동 거래조건을 설정 하지 못하게 변경 하였습니다",DisableMasterAutoPt:"해당 마스터의 자동 거래조건을 설정 하지 못하게 변경 하였습니다.",IsAuto:"자동",Live:"라이브",MasterMaxPT:"마스터 최대 PT",MasterMinPT:"마스터 최소PT",MasterPT:"마스터 PT",Max:"최대",Min:"최소",PositionTaking:"거래 조건",SuperMaxPT:"슈퍼 최대 PT",SuperMinPT:"슈퍼 최소PT",SuperPT:"슈퍼 PT",UpdateMemberPtInfo:"포지션테이킹(PT)이 변경되었습니다",UpdateMemberPtQuestion:"모든회원의 거래조건을 바꾸기를 원하십니까:",UpdateMemberPtTitle:"Confirm Update Members&apos;s PT"},e.label=e.label||{},e.label["ja-jp"]={AgentMaxPT:"Agent Max PT",AgentMinPT:"Agent Min PT",AgentPT:"Agent PT",DisableAgentAutoPt:"You have changed this agent not to take auto position taking",DisableMasterAutoPt:"You have changed this master not to take auto position taking.",IsAuto:"Is Auto",Live:"ライブ",MasterMaxPT:"Master Max PT",MasterMinPT:"Master Min PT",MasterPT:"Master PT",Max:"Max",Min:"Min",PositionTaking:"Position Taking",SuperMaxPT:"Super Max PT",SuperMinPT:"Super Min PT",SuperPT:"Super PT",UpdateMemberPtInfo:"You have changed your position taking.",UpdateMemberPtQuestion:"Do you want to update all member&apos;s position taking to:",UpdateMemberPtTitle:"Confirm Update Members&apos;s PT"},e.label=e.label||{},e.label["th-th"]={AgentMaxPT:"แบ่งสู้สูงสุดของเอเย่น",AgentMinPT:"แบ่งสู้ต่ำสุดของเอเย่น",AgentPT:"ค่าแบ่งสู้ของเอเย่น",DisableAgentAutoPt:"คุณได้เปลี่ยนเอเย่นท่านนี้ไม่ถือแบ่งสู้อัตโนมัติ",DisableMasterAutoPt:"คุณได้ทำการเปลี่ยนตัวแทนหลักและ ไม่ได้เลือกตำแหน่งอัตโนมัติ.",IsAuto:"คืออัตโนมัติ",Live:"สด",MasterMaxPT:"แบ่งสู้สูงสุดของ",MasterMinPT:"แบ่งสู้ขั้นต่ำของมาสเตอร์",MasterPT:"ค่าแบ่งสู้ของมาสเตอร์",Max:"สูงสุด",Min:"ต่ำสุด",PositionTaking:"ค่าความเสี่ยง",SuperMaxPT:"แบ่งสู้สูงสุดของซุปเปรอ์",SuperMinPT:"แบ่งสู้ต่ำสุดของซุปเปรอ์",SuperPT:"ค่าแบ่งสู้ของซุปเปอร์",UpdateMemberPtInfo:"คุณมีการเปลี่ยนค่าแบ่งสู้ของคุณ",UpdateMemberPtQuestion:"คุณต้องการจะอัพเดทแบ่งสู้บนสมาชิกทั้งหมกหรือไม่:",UpdateMemberPtTitle:"ยืนยันการอัพเดทการตั้งค่าความเสี่ยง"},e.label=e.label||{},e.label["vi-vn"]={AgentMaxPT:"PT lớn nhất của Agent",AgentMinPT:"PT nhỏ nhất của Agent",AgentPT:"PT của Agent",DisableAgentAutoPt:"Bạn vừa mới ngăn Agent này lấy Auto PT",DisableMasterAutoPt:"Bạn vừa mới ngăn Master này lấy Auto PT.",IsAuto:"Auto PT",Live:"Trực Tuyến",MasterMaxPT:"PT lớn nhất của Master",MasterMinPT:"PT nhỏ nhất của Master",MasterPT:"PT của Master",Max:"Max",MaxLive:"Max Live",Min:"Nhỏ nhất",MinLive:"Min Live",PositionTaking:"PT",SuperMaxPT:"PT lớn nhất của Super",SuperMinPT:"PT nhỏ nhất của Super",SuperPT:"PT của Super",UpdateMemberPtInfo:"Bạn đã  thay đổi giá trị PT của mình",UpdateMemberPtQuestion:"Bạn có muốn cập nhật giá trị PTcủa tất cả các thành viên:",UpdateMemberPtTitle:"Confirm Update Members&apos;s PT"},e.message=e.message||{},e.message["en-us"]={AgentMaxPTCannotBeGreaterThan:"Agent Max PT must be less than or equal to {0}.",AgentMaxPTCannotBeGreaterThanMasterMaxPT:"Agent Max PT must be less than or equal to Master Max PT.",AgentMaxPTCannotBeLessThan:"Agent Max PT must be greater than or equal to {0}.",AgentMinPTCannotBeGreaterThanAgentMaxPT:"Agent Min PT must be less than or equal to Agent Max PT.",AgentPTCannotBeGreaterThanAgentMaxPT:"Agent PT must be less than or equal to Agent Max PT.",AgentPTCannotBeLessThanAgentMinPT:"Agent PT must be greater than or equal to Agent Min PT.",MasterAutoPTCannotBeGreaterThanMasterMaxPT:"Master Auto PT must be less than or equal to Master Max PT.",MasterAutoPTOnMemberCannotBeGreaterThanMasterMaxPT:"Master Auto PT on Member must be less than or equal to Master Max PT.",MasterAutoPTOnMemberCannotBeLessThan:"Master Auto PT on Member must be greater than or equal to {0}.",MasterMaxPTCannotBeGreaterThan:"Master Max PT must be less than or equal to {0}.",MasterMaxPTCannotBeGreaterThanSuperMaxPT:"Master Max PT must be less than or equal to Super Max PT.",MasterMaxPTCannotBeLessThan:"Master Max PT must be greater than or equal to {0}.",MasterMinPTCannotBeGreaterThanMasterMaxPT:"Master Min PT must be less than or equal to Master Max PT.",MasterPTCannotBeGreaterThanMasterMaxPT:"Master PT must be less than or equal to Master Max PT.",MasterPTCannotBeLessThan:"Master PT must be greater than or equal to {0}.",MasterPTOnMemberCannotBeGreaterThan:"Master PT on Member must be less than or equal to {0}.",MasterPTOnMemberCannotBeLessThan:"Master PT on Member must be greater than or equal to {0}.",Msg_20001:"Invalid position taking.",Msg_20019:"Existing masters exceed new Super Max PT.",Msg_20020:"Existing members exceed new Super Max PT.",Msg_20021:"Existing masters exceed new Master Max PT.",Msg_20022:"Existing members exceed new Master Max PT.",Msg_20023:"Existing agents exceed new Agent Max PT.",Msg_20024:"Existing members exceed new Agent Max PT.",Msg_20025:"The position taking violates Upline Min/Max PT.",Msg_20026:"Existing agents exceed new Master Max PT.",Msg_20027:"The position taking violates Master Max PT set by company.",Msg_20028:"The position taking violates Super Max PT.",Msg_20029:"The position taking violates Super Min PT.",Msg_20030:"The Agent Min PT violates Master Max PT.",Msg_20031:"The position taking violates Master Max PT.",Msg_20032:"The position taking violates Master Min PT.",Msg_20033:"The position taking violates Agent Min/Max PT.",Msg_20034:"The position taking violates Agent Max PT.",Msg_20044:"You are not allowed to have Auto PT, please contact your upline.",SuperAutoPTCannotBeGreaterThanSuperMaxPT:"Super Auto PT must be less than or equal to Super Max PT.",SuperAutoPTOnMemberCannotBeGreaterThanSuperMaxPT:"Super Auto PT on Member must be less than or equal to Super Max PT.",SuperAutoPTOnMemberCannotBeLessThan:"Super Auto PT on Member must be greater than or equal to {0}.",SuperMaxPTCannotBeGreaterThan:"Super Max PT must be less than or equal to {0}.",SuperMinPTCannotBeGreaterThanSuperMaxPT:"Super Min PT must be less than or equal to Super Max PT.",SuperPTCannotBeGreaterThanSuperMaxPT:"Super PT must be less than or equal to Super Max PT.",SuperPTCannotBeLessThan:"Super PT must be greater than or equal to {0}.",SuperPTOnMemberCannotBeGreaterThan:"Super PT on Member must be less than or equal to {0}.",SuperPTOnMemberCannotBeLessThan:"Super PT on Member must be greater than or equal to {0}.",UpdatePositionTakingSuccessfully:"Position taking is updated successfully."},e.message=e.message||{},e.message["zh-cn"]={AgentMaxPTCannotBeGreaterThan:"代理最高占成必须少于或相等于{0}",AgentMaxPTCannotBeGreaterThanMasterMaxPT:"代理最高占成必须少于或相等于总代理最高占成",AgentMaxPTCannotBeLessThan:"代理最高占成必须大于或相等于{0}",AgentMinPTCannotBeGreaterThanAgentMaxPT:"代理最低占成必须少于或相等于代理最高占成",AgentPTCannotBeGreaterThanAgentMaxPT:"代理占成必须少于或相等于代理最高占成",AgentPTCannotBeLessThanAgentMinPT:"代理占成必须大于或相等于代理最低占成",MasterAutoPTCannotBeGreaterThanMasterMaxPT:"总代理自动占成必须少于或相等于总代理最高占成",MasterAutoPTOnMemberCannotBeGreaterThanMasterMaxPT:"总代理自动占成在会员必须少于或相等于总代理最高占成",MasterAutoPTOnMemberCannotBeLessThan:"总代理自动占成在会员必须大于或相等于{0}",MasterMaxPTCannotBeGreaterThan:"总代理最高占成必须少于或相等于{0}",MasterMaxPTCannotBeGreaterThanSuperMaxPT:"总代理最高占成必须少于或相等于超级总代理最高占成",MasterMaxPTCannotBeLessThan:"总代理最高占成必须大于或相等于{0}",MasterMinPTCannotBeGreaterThanMasterMaxPT:"总代理最低占成必须少于或相等于总代理最高占成",MasterPTCannotBeGreaterThanMasterMaxPT:"总代理占成必须低于或相等于总代理最高占成。",MasterPTCannotBeLessThan:"总代理占成必须大于或相等于 {0}。",MasterPTOnMemberCannotBeGreaterThan:"总代理于会员的占成必须小于或相等于 {0}。",MasterPTOnMemberCannotBeLessThan:"总代理于会员的占成必须大于或相等于 {0}。",Msg_20001:"占成无效",Msg_20019:"現有总代理超過新增超级总代理的最高占成。",Msg_20020:"現有会员超過新增超级总代理的最高占成。",Msg_20021:"現有总代理超過新增总代理的最高占成。",Msg_20022:"現有会员超過新增总代理的最高占成。",Msg_20023:"現有代理超過新增代理的最高占成。",Msg_20024:"現有会员超過新增代理的最高占成。",Msg_20025:"此占成违反上线的最低/最高占成.",Msg_20026:"現有代理超過新增总代理的最高占成。",Msg_20027:"此占成违反公司设置于总代理的最高占成。",Msg_20028:"此占成违反超级总代理的最高占成。",Msg_20029:"此占成违反超级总代理的最低占成。",Msg_20030:"此代理最低占成违反总代理的最高占成。",Msg_20031:"此占成违反总代理的最高占成。",Msg_20032:"此占成违反总代理的最低占成。",Msg_20033:"此占成违反代理的最低/最高占成。",Msg_20034:"此占成违反代理的最高占成。",Msg_20044:"您不被允许使用自动占成, 请您联系您的上线。",SuperAutoPTCannotBeGreaterThanSuperMaxPT:"超级总代理的自动占成必须少于或相等于超级总代理的最高占成。",SuperAutoPTOnMemberCannotBeGreaterThanSuperMaxPT:"超级总代理于会员的自动占成必须少于或相等于超级总代理的最高占成。",SuperAutoPTOnMemberCannotBeLessThan:"超级总代理于会员的自动占成必须大于或相等于 {0}。",SuperMaxPTCannotBeGreaterThan:"超级总代理的最高占成必须少于或相等于 {0}。",SuperMinPTCannotBeGreaterThanSuperMaxPT:"超级总代理的最低占成必须少于或相等于超级总代理的最高占成。",SuperPTCannotBeGreaterThanSuperMaxPT:"超级总代理的占成必须少于或相等于超级总代理的最高占成。",SuperPTOnMemberCannotBeGreaterThan:"超级总代理于会员的占成必须少于或相等于 {0}。",SuperPTOnMemberCannotBeLessThan:"超级总代理于会员的占成必须大于或相等于 {0}。",UpdatePositionTakingSuccessfully:"占成数设定已更新成功."},e.message=e.message||{},e.message["zh-tw"]={AgentMaxPTCannotBeGreaterThan:"代理最高占成必須少於或相等於{0}",AgentMaxPTCannotBeGreaterThanMasterMaxPT:"代理最高占成必須少於或相等於總代理最高占成",AgentMaxPTCannotBeLessThan:"代理最高占成必須大於或相等於{0}",AgentMinPTCannotBeGreaterThanAgentMaxPT:"代理最低占成必須少於或相等於代理最高占成",AgentPTCannotBeGreaterThanAgentMaxPT:"代理占成必須少於或相等於代理最高占成",AgentPTCannotBeLessThanAgentMinPT:"代理占成必須大於或相等於代理最低占成",MasterAutoPTCannotBeGreaterThanMasterMaxPT:"總代理自動占成必須少於或相等於總代理最高占成",MasterAutoPTOnMemberCannotBeGreaterThanMasterMaxPT:"總代理自動占成在會員必須少於或相等於總代理最高占成",MasterAutoPTOnMemberCannotBeLessThan:"總代理自動占成在會員必須大於或相等於{0}",MasterMaxPTCannotBeGreaterThan:"總代理最高占成必須少於或相等於{0}",MasterMaxPTCannotBeGreaterThanSuperMaxPT:"總代理最高占成必須少於或相等於超級總代理最高占成",MasterMaxPTCannotBeLessThan:"總代理最高占成必須大於或相等於{0}",MasterMinPTCannotBeGreaterThanMasterMaxPT:"總代理最低占成必須少於或相等於總代理最高占成",MasterPTCannotBeGreaterThanMasterMaxPT:"總代理占成必須低於或相等於總代理最高占成。",MasterPTCannotBeLessThan:"總代理占成必須大於或相等於 {0}。",MasterPTOnMemberCannotBeGreaterThan:"總代理於會員的占成必須小於或相等於 {0}。",MasterPTOnMemberCannotBeLessThan:"總代理於會員的占成必須大於或相等於 {0}。",Msg_20001:"占成無效",Msg_20019:"現有總代理超過新增超級總代理的最高占成",Msg_20020:"現有會員超過新增超級總代理的最高占成。",Msg_20021:"現有總代理超過新增總代理的最高占成。",Msg_20022:"現有會員超過新增總代理的最高占成。",Msg_20023:"現有代理超過新增代理的最高占成。",Msg_20024:"現有會員超過新增代理的最高占成。",Msg_20025:"此占成違反上線的最低/最高占成.",Msg_20026:"現有代理超過新增總代理的最高占成。",Msg_20027:"此占成違反公司設置於總代理的最高占成。",Msg_20028:"此占成違反超級總代理的最高占成。",Msg_20029:"此占成違反超級總代理的最低占成。",Msg_20030:"此代理最低占成違反總代理的最高占成。",Msg_20031:"此占成違反總代理的最高占成。",Msg_20032:"此占成違反總代理的最低占成。",Msg_20033:"此占成違反代理的最低/最高占成。",Msg_20034:"此占成違反代理的最高占成。",Msg_20044:"您不被允許使用自動占成, 請您聯系您的上線。",SuperAutoPTCannotBeGreaterThanSuperMaxPT:"超級總代理的自動占成必須少於或相等於超級總代理的最高占成。",SuperAutoPTOnMemberCannotBeGreaterThanSuperMaxPT:"超級總代理於會員的自動占成必須少於或相等於超級總代理的最高占成。",SuperAutoPTOnMemberCannotBeLessThan:"超級總代理於會員的自動占成必須大於或相等於 {0}。",SuperMaxPTCannotBeGreaterThan:"超級總代理的最高占成必須少於或相等於 {0}。",SuperMinPTCannotBeGreaterThanSuperMaxPT:"超級總代理的最低占成必須少於或相等於超級總代理的最高占成。",SuperPTCannotBeGreaterThanSuperMaxPT:"超級總代理的占成必須少於或相等於超級總代理的最高占成。",SuperPTOnMemberCannotBeGreaterThan:"超級總代理於會員的占成必須少於或相等於{0}。",SuperPTOnMemberCannotBeLessThan:"超級總代理於會員的占成必須大於或相等於 {0}。",UpdatePositionTakingSuccessfully:"佔成數設定已更新成功."},e.message=e.message||{},e.message["ko-kr"]={UpdatePositionTakingSuccessfully:"포지션 설정이 성공적으로 업데이트되었습니다."},e.message=e.message||{},e.message["ja-jp"]={UpdatePositionTakingSuccessfully:"Position taking is updated successfully."},e.message=e.message||{},e.message["th-th"]={AgentMaxPTCannotBeGreaterThan:"ค่าความเสี่ยงสูงสุดของเอเจนท์ ต้องน้อยกว่าหรือเท่ากับ {0}.",AgentMaxPTCannotBeGreaterThanMasterMaxPT:"ค่าความเสี่ยงสูงสุดของเอเจนท์ ต้องน้อยกว่าหรือเท่ากับ ค่าความเสี่ยงสูงสุดของมาสเตอร์",AgentMaxPTCannotBeLessThan:"ค่าความเสี่ยงสูงสุดของเอเจนท์ ต้องมากกว่าหรือเท่ากับ {0}.",AgentMinPTCannotBeGreaterThanAgentMaxPT:"ค่าความเสี่ยงขั้นต่ำของเอเจนท์ ต้องน้อยกว่าหรือเท่ากับ ค่าความเสี่ยงสูงสุดของเอเจนท์",AgentPTCannotBeGreaterThanAgentMaxPT:"ค่าความเสี่ยงของเอเจนท์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของเอเจนท์",AgentPTCannotBeLessThanAgentMinPT:"ค่าความเสี่ยงของเอเจนท์ต้องมากกว่าหรือเท่ากับค่าความเสี่ยงต่ำสุดของเอเจนท์",MasterAutoPTCannotBeGreaterThanMasterMaxPT:"ค่าความเสี่ยงแบบอัตโนมัติของมาสเตอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของมาสเตอร์",MasterAutoPTOnMemberCannotBeGreaterThanMasterMaxPT:"ค่าความเสี่ยงแบบอัตโนมัติของมาสเตอร์ต่อเมมเบอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของมาสเตอร์",MasterAutoPTOnMemberCannotBeLessThan:"ค่าความเสี่ยงแบบอัตโนมัติของมาสเตอร์ต้องมากกว่าหรือเท่ากับ {0}.",MasterMaxPTCannotBeGreaterThan:"ค่าความเสี่ยงสูงสุดของมาสเตอร์ต้องน้อยกว่าหรือเท่ากับ {0}.",MasterMaxPTCannotBeGreaterThanSuperMaxPT:"ค่าความเสี่ยงสูงสุดของมาสเตอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของซูเปอร์",MasterMaxPTCannotBeLessThan:"ค่าความเสี่ยงสูงสุดของมาสเตอร์ต้องมากกว่าหรือเท่ากับ {0}.",
MasterMinPTCannotBeGreaterThanMasterMaxPT:"ค่าความเสี่ยงขั้นต่ำของมาสเตอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของมาสเตอร์",MasterPTCannotBeGreaterThanMasterMaxPT:"ค่าความเสี่ยงของมาสเตอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของมาสเตอร์",MasterPTCannotBeLessThan:"ค่าความเสี่ยงของมาสเตอร์ต้องมากกว่าหรือเท่ากับ {0}.",MasterPTOnMemberCannotBeGreaterThan:"ค่าความเสี่ยงของมาสเตอร์ต่อเมมเบอร์ต้องน้อยกว่าหรือเท่ากับ {0}.",MasterPTOnMemberCannotBeLessThan:"ค่าความเสี่ยงของมาสเตอร์ต่อเมมเบอร์ต้องมากกว่าหรือเท่ากับ {0}.",Msg_20001:"ค่าความเสี่ยงไม่ถูกต้อง",Msg_20019:"การตั้งค่ามาสเตอร์ต่างๆ ที่มีอยู่ก่อนแล้วเกินกว่าการตั้งค่าความเสี่ยงสูงสุดอันใหม่ของซูเปอร์",Msg_20020:"การตั้งค่าเมมเบอร์ต่างๆ ที่มีอยู่ก่อนแล้วเกินกว่าการตั้งค่าความเสี่ยงสูงสุดอันใหม่ของซูเปอร์",Msg_20021:"การตั้งค่ามาสเตอร์ต่างๆ ที่มีอยู่ก่อนแล้วเกินกว่าการตั้งค่าความเสี่ยงสูงสุดอันใหม่ของมาสเตอร์",Msg_20022:"การตั้งค่าเมมเบอร์ต่างๆ ที่มีอยู่ก่อนแล้วเกินกว่าการตั้งค่าความเสี่ยงสูงสุดอันใหม่ของมาสเตอร์",Msg_20023:"การตั้งค่าเอเจนท์ต่างๆ ที่มีอยู่ก่อนแล้วเกินกว่าการตั้งค่าความเสี่ยงสูงสุดอันใหม่ของเอเจนท์",Msg_20024:"การตั้งค่าเมมเบอร์ต่างๆ ที่มีอยู่ก่อนแล้วเกินกว่าการตั้งค่าความเสี่ยงสูงสุดอันใหม่ของเอเจนท์",Msg_20025:"ค่าความเสี่ยงขัดกับการตั้งค่าค่าความเสี่ยงขั้นต่ำ/ขั้นสูงสุดของทางต้นสาย",Msg_20026:"การตั้งค่าเอเจนท์ต่างๆ ที่มีอยู่ก่อนแล้วเกินกว่าการตั้งค่าความเสี่ยงสูงสุดอันใหม่ของมาสเตอร์",Msg_20027:"ค่าความเสี่ยงขัดกับการตั้งค่าค่าความเสี่ยงขั้นสูงสุดของมาสเตอร์ ที่ทางบริษัทตั้งค่าไว้",Msg_20028:"ค่าความเสี่ยงขัดกับการตั้งค่าค่าความเสี่ยงขั้นสูงสุดของซูเปอร์",Msg_20029:"ค่าความเสี่ยงขัดกับการตั้งค่าความเสี่ยงขั้นต่ำของซูเปอร์",Msg_20030:"ค่าความเสี่ยงของเอเจนท์ขัดกับการตั้งค่าค่าความเสี่ยงขั้นสูงสุดของมาสเตอร์",Msg_20031:"ค่าความเสี่ยงขัดกับการตั้งค่าค่าความเสี่ยงขั้นสูงสุดของมาสเตอร์",Msg_20032:"ค่าความเสี่ยงขัดกับการตั้งค่าความเสี่ยงขั้นต่ำของมาสเตอร์",Msg_20033:"ค่าความเสี่ยงขัดกับการตั้งค่าความเสี่ยงขั้นต่ำสุด/สูงสุดของเอเจนท์",Msg_20034:"ค่าความเสี่ยงขัดกับการตั้งค่าค่าความเสี่ยงขั้นสูงสุดของเอเจนท์",Msg_20044:"คุณไม่ได้รับอนุญาตให้ใช้การตั้งค่าแบ่งสู้อัตโนมัติ โปรดติดต่ออัพไลน์ของคุณ",SuperAutoPTCannotBeGreaterThanSuperMaxPT:"ค่าความเสี่ยงแบบอัตโนมัติของซูเปอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของซูเปอร์",SuperAutoPTOnMemberCannotBeGreaterThanSuperMaxPT:"ค่าความเสี่ยงแบบอัตโนมัติของซูเปอร์ต่อเมมเบอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของซูเปอร์",SuperAutoPTOnMemberCannotBeLessThan:"ค่าความเสี่ยงแบบอัตโนมัติของซูเปอร์ต่อเมมเบอร์ต้องมากกว่าหรือเท่ากับ {0}.",SuperMaxPTCannotBeGreaterThan:"ค่าความเสี่ยงของซูเปอร์ต้องน้อยกว่าหรือเท่ากับ {0}.",SuperMinPTCannotBeGreaterThanSuperMaxPT:"ค่าความเสี่ยงขั้นต่ำของซูเปอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของซูเปอร์",SuperPTCannotBeGreaterThanSuperMaxPT:"ค่าความเสี่ยงของซูเปอร์ต้องน้อยกว่าหรือเท่ากับค่าความเสี่ยงสูงสุดของซูเปอร์",SuperPTOnMemberCannotBeGreaterThan:"ค่าความเสี่ยงของซูเปอร์ต่อเมมเบอร์ต้องน้อยกว่าหรือเท่ากับ {0}.",SuperPTOnMemberCannotBeLessThan:"ค่าความเสี่ยงของซูเปอร์ต่อเมมเบอร์ต้องมากกว่ากว่าหรือเท่ากับ {0}.",UpdatePositionTakingSuccessfully:"การตั้งค่าแบ่งสู้ ได้อัพเดทเรียบร้อยแล้ว."},e.message=e.message||{},e.message["vi-vn"]={UpdatePositionTakingSuccessfully:"PT được cập nhật thành công."},angular.module("customerSetting").value("positionTakingResources",e)}();