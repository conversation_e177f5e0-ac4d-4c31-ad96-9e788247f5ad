{"name": "chardet", "version": "0.4.2", "homepage": "https://github.com/runk/node-chardet", "description": "Character detector", "keywords": ["encoding", "character", "utf8", "detector", "chardet", "icu"], "author": "<PERSON> <<EMAIL>>", "contributors": ["@spikying"], "devDependencies": {"github-publish-release": "^4.0.0", "mocha": "^4.0.1"}, "repository": {"type": "git", "url": "**************:runk/node-chardet.git"}, "bugs": {"mail": "<EMAIL>", "url": "http://github.com/runk/node-chardet/issues"}, "scripts": {"test": "mocha -R spec --recursive", "release": "scripts/release"}, "main": "index.js", "engine": {"node": ">=4"}, "readmeFilename": "README.md", "directories": {"test": "test"}, "license": "MIT"}