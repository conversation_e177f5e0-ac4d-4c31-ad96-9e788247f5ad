angular.module("templates", []).run(["$templateCache", function($templateCache) {$templateCache.put("elements/event-item/event-item.html","<tr ng-class=\"{\'live displayOn\': type == \'inplay\', \'bgcpe displayOn\': type !== \'inplay\', \'liveligh\': event.key%2==0 && type == \'inplay\', \'bgcpe bgcpelight\': event.key%2==0 && type !== \'inplay\'}\" >\r\n   <td rowspan=\"2\" class=\"text_time\" ng-if=\"type!==\'inplay\'\">\r\n      <p define-text=\"event.ss\" ng-bind-html=\"event.time | to_trusted\"></p>\r\n   </td>\r\n   <td rowspan=\"2\" nowrap=\"nowrap\" class=\"text_time\" ng-if=\"type===\'inplay\'\">\r\n      <b define-text=\"event.ss\">{{event.ss}}</b>        \r\n      <div nowrap=\"nowrap\" style=\"color:red\">\r\n         <span class=\"displayOff\" title=\"In Running\"><b class=\"IsLive\">IR</b></span>\r\n         <span class=\"displayOn\">\r\n            <b class=\"LiveTime\">{{event.time}} <small ng-show=\"event.timer.added != 0\" style=\"color: black\">+{{event.timer.added}}</small></b>\r\n            <span style=\"color:#566C9E\">\r\n            </span>\r\n         </span>\r\n      </div>\r\n   </td>\r\n   <td rowspan=\"2\" class=\"line_unR\" valign=\"top\">\r\n      <div class=\"FavTeamClass\" ng-class=\"{\'UdrDogTeamClass\': !event.ft_hdp.handicap || event.ft_hdp.handicap_team == \'away\'}\">\r\n         <span class=\"D_liveinfoM_23883982_H\" >{{::event.home}}\r\n         <img ng-repeat=\"(key, value) in [] | range:event.reds.home\" class=\"code\" border=\"0\" src=\"https://ssl-1-1.bongcdn.com/template/public/images/RedCard.gif\">\r\n         </span>\r\n      </div>\r\n      <div class=\"FavTeamClass\" ng-class=\"{\'UdrDogTeamClass\': !event.ft_hdp.handicap || event.ft_hdp.handicap_team == \'home\'}\">\r\n         <span class=\"D_liveinfoM_23883982_A\">{{::event.away}}\r\n         <img ng-repeat=\"(key, value) in [] | range:event.reds.away\" class=\"code\" border=\"0\" src=\"https://ssl-1-1.bongcdn.com/template/public/images/RedCard.gif\">\r\n      </span></div>\r\n      <div class=\"HdpGoalClass\">Hòa</div>\r\n   </td>\r\n   <td align=\"right\" rowspan=\"2\" nowrap=\"nowrap\">\r\n      <span class=\"displayOn\">\r\n         <a title=\"Live Match\" style=\"display:inline-block;\" target=\"_blank\" href=\"http://ls.1266366.com/index.aspx?clientid=846&flag=lc&language=en&clientmatchid={{event.event_id}}&t=S\">\r\n            <span class=\"iconOdds soccer\"></span>\r\n            <div id=\"lf23883982\" style=\"display:none; position:absolute; float:right;\"></div>\r\n         </a>\r\n         <a title=\"Add My Favorite\"><span name=\"fav_0123883982\" class=\"iconOdds favorite\"></span></a>\r\n      </span>\r\n   </td>\r\n   <td valign=\"top\" class=\"none_rline none_dline\">\r\n      <div class=\"line_divL HdpGoalClass\">\r\n         <br ng-if=\"event.ft_hdp.handicap_team == \'away\'\"/>\r\n         {{event.ft_hdp.handicap}}\r\n      </div>\r\n      <div class=\"line_divR OddsDiv\">\r\n         <a class=\"UdrDogOddsClass pointer\" define-text=\'event.ft_hdp.home_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'Handicap\', \'ft_hdp\', \'home\', event.ft_hdp.home_od)\">{{event.ft_hdp.home_od | numberOdd:2}}</a><br>\r\n         <a class=\"UdrDogOddsClass pointer\" define-text=\'event.ft_hdp.away_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'Handicap\', \'ft_hdp\', \'away\', event.ft_hdp.away_od)\">{{event.ft_hdp.away_od | numberOdd:2}}</a><br>\r\n      </div>\r\n   </td>\r\n   <td valign=\"top\" class=\"none_dline none_rline\">\r\n      <div class=\"line_divL HdpGoalClass\" visible=\'event.ft_ou.handicap\'>\r\n         {{event.ft_ou.handicap}}\r\n         <br>u\r\n      </div>\r\n      <div class=\"line_divR OddsDiv\">\r\n         <a class=\"UdrDogOddsClass pointer\" define-text=\'event.ft_ou.over_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'Tài/Xỉu\', \'ft_ou\', \'home\', event.ft_ou.over_od)\">{{event.ft_ou.over_od | numberOdd:2}}</a><br>\r\n         <a class=\"UdrDogOddsClass pointer\" define-text=\'event.ft_ou.under_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'Tài/Xỉu\', \'ft_ou\', \'away\', event.ft_ou.under_od)\">{{event.ft_ou.under_od | numberOdd:2}}</a><br>\r\n      </div>\r\n   </td>\r\n   <td rowspan=\"2\" align=\"right\" valign=\"top\" class=\"tabt_R\">\r\n      <div class=\"line_divL line_divR UdrDogOddsClass\"> \r\n         <a>{{event.ft_1x2.home_od | numberOdd:2}}</a><br>\r\n         <a>{{event.ft_1x2.away_od | numberOdd:2}}</a><br>\r\n         <a>{{event.ft_1x2.draw_od | numberOdd:2}}</a>\r\n      </div>\r\n   </td>\r\n   <td valign=\"top\" class=\"none_rline none_dline\" ng-class=\"{\'hide-span\': event.time_position > 2 && false}\">\r\n      <div class=\"line_divL HdpGoalClass\">\r\n         <br ng-if=\"event.hf_hdp.handicap_team == \'away\'\"/>\r\n         <span>{{event.hf_hdp.handicap}}</span>\r\n      </div>\r\n      <div class=\"line_divR OddsDiv \">\r\n          <a class=\"UdrDogOddsClass pointer\" define-text=\'event.hf_hdp.home_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'1H - Handicap\', \'hf_hdp\', \'home\', event.hf_hdp.home_od)\">\r\n          <span>{{event.hf_hdp.home_od | numberOdd:2}}</span>\r\n         </a><br>\r\n         <a class=\"UdrDogOddsClass pointer\" define-text=\'event.hf_hdp.away_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'1H - Handicap\', \'hf_hdp\', \'away\', event.hf_hdp.away_od)\"><span>{{event.hf_hdp.away_od | numberOdd:2}}\r\n         </span>\r\n         </a><br>\r\n      </div>\r\n   </td>\r\n   <td valign=\"top\" class=\"none_dline none_rline\" ng-class=\"{\'hide-span\': event.time_position > 2}\">\r\n      <div class=\"line_divL HdpGoalClass\" visible=\'event.hf_ou.handicap\'>\r\n         <span>{{event.hf_ou.handicap}}\r\n         <br>u\r\n         </span>\r\n      </div>\r\n      <div class=\"line_divR OddsDiv\">\r\n         <a class=\"UdrDogOddsClass pointer\" define-text=\'event.hf_ou.over_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'1H - Tài/Xỉu\', \'hf_ou\', \'home\', event.hf_ou.over_od)\">\r\n         <span>{{event.hf_ou.over_od | numberOdd:2}}</span>\r\n         </a><br>\r\n         <a class=\"UdrDogOddsClass pointer\" define-text=\'event.hf_ou.under_od\' odd-kind=\'{{odd_kind}}\' ng-click=\"onBet(\'1H - Tài/Xỉu\', \'hf_ou\', \'away\', event.hf_ou.under_od)\">\r\n            <span>{{event.hf_ou.under_od | numberOdd:2}}</span>\r\n         </a>\r\n         <br>\r\n      </div>\r\n   </td>\r\n   <td rowspan=\"2\" align=\"right\" valign=\"top\">\r\n      <div class=\"line_divL line_divR UdrDogOddsClass \">\r\n         <a>{{event.hf_1x2.home_od | numberOdd:2}}</a><br>\r\n         <a>{{event.hf_1x2.away_od | numberOdd:2}}</a><br>\r\n         <a>{{event.hf_1x2.draw_od | numberOdd:2}}</a>\r\n      </div>\r\n   </td>\r\n   <td rowspan=\"2\" align=\"center\" class=\"breakLine\">\r\n      <span class=\"displayOn\">\r\n      <a style=\"cursor:pointer\" title=\"Score Map\"><span class=\"iconOdds scoreMap\"></span></a>\r\n      <a title=\"Statistic Information\" href=\"http://ls.1266366.com/index.aspx?clientid=846&flag=ls&language=en&clientmatchid={{event.event_id}}\" target=\"_blank\"><span class=\"iconOdds stats\"></span></a></span></td>\r\n</tr>\r\n<tr id=\"e_23883982_1_2\" ng-class=\"{\'live displayOn\': type == \'inplay\', \'bgcpe displayOn\': type !== \'inplay\', \'liveligh\': event.key%2==0 && type == \'inplay\', \'bgcpe bgcpelight\': event.key%2==0 && type !== \'inplay\'}\">\r\n   <td colspan=\"2\" align=\"center\" class=\"none_rline none_lline none_tline\">\r\n      <span class=\"displayOn\">\r\n         <a href=\"#\" class=\"en_Point displayOff\" title=\"More Bet Types\"><span style=\"width:20px\"></span></a>\r\n         <a href=\"#\" class=\"btn redBg iconfast displayOff\" title=\"Fast Markets\">Fast</a>\r\n         <a href=\"#\" class=\"btn displayOff\" title=\"SuperLive\">SuperLive</a>\r\n      </span>\r\n   </td>\r\n   <td class=\"none_rline none_tline\" colspan=\"2\"></td>\r\n</tr>\r\n<tr>\r\n   <td class=\"moreBetType tag displayOff\" colspan=\"10\"></td>\r\n</tr>");
$templateCache.put("elements/account-info/account-info.html","<div id=\"div_accountInfoFull\" ng-show=\"show == \'full\'\">\n  <!--account information-->\n  <div id=\"account_allhard\">\n   <table width=\"95%\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">\n    <tbody>\n     <tr>\n      <td class=\"Tcolor01\" height=\"20\" style=\"cursor: pointer;\">\n       <div id=\"divFullUserName\">{{account.username}}</div>\n     </td>\n     <td valign=\"top\" nowrap=\"nowrap\" style=\"cursor: pointer;\" ng-click=\"onMenuAccount(\'mini\')\">\n       <a  target=\"leftFrame\" class=\"btnIcon right balance-open\" title=\"My Account\">\n         <span class=\"icon-arrow\"></span>\n       </a>\n     </td>\n     <td valign=\"top\" nowrap=\"nowrap\" width=\"20\" ng-click=\"loadInfo()\">\n      <a ng-class=\"{\'disable\': accountSetting.loading}\" name=\"btnRefresh\" class=\"btnIcon right\" title=\"Refresh\">\n        <span class=\"icon-refresh\"></span>\n      </a>\n   </td>\n </tr>\n</tbody>\n</table>\n<div class=\"line\"></div>\n<table width=\"95%\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">\n  <tbody>\n   <tr>\n    <td height=\"20\" align=\"left\" title=\"Nickname\">\n     <div class=\"text-ellipsis\" style=\"max-width:80px;\">Biệt danh</div>\n   </td>\n   <td align=\"right\"><span id=\"txt_nickname\">{{account.username}}</span></td>\n </tr>\n</tbody>\n</table>\n</div>\n<div class=\"account_body\">\n <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"tabstyle01\">\n  <tbody>\n   <tr>\n    <th align=\"left\" nowrap=\"nowrap\" title=\"Số dư\">\n     <div class=\"text-ellipsis\" style=\"width:70px;\">Số dư</div>\n   </th>\n   <td id=\"cashBalance\" align=\"right\" class=\"FavOddsClass\" ng-class=\"{\'UdrDogOddsClass\': account.profit >= 0}\"><span id=\"txt_cash\">{{account.profit | numberPretty:2}}</span></td>\n </tr>\n <tr>\n  <th align=\"left\" nowrap=\"nowrap\" title=\"Chưa xử lý\">\n   <div class=\"text-ellipsis\" style=\"width:70px;\">Chưa xử lý</div>\n </th>\n <td align=\"right\"><span id=\"txt_outstanding\">{{account.runing_amount_today | numberPretty:2}}</span>\n </td>\n <tr>\n  <th align=\"left\" nowrap=\"nowrap\" title=\"Chưa xử lý ngày trước\">\n   <div class=\"text-ellipsis\" style=\"width:80px;\">Chưa xử lý tồn</div>\n </th>\n <td align=\"right\"><span id=\"txt_outstanding\">{{account.runing_amount_not_today | numberPretty:2}}</span>\n </td>\n</tr>\n<tr>\n  <th align=\"left\" nowrap=\"nowrap\" title=\"Net Position\">\n   <a title=\"Net Position Help\"><span class=\"iconOdds help right\" style=\"margin-top: 3px;\"></span></a>\n   <div class=\"text-ellipsis\" style=\"width:60px;\">Net Position</div>\n </th>\n <td align=\"right\"><span id=\"txt_netposition\">0.00</span></td>\n</tr>\n<tr>\n  <th align=\"left\" nowrap=\"nowrap\" title=\"Hạn Mức\">\n   <div class=\"text-ellipsis\" style=\"width:70px;\">Hạn Mức</div>\n </th>\n <td align=\"right\"><span id=\"txt_betcredit\">{{account.wallet | numberPretty:2}}</span></td>\n</tr>\n<tr>\n  <th align=\"left\" nowrap=\"nowrap\" title=\"Hạn Mức Tín Dụng\">\n   <div class=\"text-ellipsis\" style=\"width:70px;\">Hạn Mức Tín Dụng</div>\n </th>\n <td align=\"right\"><span id=\"txt_credit\">{{account.credit_line | numberPretty:2}}</span></td>\n</tr>\n<tr>\n  <th colspan=\"2\" align=\"left\" title=\"Truy cập gần nhất\">\n   <div class=\"text-ellipsis\" style=\"width:160px;\">Truy cập gần nhất</div>\n </th>\n</tr>\n<tr>\n  <td colspan=\"2\"><span id=\"txt_login\">{{account.last_time_login}}</span></td>\n</tr>\n<tr>\n  <th colspan=\"2\" align=\"left\" title=\"Giao Dịch Gần Nhất\">\n   <div class=\"text-ellipsis\" style=\"width:160px;\">Giao Dịch Gần Nhất</div>\n </th>\n</tr>\n<tr>\n  <td colspan=\"2\"><span id=\"txt_transaction\">{{account.last_time_bet}}</span></td>\n</tr>\n<!-- <tr>\n  <th colspan=\"2\" align=\"left\" title=\"Ngày Hết Hạn Của Mật Khẩu\">\n   <div class=\"text-ellipsis\" style=\"width:160px;\">Ngày Hết Hạn Của Mật Khẩu</div>\n </th>\n</tr>\n<tr>\n  <td colspan=\"2\"><span id=\"txt_expassword\">3/10/2018 12:17:50 AM</span></td>\n</tr> -->\n</tbody>\n</table>\n</div>\n<div class=\"account_foot\"></div>\n</div>\n<div id=\"div_accountInfoMini\" ng-show=\"show == \'mini\'\">\n  <span id=\"account_info\"></span>\n  <div id=\"account_hard\">\n   <table width=\"95%\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">\n    <tbody>\n     <tr>\n      <td height=\"20\" valign=\"top\" class=\"Tcolor01\" style=\"cursor: pointer;\">\n       <div id=\"divMiniUserName\">{{account.username}}</div>\n     </td>\n     <td valign=\"top\" nowrap=\"nowrap\" style=\"cursor: pointer;\" ng-click=\"onMenuAccount(\'full\')\">\n       <a class=\"btnIcon right balance\" title=\"My Account\">\n        <span class=\"icon-arrow\"></span></a>\n     </td>\n     <td valign=\"top\" nowrap=\"nowrap\" width=\"20\" ng-click=\"loadInfo()\">\n       <a name=\"btnRefresh\" ng-class=\"{\'disable\': accountSetting.loading}\" class=\"btnIcon right\" title=\"Refresh\"><span class=\"icon-refresh\"></span>\n       </a>\n     </td>\n   </tr>\n </tbody>\n</table>\n<div class=\"line\"></div>\n<table width=\"95%\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">\n  <tbody>\n   <tr>\n    <td height=\"20\" valign=\"top\" title=\"Bet Credit\">\n     <div class=\"text-ellipsis\" style=\"width:80px;\">Hạn Mức</div>\n   </td>\n   <td align=\"right\" nowrap=\"nowrap\"><span id=\"AccCurrency_P\">UT </span><span id=\"txt_betcreditInfo\">{{account.wallet | numberPretty:2}}</span></td>\n </tr>\n</tbody>\n</table>\n</div>\n</div>");
$templateCache.put("elements/left-bet-wait/left-bet-item.html"," <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-size:10px;\" ng-if=\"value.bet_kind == \'normal\'\">\n 	<tbody>\n 		<tr><td colspan=\"2\" class=\"TextStyle01\">{{::value.game_type}} / <span style=\"text-transform: uppercase;\">{{::value.bet_type}}</span></td></tr>\n 		<tr>\n 			<td colspan=\"0\" class=\"FavTeamClass\" title=\"\">{{::value.bet_name}}</td>\n 			<td align=\"right\"><span title=\"Score Map\" class=\"iconOdds scoreMap\"></span>\n 			</td>\n 		</tr>\n 		<tr>\n 			<td nowrap=\"\">\n 				<div style=\"overflow: hidden; text-overflow: ellipsis; white-space:nowrap; width: 110px; \" title=\"-121\">\n 				<span class=\"TextStyle03\">{{::value.bet_odd}}</span>\n 				<span class=\"TextStyle01\"> [{{::value.ss}}] @</span>\n 				<span class=\"UdrDogOddsClass\" ng-class=\"{\'FavOddsClass\': value.bet_value < 0}\">{{::value.bet_value | number:2}}</span>\n 			</div>\n 			</td><td align=\"right\" class=\"TextStyle01\"><b>{{value.bet_amount | numberPretty:2}}</b></td>\n 		</tr>\n 		<tr>\n         <td ng-if=\"!value.number_code\" colspan=\"2\" class=\"TextStyle04\">{{::value.home}} -vs- {{::value.away}}</td>\n         <td ng-if=\"value.number_code\" colspan=\"2\" class=\"TextStyle04\">No. {{::value.number_code}}</td>\n      </tr>\n 		<tr>\n 			<td colspan=\"2\" class=\"TextStyle06\"><div class=\"left\">ID:{{::value.id}}</div><div class=\"right text-ellipsis\" style=\"width:70px; text-align:right; line-height:inherit;\" title=\"P\">{{::value.status_name}}</div>\n 			</td>\n 		</tr>\n 	</tbody>\n </table>\n\n <div ng-if=\"value.bet_kind == \'group\'\">\n   <div class=\"TextStyle01\">Cá cược tổng hợp</div>\n   <div class=\"multiple\" ng-class=\"{\'line-through\': bet.status == \'cancel\'}\" ng-repeat=\"(key, bet) in value.bets\">\n      <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n         <tbody>\n            <tr>\n               <td class=\"TextStyle01 \">Bóng đá / {{::bet.bet_type}}</td>\n            </tr>\n            <tr>\n               <td class=\"TextStyle02 \">{{::bet.bet_name}}</td>\n            </tr>\n            <tr>\n               <td>\n               	<span class=\"TextStyle03 \"></span>\n               	<span class=\"TextStyle01 \">{{::bet.bet_odd}}</span>\n               	<span class=\"TextStyle03\">@</span>\n                <span class=\"UdrDogOddsClass\" ng-class=\"{\'FavOddsClass\': bet.bet_value < 0}\">{{::bet.bet_value | number:2}}</span>\n               </td>\n            </tr>\n            <tr>\n               <td class=\"TextStyle04 \">{{::bet.home}} -vs- {{::bet.away}}</td>\n            </tr>\n         </tbody>\n      </table>\n   </div>\n   <!-- <div class=\"multiple\">\n      <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n         <tbody>\n            <tr>\n               <td class=\"TextStyle01 \">Bóng đá / Tài/Xỉu</td>\n            </tr>\n            <tr>\n               <td class=\"TextStyle02 \">Xỉu</td>\n            </tr>\n            <tr>\n               <td><span class=\"TextStyle03 \"></span><span class=\"TextStyle01 \">\n                  2.5</span><span class=\"TextStyle03\">@</span><span class=\"UdrDogOddsClass \">1.80</span>\n               </td>\n            </tr>\n            <tr>\n               <td class=\"TextStyle04 \">Lorca-vs- Granada CF</td>\n            </tr>\n         </tbody>\n      </table>\n   </div>\n   <div class=\"multiple\">\n      <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n         <tbody>\n            <tr>\n               <td class=\"TextStyle01 \">Bóng đá / Handicap</td>\n            </tr>\n            <tr>\n               <td class=\"TextStyle02 \">Osasuna</td>\n            </tr>\n            <tr>\n               <td><span class=\"TextStyle03 \"></span><span class=\"TextStyle01 \">\n                  -0.25</span><span class=\"TextStyle03\">@</span><span class=\"UdrDogOddsClass \">1.88</span>\n               </td>\n            </tr>\n            <tr>\n               <td class=\"TextStyle04 \">Osasuna-vs- Tenerife</td>\n            </tr>\n         </tbody>\n      </table>\n   </div> -->\n   <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n      <tbody>\n         <tr>\n            <td class=\"TextStyle01\"><b>Rate:  {{::value.rate}}</b></td>\n            <td align=\"right\" class=\"TextStyle01\"><b> {{::value.bet_amount | numberPretty:2}}</b></td>\n         </tr>\n         <tr>\n            <td colspan=\"2\" class=\"TextStyle06\">\n               <div class=\"left\">ID:{{::value.id}}</div>\n               <div class=\"right text-ellipsis\" style=\"width:70px; text-align:right; line-height:inherit;\" title=\"Running\">{{::value.status_name}}</div>\n            </td>\n         </tr>\n      </tbody>\n   </table>\n</div>");
$templateCache.put("elements/left-bet-wait/left-bet-wait.html","  <!-- BET -->\n  <!-- BetListMini Session -->\n  <div ng-show=\"waitSetting.type == \'betList\'\" id=\"div_BetListMini\">\n  <div title=\"Bet List (Mini)\" class=\"leftBoxTitle\">\n    <span class=\"icon-arrow\"></span>\n    <div title=\"Refresh\" ng-click=\"loadBets()\" ng-class=\"{\'disable\': waitSetting.loading}\" name=\"btnRefresh_BetListMini\" class=\"btnIcon mark right\" id=\"BetListMiniRefreshIcon\">\n      <span class=\"icon-refresh\"></span>\n    </div>\n    <span style=\"width: 130px;\" class=\"text-ellipsis left titleTxt\">Đang chạy</span>\n  </div>\n  <div class=\"leftBoxbody\">\n    <div class=\"boxbg\">\n      <div class=\"reSet\">\n        <a title=\"Waiting Bet\" class=\"button\" ng-click=\"openBetList(\'waitingBets\')\" style=\"cursor: pointer\"><span>Đang chờ</span>\n        </a>\n        <a title=\"Void\" class=\"button\" ng-click=\"openBetList(\'voidTicket\')\" ng-class=\"{\'disable\': waitSetting.loading}\" style=\"cursor: pointer\"><span>Hủy</span>\n        </a>\n      </div>\n      <span id=\"BetListMiniContainer\">\n        <div class=\"BetInfo\" left-bet-item value=\"value\" ng-class-odd=\"\'bgcpelight\'\" ng-class-even=\"\'bgcpe\'\" ng-repeat=\"(key, value) in waitData.betList\">\n        </div>\n      </span>\n    </div>\n  </div>\n  <div class=\"leftBoxFoot\"></div>\n</div>\n  <!-- END BetListMini Session -->\n  <!-- WaitingBets Session -->\n  <div ng-show=\"waitSetting.type == \'waitingBets\'\" id=\"div_WaitingBets\">\n    <div title=\"Waiting &amp; Cancelled\" class=\"leftBoxTitle\">\n      <span class=\"icon-arrow\"></span>\n      <div title=\"Refresh\" ng-click=\"loadBets()\" name=\"btnRefresh_WaitingBets\" ng-class=\"{\'disable\': waitSetting.loading}\" class=\"btnIcon mark right\" id=\"WaitingBetRefreshIcon\">\n        <span class=\"icon-refresh\"></span>\n      </div>\n      <span style=\"width: 130px;\" class=\"text-ellipsis left titleTxt\">Đang Chờ &amp; Hủy</span>\n    </div>\n    <div class=\"leftBoxbody\">\n      <div class=\"boxbg\">\n        <div class=\"reSet\" id=\"account\">\n          <a title=\"Bet List (Mini)\" class=\"button\" ng-click=\"openBetList(\'betList\')\" style=\"cursor: pointer\"><span>Đang chạy</span>\n          </a>\n          <a title=\"Void\" class=\"button\" ng-click=\"openBetList(\'voidTicket\')\" style=\"cursor: pointer\"><span>Hủy</span>\n          </a>\n        </div>\n        <span id=\"WaitingBetsSpan\">\n          <div align=\"center\" id=\"lastrefresh\" ng-show=\"waitData.waitingBets.length && waitSetting.bet_number !== 0\">Time : <span id=\"refresh-time\">{{waitSetting.bet_number}}</span>\n          </div>\n          <div align=\"center\" id=\"checkStatus\" ng-show=\"waitData.waitingBets.length && waitSetting.bet_number === 0\">Đang kiểm tra...</div>\n          <div class=\"BetInfo Odds_Upd void-cache\" style=\"text-decoration: line-through;\" left-bet-item value=\"value\" ng-repeat=\"(key, value) in waitData.voidTicketCaches\">\n            <!-- <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-size:10px;text-decoration: line-through!important\">\n              <tbody>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle01 \">Bóng đá / {{::value.bet_type}}</td>\n                </tr>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle02 \"\">{{::value.bet_name}}</td>\n                </tr>\n                <tr>\n                  <td><span class=\"TextStyle03 \"><span class=\"TextStyle03\">{{::value.bet_odd}}</span><span class=\"TextStyle01\">[{{::value.ss}}]</span></span><span class=\"\">@</span><span class=\"UdrDogOddsClass\" ng-class=\"{\'FavOddsClass\': value.bet_value < 0}\">{{::value.bet_value | numberOdd:2}}</span>\n                  </td>\n                  <td align=\"right\" class=\"TextStyle01 \"><b>{{::value.bet_amount | numberPretty:2}}</b>\n                  </td>\n                </tr>\n                <tr><td colspan=\"2\" class=\"TextStyle04 \"><div>{{::value.home}} -vs- {{::value.away}}</div><div></div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle06\"><div class=\"left\">ID:{{::value.id}}</div>\n                    <div class=\"right text-ellipsis\" style=\"width:70px; text-align:right; line-height:inherit;\" title=\"Waiting\">Cược hủy</div>\n                  </td>\n                </tr>\n              </tbody>\n            </table> -->\n          </div>\n          <div class=\"BetInfo liveligh\" left-bet-item value=\"value\" ng-repeat=\"(key, value) in waitData.waitingBets\">\n            <!-- <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-size:10px;\">\n              <tbody>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle01 \">Bóng đá / {{::value.bet_type}}</td>\n                </tr>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle02 \"\">{{::value.bet_name}}</td>\n                </tr>\n                <tr>\n                  <td><span class=\"TextStyle03 \"><span class=\"TextStyle03\">{{::value.bet_odd}}</span><span class=\"TextStyle01\">[{{::value.ss}}]</span></span><span class=\"\">@</span><span class=\"UdrDogOddsClass\" ng-class=\"{\'FavOddsClass\': value.bet_value < 0}\">{{::value.bet_value | numberOdd:2}}</span>\n                  </td>\n                  <td align=\"right\" class=\"TextStyle01 \"><b>{{::value.bet_amount| numberPretty:2}}</b>\n                  </td>\n                </tr>\n                <tr><td colspan=\"2\" class=\"TextStyle04 \"><div>{{::value.home}} -vs- {{::value.away}}</div><div></div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle06\"><div class=\"left\">ID:{{::value.id}}</div>\n                    <div class=\"right text-ellipsis\" style=\"width:70px; text-align:right; line-height:inherit;\" title=\"Waiting\">Đang chờ</div>\n                  </td>\n                </tr>\n              </tbody>\n            </table> -->\n          </div>\n          <div align=\"center\" ng-show=\"!waitData.waitingBets.length\">Không có cược chưa xử lý</div>\n        </span>\n      </div>\n    </div>\n    <div class=\"leftBoxFoot\"></div>\n  </div>\n  <!-- End Of WaitingBets Session -->\n  <!-- VoidTicket Session -->\n  <div ng-show=\"waitSetting.type == \'voidTicket\'\" id=\"div_VoidTicket\">\n    <div title=\"Void &amp; Cancelled Bet\" class=\"leftBoxTitle\">\n      <span class=\"icon-arrow\"></span>\n      <div title=\"Refresh\" ng-click=\"loadBets()\" ng-class=\"{\'disable\': waitSetting.loading}\" name=\"btnRefresh_VoidTicket\" class=\"btnIcon mark right\" id=\"VoidTicketRefreshIcon\">\n        <span class=\"icon-refresh\"></span>\n      </div>\n      <span style=\"width: 130px;\" class=\"text-ellipsis left titleTxt\">Hủy</span>\n    </div>\n    <div class=\"leftBoxbody\">\n      <div class=\"boxbg\">\n        <div class=\"reSet\" id=\"account\">\n          <a title=\"Bet List (Mini)\" class=\"button\" ng-click=\"openBetList(\'betList\')\" style=\"cursor: pointer\"><span>Đang chạy</span>\n          </a>\n          <a title=\"Waiting Bet\" class=\"button\" ng-click=\"openBetList(\'waitingBets\')\" style=\"cursor: pointer\"><span>Đang chờ</span>\n          </a>\n        </div>\n        <div class=\"BetInfo Void\" left-bet-item value=\"value\" ng-repeat=\"(key, value) in waitData.voidTicket\">\n            <!-- <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-size:10px;\">\n              <tbody>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle01 \">Bóng đá / {{value.bet_type}}</td>\n                </tr>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle02 \"\">{{::value.bet_name}}</td>\n                </tr>\n                <tr>\n                  <td><span class=\"TextStyle03 \"><span class=\"TextStyle03\">{{::value.bet_odd}}</span><span class=\"TextStyle01\">[{{::value.ss}}]</span></span><span class=\"\">@</span><span class=\"FavOddsClass\">{{::value.bet_value | numberOdd:2}}</span>\n                  </td>\n                  <td align=\"right\" class=\"TextStyle01 \"><b>{{::value.bet_amount | numberPretty:2}}</b>\n                  </td>\n                </tr>\n                <tr><td colspan=\"2\" class=\"TextStyle04 \"><div>{{::value.home}} -vs- {{::value.away}}</div><div></div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"2\" class=\"TextStyle06\"><div class=\"left\">ID:{{::value.id}}</div>\n                    <div class=\"right text-ellipsis\" style=\"width:70px; text-align:right; line-height:inherit;\" title=\"Waiting\">Hủy</div>\n                  </td>\n                </tr>\n              </tbody>\n            </table> -->\n          </div>\n        <span id=\"VoidTicketSpan\" ng-show=\"!waitData.voidTicket.length\">\n          <div align=\"center\">Không Có Tiền Cược Bị Vô Hiệu Lực</div>\n        </span>\n      </div>\n    </div>\n    <div class=\"leftBoxFoot\"></div>\n  </div>\n  <!-- End Of VoidTicket Session -->");
$templateCache.put("elements/numbe-games/index.html","<table id=\"tmplTable\" width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n   <thead>\n      <tr>\n      </tr>\n   </thead>\n   <tbody>\n      <tr></tr>\n      <tr>\n         <td class=\"numbeGame live-double\">\n            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" ng-repeat=\"(key, value) in list\">\n               <tbody>\n                  <tr>\n                     <td>\n                        <div class=\"board\">\n                           <div class=\"tabtitle\">\n                              <% value.name %>\n                           </div>\n                           <ul class=\"panelInfo row\">\n                              <li style=\"width: 23%\"><span class=\"title text-ellipsis\" title=\"Event Number\">Lựa Chọn:</span><span class=\"detail\">Số.  <% value.code %></span></li>\n                              <li style=\"width: 21%\"><span class=\"title\" title=\"Ratio of Over/Under\">Over/Under:</span><span class=\"detail\"><span class=\"\">[<% value.data[21] + \'-\' + value.data[22] %>]</span></span> </li>\n                              <li style=\"width: 20%\"><span class=\"title\" title=\"Ratio of Odd/Even\">Ratio of O/E:</span><span class=\"detail\"><span class=\"\">[<% value.data[23] + \'-\' + value.data[24] %>]</span></span> </li>\n                              <li style=\"width: 25%\"><span class=\"title\" title=\"No. of The Last Drawn Ball\">Last Drawn Ball:</span><span class=\"detail IsLive\"><strong><% value.last_ball %></strong></span></li>\n                           </ul>\n                        </div>\n                        <div class=\"infoBar musicFilter\">\n                           <ul class=\"infoList right\">\n                              <li>\n                                 <a id=\"BingoPos_\' + value.code %>\" title=\"Kickoff Sequence\">\n                                 <span class=\"iconOdds info\"></span>\n                                 </a>\n                              </li>\n                              <li>\n                                 <a title=\"Kết quả\">\n                                 <span class=\"iconOdds stats\">\n                                 </span>\n                                 </a>\n                              </li>\n                              <li>\n                                 <a id=\"BingoStreamingTV\" title=\"Truyền Hình Trực Tiếp\">\n                                    <div class=\"btnBlock\">\n                                       <span class=\"iconOdds tv\"></span>\n                                       <div class=\"textBlock\">Music TV</div>\n                                       <div class=\"music-wave\">\n                                          <span class=\"music-wave__bar\"></span>\n                                          <span class=\"music-wave__bar\"></span>\n                                          <span class=\"music-wave__bar\"></span>\n                                          <span class=\"music-wave__bar\"></span>\n                                       </div>\n                                    </div>\n                                 </a>\n                              </li>\n                           </ul>\n                           <ul class=\"infoList left\">\n                              <li ng-if=\"!value.id\">Game Closed</li>\n                              <li ng-if=\"value.id\"><span>Game Start:</span>&nbsp;<span class=\"color01\" id=\"CountDown_\' + value.code %>\"><time-item time=\"value.data[12]\"></time-item></span></li>\n                           </ul>\n                           <br clear=\"all\">\n                        </div>\n                     </td>\n                  </tr>\n                  <tr>\n                     <td>\n                        <div class=\"tabbox_F\" style=\"border-bottom: none\">\n                        </div>\n                        <div style=\"display: block;\">\n                           <table id=\"Table1\" class=\"oddsTable\" width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n                              <tbody>\n                                 <tr>\n                                    <th title=\"Next Over/Under\">\n                                       <span style=\"width:69px;\" class=\"text-ellipsis\">Next O/U</span>\n                                    </th>\n                                    <th title=\"Next Lẻ/Chẵn\" class=\"even\">\n                                       <span style=\"width:59px;\" class=\"text-ellipsis\">Next O/E</span>\n                                    </th>\n                                    <th title=\"Next Combo [O/U 37.5, O/E]\" colspan=\"2\">\n                                       <span style=\"width:152px;\" class=\"text-ellipsis\">Next Combo<br>[O/U 37.5, O/E]</span>\n                                    </th>\n                                    <th ng-attr-id=\"<% \'l_next_HL_Header_\' + value.code %>\" title=\"Banh kế tiếp - High/Low\" nowrap=\"nowrap\" class=\"OddsDiv  even displayOff\">\n                                       <span style=\"width:62px;text-align:center;\" class=\"text-ellipsis\">Next H/L</span>\n                                    </th>\n                                    <th title=\"Warrior\" class=\"even none_rline displayOn\">\n                                       <span style=\"width:62px;\" class=\"text-ellipsis\">Warrior</span>\n                                    </th>\n                                 </tr>\n                              </tbody>\n                              <tbody>\n                                 <tr class=\"live\" onmouseover=\"this.className=\'trbgov\';\" onmouseout=\"this.className=\'live\';\">\n                                    <td height=\"34\">\n                                       <div class=\"line_divL HdpGoalClass\">\n                                          <span class=\"displayOn\" title=\"37.5\">37.5</span><br>\n                                          <span title=\"Xỉu\">u</span>\n                                       </div>\n                                       <div class=\"line_divR OddsDiv\">\n                                          <a define-text=\'value.data[30]\' class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_ou\', 30, value.data[30], 30, \'Next Tài/Xỉu\', value.last_ball);\">\n                                          <% value.data[30] %></a><br>\n                                          <a class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_ou\', 31, value.data[31], 31, \'Next Tài/Xỉu\', value.last_ball);\">\n                                          <% value.data[31] %></a><br>\n                                       </div>\n                                    </td>\n                                    <td>\n                                       <div class=\"line_divL HdpGoalClass\">\n                                          <span title=\"Lẻ\">O</span><br>\n                                          <span title=\"Chẵn\">E</span>\n                                       </div>\n                                       <div class=\"line_divR OddsDiv \">\n                                          <a class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_oe\', 33, value.data[33], 33, \'Next Chẳn/lẽ\', value.last_ball);\">\n                                          <% value.data[33] %></a><br>\n                                          <a class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_oe\', 34, value.data[34], 34, \'Next Chẳn/lẽ\', value.last_ball);\">\n                                          <% value.data[34] %></a><br>\n                                       </div>\n                                    </td>\n                                    <td style=\"width:125px;\" class=\"none_rline\">\n                                       <div class=\"line_divL HdpGoalClass\">\n                                          <span title=\"Tài/Lẻ\">O/O</span><br>\n                                          <span title=\"Tài/Chẵn\">O/E</span>\n                                       </div>\n                                       <div class=\"line_divR OddsDiv \">\n                                          <a class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_combo\', 43, value.data[43], 43, \'Next Combo\', value.last_ball);\">\n                                          <% value.data[43] %></a><br>\n                                          <a class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_combo\', 45, value.data[45], 45, \'Next Combo\', value.last_ball);\">\n                                          <% value.data[45] %></a><br>\n                                       </div>\n                                    </td>\n                                    <td style=\"width:125px;\">\n                                       <div class=\"line_divL HdpGoalClass\">\n                                          <span title=\"Xỉu/Lẻ\">u/O</span><br>\n                                          <span title=\"Xỉu/Chẵn\">u/E</span>\n                                       </div>\n                                       <div class=\"line_divR OddsDiv \">\n                                          <a class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_combo\', 44, value.data[44], 44, \'Next Combo\', value.last_ball);\">\n                                          <% value.data[44] %></a><br>\n                                          <a class=\"UdrDogOddsClass pointer\" ng-click=\"betBingo(value, \'game_next_combo\', 46, value.data[46], 46, \'Next Combo\', value.last_ball);\">\n                                          <% value.data[46] %></a><br>\n                                       </div>\n                                    </td>\n                                    <td ng-attr-id=\"<% \'l_next_HL_\' + value.code %>\" class=\"displayOff\">\n                                       <div class=\"line_divL HdpGoalClass\">\n                                          <span title=\"\"></span><br>\n                                          <span title=\"Low\"></span>\n                                       </div>\n                                       <div class=\"line_divR OddsDiv \">\n                                          <a class=\"UdrDogOddsClass\" href=\"javascript:betBingo(\'0\',\'27225822\',\'\',\'h\',\'\',87);\">\n                                          </a><br>\n                                          <a class=\"UdrDogOddsClass\" href=\"javascript:betBingo(\'0\',\'27225822\',\'\',\'a\',\'\',87);\">\n                                          </a><br>\n                                       </div>\n                                    </td>\n                                    <td class=\"displayOn\">\n                                       <div class=\"line_divL HdpGoalClass\">\n                                          <span title=\"2nd\">2nd</span><br>\n                                          <span title=\"3rd\">3rd</span>\n                                       </div>\n                                       <div class=\"line_divR OddsDiv \">\n                                          <a class=\"UdrDogOddsClass\" href=\"javascript:betBingo(\'0\',\'27225822\',\'166028179\',\'h\',\'0.93\',88);\">\n                                          <% value.data[37] %></a><br>\n                                          <a class=\"UdrDogOddsClass\" href=\"javascript:betBingo(\'0\',\'27225822\',\'166028179\',\'a\',\'0.93\',88);\">\n                                          <% value.data[38] %></a><br>\n                                       </div>\n                                    </td>\n                                 </tr>\n                              </tbody>\n                           </table>\n                        </div>\n                        <div class=\"tabbox_F\">\n                        </div>\n                        <div class=\"displayOn\">\n                           <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" class=\"oddsTable numberGroup inlive\">\n                              <tbody>\n                                 <tr>\n                                    <th nowrap=\"nowrap\">\n                                       <font title=\"Number Wheel\">Number Wheel</font>\n                                    </th>\n                                 </tr>\n                              </tbody>\n                              <tbody>\n                                 <tr>\n                                    <td class=\"tab_bg\">\n                                       <table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n                                          <tbody>\n                                             <tr class=\"trbg\">\n                                                <td height=\"35\" class=\"tabt_unL tabt_unT\">\n                                                   &nbsp;\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_4_1_\' + value.code %>\" height=\"35\" class=\"tabt_unR live\" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 74, value.data[74], 74, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[74] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_4_2_\' + value.code %>\" class=\"tabt_unR liveligh\" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 75, value.data[75], 75, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[75] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_4_3_\' + value.code %>\" class=\"tabt_unR live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 76, value.data[76], 76, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[76] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_4_4_\' + value.code %>\" class=\"tabt_unR liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 77, value.data[77], 77, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[77] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_4_5_\' + value.code %>\" class=\"live \" \n                                                   onmousemove=\"BingoMouseMove(this);\"\n                                                   onmouseout=\"BingoMouseOut(this);\"\n                                                   ng-click=\"betBingo(value, \'game_wheel\', 78, value.data[78], 78, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[78] %>\n                                                   </div>\n                                                </td>\n                                                <td colspan=\"2\" class=\"tabt_unR tabt_unT\">\n                                                   <div class=\"ball-current UdrDogOddsClass \">\n                                                      <span>Single Ball</span>\n                                                      <span><% value.data[80] %></span>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_1_\' + value.code %>\" width=\"90\" class=\"live Odds_Upd statusChanged\" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 48, value.data[48], 48, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[48] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_1_\' + value.code %>\" class=\"ball tabt_L tabt_T\"\n                                                   ng-click=\"betBingo(value, \'game_wheel\', 80, value.data[80], 80, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[80] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 1)}\">\n                                                      1\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_2_\' + value.code %>\" class=\"ball tabt_T even\" \n                                                   ng-click=\"betBingo(value, \'game_wheel\', 81, value.data[81], 81, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[81] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 2)}\">\n                                                      2\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_3_\' + value.code %>\" class=\"ball tabt_T  \" \n                                                    ng-click=\"betBingo(value, \'game_wheel\', 82, value.data[82], 82, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[82] %>\n                                                   </div>\n                                                   <div class=\"num\" ng-class=\"{\'on\': checkOn(value, 3)}\">\n                                                      3\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_4_\' + value.code %>\" class=\"ball tabt_T even\" ng-click=\"betBingo(value, \'game_wheel\', 83, value.data[83], 83, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[83] %>\n                                                   </div>\n                                                   <div class=\"num\" ng-class=\"{\'on\': checkOn(value, 4)}\">\n                                                      4\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_5_\' + value.code %>\" class=\"ball tabt_R tabt_T\" ng-click=\"betBingo(value, \'game_wheel\', 84, value.data[84], 84, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[84] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 5)}\">\n                                                      5\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_2_1_\' + value.code %>\" rowspan=\"3\" width=\"90\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 64, value.data[64], 64, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[64] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_3_1_\' + value.code %>\" rowspan=\"5\" width=\"90\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 70, value.data[70], 70, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[70] %>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_2_\' + value.code %>\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 49, value.data[49], 49, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[49] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_6_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_6_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_6_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 85, value.data[85], 85, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[85] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 6)}\">\n                                                      6\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_7_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_7_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_7_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 86, value.data[86], 86, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[86] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 7)}\">\n                                                      7\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_8_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_8_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_8_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 87, value.data[87], 87, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[87] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 8)}\">\n                                                      8\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_9_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_9_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_9_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 88, value.data[88], 88, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[88] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 9)}\">\n                                                      9\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_10_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_10_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_10_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 89, value.data[89], 89, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[89] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 10)}\">\n                                                      10\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_3_\' + value.code %>\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 50, value.data[50], 50, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[50] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_11_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_11_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_11_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 90, value.data[90], 90, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[90] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 11)}\">\n                                                      11\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_12_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_12_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_12_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 91, value.data[91], 91, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[91] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 12)}\">\n                                                      12\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_13_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_13_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_13_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 92, value.data[92], 92, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[92] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 13)}\">\n                                                      13\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_14_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_14_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_14_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 93, value.data[93], 93, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[93] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 14)}\">\n                                                      14\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_15_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_15_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_15_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 94, value.data[94], 94, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[94] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 15)}\">\n                                                      15\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_4_\' + value.code %>\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 51, value.data[51], 51, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[51] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_16_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_16_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_16_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 95, value.data[95], 95, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[95] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 16)}\">\n                                                      16\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_17_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_17_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_17_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 96, value.data[96], 96, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[96] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 17)}\">\n                                                      17\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_18_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_18_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_18_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 97, value.data[97], 97, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[97] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 18)}\">\n                                                      18\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_19_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_19_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_19_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 98, value.data[98], 98, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[98] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 19)}\">\n                                                      19\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_20_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_20_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_20_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 99, value.data[99], 99, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[99] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 20)}\">\n                                                      20\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_2_2_\' + value.code %>\" rowspan=\"3\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 65, value.data[65], 65, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[65] %>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_5_\' + value.code %>\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 52, value.data[52], 52, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[52] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_21_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_21_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_21_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 100, value.data[100], 100, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[100] %>\n                                                   </div>\n                                                   <div class=\"num\" ng-class=\"{\'on\': checkOn(value, 21)}\">\n                                                      21\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_22_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_22_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_22_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 101, value.data[101], 101, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[101] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 22)}\">\n                                                      22\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_23_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_23_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_23_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 102, value.data[102], 102, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[102] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 23)}\">\n                                                      23\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_24_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_24_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_24_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 103, value.data[103], 103, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[103] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 24)}\">\n                                                      24\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_25_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_25_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_25_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 104, value.data[104], 104, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[104] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 25)}\">\n                                                      25\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_6_\' + value.code %>\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 53, value.data[53], 53, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[53] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_26_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_26_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_26_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 105, value.data[105], 105, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[105] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 26)}\">\n                                                      26\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_27_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_27_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_27_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 106, value.data[106], 106, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[106] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 27)}\">\n                                                      27\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_28_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_28_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_28_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 107, value.data[107], 107, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[107] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 28)}\">\n                                                      28\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_29_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_29_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_29_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 108, value.data[108], 108, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[108] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 29)}\">\n                                                      29\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_30_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_30_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_30_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 109, value.data[109], 109, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds\">\n                                                      <% value.data[109] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 30)}\">\n                                                      30\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_3_2_\' + value.code %>\" rowspan=\"5\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 71, value.data[71], 71, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[71] %>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_7_\' + value.code %>\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 54, value.data[54], 54, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[54] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_31_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_31_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_31_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 110, value.data[110], 110, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[110] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 31)}\">\n                                                      31\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_32_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_32_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_32_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 111, value.data[111], 111, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[111] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 32)}\">\n                                                      32\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_33_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_33_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_33_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 112, value.data[112], 112, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[112] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 33)}\">\n                                                      33\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_34_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_34_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_34_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 113, value.data[113], 113, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[113] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 34)}\">\n                                                      34\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_35_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_35_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_35_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 114, value.data[114], 114, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[114] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 35)}\">\n                                                      35\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_2_3_\' + value.code %>\" rowspan=\"3\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 66, value.data[66], 66, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[66] %>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_8_\' + value.code %>\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 55, value.data[55], 55, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[55] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_36_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_36_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_36_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 115, value.data[115], 115, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[115] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 36)}\">\n                                                      36\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_37_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_37_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_37_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 116, value.data[116], 116, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[116] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 37)}\">\n                                                      37\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_38_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_38_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_38_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 117, value.data[117], 117, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[117] %>\n                                                   </div>\n                                                   <div class=\"num\" ng-class=\"{\'on\': checkOn(value, 38)}\">\n                                                      38\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_39_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_39_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_39_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 118, value.data[118], 118, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[118] %>\n                                                   </div>\n                                                   <div class=\"num\" ng-class=\"{\'on\': checkOn(value, 39)}\">\n                                                      39\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_40_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_40_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_40_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 119, value.data[119], 119, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[119] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 40)}\">\n                                                      40\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_9_\' + value.code %>\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 56, value.data[56], 56, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[56] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_41_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_41_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_41_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 120, value.data[120], 120, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[120] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 41)}\">\n                                                      41\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_42_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_42_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_42_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 121, value.data[121], 121, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[121] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 42)}\">\n                                                      42\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_43_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_43_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_43_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 122, value.data[122], 122, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[122] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 43)}\">\n                                                      43\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_44_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_44_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_44_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 123, value.data[123], 123, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[123] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 44)}\">\n                                                      44\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_45_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_45_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_45_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 124, value.data[124], 124, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[124] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 45)}\">\n                                                      45\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_10_\' + value.code %>\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 57, value.data[57], 57, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[57] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_46_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_46_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_46_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 125, value.data[125], 125, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[125] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 46)}\">\n                                                      46\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_47_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_47_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_47_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 126, value.data[126], 126, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[126] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 47)}\">\n                                                      47\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_48_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_48_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_48_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 127, value.data[127], 127, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[127] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 48)}\">\n                                                      48\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_49_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_49_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_49_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 128, value.data[128], 128, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[128] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 49)}\">\n                                                      49\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_50_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_50_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_50_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 129, value.data[129], 129, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[129] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 50)}\">\n                                                      50\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_2_4_\' + value.code %>\" rowspan=\"3\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 67, value.data[67], 67, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[67] %>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_11_\' + value.code %>\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 58, value.data[58], 58, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[58] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_51_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_51_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_51_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 130, value.data[130], 130, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[130] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 51)}\">\n                                                      51\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_52_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_52_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_52_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 131, value.data[131], 131, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[131] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 52)}\">\n                                                      52\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_53_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_53_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_53_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 132, value.data[132], 132, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[132] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 53)}\">\n                                                      53\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_54_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_54_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_54_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 133, value.data[133], 133, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[133] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 54)}\">\n                                                      54\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_55_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_55_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_55_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 134, value.data[134], 134, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[134] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 55)}\">\n                                                      55\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_3_3_\' + value.code %>\" rowspan=\"5\" class=\"live tabt_B \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 72, value.data[72], 72, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[72] %>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_12_\' + value.code %>\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 59, value.data[59], 59, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[59] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_56_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_56_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_56_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 135, value.data[135], 135, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[135] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 56)}\">\n                                                      56\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_57_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_57_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_57_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 136, value.data[136], 136, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[136] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 57)}\">\n                                                      57\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_58_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_58_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_58_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 137, value.data[137], 137, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[137] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 58)}\">\n                                                      58\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_59_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_59_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_59_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 138, value.data[138], 138, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[138] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 59)}\">\n                                                      59\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_60_\' + value.code %>\" class=\"ball tabt_R\"\n                                                   ng-click=\"betBingo(value, \'game_wheel\', 139, value.data[139], 139, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds\">\n                                                      <% value.data[139] %>\n                                                   </div>\n                                                   <div class=\"num\" ng-class=\"{\'on\': checkOn(value, 60)}\">\n                                                      60\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_13_\' + value.code %>\" class=\"live \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 60, value.data[60], 60, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[60] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_61_\' + value.code %>\" class=\"ball tabt_L\"\n                                                   ng-click=\"betBingo(value, \'game_wheel\', 140, value.data[140], 140, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[140] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 61)}\">\n                                                      61\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_62_\' + value.code %>\" class=\"ball even\"\n                                                   ng-click=\"betBingo(value, \'game_wheel\', 141, value.data[141], 141, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[141] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 62 )}\">\n                                                      62\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_63_\' + value.code %>\" class=\"ball\"\n                                                   ng-click=\"betBingo(value, \'game_wheel\', 142, value.data[142], 142, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[142] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 63)}\">\n                                                      63\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_64_\' + value.code %>\" class=\"ball even\" ng-click=\"betBingo(value, \'game_wheel\', 143, value.data[143], 143, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[143] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 64)}\">\n                                                      64\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_65_\' + value.code %>\" class=\"ball tabt_R\" ng-click=\"betBingo(value, \'game_wheel\', 144, value.data[144], 144, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[144] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 65)}\">\n                                                      65\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_2_5_\' + value.code %>\" rowspan=\"3\" class=\"live tabt_B \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 68, value.data[68], 68, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[68] %>\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_14_\' + value.code %>\" class=\"liveligh \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 61, value.data[61], 61, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[61] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_66_\' + value.code %>\" class=\"ball tabt_L  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_66_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_66_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 145, value.data[145], 145, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[145] %>\n                                                   </div>\n                                                   <div class=\"num\" ng-class=\"{\'on\': checkOn(value, 66)}\">\n                                                      66\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_67_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_67_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_67_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 146, value.data[146], 146, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[146] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 67)}\">\n                                                      67\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_68_\' + value.code %>\" class=\"ball  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_68_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_68_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 147, value.data[147], 147, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[147] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 68)}\">\n                                                      68\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_69_\' + value.code %>\" class=\"ball even  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_69_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_69_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 148, value.data[148], 148, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[148] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 69)}\">\n                                                      69\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_70_\' + value.code %>\" class=\"ball tabt_R  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_70_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_70_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 149, value.data[149], 149, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[149] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 70)}\">\n                                                      70\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                             <tr>\n                                                <td ng-attr-id=\"<% \'l_1_15_\' + value.code %>\" class=\"live tabt_B \" onmousemove=\"BingoMouseMove(this);\" onmouseout=\"BingoMouseOut(this);\" ng-click=\"betBingo(value, \'game_wheel\', 62, value.data[62], 62, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"UdrDogOddsClass\">\n                                                      <% value.data[62] %>\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_71_\' + value.code %>\" class=\"ball tabt_L tabt_B  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_71_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_71_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 150, value.data[150], 150, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[150] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 71)}\">\n                                                      71\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_72_\' + value.code %>\" class=\"ball even tabt_B  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_72_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_72_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 151, value.data[151], 151, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[151] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 72)}\">\n                                                      72\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_73_\' + value.code %>\" class=\"ball tabt_B  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_73_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_73_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 152, value.data[152], 152, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[152] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 73)}\">\n                                                      73\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_74_\' + value.code %>\" class=\"ball even tabt_B  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_74_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_74_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 153, value.data[153], 153, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[153] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 74)}\">\n                                                      74\n                                                   </div>\n                                                </td>\n                                                <td ng-attr-id=\"<% \'l_5_75_\' + value.code %>\" class=\"ball tabt_R tabt_B  \" onmousemove=\"SingleNumberWheelMouseMove(this, \'l_5_75_\' + value.code %>\');\" onmouseout=\"SingleNumberWheelMouseOut(this, \'l_5_75_\' + value.code %>\');\" ng-click=\"betBingo(value, \'game_wheel\', 154, value.data[154], 154, \'Number Wheel\', value.last_ball);\">\n                                                   <div class=\"ball-odds \">\n                                                      <% value.data[154] %>\n                                                   </div>\n                                                   <div class=\"num \" ng-class=\"{\'on\': checkOn(value, 75)}\">\n                                                      75\n                                                   </div>\n                                                </td>\n                                             </tr>\n                                          </tbody>\n                                       </table>\n                                    </td>\n                                 </tr>\n                              </tbody>\n                           </table>\n                        </div>\n                     </td>\n                  </tr>\n               </tbody>\n            </table>\n         </td>\n      </tr>\n   </tbody>\n</table>");
$templateCache.put("elements/left-sports/left-sports.html","<div id=\"div_menu\" ng-class=\"{\'hidemenu\': !show, \'showmenu\': show}\">\n   <span class=\"icon-arrow\"></span>\n   <a ng-click=\"onMenuSports()\" class=\"pointer\">\n   <span id=\"lblShowSportsMenu\" ng-show=\"!show\">Hiện Menu Thể thao</span>\n   <span id=\"lblHideSportsMenu\" ng-show=\"show\">Ẩn Menu Thể thao</span></a>\n</div>\n<!--sport menu--> \n<span id=\"MenuContainer\" style=\"\">\n<!--olympic menu-->\n<div id=\"subnav_olympic\" class=\"special\">\n<table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n    <tbody><tr id=\"menu_all_tr\" style=\"display: none;\">\n        <td id=\"menu_all\" class=\"current\" colspan=\"2\"><a href=\"JavaScript:SwitchMenuType(0,\'template/sportsbook/vn/images/\')\"><span title=\"Tất cả\">Tất cả</span></a></td>\n    </tr>\n    <tr>\n      <td id=\"menu_ep\" class=\"\" style=\"display: none;\"><a href=\"JavaScript:SwitchMenuType(3,\'template/sportsbook/vn/images/\')\"><span title=\"Euro&nbsp;2016\">Euro&nbsp;2016</span></a></td>\n      <td id=\"menu_wp\" class=\"\" style=\"display: none;\"><a href=\"JavaScript:SwitchMenuType(1,\'template/sportsbook/vn/images/\')\"><span title=\"Olympic&nbsp;2016\">Olympic&nbsp;2016</span></a></td>\n      <td id=\"menu_cp\" class=\"\"><a href=\"JavaScript:SwitchMenuType(2,\'template/sportsbook/vn/images/\')\"><span title=\"Cúp Thế Giới\">Cúp Thế Giới</span></a></td>\n      <td id=\"menu_ap\" class=\"current\"><a href=\"JavaScript:SwitchMenuType(0,\'template/sportsbook/vn/images/\')\"><span title=\"Tất cả\">Tất cả</span></a></td>\n    </tr>\n  </tbody></table>\n</div>\n<div id=\"subnav_head\" class=\"item\">\n    <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n        <tbody><tr>\n          <td id=\"market_E_head\" width=\"30%\" valign=\"top\" class=\"itemrd\"><span id=\"market_E_text\" style=\"cursor: pointer;\" onclick=\"openMenu(\'vn\'); LoadMenuData(\'E\');\" title=\"Sớm\">Sớm</span></td>\n          <td class=\"itemline\" width=\"2\">&nbsp;</td>\n          <td id=\"market_T_head\" width=\"30%\" valign=\"top\" class=\"itemrdon\"><span id=\"market_T_text\" style=\"cursor: pointer;\" onclick=\"openMenu(\'vn\'); LoadMenuData(\'T\');\" title=\"Hôm nay\">Hôm nay</span></td>\n          <td class=\"itemline\" width=\"2\">&nbsp;</td>\n          <td width=\"40%\" valign=\"top\" nowrap=\"nowrap\" id=\"market_L_head\" class=\"itemrd\"><span style=\"cursor: pointer; display: block;\" onclick=\"openMenu(\'vn\'); LoadMenuData(\'L\'); LiveSportsClickAll(true);\" title=\"Trực tiếp\"> <span id=\"market_L_text\">Trực tiếp</span> <span id=\"market_L_head_Cnt\">56</span></span></td>\n          <!--td width=\"1%\" valign=\"top\"><div class=\"menu_hard_line\">&nbsp;</div></td--> \n        </tr>\n    </tbody></table>\n</div>\n\n<!--Today and Early-->\n<div id=\"subnavbg\">\n<div id=\"subnav\">\n    \n  <div id=\"market_body\" style=\"\" ng-show=\"show\"> \n    \n    <div id=\"1_head\">\n      <a class=\"navon current\" title=\"Bóng đá\" href=\"javascript:ShowOdds(\'A\')\">\n        <div class=\"right\">\n          <span id=\"img_1_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n          <span id=\"img_1_LV\" class=\"icon_live\"></span>\n          <span id=\"1_Cnt\" class=\"text-number\">238</span>\n        </div>\n        <div class=\"text-ellipsis\">Bóng đá</div>\n      </a>\n    </div>\n    <div id=\"1_body\" class=\"MuSubbg\">\n      <div id=\"1_A\">\n        <div class=\"subnav-link\" style=\"display: block; position:relative;\">\n            <a href=\"JavaScript:ShowOdds(\'A\')\" class=\"current\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"1_A_Cnt\" class=\"text-number\">124</span>\n            </a>&nbsp;<div style=\"position:absolute; top:22px; left: 16px;\">\n              <a href=\"JavaScript:ShowOdds(\'\',\'1\',\'1\')\" style=\"display:inline;padding:0px; margin:0px;\"><b>Mới</b></a>|<a href=\"JavaScript:ShowOdds(\'\',\'1\',\'1F\')\" style=\"display:inline; padding:0px; margin:0px;\"><b>Cũ</b></a></div>\n          </div>\n      </div>\n      <div id=\"1_1X2\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'1X2\',\'1X2\')\">\n              <span class=\"submenu\" title=\"Match Odds 1X2\">Match Odds 1X2</span>\n              <span id=\"1_1X2_Cnt\" class=\"text-number\">108</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"1_CS\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'CS\',\'CorrectScore\')\">\n              <span class=\"submenu\" title=\"Điểm Số Chính Xác\">Điểm Số Chính Xác</span>\n              <span id=\"1_CS_Cnt\" class=\"text-number\">14</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"1_OE\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OE\',\'Oe\')\">\n              <span class=\"submenu\" title=\"Odd/Even\">Odd/Even</span>\n              <span id=\"1_OE_Cnt\" class=\"text-number\">105</span>\n            </a>\n          </div>\n      </div>\n            <div id=\"1_TG\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'TG\',\'TG\')\">\n              <span class=\"submenu\" title=\"Tổng Số Bàn Thắng\">Tổng Số Bàn Thắng</span>\n              <span id=\"1_TG_Cnt\" class=\"text-number\">14</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"1_HTFT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'HTFT\',\'HTFT\')\">\n              <span class=\"submenu\" title=\"Half Time/Full Time\">Half Time/Full Time</span>\n              <span id=\"1_HTFT_Cnt\" class=\"text-number\">13</span>\n            </a>\n          </div>\n      </div>\n            <div id=\"1_HTFTOE\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'HTFTOE\',\'HTFTOE\')\">\n              <span class=\"submenu\" title=\"HT / FT Odd/Even\">HT / FT Odd/Even</span>\n              <span id=\"1_HTFTOE_Cnt\" class=\"text-number\">7</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"1_FGLG\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'FGLG\',\'FGLG\')\">\n              <span class=\"submenu\" title=\"Bàn Thắng Đầu/Cuối\">Bàn Thắng Đầu/Cuối</span>\n              <span id=\"1_FGLG_Cnt\" class=\"text-number\">7</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"1_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'1\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"1_P_Cnt\" class=\"text-number\">12</span>\n              <span id=\"img_1_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"1_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'1\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"1_OT_Cnt\" class=\"text-number\">114</span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"161_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'161\')\" title=\"Number Game\">\n                  <div class=\"right\">\n                      <span id=\"img_161_TV\" class=\"iconOdds tv\" onclick=\"fFrame.openBingoStreamingMain();\" title=\"Truyền Hình Trực Tiếp\"></span>\n                      <span id=\"img_161_LV\" class=\"icon_live\"></span>\n                      <span id=\"161_Cnt\" class=\"text-number\">2</span>\n                  </div>\n                  <div class=\"text-ellipsis\">Number Game</div>\n      </a>\n    </div>\n    <div id=\"161_body\" class=\"MuSubbg\" style=\"display: none;\">       \n    </div>\n        \n    <div id=\"154_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'154\')\" title=\"Horse Racing Fixed Odds\">\n                  <div class=\"right\">\n                      <span id=\"154_Cnt\" class=\"text-number\"></span>\n                  </div>\n                  <div class=\"text-ellipsis\">Horse Racing Fixed Odds</div>\n      </a>\n    </div>\n    <div id=\"154_body\" class=\"MuSubbg\" style=\"display: none;\">       \n    </div>\n        \n    <div id=\"15X_head\" style=\"display: none;\">\n      <a class=\"navon\" id=\"lnkHorse\" href=\"JavaScript:SwitchSport(\'\',\'151\')\" title=\"Racing\">\n                <div class=\"right\">           \n                    <span id=\"img_15X_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"15X_Cnt\" class=\"text-number\"></span>  \n                </div>\n                <div class=\"text-ellipsis\">Racing</div>\n            </a>\n    </div>\n    <div id=\"15X_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"151_A\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:SwitchSport(\'\',\'151\')\">\n              <span class=\"submenu\" title=\"Đua Ngựa\">Đua Ngựa</span>\n              <span id=\"151_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"152_A\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:SwitchSport(\'\',\'152\')\">\n              <span class=\"submenu\" title=\"Greyhounds\">Greyhounds</span>\n              <span id=\"152_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"153_A\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:SwitchSport(\'\',\'153\')\">\n              <span class=\"submenu\" title=\"Harness\">Harness</span>\n              <span id=\"153_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n        \n    <div id=\"18X_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'190\')\" title=\"Thể Thao Ảo\">\n                <div class=\"text-ellipsis\">Thể Thao Ảo</div>\n            </a>\n    </div>\n    <div id=\"18X_body\" class=\"MuSubbg\" style=\"display: none;\">\n            <!-- BEGIN T_VirtualSport_190 -->\n      <div id=\"190_A\">\n        <div class=\"subnav-link two-tags\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'190\')\">\n              <span class=\"submenu\" title=\"Giải đấu Bóng đá Ảo\">Giải đấu Bóng đá Ảo</span>\n                            <span class=\"icon_new\"></span>\n                            <span class=\"icon_trophy\"></span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_190 -->\n            <!-- BEGIN T_VirtualSport_191 -->\n      <div id=\"191_A\">\n        <div class=\"subnav-link two-tags\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'191\')\">\n              <span class=\"submenu\" title=\"Quốc gia Bóng đá Ảo\">Quốc gia Bóng đá Ảo</span>\n                            <span class=\"icon_new\"></span>\n                            <span class=\"icon_trophy\"></span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_191 -->\n            <!-- BEGIN T_VirtualSport_180 -->\n      <div id=\"180_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'180\')\">\n              <span class=\"submenu\" title=\"Bóng đá Ảo\">Bóng đá Ảo</span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_180 -->\n            <!-- BEGIN T_VirtualSport_181 -->\n      <div id=\"181_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'181\')\">\n              <span class=\"submenu\" title=\"Đua Ngựa Ảo\">Đua Ngựa Ảo</span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_181 -->\n            <!-- BEGIN T_VirtualSport_182 -->\n      <div id=\"182_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'182\')\">\n              <span class=\"submenu\" title=\"Đua Chó Ảo\">Đua Chó Ảo</span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_182 -->\n            <!-- BEGIN T_VirtualSport_186 -->\n            <div id=\"186_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'186\')\">\n              <span class=\"submenu\" title=\"Quần Vợt Ảo\">Quần Vợt Ảo</span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_186 -->\n            <!-- BEGIN T_VirtualSport_184 -->\n            <div id=\"184_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'184\')\">\n              <span class=\"submenu\" title=\"Đua xe Ảo\">Đua xe Ảo</span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_184 -->\n            <!-- BEGIN T_VirtualSport_185 -->\n            <div id=\"185_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'185\')\">\n              <span class=\"submenu\" title=\"Đua xe đạp ảo\">Đua xe đạp ảo</span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_185 -->\n            <!-- BEGIN T_VirtualSport_183 -->\n             <div id=\"183_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'183\')\">\n              <span class=\"submenu\" title=\"Đua xe mô tô ảo\">Đua xe mô tô ảo</span>\n            </a>\n          </div>\n      </div>\n            <!-- END T_VirtualSport_183 -->\n    </div>\n        \n    <div id=\"43_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'43\',false,false,\'OU\')\" title=\"E-Sports\">\n                <div class=\"right\">\n                    <span id=\"img_43_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_43_LV\" class=\"icon_live\"></span>\n                    <span id=\"43_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">E-Sports</div>\n            </a>\n    </div>\n    <div id=\"43_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"43_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'43\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"43_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"43_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'43\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"43_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_43_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"43_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'43\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"43_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"2_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'2\',false,false,\'OU\')\" title=\"Bóng rổ\">\n                <div class=\"right\">\n                    <span id=\"img_2_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_2_LV\" class=\"icon_live\"></span>\n                    <span id=\"2_Cnt\" class=\"text-number\">66</span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng rổ</div>\n            </a>\n    </div>\n    <div id=\"2_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"2_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'2\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"2_A_Cnt\" class=\"text-number\">60</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"2_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'2\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"2_P_Cnt\" class=\"text-number\">15</span>\n              <span id=\"img_2_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"2_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'2\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"2_OT_Cnt\" class=\"text-number\">6</span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"8_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'8\',false,false,\'OU\')\" title=\"Bóng chày\">\n                <div class=\"right\">\n                    <span id=\"img_8_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_8_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"8_Cnt\" class=\"text-number\">1</span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng chày</div>\n            </a>\n    </div>\n    <div id=\"8_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"8_A\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'8\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"8_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"8_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'8\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"8_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_8_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"8_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'8\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"8_OT_Cnt\" class=\"text-number\">1</span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"3_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'3\',false,false,\'OU\')\" title=\"Bóng bầu dục Mỹ\">\n                <div class=\"right\">\n                    <span id=\"img_3_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_3_LV\" class=\"icon_live\"></span>\n                    <span id=\"3_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng bầu dục Mỹ</div>\n            </a>\n    </div>\n    <div id=\"3_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"3_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'3\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"3_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"3_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'3\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"3_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_3_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"3_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'3\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"3_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"4_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'4\',false,false,\'OU\')\" title=\"Khúc côn cầu trên băng\">\n                <div class=\"right\">\n                    <span id=\"img_4_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_4_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"4_Cnt\" class=\"text-number\">12</span>\n                </div>\n                <div class=\"text-ellipsis\">Khúc côn cầu trên băng</div>\n            </a>\n    </div>\n    <div id=\"4_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"4_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'4\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"4_A_Cnt\" class=\"text-number\">12</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"4_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'4\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"4_P_Cnt\" class=\"text-number\">4</span>\n              <span id=\"img_4_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"4_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'4\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"4_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"29_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'29\',false,false,\'OU\')\" title=\"Winter Sports\">\n                <div class=\"right\">\n                    <span id=\"img_29_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_29_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"29_Cnt\" class=\"text-number\">4</span>\n                </div>\n                <div class=\"text-ellipsis\">Winter Sports</div>\n            </a>\n    </div>\n    <div id=\"29_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"29_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'29\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"29_A_Cnt\" class=\"text-number\">4</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"29_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'29\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"29_P_Cnt\" class=\"text-number\">4</span>\n              <span id=\"img_29_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"29_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'29\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"29_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"5_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'5\',false,false,\'OU\')\" title=\"Quần vợt\">\n                <div class=\"right\">\n                    <span id=\"img_5_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_5_LV\" class=\"icon_live\"></span>\n                    <span id=\"5_Cnt\" class=\"text-number\">30</span>\n                </div>\n                <div class=\"text-ellipsis\">Quần vợt</div>\n            </a>\n    </div>\n    <div id=\"5_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"5_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'5\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"5_A_Cnt\" class=\"text-number\">30</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"5_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'5\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"5_P_Cnt\" class=\"text-number\">30</span>\n              <span id=\"img_5_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"5_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'5\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"5_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"9_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'9\',false,false,\'OU\')\" title=\"Cầu lông\">\n                <div class=\"right\">\n                    <span id=\"img_9_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_9_LV\" class=\"icon_live\"></span>\n                    <span id=\"9_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Cầu lông</div>\n            </a>\n    </div>\n    <div id=\"9_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"9_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'9\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"9_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"9_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'9\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"9_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_9_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"9_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'9\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"9_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"6_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'6\',false,false,\'OU\')\" title=\"Bóng chuyền\">\n                <div class=\"right\">\n                    <span id=\"img_6_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_6_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"6_Cnt\" class=\"text-number\">6</span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng chuyền</div>\n            </a>\n    </div>\n    <div id=\"6_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"6_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'6\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"6_A_Cnt\" class=\"text-number\">6</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"6_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'6\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"6_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_6_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"6_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'6\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"6_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"7_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'7\',false,false,\'OU\')\" title=\"Snooker/Pool\">\n                <div class=\"right\">\n                    <span id=\"img_7_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_7_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"7_Cnt\" class=\"text-number\">3</span>\n                </div>\n                <div class=\"text-ellipsis\">Snooker/Pool</div>\n            </a>\n    </div>\n    <div id=\"7_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"7_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'7\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"7_A_Cnt\" class=\"text-number\">3</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"7_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'7\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"7_P_Cnt\" class=\"text-number\">1</span>\n              <span id=\"img_7_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"7_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'7\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"7_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"11_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'11\',false,false,\'OU\')\" title=\"Thể Thao Môtô\">\n                <div class=\"right\">\n                    <span id=\"img_11_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_11_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"11_Cnt\" class=\"text-number\">4</span>\n                </div>\n                <div class=\"text-ellipsis\">Thể Thao Môtô</div>\n            </a>\n    </div>\n    <div id=\"11_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"11_A\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'11\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"11_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"11_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'11\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"11_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_11_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"11_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'11\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"11_OT_Cnt\" class=\"text-number\">4</span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"10_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'10\',false,false,\'OU\')\" title=\"Đánh Golf\">\n                <div class=\"right\">\n                    <span id=\"img_10_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_10_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"10_Cnt\" class=\"text-number\">15</span>\n                </div>\n                <div class=\"text-ellipsis\">Đánh Golf</div>\n            </a>\n    </div>\n    <div id=\"10_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"10_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'10\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"10_A_Cnt\" class=\"text-number\">15</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"10_P\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'10\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"10_P_Cnt\" class=\"text-number\">15</span>\n              <span id=\"img_10_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"10_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'10\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"10_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"50_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'50\',false,false,\'OU\')\" title=\"Cricket\">\n                <div class=\"right\">\n                    <span id=\"img_50_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_50_LV\" class=\"icon_live\"></span>\n                    <span id=\"50_Cnt\" class=\"text-number\">17</span>\n                </div>\n                <div class=\"text-ellipsis\">Cricket</div>\n            </a>\n    </div>\n    <div id=\"50_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"50_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'50\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Match, Handicap &amp; Over/Under\">Match, Handicap &amp; Over/Under</span>\n              <span id=\"50_A_Cnt\" class=\"text-number\">16</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"50_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'50\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"50_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_50_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"50_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'50\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"50_OT_Cnt\" class=\"text-number\">1</span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"99_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'99\',false,false,\'OU\')\" title=\"Môn thể thao khác\">\n                <div class=\"right\">\n                    <span id=\"img_99_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_99_LV\" class=\"icon_live\"></span>\n                    <span id=\"99_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Môn thể thao khác</div>\n            </a>\n    </div>\n    <div id=\"99_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"99_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'99\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"99_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"99_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'99\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"99_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_99_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"99_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'99\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"99_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"44_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'44\',false,false,\'OU\')\" title=\"Quyền Thái\">\n                <div class=\"right\">\n                    <span id=\"img_44_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_44_LV\" class=\"icon_live\"></span>\n                    <span id=\"44_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Quyền Thái</div>\n            </a>\n    </div>\n    <div id=\"44_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"44_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'44\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"44_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"44_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'44\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"44_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_44_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"44_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'44\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"44_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"16_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'16\',false,false,\'OU\')\" title=\"Quyền anh\">\n                <div class=\"right\">\n                    <span id=\"img_16_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_16_LV\" class=\"icon_live\"></span>\n                    <span id=\"16_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Quyền anh</div>\n            </a>\n    </div>\n    <div id=\"16_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"16_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'16\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"16_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"16_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'16\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"16_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_16_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"16_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'16\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"16_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"26_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'26\',false,false,\'OU\')\" title=\"Bóng bầu dục\">\n                <div class=\"right\">\n                    <span id=\"img_26_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_26_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"26_Cnt\" class=\"text-number\">1</span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng bầu dục</div>\n            </a>\n    </div>\n    <div id=\"26_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"26_A\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'26\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"26_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"26_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'26\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"26_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_26_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"26_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'26\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"26_OT_Cnt\" class=\"text-number\">1</span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"25_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'25\',false,false,\'OU\')\" title=\"Ném phi tiêu\">\n                <div class=\"right\">\n                    <span id=\"img_25_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_25_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"25_Cnt\" class=\"text-number\">1</span>\n                </div>\n                <div class=\"text-ellipsis\">Ném phi tiêu</div>\n            </a>\n    </div>\n    <div id=\"25_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"25_A\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'25\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"25_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"25_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'25\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"25_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_25_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"25_OT\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'25\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"25_OT_Cnt\" class=\"text-number\">1</span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"18_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'18\',false,false,\'OU\')\" title=\"Bóng bàn\">\n                <div class=\"right\">\n                    <span id=\"img_18_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_18_LV\" class=\"icon_live\"></span>\n                    <span id=\"18_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng bàn</div>\n            </a>\n    </div>\n    <div id=\"18_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"18_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'18\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"18_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"18_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'18\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"18_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_18_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"18_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'18\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"18_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"30_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'30\',false,false,\'OU\')\" title=\"Squash\">\n                <div class=\"right\">\n                    <span id=\"img_30_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_30_LV\" class=\"icon_live\"></span>\n                    <span id=\"30_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Squash</div>\n            </a>\n    </div>\n    <div id=\"30_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"30_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'30\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"30_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"30_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'30\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"30_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_30_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"30_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'30\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"30_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"28_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'28\',false,false,\'OU\')\" title=\"Khúc côn cầu trên cỏ\">\n                <div class=\"right\">\n                    <span id=\"img_28_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_28_LV\" class=\"icon_live\"></span>\n                    <span id=\"28_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Khúc côn cầu trên cỏ</div>\n            </a>\n    </div>\n    <div id=\"28_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"28_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'28\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"28_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"28_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'28\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"28_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_28_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"28_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'28\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"28_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"24_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'24\',false,false,\'OU\')\" title=\"Bóng ném\">\n                <div class=\"right\">\n                    <span id=\"img_24_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" style=\"display: none;\"></span>\n                    <span id=\"img_24_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n                    <span id=\"24_Cnt\" class=\"text-number\">10</span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng ném</div>\n            </a>\n    </div>\n    <div id=\"24_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"24_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'24\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"24_A_Cnt\" class=\"text-number\">10</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"24_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'24\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"24_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_24_P_LV\" class=\"icon_live\" style=\"display: none;\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"24_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'24\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"24_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"32_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'32\',false,false,\'OU\')\" title=\"Netball\">\n                <div class=\"right\">\n                    <span id=\"img_32_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_32_LV\" class=\"icon_live\"></span>\n                    <span id=\"32_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Netball</div>\n            </a>\n    </div>\n    <div id=\"32_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"32_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'32\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"32_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"32_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'32\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"32_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_32_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"32_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'32\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"32_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"22_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'22\',false,false,\'OU\')\" title=\"Điền kinh\">\n                <div class=\"right\">\n                    <span id=\"img_22_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_22_LV\" class=\"icon_live\"></span>\n                    <span id=\"22_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Điền kinh</div>\n            </a>\n    </div>\n    <div id=\"22_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"22_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'22\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"22_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"22_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'1\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"22_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_22_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"22_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'22\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"22_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"12_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'12\',false,false,\'OU\')\" title=\"Bơi lội\">\n                <div class=\"right\">\n                    <span id=\"img_12_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_12_LV\" class=\"icon_live\"></span>\n                    <span id=\"12_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Bơi lội</div>\n            </a>\n    </div>\n    <div id=\"12_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"12_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'12\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"12_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"12_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'12\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"12_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_12_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"12_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'12\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"12_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"14_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'14\',false,false,\'OU\')\" title=\"Bóng nước\">\n                <div class=\"right\">\n                    <span id=\"img_14_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_14_LV\" class=\"icon_live\"></span>\n                    <span id=\"14_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Bóng nước</div>\n            </a>\n    </div>\n    <div id=\"14_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"14_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'14\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"14_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"14_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'14\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"14_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_14_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"14_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'14\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"14_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"15_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'15\',false,false,\'OU\')\" title=\"Lặn\">\n                <div class=\"right\">\n                    <span id=\"img_15_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_15_LV\" class=\"icon_live\"></span>\n                    <span id=\"15_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Lặn</div>\n            </a>\n    </div>\n    <div id=\"15_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"15_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'15\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"15_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"15_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'15\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"15_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_15_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"15_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'15\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"15_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"17_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'17\',false,false,\'OU\')\" title=\"Bắn cung\">\n                <div class=\"right\">\n                    <span id=\"img_17_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_17_LV\" class=\"icon_live\"></span>\n                    <span id=\"17_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Bắn cung</div>\n            </a>\n    </div>\n    <div id=\"17_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"17_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'17\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"17_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"17_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'17\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"17_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_17_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"17_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'17\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"17_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"20_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'20\',false,false,\'OU\')\" title=\"Canoeing\">\n                <div class=\"right\">\n                    <span id=\"img_20_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_20_LV\" class=\"icon_live\"></span>\n                    <span id=\"20_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Canoeing</div>\n            </a>\n    </div>\n    <div id=\"20_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"20_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'20\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"20_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"20_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'20\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"20_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_20_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"20_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'20\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"20_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"33_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'33\',false,false,\'OU\')\" title=\"Đua xe đạp\">\n                <div class=\"right\">\n                    <span id=\"img_33_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_33_LV\" class=\"icon_live\"></span>\n                    <span id=\"33_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Đua xe đạp</div>\n            </a>\n    </div>\n    <div id=\"33_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"33_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'33\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"33_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"33_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'33\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"33_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_33_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"33_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'33\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"33_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"31_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'31\',false,false,\'OU\')\" title=\"Giải Trí\">\n                <div class=\"right\">\n                    <span id=\"img_31_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_31_LV\" class=\"icon_live\"></span>\n                    <span id=\"31_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Giải Trí</div>\n            </a>\n    </div>\n    <div id=\"31_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"31_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'31\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"31_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"31_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'31\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"31_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_31_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"31_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'31\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"31_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"23_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'23\',false,false,\'OU\')\" title=\"Cưỡi ngựa\">\n                <div class=\"right\">\n                    <span id=\"img_23_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_23_LV\" class=\"icon_live\"></span>\n                    <span id=\"23_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Cưỡi ngựa</div>\n            </a>\n    </div>\n    <div id=\"23_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"23_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'23\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"23_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"23_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'23\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"23_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_23_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"23_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'23\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"23_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"34_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'34\',false,false,\'OU\')\" title=\"Đấu kiếm\">\n                <div class=\"right\">\n                    <span id=\"img_34_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_34_LV\" class=\"icon_live\"></span>\n                    <span id=\"34_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Đấu kiếm</div>\n            </a>\n    </div>\n    <div id=\"34_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"34_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'34\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"34_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"34_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'34\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"34_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_34_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"34_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'34\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"34_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"21_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'21\',false,false,\'OU\')\" title=\"Thể dục\">\n                <div class=\"right\">\n                    <span id=\"img_21_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_21_LV\" class=\"icon_live\"></span>\n                    <span id=\"21_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Thể dục</div>\n            </a>\n    </div>\n    <div id=\"21_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"21_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'21\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"21_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"21_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'21\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"21_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_21_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"21_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'21\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"21_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"35_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'35\',false,false,\'OU\')\" title=\"Judo\">\n                <div class=\"right\">\n                    <span id=\"img_35_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_35_LV\" class=\"icon_live\"></span>\n                    <span id=\"35_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Judo</div>\n            </a>\n    </div>\n    <div id=\"35_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"35_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'35\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"35_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"35_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'35\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"35_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_35_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"35_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'35\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"35_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"36_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'36\',false,false,\'OU\')\" title=\"M. Pentathlon\">\n                <div class=\"right\">\n                    <span id=\"img_36_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_36_LV\" class=\"icon_live\"></span>\n                    <span id=\"36_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">M. Pentathlon</div>\n            </a>\n    </div>\n    <div id=\"36_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"36_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'36\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"36_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"36_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'36\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"36_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_36_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"36_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'36\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"36_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"37_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'37\',false,false,\'OU\')\" title=\"Rowing\">\n                <div class=\"right\">\n                    <span id=\"img_37_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_37_LV\" class=\"icon_live\"></span>\n                    <span id=\"37_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Rowing</div>\n            </a>\n    </div>\n    <div id=\"37_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"37_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'37\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"37_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"37_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'37\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"37_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_37_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"37_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'37\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"37_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"38_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'38\',false,false,\'OU\')\" title=\"Đua thuyền buồm\">\n                <div class=\"right\">\n                    <span id=\"img_38_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_38_LV\" class=\"icon_live\"></span>\n                    <span id=\"38_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Đua thuyền buồm</div>\n            </a>\n    </div>\n    <div id=\"38_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"38_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'38\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"38_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"38_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'38\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"38_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_38_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"38_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'38\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"38_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"39_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'39\',false,false,\'OU\')\" title=\"Bắn súng\">\n                <div class=\"right\">\n                    <span id=\"img_39_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_39_LV\" class=\"icon_live\"></span>\n                    <span id=\"39_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Bắn súng</div>\n            </a>\n    </div>\n    <div id=\"39_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"39_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'39\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"39_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"39_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'39\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"39_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_39_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"39_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'39\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"39_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"40_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'40\',false,false,\'OU\')\" title=\"Taekwondo\">\n                <div class=\"right\">\n                    <span id=\"img_40_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_40_LV\" class=\"icon_live\"></span>\n                    <span id=\"40_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Taekwondo</div>\n            </a>\n    </div>\n    <div id=\"40_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"40_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'40\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"40_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"40_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'40\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"40_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_40_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"40_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'40\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"40_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"41_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'41\',false,false,\'OU\')\" title=\"Triathlon\">\n                <div class=\"right\">\n                    <span id=\"img_41_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_41_LV\" class=\"icon_live\"></span>\n                    <span id=\"41_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Triathlon</div>\n            </a>\n    </div>\n    <div id=\"41_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"41_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'41\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"41_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"41_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'41\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"41_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_41_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"41_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'41\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"41_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"19_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'19\',false,false,\'OU\')\" title=\"Cử tạ\">\n                <div class=\"right\">\n                    <span id=\"img_19_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_19_LV\" class=\"icon_live\"></span>\n                    <span id=\"19_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Cử tạ</div>\n            </a>\n    </div>\n    <div id=\"19_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"19_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'19\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"19_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"19_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'19\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"19_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_19_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"19_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'19\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"19_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"42_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'42\',false,false,\'OU\')\" title=\"Đấu vật\">\n                <div class=\"right\">\n                    <span id=\"img_42_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_42_LV\" class=\"icon_live\"></span>\n                    <span id=\"42_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Đấu vật</div>\n            </a>\n    </div>\n    <div id=\"42_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"42_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'42\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"42_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"42_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'42\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"42_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_42_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"42_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'42\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"42_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <div id=\"13_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'13\',false,false,\'OU\')\" title=\"Chính trị\">\n                <div class=\"right\">\n                    <span id=\"img_13_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_13_LV\" class=\"icon_live\"></span>\n                    <span id=\"13_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Chính trị</div>\n            </a>\n    </div>\n    <div id=\"13_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"13_A\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'\',\'13\',\'\',\'OU\')\">\n              <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under</span>\n              <span id=\"13_A_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"13_P\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'P\',\'13\')\">\n              <span class=\"submenu\" title=\"Cá cược tổng hợp\">Cá cược tổng hợp</span>\n              <span id=\"13_P_Cnt\" class=\"text-number\"></span>\n              <span id=\"img_13_P_LV\" class=\"icon_live\"></span>\n            </a>\n          </div>\n      </div>\n      <div id=\"13_OT\" style=\"display: none;\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'OT\',\'13\')\">\n              <span class=\"submenu\" title=\"Cược Thắng\">Cược Thắng</span>\n              <span id=\"13_OT_Cnt\" class=\"text-number\"></span>\n            </a>\n          </div>\n      </div>\n    </div>\n    \n    <!--Finance-->\n    <div id=\"201_head\" style=\"display: none;\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'\',\'201\')\" title=\"Tài chính\">\n                <div class=\"right\">\n                    <span id=\"iveimg_201_LV\" class=\"icon_live\"></span>\n                    <span id=\"201_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\">Tài chính</div>\n            </a>\n    </div>\n    <div id=\"201_body\" class=\"MuSubbg\" style=\"display: none;\">\n      <div id=\"201_0\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'0\',\'201\')\">\n              <span class=\"submenu\" title=\"Random\">Random</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"201_5300\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'5300\',\'201\')\">\n              <span class=\"submenu\" title=\"Nikkei\">Nikkei</span>\n            </a>\n          </div>\n      </div>\n      <div id=\"201_5301\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'5301\',\'201\')\">\n              <span class=\"submenu\" title=\"Hong Kong\">Hong Kong</span>\n            </a>\n          </div>\n      </div>  \n      <div id=\"201_5306\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'5306\',\'201\')\">\n              <span class=\"submenu\" title=\"Wall Street\">Wall Street</span>\n            </a>\n          </div>\n      </div>  \n      <div id=\"201_5309\">\n        <div class=\"subnav-link\" style=\"display: block;\">\n            <a href=\"JavaScript:ShowOdds(\'5309\',\'201\')\">\n              <span class=\"submenu\" title=\"UK 100\">UK 100</span>\n            </a>\n          </div>\n      </div>            \n    </div>\n    <!--SportsParlay-->\n    <div id=\"P_head\">\n      <a class=\"navon\" href=\"JavaScript:ShowOdds(\'P\',\'1\')\" title=\"Cá cược tổng hợp\">\n                <div class=\"right\">\n                    <span id=\"img_P_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"img_P_LV\" class=\"icon_live\"></span>\n                    <span id=\"P_Cnt\" class=\"text-number\">81</span>\n                </div>\n                <div class=\"text-ellipsis\">Cá cược tổng hợp</div>\n            </a>\n    </div>\n    <div id=\"0_body\" class=\"MuSubbg\"></div>\n    \n    <!--Oudivight-->\n    <div id=\"OT_head\">\n      <a class=\"navon\" href=\"JavaScript:SwitchSport(\'OT\',\'0\')\" title=\"Cược Thắng\">\n                <div class=\"right\">\n                    <span id=\"OT_Cnt\" class=\"text-number\">129</span>\n                </div>\n                <div class=\"text-ellipsis\">Cược Thắng</div>\n            </a>\n    </div>\n    <div id=\"OT_body\" class=\"MuSubbg\"></div>\n</div>\n\n</div>\n</div>\n\n<!--Live-->\n<div id=\"subnavbg\">\n<div id=\"subnav\">\n\n<div id=\"market_L_body\" style=\"display:none\">\n\n    <!--All Sports-->\n    <div id=\"L_A_head\">\n      <a class=\"navon\" href=\"JavaScript:LiveSportsClickAll(false)\" title=\"Tất Cả\">\n                <div class=\"right\">\n                    <span id=\"img_L_A_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_A_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSpotrs_All\" class=\"hight\" checked=\"checked\" onclick=\"LiveSportsClickAll(false);\" hidefocus=\"\" type=\"checkbox\">Tất Cả</div>\n            </a>\n    </div>\n    \n    <!-- BEGIN L_LiveCasino -->\n    <!--<div id=\'L_162_head\'>   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'162\')\" title=\"Live Casino\">\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_162\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\"  value=\"162\" checked=\"checked\" hidefocus=\"\" />Live Casino</div>\n            </a>\n</div>\n    <div id=\'L_162\'></div>    -->\n    <!-- END L_LiveCasino --> \n    \n    \n        \n    <div id=\"L_1_head\">\n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'1\')\" title=\"Bóng đá\">\n                <div class=\"right\">\n                    <span id=\"img_L_1_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_1_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_1\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" checked=\"checked\" value=\"1\" hidefocus=\"\">Bóng đá</div>\n            </a>\n    </div>\n    <div id=\"L_1\" class=\"MuSubbg\">\n        <div class=\"subnav-link\" style=\"display: block; position:relative;\">\n          <a href=\"JavaScript:changeLiveDisplayMode(fFrame.DisplayMode)\">\n            <span class=\"submenu\" title=\"Handicap &amp; Over/Under\">Handicap &amp; Over/Under\n              <span id=\"L_1_A_Cnt\" class=\"text-number\"></span>\n            </span>\n          </a>\n          &nbsp;<div style=\"position:absolute; top:22px; left: 16px;\"><a href=\"JavaScript:changeLiveDisplayMode(\'1\')\" style=\"display:inline;padding:0px; margin:0px;\"><b>Mới</b></a> | \n          <a href=\"JavaScript:changeLiveDisplayMode(\'1F\')\" style=\"display:inline;padding:0px; margin:0px;\"><b>Cũ</b></a></div>\n        </div>\n    </div>\n    \n    <div id=\"L_161_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'161\')\" title=\"Number Game\">\n                <div class=\"right\">\n                    <span id=\"img_L_161_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\" onclick=\"fFrame.openBingoStreamingMain();\"></span>\n                    <span id=\"L_161_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_161\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"161\" checked=\"checked\" hidefocus=\"\">Number Game</div>\n            </a>\n        </div>\n    <div id=\"L_161\"></div>\n        <!---->\n<!---->\n<!---->\n\n    <div id=\"L_43_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'43\')\" title=\"E-Sports\">\n                <div class=\"right\">\n                    <span id=\"img_L_43_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_43_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_43\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"43\" checked=\"checked\" hidefocus=\"\">E-Sports</div>\n            </a>\n</div>\n    <div id=\"L_43\"></div>\n    \n    <div id=\"L_2_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'2\')\" title=\"Bóng rổ\">\n                <div class=\"right\">\n                    <span id=\"img_L_2_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_2_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_2\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"2\" checked=\"checked\" hidefocus=\"\">Bóng rổ</div>\n            </a>\n</div>\n    <div id=\"L_2\"></div>\n    \n    <div id=\"L_8_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'8\')\" title=\"Bóng chày\">\n                <div class=\"right\">\n                    <span id=\"img_L_8_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_8_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_8\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"8\" checked=\"checked\" hidefocus=\"\">Bóng chày</div>\n            </a>\n</div>\n    <div id=\"L_8\"></div>\n    \n    <div id=\"L_3_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'3\')\" title=\"Bóng bầu dục Mỹ\">\n                <div class=\"right\">\n                    <span id=\"img_L_3_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_3_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_3\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"3\" checked=\"checked\" hidefocus=\"\">Bóng bầu dục Mỹ</div>\n            </a>\n</div>\n    <div id=\"L_3\"></div>\n    \n    <div id=\"L_4_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'4\')\" title=\"Khúc côn cầu trên băng\">\n                <div class=\"right\">\n                    <span id=\"img_L_4_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_4_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_4\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"4\" checked=\"checked\" hidefocus=\"\">Khúc côn cầu trên băng</div>\n            </a>\n</div>\n    <div id=\"L_4\"></div>\n    \n    <div id=\"L_29_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'29\')\" title=\"Winter Sports\">\n                <div class=\"right\">\n                    <span id=\"img_L_29_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_29_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_29\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"29\" checked=\"checked\" hidefocus=\"\">Winter Sports</div>\n            </a>\n</div>\n    <div id=\"L_29\"></div>\n    \n    <div id=\"L_5_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'5\')\" title=\"Quần vợt\">\n                <div class=\"right\">\n                    <span id=\"img_L_5_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_5_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_5\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"5\" checked=\"checked\" hidefocus=\"\">Quần vợt</div>\n            </a>\n</div>\n    <div id=\"L_5\"></div>\n    \n    <div id=\"L_9_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'9\')\" title=\"Cầu lông\">\n                <div class=\"right\">\n                    <span id=\"img_L_9_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_9_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_9\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"9\" checked=\"checked\" hidefocus=\"\">Cầu lông</div>\n            </a>\n</div>\n    <div id=\"L_9\"></div>\n    \n    <div id=\"L_6_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'6\')\" title=\"Bóng chuyền\">\n                <div class=\"right\">\n                    <span id=\"img_L_6_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_6_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_6\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"6\" checked=\"checked\" hidefocus=\"\">Bóng chuyền</div>\n            </a>\n</div>\n    <div id=\"L_6\"></div>\n    \n    <div id=\"L_7_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'7\')\" title=\"Snooker/Pool\">\n                <div class=\"right\">\n                    <span id=\"img_L_7_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_7_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_7\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"7\" checked=\"checked\" hidefocus=\"\">Snooker/Pool</div>\n            </a>\n</div>\n    <div id=\"L_7\"></div>\n    \n    <div id=\"L_11_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'11\')\" title=\"Thể Thao Môtô\">\n                <div class=\"right\">\n                    <span id=\"img_L_11_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_11_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_11\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"11\" checked=\"checked\" hidefocus=\"\">Thể Thao Môtô</div>\n            </a>\n</div>\n    <div id=\"L_11\"></div>\n    \n    <div id=\"L_10_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'10\')\" title=\"Đánh Golf\">\n                <div class=\"right\">\n                    <span id=\"img_L_10_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_10_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_10\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"10\" checked=\"checked\" hidefocus=\"\">Đánh Golf</div>\n            </a>\n</div>\n    <div id=\"L_10\"></div>\n    \n    <div id=\"L_50_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'50\')\" title=\"Cricket\">\n                <div class=\"right\">\n                    <span id=\"img_L_50_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_50_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_50\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"50\" checked=\"checked\" hidefocus=\"\">Cricket</div>\n            </a>\n</div>\n    <div id=\"L_50\"></div>\n    \n    <div id=\"L_99_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'99\')\" title=\"Môn thể thao khác\">\n                <div class=\"right\">\n                    <span id=\"img_L_99_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_99_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_99\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"99\" checked=\"checked\" hidefocus=\"\">Môn thể thao khác</div>\n            </a>\n</div>\n    <div id=\"L_99\"></div>\n    \n    <div id=\"L_44_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'44\')\" title=\"Quyền Thái\">\n                <div class=\"right\">\n                    <span id=\"img_L_44_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_44_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_44\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"44\" checked=\"checked\" hidefocus=\"\">Quyền Thái</div>\n            </a>\n</div>\n    <div id=\"L_44\"></div>\n    \n    <div id=\"L_16_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'16\')\" title=\"Quyền anh\">\n                <div class=\"right\">\n                    <span id=\"img_L_16_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_16_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_16\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"16\" checked=\"checked\" hidefocus=\"\">Quyền anh</div>\n            </a>\n</div>\n    <div id=\"L_16\"></div>\n    \n    <div id=\"L_26_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'26\')\" title=\"Bóng bầu dục\">\n                <div class=\"right\">\n                    <span id=\"img_L_26_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_26_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_26\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"26\" checked=\"checked\" hidefocus=\"\">Bóng bầu dục</div>\n            </a>\n</div>\n    <div id=\"L_26\"></div>\n    \n    <div id=\"L_25_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'25\')\" title=\"Ném phi tiêu\">\n                <div class=\"right\">\n                    <span id=\"img_L_25_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_25_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_25\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"25\" checked=\"checked\" hidefocus=\"\">Ném phi tiêu</div>\n            </a>\n</div>\n    <div id=\"L_25\"></div>\n    \n    <div id=\"L_18_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'18\')\" title=\"Bóng bàn\">\n                <div class=\"right\">\n                    <span id=\"img_L_18_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_18_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_18\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"18\" checked=\"checked\" hidefocus=\"\">Bóng bàn</div>\n            </a>\n</div>\n    <div id=\"L_18\"></div>\n    \n    <div id=\"L_30_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'30\')\" title=\"Squash\">\n                <div class=\"right\">\n                    <span id=\"img_L_30_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_30_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_30\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"30\" checked=\"checked\" hidefocus=\"\">Squash</div>\n            </a>\n</div>\n    <div id=\"L_30\"></div>\n    \n    <div id=\"L_28_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'28\')\" title=\"Khúc côn cầu trên cỏ\">\n                <div class=\"right\">\n                    <span id=\"img_L_28_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_28_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_28\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"28\" checked=\"checked\" hidefocus=\"\">Khúc côn cầu trên cỏ</div>\n            </a>\n</div>\n    <div id=\"L_28\"></div>\n    \n    <div id=\"L_24_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'24\')\" title=\"Bóng ném\">\n                <div class=\"right\">\n                    <span id=\"img_L_24_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_24_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_24\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"24\" checked=\"checked\" hidefocus=\"\">Bóng ném</div>\n            </a>\n</div>\n    <div id=\"L_24\"></div>\n    \n    <div id=\"L_32_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'32\')\" title=\"Netball\">\n                <div class=\"right\">\n                    <span id=\"img_L_32_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_32_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_32\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"32\" checked=\"checked\" hidefocus=\"\">Netball</div>\n            </a>\n</div>\n    <div id=\"L_32\"></div>\n    \n    <div id=\"L_22_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'22\')\" title=\"Điền kinh\">\n                <div class=\"right\">\n                    <span id=\"img_L_22_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_22_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_22\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"22\" checked=\"checked\" hidefocus=\"\">Điền kinh</div>\n            </a>\n</div>\n    <div id=\"L_22\"></div>\n    \n    <div id=\"L_12_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'12\')\" title=\"Bơi lội\">\n                <div class=\"right\">\n                    <span id=\"img_L_12_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_12_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_12\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"12\" checked=\"checked\" hidefocus=\"\">Bơi lội</div>\n            </a>\n</div>\n    <div id=\"L_12\"></div>\n    \n    <div id=\"L_14_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'14\')\" title=\"Bóng nước\">\n                <div class=\"right\">\n                    <span id=\"img_L_14_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_14_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_14\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"14\" checked=\"checked\" hidefocus=\"\">Bóng nước</div>\n            </a>\n</div>\n    <div id=\"L_14\"></div>\n    \n    <div id=\"L_15_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'15\')\" title=\"Lặn\">\n                <div class=\"right\">\n                    <span id=\"img_L_15_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_15_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_15\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"15\" checked=\"checked\" hidefocus=\"\">Lặn</div>\n            </a>\n</div>\n    <div id=\"L_15\"></div>\n    \n    <div id=\"L_17_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'17\')\" title=\"Bắn cung\">\n                <div class=\"right\">\n                    <span id=\"img_L_17_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_17_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_17\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"17\" checked=\"checked\" hidefocus=\"\">Bắn cung</div>\n            </a>\n</div>\n    <div id=\"L_17\"></div>\n    \n    <div id=\"L_20_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'20\')\" title=\"Canoeing\">\n                <div class=\"right\">\n                    <span id=\"img_L_20_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_20_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_20\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"20\" checked=\"checked\" hidefocus=\"\">Canoeing</div>\n            </a>\n</div>\n    <div id=\"L_20\"></div>\n    \n    <div id=\"L_33_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'33\')\" title=\"Đua xe đạp\">\n                <div class=\"right\">\n                    <span id=\"img_L_33_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_33_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_33\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"33\" checked=\"checked\" hidefocus=\"\">Đua xe đạp</div>\n            </a>\n</div>\n    <div id=\"L_33\"></div>\n    \n    <div id=\"L_31_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'31\')\" title=\"Giải Trí\">\n                <div class=\"right\">\n                    <span id=\"img_L_31_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_31_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_31\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"31\" checked=\"checked\" hidefocus=\"\">Giải Trí</div>\n            </a>\n</div>\n    <div id=\"L_31\"></div>\n    \n    <div id=\"L_23_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'23\')\" title=\"Cưỡi ngựa\">\n                <div class=\"right\">\n                    <span id=\"img_L_23_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_23_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_23\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"23\" checked=\"checked\" hidefocus=\"\">Cưỡi ngựa</div>\n            </a>\n</div>\n    <div id=\"L_23\"></div>\n    \n    <div id=\"L_34_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'34\')\" title=\"Đấu kiếm\">\n                <div class=\"right\">\n                    <span id=\"img_L_34_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_34_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_34\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"34\" checked=\"checked\" hidefocus=\"\">Đấu kiếm</div>\n            </a>\n</div>\n    <div id=\"L_34\"></div>\n    \n    <div id=\"L_21_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'21\')\" title=\"Thể dục\">\n                <div class=\"right\">\n                    <span id=\"img_L_21_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_21_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_21\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"21\" checked=\"checked\" hidefocus=\"\">Thể dục</div>\n            </a>\n</div>\n    <div id=\"L_21\"></div>\n    \n    <div id=\"L_35_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'35\')\" title=\"Judo\">\n                <div class=\"right\">\n                    <span id=\"img_L_35_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_35_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_35\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"35\" checked=\"checked\" hidefocus=\"\">Judo</div>\n            </a>\n</div>\n    <div id=\"L_35\"></div>\n    \n    <div id=\"L_36_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'36\')\" title=\"M. Pentathlon\">\n                <div class=\"right\">\n                    <span id=\"img_L_36_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_36_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_36\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"36\" checked=\"checked\" hidefocus=\"\">M. Pentathlon</div>\n            </a>\n</div>\n    <div id=\"L_36\"></div>\n    \n    <div id=\"L_37_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'37\')\" title=\"Rowing\">\n                <div class=\"right\">\n                    <span id=\"img_L_37_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_37_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_37\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"37\" checked=\"checked\" hidefocus=\"\">Rowing</div>\n            </a>\n</div>\n    <div id=\"L_37\"></div>\n    \n    <div id=\"L_38_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'38\')\" title=\"Đua thuyền buồm\">\n                <div class=\"right\">\n                    <span id=\"img_L_38_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_38_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_38\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"38\" checked=\"checked\" hidefocus=\"\">Đua thuyền buồm</div>\n            </a>\n</div>\n    <div id=\"L_38\"></div>\n    \n    <div id=\"L_39_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'39\')\" title=\"Bắn súng\">\n                <div class=\"right\">\n                    <span id=\"img_L_39_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_39_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_39\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"39\" checked=\"checked\" hidefocus=\"\">Bắn súng</div>\n            </a>\n</div>\n    <div id=\"L_39\"></div>\n    \n    <div id=\"L_40_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'40\')\" title=\"Taekwondo\">\n                <div class=\"right\">\n                    <span id=\"img_L_40_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_40_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_40\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"40\" checked=\"checked\" hidefocus=\"\">Taekwondo</div>\n            </a>\n</div>\n    <div id=\"L_40\"></div>\n    \n    <div id=\"L_41_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'41\')\" title=\"Triathlon\">\n                <div class=\"right\">\n                    <span id=\"img_L_41_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_41_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_41\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"41\" checked=\"checked\" hidefocus=\"\">Triathlon</div>\n            </a>\n</div>\n    <div id=\"L_41\"></div>\n    \n    <div id=\"L_19_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'19\')\" title=\"Cử tạ\">\n                <div class=\"right\">\n                    <span id=\"img_L_19_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_19_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_19\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"19\" checked=\"checked\" hidefocus=\"\">Cử tạ</div>\n            </a>\n</div>\n    <div id=\"L_19\"></div>\n    \n    <div id=\"L_42_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'42\')\" title=\"Đấu vật\">\n                <div class=\"right\">\n                    <span id=\"img_L_42_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_42_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_42\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"42\" checked=\"checked\" hidefocus=\"\">Đấu vật</div>\n            </a>\n</div>\n    <div id=\"L_42\"></div>\n    \n    <div id=\"L_13_head\">    \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'13\')\" title=\"Chính trị\">\n                <div class=\"right\">\n                    <span id=\"img_L_13_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_13_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_13\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"13\" checked=\"checked\" hidefocus=\"\">Chính trị</div>\n            </a>\n</div>\n    <div id=\"L_13\"></div>\n    \n    <!--Finance-->\n    <div id=\"L_201_head\">   \n            <a class=\"navon\" onclick=\"JavaScript:LiveSportLinkClick(\'201\')\" title=\"Tài chính\">\n                <div class=\"right\">\n                    <span id=\"img_L_201_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n                    <span id=\"L_201_Cnt\" class=\"text-number\"></span>\n                </div>\n                <div class=\"text-ellipsis\"><input id=\"chkLvSport_201\" name=\"chkLvSport\" type=\"checkbox\" class=\"hight\" value=\"201\" checked=\"checked\" hidefocus=\"\">Tài chính</div>\n            </a>\n</div>\n    <div id=\"L_201\"></div>\n    <!--SportsParlay-->\n    <div id=\"L_P_head\">   \n          <a class=\"navon\" href=\"JavaScript:SwitchSport(\'LP\',\'0\')\" title=\"Cá cược tổng hợp\">\n                      <div class=\"right\">           \n              <span id=\"img_L_P_TV\" class=\"iconOdds tv\" title=\"Truyền Hình Trực Tiếp\"></span>\n              <span id=\"L_P_Cnt\" class=\"text-number\"></span>\n                        </div>\n            <div class=\"text-ellipsis\">Cá cược tổng hợp</div>\n          </a>\n</div>\n    <div id=\"L_P\"></div>\n</div>\n\n</div>\n</div>\n\n<!--div id=\"subnav-foot\"><img src=\"https://ssl-1-1.bongcdn.com/../template/sportsbook/public/images/layout/L_menu_bg1.gif\" /></div-->\n<div class=\"leftBoxFoot\"></div>\n<span id=\"tmplEnd\"></span>\n</span>");
$templateCache.put("elements/left-bet/left-bet.html","<div ng-if=\"settings.show\">\n<!-- BET -->\n   <div id=\"BettingModeContainer\" class=\"TabBox\" style=\"\">\n    <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n      <tbody>\n        <tr>\n          <td id=\"div_menu_single\" class=\"R_menu_R pointer\" ng-class=\"{\'current\': settings.type == \'single\'}\" title=\"Single\">\n            <span class=\"icon-arrow\"></span>\n            <a ng-click=\"switchBettingMode(\'single\')\"><span class=\"subTitle\">Single</span></a>\n          </td>\n          <td id=\"div_menu_parlay\" class=\"R_menu_gr pointer\" ng-class=\"{\'current\': settings.type == \'multiple\'}\" title=\"Cá cược tổng hợp\">\n            <span class=\"icon-arrow\"></span>\n            <a ng-click=\"switchBettingMode(\'multiple\')\">\n              <span class=\"subTitle\">Cá cược tổng hợp</span>\n              <span class=\"numSelections\" id=\"mparlay_cnt\" ng-if=\"mulBets.length\">{{mulBets.length}}</span>\n            </a>\n            </td>\n        </tr>\n      </tbody>\n    </table>\n  </div>\n  <!-- MENU -->\n   <div id=\"BetProcessContainer\" ng-show=\"settings.type == \'single\'\">\n    <form name=\"fomConfirmBet\" ng-submit=\"betSubmit()\" autocomplete=\"off\" novalidate>\n      <div id=\"BP_SPORT\" style=\"\">\n        <div class=\"leftBoxbody\">\n          <div class=\"boxbg\">\n            <div id=\"BetInfo\" class=\"BetInfo\" ng-class=\"{\'liveligh\': model.event.time_status == 1}\"> \n              <div class=\"TextStyle01 pad\"><span id=\"menuTitle\">{{game_name}} / </span> <span id=\"menuTitleOT\" style=\"display: none;\">Cược Thắng</span> <span id=\"tdBetKind\" height=\"=27\">{{model.odd_type}}</span></div>\n              <div id=\"trOddsInfo\">\n                <div id=\"tdLiveBgColor\" class=\"bet\">\n                  <div class=\"pad\"><span id=\"spChoiceClass\" class=\"FavTeamClass\">{{model.odd_name}}</span><br>\n                    <span id=\"sbBetKindValue\" class=\"HdpGoalClass\">{{model | handicapValue}}</span>\n                    <span id=\"spScoreValue\" class=\"TextStyle01\">[{{model.ss}}]</span>\n                    <span class=\"TextStyle03\">@</span>\n                    <span id=\"bodds\" class=\"UdrDogOddsClassBetProcess\" ng-class=\"{\'FavOddsClass\': model.bet_value < 0}\">{{model.bet_value | number:2}}</span>\n                  </div>\n                  <div id=\"sbBetAOSValue\" class=\"\"></div>\n                </div>\n                <div id=\"divKeepBetProcess\" class=\"checkbox\">\n                  <input id=\"chKeepBet\" ng-model=\"settings.auto\" name=\"chKeepBet\" type=\"checkbox\" ng-change=\"onChange()\">                    \n                  <span id=\"KeepOdds\"><span>Tự làm mới({{number}})</span></span></div>\n                </div>\n                  <div class=\"TextStyle04 pad\">\n                    <div id=\"spHome_id\"><span id=\"spHome\">{{model.event.home}}</span></div>\n                    <div id=\"spAway_id\"><span id=\"spAway\">{{model.event.away}}</span></div><div id=\"spMatchCode_id\"><span id=\"spMatchCode\"></span></div>\n                  <div id=\"ot_dvChoiceValue_id\" style=\"display: none;\"><span id=\"ot_dvChoiceValue\" style=\"display: none;\"></span></div>\n                  <div id=\"spLeaguename_id\" class=\"TextStyle07\">\n                    <span id=\"spLeaguename\">{{model.event.league_name}}</span>\n                  </div>\n                </div>\n                <div id=\"div_addparlay\" class=\"checkbox txtleft addToParlay\" style=\"display: none;\"><input type=\"checkbox\" id=\"chk_addToParlay\" onclick=\"SingleToParlay();\"><div id=\"icon_addParlay\">Add to Parlay</div></div>\n                <div class=\"Currency pad mag\">UT\n                  <span class=\"deleteicon\">\n                    <input id=\"BPstake\" ng-model=\"model.bet_amount\" name=\"BPstake\" type=\"number\" size=\"10\" class=\"deletable\" style=\"ime-mode:disabled\" autocomplete=\"off\" onpaste=\"return false;\"><span></span>\n                  </span>\n                </div>\n                <div id=\"divAcceptBetterOdds\" class=\"checkbox txtleft\">\n                  <input id=\"cbAcceptBetterOdds\" name=\"cbAcceptBetterOdds\" type=\"checkbox\" checked=\"\" value=\"1\">\n                  <div>Tự nhận tỷ lệ cược tốt</div>\n                </div>\n                <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"tabstyle02\">\n                  <tbody><tr id=\"trlPayOut\">\n                    <th nowrap=\"nowrap\" width=\"10px\">Thanh Toán</th>\n                    <td>&nbsp;<span id=\"payOut\">{{model | payAmount | number:2}}</span><span id=\"scoremap\" class=\"iconOdds scoreMap right\" title=\"Score Map\" style=\"display: block;\"></span></td>\n                  </tr>\n                  <tr>\n                    <th nowrap=\"nowrap\">Tối Thiểu</th>\n                    <td id=\"tdBetprocessMinBet\" class=\"BetprocessMinBet\">UT&nbsp;<span id=\"spMinBetValue\">{{data.bongdamin | number}}</span></td>\n                  </tr>\n                  <tr>\n                    <th nowrap=\"nowrap\">Tối đa</th>\n                    <td id=\"tdBetprocessMaxBet\" nowrap=\"nowrap\" class=\"BetprocessMaxBet\">UT&nbsp;<span id=\"spMaxBetValue\">{{data.bongdamax | number}}</span></td>\n                  </tr>\n                </tbody></table>\n              </div>\n              <div class=\"BetProcessBtnBox\">\n                <a id=\"btnBPSubmit\" style=\"cursor:pointer;\" type=\"submit\" class=\"button mark\" title=\"Đặt Cược\"><span ng-click=\"betSubmit()\">Đặt Cược</span>\n                </a> \n                <a style=\"cursor:pointer;\" type=\"button\" ng-click=\"backToHome()\" class=\"button\" title=\"Hủy\"><span>Hủy</span></a> </div>\n                <!-- <div id=\"BPOddsChangeAlert\" class=\"tips box\" style=\"display: block;\" ng-show=\"oldValue && oldValue != model.bet_value\">\n                  <div class=\"content info\">Tỷ lệ cược đã thay đổi từ <em>{{oldValue}}</em> Thành <em>{{model.bet_value}}</em>\n                  </div>\n                </div> -->\n                <div id=\"BPOddsChangeAlert\" class=\"tips box\" style=\"display: block;\" ng-show=\"error_message\">\n                  <div class=\"content info\" ng-bind-html=\"error_message | safeHtml\">\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"leftBoxFoot\"></div>\n            <div class=\"Backmenu pointer\" ng-click=\"backToHome()\">\n              <span class=\"icon-arrow\"></span>\n              <a >Trở về Menu</a>\n            </div>\n          </div>\n              </form>\n            </div>\n   <!-- BET END -->\n\n   <div id=\"div_MixParlay\" ng-show=\"settings.type == \'multiple\'\">\n    <form name=\"betform\" id=\"betform\" ng-submit=\"betMulSubmit()\" autocomplete=\"off\" novalidate>\n      <div id=\"ParlayTitle\" class=\"leftBoxTitle current\" style=\"display: none;\">\n        <span class=\"icon-arrow\"></span><span class=\"titleTxt\">Cá cược tổng hợp</span>\n      </div>\n      <div class=\"leftBoxbody\">\n        <div class=\"boxbg multiple\">\n        <div id=\"noparlayinfo\" style=\"background-color: rgb(255, 255, 255);\" ng-hide=\"mulBets.length > 0\" class=\"BetInfo\">\n            <div class=\"TextStyle04\">Không đủ trận đấu (ít nhất 3 trận đấu) để đặt cược cho Cá Cược Tổng Hợp</div>  \n        </div>\n        <div id=\"ParlayDetail\" style=\"\">\n          <mul-bet-item on-delete=\"onDelete\" item=\"value\" ng-repeat=\"(key, value) in mulBets\"></mul-bet-item>\n        </div>\n        <div id=\"divKeepOdds\" ng-show=\"mulBets.length > 2\">\n            <div class=\"checkbox txtleft\">\n              <input id=\"cbKeepOdds\" name=\"cbKeepOdds\" type=\"checkbox\" checked value=\"1\">\n              <div> Tự nhận tỉ lệ cược tốt nhất</div>\n            </div>\n            <div class=\"line mag\"></div>\n            </div>\n            <div id=\"Div_Over100\" class=\"tips box\" style=\"display:none\">\n                <div class=\"content info\"> Selection limit for parlay had been exceeded. Maximum <em>100</em> selections are allowed. Please reduce the selections.</div>\n            </div>\n            <div id=\"Div_Over8\" class=\"tips box\" style=\"display:none\">\n                <div class=\"content info\"> Selection limit for parlay had been exceeded. Maximum <em>8</em> selections are allowed. Please reduce the selections.</div>\n            </div>\n          <div id=\"parlay_bet_info\">\n\n      <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"tabstyle02\" ng-show=\"mulBets.length > 2\">\n\n        <tbody><tr id=\"tr_mixparlay_odds\">\n\n          <th>Tỷ lệ</th>\n          <td><span id=\"TotalOdds\">{{(mulModel.bet_total = (mulBets | betsValue)) | number:2}}</span>\n            <span id=\"CombiOdds\" style=\"display:none;\"> </span>\n          </td>\n        </tr>\n        <tr>\n          <th>Tiền cược</th>\n\n          <td nowrap=\"nowrap\" class=\"Currency\">UT\n            <input id=\"mulStake\" ng-model=\"mulModel.bet_amount\" autofocus name=\"tien\" autocomplete=\"off\" type=\"text\" style=\"ime-mode:disabled; width:55px;\">\n          </td>\n          </tr>\n          <tr>\n            <th id=\"TotalStakeName\">Tổng Tiền Cược</th>\n            <td class=\"Odds_Upd\">&nbsp;<span id=\"TotalStakeValue\">{{mulModel.bet_amount | numberPretty:2}}</span>\n            </td>\n          </tr>\n          <tr>\n            <th>Tiền Trả Trước</th>\n            <td>&nbsp;<span id=\"txtPayOut\">{{mulModel.bet_total * mulModel.bet_amount | numberPretty:2}}</span></td>\n          </tr>\n\n          <tr>\n            <th>Tối thiểu</th>\n            <td id=\"tdMixParlayMinBet\" class=\"MinBet\"><span id=\"mincurrency\">UT</span> &nbsp;<span id=\"MPMinBet\">{{data.bongdamin | number}}</span></td>\n          </tr>\n          <tr>\n            <th>Tối đa</th>\n            <td id=\"tdMixParlayMaxBet\" class=\"MaxBet\"><span id=\"maxcurrency\">UT</span> &nbsp;<span id=\"MPMaxBet\">{{data.bongdamax | number}}</span></td>\n          </tr>\n        </tbody></table>\n      </div>\n          <div id=\"parlay_bet_info2\" style=\"\" ng-show=\"mulBets.length > 2\">\n            <div class=\"BetProcessBtnBox\">\n              <a id=\"btnMPSubmit\" style=\"cursor:pointer\" type=\"submit\" class=\"button mark\" title=\"Đặt Cược\"><span ng-click=\"betMulSubmit()\">Đặt Cược</span>\n              </a> \n              <a style=\"cursor:pointer\" type=\"button\" ng-click=\"backToHome()\" class=\"button\" title=\"Hủy\"><span>Hủy</span></a></div>\n              <div id=\"ParlayOddsChangeAlert\" class=\"tips box\" ng-if=\"mul_error_message\">\n                <div class=\"content info\" ng-bind-html=\"mul_error_message | safeHtml\">\n                  </div>\n              </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"account_foot\"></div>\n      <div class=\"Backmenu\"><span class=\"icon-arrow\"></span><a ng-click=\"backToHome()\">Trở về Menu</a></div>\n    </form>\n  </div>\n </div>");
$templateCache.put("elements/left-bet/mul-bet-item.html","<div  class=\"BetInfo\" ng-class=\"{\'red\': item.event.time_status == 1}\"> \n	<!-- F8F8F8 style=\"background-color:#FFCCBC\" -->\n	<span class=\"TextStyle01\">\n		<div ng-click=\"onDeleteItem();\" class=\"btnIcon right\"><span class=\"icon-close\"></span></div>\n		Bóng đá / {{item.odd_type}}\n	</span>\n	<br>  <span class=\"FavTeamClass\">{{item.odd_name}}</span><br>\n	<span class=\"TextStyle03\">{{item | handicapValue}}</span>\n	<span class=\"TextStyle01\">[{{item.event.ss}}]</span><span class=\"TextStyle03\"> @ </span>\n	<span class=\"UdrDogOddsClassBetProcess\">{{item.bet_value | numberOdd:2}}</span>\n	<div class=\"TextStyle04 \"></div>\n	<div class=\"TextStyle04\">{{item.event.home}} -vs- {{item.event.away}}</div>\n	<div class=\"TextStyle04\"></div>\n</div>");
$templateCache.put("elements/odd-item/odd-item.html","<div class=\"multiOdds\" ng-if=\"type!==\'correct_score\'\">\r\n   <div class=\"event\">\r\n      <div class=\"team\" ng-show=\"key == 0\">\r\n         <div class=\"name name-pointer\" ng-class=\"{\'accent\': odd.ft_hdp.handicap && odd.ft_hdp.handicap_team == \'home\'}\"\r\n            title=\"CA Independiente\">\r\n            <div class=\"text\">\r\n               <span>{{event.home}}</span>\r\n               <div class=\"card \" ng-if=\"event.reds.home > 0\">{{event.reds.home}}</div>\r\n            </div>\r\n         </div>\r\n         <div class=\"iconSet\">\r\n            <button ng-click=\"openLive()\" ng-show=\"event.live_id !== \'\' && !event.saba\" class=\"icon-streaming accent smallBtn\"\r\n               title=\"Live Streaming\"></button>\r\n            <button ng-click=\"openMatch()\" title=\"Live Match\" class=\"accent icon-soccer smallBtn\">\r\n            </button>\r\n            <!-- <button class=\"icon-favorite smallBtn\" title=\"Favorite\"></button> -->\r\n         </div>\r\n      </div>\r\n      <div class=\"team\" ng-show=\"key == 0\">\r\n         <div class=\"name name-pointer\" ng-class=\"{\'accent\': odd.ft_hdp.handicap && odd.ft_hdp.handicap_team == \'away\'}\"\r\n            title=\"Defensa y Justicia\">\r\n            <div class=\"text\">\r\n               <span>{{event.away}}</span>\r\n               <div class=\"card \" ng-if=\"event.reds.away > 0\">{{event.reds.away}}</div>\r\n            </div>\r\n         </div>\r\n         <div class=\"iconSet\">\r\n            <button ng-click=\"openStatic()\" class=\"primary icon-statistic smallBtn\"\r\n               title=\"Statistic Information\"></button>\r\n         </div>\r\n      </div>\r\n      <div class=\"team\" ng-show=\"key == 0\">\r\n         <div class=\"extra\">Hòa</div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds subtxt\">\r\n      <div class=\"betArea\" visible=\"odd.ft_hdp.home_od\">\r\n         <span class=\"txt\">\r\n            <span ng-if=\"odd.ft_hdp.handicap_team == \'home\'\">{{odd.ft_hdp.handicap}}</span>\r\n         </span>\r\n         <div class=\"oddsBet\" style=\"cursor:pointer;\" define-text-v2=\'odd.ft_hdp.home_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'Handicap\', \'ft_hdp\', \'home\', odd.ft_hdp.home_od)\">{{odd.ft_hdp.home_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\" visible=\"odd.ft_hdp.home_od\">\r\n         <span class=\"txt\">\r\n            <span ng-if=\"odd.ft_hdp.handicap_team == \'away\'\">{{odd.ft_hdp.handicap}}</span>\r\n         </span>\r\n         <div class=\"oddsBet\" style=\"cursor:pointer;\" define-text-v2=\'odd.ft_hdp.away_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'Handicap\', \'ft_hdp\', \'away\', odd.ft_hdp.away_od)\">{{odd.ft_hdp.away_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds subtxt\" visible=\"odd.ft_ou.over_od\">\r\n      <div class=\"betArea\">\r\n         <span class=\"txt\">\r\n            <span>{{odd.ft_ou.handicap}}</span>\r\n         </span>\r\n         <div class=\"oddsBet\" style=\"cursor:pointer;\" define-text-v2=\'odd.ft_ou.over_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'Tài/Xỉu\', \'ft_ou\', \'home\', odd.ft_ou.over_od)\">{{odd.ft_ou.over_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <span class=\"txt\">\r\n            <span ng-if=\"odd.ft_ou.under_od\">u</span>\r\n         </span>\r\n         <div class=\"oddsBet underdog\" style=\"cursor:pointer;\" define-text-v2=\'odd.ft_ou.under_od\'\r\n            odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'Tài/Xỉu\', \'ft_ou\', \'away\', odd.ft_ou.under_od)\">{{odd.ft_ou.under_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds\" visible=\"key == 0 && odd.ft_1x2.home_od\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-click=\"onBet(\'FT. 1X2\', \'ft_1x2\', \'home\', odd.ft_1x2.home_od)\" style=\"cursor:pointer;\"\r\n            define-text-v2=\'odd.ft_1x2.home_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span>{{odd.ft_1x2.home_od | numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-click=\"onBet(\'FT. 1X2\', \'ft_1x2\', \'away\', odd.ft_1x2.away_od)\" style=\"cursor:pointer;\"\r\n            define-text-v2=\'odd.ft_1x2.away_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span>{{odd.ft_1x2.away_od | numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\" ng-show=\"key == 0\">\r\n         <div class=\"oddsBet\" ng-click=\"onBet(\'FT. 1X2\', \'ft_1x2\', \'draw\', odd.ft_1x2.draw_od)\" style=\"cursor:pointer;\"\r\n            define-text-v2=\'odd.ft_1x2.draw_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span>{{odd.ft_1x2.draw_od | numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds subtxt\">\r\n      <div class=\"betArea\" visible=\"odd.hf_hdp.home_od\">\r\n         <span class=\"txt\">\r\n            <span ng-if=\"odd.hf_hdp.handicap_team == \'home\'\">{{odd.hf_hdp.handicap}}</span>\r\n         </span>\r\n         <div class=\"oddsBet\" style=\"cursor:pointer;\" define-text-v2=\'odd.hf_hdp.home_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'1H - Handicap\', \'hf_hdp\', \'home\', odd.hf_hdp.home_od)\">{{odd.hf_hdp.home_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\" visible=\"odd.hf_hdp.home_od\">\r\n         <span class=\"txt\">\r\n            <span ng-if=\"odd.hf_hdp.handicap_team == \'away\'\">{{odd.hf_hdp.handicap}}</span>\r\n         </span>\r\n         <div class=\"oddsBet underdog\" style=\"cursor:pointer;\" define-text-v2=\'odd.hf_hdp.away_od\'\r\n            odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'1H - Handicap\', \'hf_hdp\', \'away\', odd.hf_hdp.away_od)\">{{odd.hf_hdp.away_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds subtxt\" visible=\"odd.hf_ou.over_od\">\r\n      <div class=\"betArea\">\r\n         <span class=\"txt\">\r\n            <span>{{odd.hf_ou.handicap}}</span>\r\n         </span>\r\n         <div class=\"oddsBet\" style=\"cursor:pointer;\" define-text-v2=\'odd.hf_ou.over_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'1H - Tài/Xỉu\', \'hf_ou\', \'home\', odd.hf_ou.over_od)\">{{odd.hf_ou.over_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <span class=\"txt\">\r\n            <span ng-if=\"odd.hf_ou.over_od\">u</span>\r\n         </span>\r\n         <div class=\"oddsBet\" style=\"cursor:pointer;\" define-text-v2=\'odd.hf_ou.under_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span ng-click=\"onBet(\'1H - Tài/Xỉu\', \'hf_ou\', \'away\', odd.hf_ou.under_od)\">{{odd.hf_ou.under_od |\r\n               numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds\" visible=\"key == 0 && odd.hf_1x2.home_od\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-click=\"onBet(\'1H. 1X2\', \'hf_1x2\', \'home\', odd.hf_1x2.home_od)\" style=\"cursor:pointer;\"\r\n            define-text-v2=\'odd.hf_1x2.home_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span>{{odd.hf_1x2.home_od | numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-click=\"onBet(\'1H. 1X2\', \'hf_1x2\', \'away\', odd.hf_1x2.away_od)\" style=\"cursor:pointer;\"\r\n            define-text-v2=\'odd.hf_1x2.away_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span>{{odd.hf_1x2.away_od | numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n      <div class=\"betArea\" ng-show=\"key == 0\">\r\n         <div class=\"oddsBet\" ng-click=\"onBet(\'1H. 1X2\', \'hf_1x2\', \'draw\', odd.hf_1x2.draw_od)\" style=\"cursor:pointer;\"\r\n            define-text-v2=\'odd.hf_1x2.draw_od\' odd-kind=\'{{odd_kind}}\'>\r\n            <span>{{odd.hf_1x2.draw_od | numberOdd:2}}</span>\r\n         </div>\r\n      </div>\r\n   </div>\r\n</div>\r\n\r\n<div class=\"multiOdds\" ng-if=\"type==\'correct_score\'\">\r\n   <div class=\"event\">\r\n      <div class=\"team\">\r\n         <div class=\"name name-pointer\" ng-class=\"{\'accent\': odd.ft_hdp.handicap && odd.ft_hdp.handicap_team == \'home\'}\"\r\n            title=\"CA Independiente\">\r\n            <div class=\"text\">\r\n               <span>{{event.home}}</span>\r\n               <div class=\"card \" ng-if=\"event.reds.home> 0\">{{event.reds.home}}</div>\r\n            </div>\r\n         </div>\r\n         <div class=\"iconSet\">\r\n            <button ng-click=\"openLive()\" ng-show=\"event.live_id !== \'\' && !event.saba\" class=\"icon-streaming accent smallBtn\"\r\n               title=\"Live Streaming\"></button>\r\n            <button ng-click=\"openMatch()\" title=\"Live Match\" class=\"accent icon-soccer smallBtn\">\r\n            </button>\r\n            <!-- <button class=\"icon-favorite smallBtn\" title=\"Favorite\"></button> -->\r\n         </div>\r\n      </div>\r\n      <div class=\"team\">\r\n         <div class=\"name name-pointer\" ng-class=\"{\'accent\': odd.ft_hdp.handicap && odd.ft_hdp.handicap_team == \'away\'}\"\r\n            title=\"Defensa y Justicia\">\r\n            <div class=\"text\">\r\n               <span>{{event.away}}</span>\r\n               <div class=\"card \" ng-if=\"event.reds.away> 0\">{{event.reds.away}}</div>\r\n            </div>\r\n         </div>\r\n         <div class=\"iconSet\">\r\n            <button ng-click=\"openStatic()\" class=\"primary icon-statistic smallBtn\"\r\n               title=\"Statistic Information\"></button>\r\n         </div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'1_0\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'1_0\', event.correct_score[\'1_0\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'1_0\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'1_0\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'1_0\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'0_1\']\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'0_1\', event.correct_score[\'0_1\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'0_1\']\"><span\r\n               class=\"\">{{event.correct_score[\'0_1\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'0_1\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'2_0\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'2_0\', event.correct_score[\'2_0\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'2_0\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'2_0\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'2_0\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'0_2\']\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'0_2\', event.correct_score[\'0_2\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'0_2\']\"><span\r\n               class=\"\">{{event.correct_score[\'0_2\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'0_2\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'2_1\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'2_1\', event.correct_score[\'2_1\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'2_1\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'2_1\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'2_1\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'1_2\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'1_2\', event.correct_score[\'1_2\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'1_2\']\"><span\r\n               class=\"\">{{event.correct_score[\'1_2\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'1_2\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'3_0\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'3_0\', event.correct_score[\'3_0\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'3_0\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'3_0\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'3_0\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'0_3\']\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'0_3\', event.correct_score[\'0_3\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'0_3\']\"><span\r\n               class=\"\">{{event.correct_score[\'0_3\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'0_3\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'3_1\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'3_1\', event.correct_score[\'3_1\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'3_1\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'3_1\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'3_1\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'1_3\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'1_3\', event.correct_score[\'1_3\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'1_3\']\"><span\r\n               class=\"\">{{event.correct_score[\'1_3\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'1_3\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'3_2\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'3_2\', event.correct_score[\'3_2\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'3_2\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'3_2\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'3_2\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'2_3\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'2_3\', event.correct_score[\'2_3\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'2_3\']\"><span\r\n               class=\"\">{{event.correct_score[\'2_3\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'2_3\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-if=\"event.correct_score[\'4_0\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'4_0\', event.correct_score[\'4_0\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'4_0\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'4_0\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'4_0\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'0_4\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'0_4\', event.correct_score[\'0_4\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'0_4\']\"><span\r\n               class=\"\">{{event.correct_score[\'0_4\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'0_4\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'4_1\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'4_1\', event.correct_score[\'4_1\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'4_1\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'4_1\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'4_1\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'1_4\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'1_4\', event.correct_score[\'1_4\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\'><span class=\"\">{{event.correct_score[\'1_4\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'1_4\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'4_2\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'4_2\', event.correct_score[\'4_2\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'4_2\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'4_2\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'4_2\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'2_4\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'2_4\', event.correct_score[\'2_4\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'2_4\']\"><span\r\n               class=\"\">{{event.correct_score[\'2_4\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'2_4\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'4_3\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'4_3\', event.correct_score[\'4_3\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'4_3\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'4_3\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'4_3\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'3_4\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'3_4\', event.correct_score[\'3_4\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'3_4\']\"><span\r\n               class=\"\">{{event.correct_score[\'3_4\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'3_4\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'0_0\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'0_0\', event.correct_score[\'0_0\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'0_0\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'0_0\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'0_0\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'1_1\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'1_1\', event.correct_score[\'1_1\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'1_1\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'1_1\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'1_1\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'2_2\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'2_2\', event.correct_score[\'2_2\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'2_2\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'2_2\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'2_2\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'3_3\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'3_3\', event.correct_score[\'3_3\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'3_3\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'3_3\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'3_3\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'4_4\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'4_4\', event.correct_score[\'4_4\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'4_4\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'4_4\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'4_4\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n   <div class=\"odds\">\r\n      <div class=\"betArea\">\r\n         <div class=\"oddsBet\" ng-show=\"event.correct_score[\'AOS\']!== \'\'\"\r\n            ng-click=\"onBet(\'Điểm số chính xác\', \'correct_score\', \'AOS\', event.correct_score[\'AOS\'])\"\r\n            style=\"cursor:pointer;\" odd-kind=\'{{odd_kind}}\' define-text-v2=\"event.correct_score[\'AOS\']\"><span\r\n               class=\"\">{{\r\n               event.correct_score[\'AOS\']}}</span></div>\r\n         <div class=\"oddsBet disable\" ng-show=\"event.correct_score[\'AOS\'] === \'\'\"><span class=\"\">--</span></div>\r\n      </div>\r\n   </div>\r\n\r\n</div>\r\n\r\n<div class=\"multiOdds\" ng-if=\"type===\'saba_game\'\">\r\n   <div class=\"event\">\r\n      <div class=\"team\">\r\n         <div class=\"name name-pointer\" ng-class=\"{\'accent\': odd.ft_hdp.handicap && odd.ft_hdp.handicap_team == \'home\'}\"\r\n            title=\"CA Independiente\">\r\n            <div class=\"text\">\r\n               <span>{{event.home}}</span>\r\n               <div class=\"card \" ng-if=\"event.reds.home> 0\">{{event.reds.home}}</div>\r\n            </div>\r\n         </div>\r\n         <div class=\"iconSet\">\r\n            <button ng-click=\"openLive()\" ng-show=\"event.live_id !== \'\' && !event.saba\" class=\"icon-streaming accent smallBtn\"\r\n               title=\"Live Streaming\"></button>\r\n            <button ng-click=\"openMatch()\" title=\"Live Match\" class=\"accent icon-soccer smallBtn\">\r\n            </button>\r\n            <!-- <button class=\"icon-favorite smallBtn\" title=\"Favorite\"></button> -->\r\n         </div>\r\n      </div>\r\n      <div class=\"team\">\r\n         <div class=\"name name-pointer\" ng-class=\"{\'accent\': odd.ft_hdp.handicap && odd.ft_hdp.handicap_team == \'away\'}\"\r\n            title=\"Defensa y Justicia\">\r\n            <div class=\"text\">\r\n               <span>{{event.away}}</span>\r\n               <div class=\"card \" ng-if=\"event.reds.away> 0\">{{event.reds.away}}</div>\r\n            </div>\r\n         </div>\r\n         <div class=\"iconSet\">\r\n            <button ng-click=\"openStatic()\" class=\"primary icon-statistic smallBtn\"\r\n               title=\"Statistic Information\"></button>\r\n         </div>\r\n      </div>\r\n   </div>\r\n</div>");
$templateCache.put("elements/refresh-item/refresh-item.html","<span ng-click=\"reload()\" ng-if=\"type!==\'inplay\'\">\n   <div class=\"icon-refresh\" title=\"Trực tiếp\"></div>\n   {{number}}\n</span>\n<div class=\"icon-refresh\" ng-if=\"type==\'inplay\'\" ng-click=\"reload()\" title=\"Refresh\"></div>");
$templateCache.put("elements/popup-league/popup-league.html","<div id=\"PopDiv\" ng-show=\"show\" style=\"display: inline; width: 640px; position: absolute; visibility: visible; z-index: 1000; top: 50px; left: 50px;\">\n	<div class=\"popupW\">\n        <div id=\"PopTitle\" class=\"popupWTitle\" style=\"cursor: move;\">\n        	<span>\n                <div class=\"popWIcon\"></div>\n                <div id=\"PopTitleText\" class=\"popWTitleContain\">Chọn Giải Đấu</div>\n                <div id=\"PopCloser\" ng-click=\"closeLeague()\" class=\"popWClose\" title=\"ĐÓNG\"></div>\n            </span>\n        </div>\n        <div id=\"oPopContainer\" class=\"popWContain\"><div class=\"popWTableArea\">\n            <div class=\"popWCheckAll\">\n                <input id=\"chkLFAll\" name=\"LF\" type=\"checkbox\" value=\"0\" onclick=\"checkAll();\">Kiểm Tra Tất Cả	\n                <span style=\"display: \"><input id=\"chkSave\" name=\"chkSave\" type=\"checkbox\" value=\"0\"> Remember My League</span>\n                <div class=\"popWCheckBtn\">\n                    <a href=\"#\" name=\"Submit\" class=\"button mark\" onclick=\"go();\"><span>Truy Cập</span></a>\n                    <a href=\"#\" name=\"cancel\" class=\"button\" ng-click=\"closeLeague()\"><span>Hủy</span></a> \n                </div>\n            </div>\n            <div id=\"SportArea_1\" class=\"popWBlueArea\">\n                <div class=\"header\">\n                    <span class=\"icon\" onclick=\"SportControl(1);\" style=\"display:block;\"></span>\n                    <input id=\"chkSport_1\" type=\"checkbox\" style=\"display:none;\" onclick=\"checkAllBySport(1);\" name=\"chkSport_1\"><span class=\"title\" onclick=\"SportControl(1);\">Bóng đá</span>\n                </div>\n                <div class=\"content\">\n                    <div class=\"line\"></div>\n                    <table class=\"popWSelectTab\" width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tbody>\n                        <tr valign=\"top\" align=\"left\" style=\"line-height:16px;\" ng-repeat=\"(key, value) in leagues\">\n                            <td width=\"23\" style=\"vertical-align: top;\">\n                                <input id=\"700\" type=\"checkbox\" style=\"margin:2px;padding:0; display:block;\" value=\"700\" onclick=\"checkLeague();\" name=\"LF\">\n                            </td>\n                            <td width=\"270\" style=\"vertical-align: top;\">{{value.left.name}}<br></td>\n                            <td width=\"1\"> </td>\n                            <td width=\"23\" style=\"vertical-align: top;\">\n                                <input id=\"10716\" type=\"checkbox\" style=\"margin:2px;padding:0; display:block;\" value=\"10716\" onclick=\"checkLeague();\" name=\"LF\">\n                            </td>\n                            <td width=\"270\" style=\"vertical-align: top;\">{{value.right.name}}<br></td>\n                            <td width=\"1\"> </td>\n                        </tr>\n                    </tbody></table></div></div>\n            <div class=\"popWCheckAll\">\n                <div class=\"popWCheckBtn\">\n                    <a href=\"#\" name=\"Submit\" class=\"button mark\" onclick=\"go();\"><span>Truy Cập</span></a>\n                    <a href=\"#\" name=\"cancel\" class=\"button\" ng-click=\"closeLeague()\"><span>Hủy</span></a> 				\n                </div>\n            </div>\n        </div></div>\n    </div>\n</div>");
$templateCache.put("elements/time-item/index.html","<span ng-show=\"time\">{{time}}  &nbsp;Seconds</span>\n<span ng-show=\"!time\">No More Bet</span>");
$templateCache.put("elements/v2/left-bet-wait/left-bet-item.html","<div  ng-if=\"value.bet_kind == \'normal\'\">\n <div class=\"betInfo\">\n  <div class=\"mainInfo\">\n   <div class=\"betType\" ng-if=\"value.bet_type_raw!=\'correct_score\'\">Bóng đá / {{::value.bet_type}}</div>\n   <div class=\"betType\" ng-if=\"value.bet_type_raw==\'correct_score\'\">{{::value.bet_type}} - <b>[{{::value.type}}]</b></div>\n   <div class=\"betDetial\">\n    <div class=\"name\"  ng-if=\"value.bet_type_raw!=\'correct_score\'\">\n     <span>{{::value.bet_name}}</span>\n     <div class=\"smallBtn  primary icon-scoreMap\" title=\"Bản đồ điểm\"></div>\n  </div>\n  <div class=\"oddsDetail\">\n   <span class=\"selectorName\" ng-if=\"value.bet_type_raw!=\'correct_score\'\">{{::value.bet_odd}}</span>\n     <span class=\"selectorScore\">[{{::value.ss}}]</span>\n     <span>\n      <span class=\"selectorOther\">@</span>\n      <span class=\"selectorOdds\" ng-class=\"{\'accent\': value.bet_value < 0}\">{{::value.bet_value | number:2}}</span>\n   </span>\n   <div class=\"stacks\">{{value.bet_amount | numberPretty:2}}</div>\n</div>\n</div>\n</div>\n<div class=\"matchInfo-line\">\n   <div class=\"homeName teamName-pointer\" title=\"{{::value.home}}\">{{::value.home}}</div>\n   <div class=\"awayName teamName-pointer\" title=\"{{::value.away}}\">{{::value.away}}</div>\n</div>\n<div class=\"ticketInfo\">\n   <div class=\"ticketID\">ID:{{::value.id}}</div>\n   <div class=\"ticketStatus\">{{::value.status_name}}</div>\n</div>\n</div>\n</div>\n\n<div ng-if=\"value.bet_kind == \'group\'\" class=\"betInfo\">\n <div class=\"TextStyle01\">Cá cược tổng hợp\n <div class=\"primary stacks\">{{::value.bet_amount | numberPretty:2}}</div>\n </div>\n <div class=\"\" ng-class=\"{\'line-through\': bet.status == \'cancel\'}\" ng-repeat=\"(key, bet) in value.bets\">\n  <div class=\"betInfo\">\n    <div class=\"mainInfo\">\n     <div class=\"betType\">Bóng đá / {{::bet.bet_type}}</div>\n     <div class=\"betDetial\">\n      <div class=\"name\">\n       <span>{{::bet.bet_name}}</span>\n     </div>\n     <div class=\"oddsDetail\">\n       <span class=\"selectorName\">{{::bet.bet_odd}}</span>\n       <span class=\"selectorScore\">[{{::bet.ss}}]</span>\n       <span>\n        <span class=\"selectorOther\">@</span>\n        <span class=\"selectorOdds\" ng-class=\"{\'accent\': bet.bet_value < 0}\">{{::bet.bet_value | number:2}}</span>\n      </span>\n    </div>\n  </div>\n</div>\n<div class=\"matchInfo-line\">\n <div class=\"homeName teamName-pointer\" title=\"{{::bet.home}}\">{{::bet.home}}</div>\n <div class=\"awayName teamName-pointer\" title=\"{{::value.away}}\">{{::bet.away}}</div>\n</div>\n</div>\n</div>\n<div class=\"ticketInfo\">\n   <div class=\"ticketID\">Rate:  {{::value.rate}}</div>\n</div>\n<div class=\"ticketInfo\">\n   <div class=\"ticketID\">ID:{{::value.id}}</div>\n   <div class=\"ticketStatus\">{{::value.status_name}}</div>\n</div>\n</div>");
$templateCache.put("elements/v2/left-bet-wait/left-bet-wait.html","<div class=\"nav-widgetPanel  icon-betList-bets threeColum\">\n  <div class=\"item icon-betList-bets\" ng-class=\"{\'active\': waitSetting.type == \'betList\'}\" title=\"Đang Chạy\" ng-click=\"openBetList(\'betList\')\">\n    <span class=\"itemContent\">\n      <span class=\"text\">Đang Chạy</span>\n    </span>\n  </div>\n  <div class=\"item  icon-betList-waiting\" ng-class=\"{\'active\': waitSetting.type == \'waitingBets\'}\" title=\"Đang Chờ\" ng-click=\"openBetList(\'waitingBets\')\">\n    <span class=\"itemContent\">\n      <span class=\"text\">Đang Chờ</span>\n      <div class=\"alertArea\" ng-if=\"waitData.waitingBets.length\"><div class=\"alert\">{{waitData.waitingBets.length}}</div></div>\n    </span>\n  </div>\n  <div class=\"item  icon-betList-void\" ng-class=\"{\'active\': waitSetting.type == \'voidTicket\'}\" title=\"Hủy\" ng-click=\"openBetList(\'voidTicket\')\">\n    <span class=\"itemContent\">\n      <span class=\"text\">Hủy</span>\n    </span>\n  </div>\n</div>\n<div class=\"mainSection empty\" ng-show=\"waitSetting.type == \'betList\' && !waitData.betList.length\">\n  <div class=\"scroll-panel\"><div class=\"scroll-content\"><div class=\"ticketListGroup\"><div class=\"betInfo\">Không có thông tin</div></div></div><div class=\"v-scrollbar-track\"><div></div></div></div>\n</div>\n\n<div class=\"mainSection running\" ng-show=\"waitSetting.type == \'betList\'\">\n  <div class=\"scroll-panel\">\n    <div class=\"scroll-content\">\n      <div class=\"ticketListGroup single\" left-bet-item-v2 value=\"value\" ng-repeat=\"(key, value) in waitData.betList\">\n        </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"mainSection empty\" ng-show=\"waitSetting.type == \'waitingBets\' && !waitData.waitingBets.length && !waitData.voidTicketCaches.length\">\n  <div class=\"scroll-panel\"><div class=\"scroll-content\"><div class=\"ticketListGroup\"><div class=\"betInfo\">Không có cược chưa xử lý</div></div></div><div class=\"v-scrollbar-track\"><div></div></div></div>\n</div>\n<div class=\"mainSection waiting\" ng-show=\"waitSetting.type == \'waitingBets\'\">\n  <div class=\"scroll-panel\">\n    <div class=\"scroll-content\">\n      <div class=\"timerArea\" ng-show=\"waitData.waitingBets.length\">Giờ:{{waitSetting.bet_number}}</div>\n      <div class=\"ticketListGroup single reject\" left-bet-item-v2 value=\"value\" ng-repeat=\"(key, value) in waitData.voidTicketCaches\">\n        </div>\n      <div class=\"ticketListGroup single\" left-bet-item-v2 value=\"value\" ng-repeat=\"(key, value) in waitData.waitingBets\">\n        </div>\n    </div>\n    <div class=\"v-scrollbar-track\">\n      <div></div>\n    </div>\n  </div>\n</div>\n\n<div class=\"mainSection empty\" ng-show=\"waitSetting.type == \'voidTicket\' && !waitData.voidTicket.length\">\n  <div class=\"scroll-panel\"><div class=\"scroll-content\"><div class=\"ticketListGroup\"><div class=\"betInfo\">Không Có Tiền Cược Bị Vô Hiệu Lực</div></div></div><div class=\"v-scrollbar-track\"><div></div></div></div></div>\n\n<div class=\"mainSection waiting\" ng-show=\"waitSetting.type == \'voidTicket\'\">\n  <div class=\"scroll-panel\">\n    <div class=\"scroll-content\">\n      <div class=\"ticketListGroup single reject\" left-bet-item-v2 value=\"value\" ng-repeat=\"(key, value) in waitData.voidTicket\">\n        </div>\n    </div>\n    <div class=\"v-scrollbar-track\">\n      <div></div>\n    </div>\n  </div>\n</div>\n");
$templateCache.put("elements/v2/left-bet/left-bet.html","<div class=\"widgetPanel group betSlip active\" ng-class=\"{\'collapse\': !settings.show}\">\r\n  <div class=\"glyphIcon icon-widgetCollapse\" title=\"Collapse the Widget Panel\" ></div>\r\n  <div class=\"heading icon-betSlip\" ng-class=\"{\'current\': settings.menu == \'bet\'}\" title=\"Phiếu Đặt Cược\" ng-click=\"onSwithMenu(\'bet\')\">\r\n    <div class=\"text\" >Phiếu Đặt Cược</div>\r\n  </div>\r\n  <div class=\"heading icon-betList\" ng-class=\"{\'current\': settings.menu == \'betList\'}\" title=\"Bảng Cược\" ng-click=\"onSwithMenu(\'betList\')\">\r\n    <div class=\"text\" >Bảng Cược</div>\r\n    <!-- <div class=\"alertArea\"><div class=\"alert\">3</div></div> -->\r\n  </div>\r\n  <div class=\"contentArea\" ng-if=\"settings.menu == \'betList\'\">\r\n    <left-bet-wait-v2></left-bet-wait-v2>\r\n  </div>\r\n  <div class=\"contentArea\" ng-if=\"settings.menu == \'bet\'\">\r\n    <div class=\"nav-widgetPanel  icon-betSlip-single twoColum\" >\r\n      <div class=\"item active icon-betSlip-single\" ng-class=\"{\'active\': settings.type == \'single\'}\" title=\"Đơn\" ng-click=\"switchBettingMode(\'single\')\">\r\n        <span class=\"itemContent\" >\r\n          <span class=\"text\">Đơn</span>\r\n        </span>\r\n      </div>\r\n      <div class=\"item  icon-betSlip-parlay\" ng-class=\"{\'active\': settings.type == \'multiple\'}\" title=\"Cá cược tổng hợp\" ng-click=\"switchBettingMode(\'multiple\')\">\r\n        <span class=\"itemContent\" >\r\n          <span class=\"text\" >Cá cược tổng hợp</span>\r\n          <!-- <div class=\"alertArea\"><div class=\"alert\">3</div></div> -->\r\n        </span>\r\n      </div>\r\n    </div>\r\n\r\n    <div id=\"mainSection\" ng-if=\"!model.event\" class=\"mainSection empty\"><div class=\"scroll-panel\"><div class=\"scroll-content\"><div class=\"ticket\"><div class=\"betInfoArea\"><div class=\"betInfo\">Phiếu cược của bạn đang trống. Vui lòng nhấn vào tỷ lệ cược trong bảng để chọn cược</div></div></div></div><div class=\"v-scrollbar-track\"><div></div></div></div></div>\r\n\r\n    <div id=\"mainSection\" class=\"mainSection single\" ng-show=\"settings.type == \'single\' && !!model.event\">\r\n      <div class=\"scroll-panel\" >\r\n        <div class=\"scroll-content\" >\r\n          <div class=\"ticket\" >\r\n            <div class=\"betInfoArea\" >\r\n              <div class=\"betInfo-live statusChanged\" >\r\n                <div class=\"betType\" ng-if=\"model.bet_type!=\'correct_score\'\">\r\n                  <span >Bóng đá</span>\r\n                  <span > / </span>\r\n                  <span >{{model.odd_type}}</span>\r\n                </div>\r\n                <div class=\"betType\" ng-if=\"model.bet_type==\'correct_score\'\">\r\n                  <span >{{model.odd_type}}</span>\r\n                  <span > - </span>\r\n                  <b >[{{model.type}}]</b>\r\n                </div>\r\n                <div class=\"betDetial\" >\r\n                  <div class=\"name\" ng-if=\"model.bet_type!=\'correct_score\'\">{{model.odd_name}}</div>\r\n                  <div class=\"oddsDetail\" >\r\n                    <span class=\"selectorName\" ng-if=\"model.bet_type!=\'correct_score\'\">\r\n                      <span>{{model | handicapValue}}</span>\r\n                    </span>\r\n                    <span class=\"selectorScore\">[{{model.event.ss}}]</span>\r\n                    <span class=\"selectorOther\">@</span>\r\n                    <span class=\"selectorOdds\" ng-class=\"{\'accent\': model.bet_value < 0}\">{{model.bet_value | number:2}}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"betOtherArea\" >\r\n              <div class=\"betInfoSub\" >\r\n                <label class=\"\" >\r\n                  <input type=\"checkbox\" ng-model=\"settings.auto\" checked=\"\" ng-change=\"onChange()\">\r\n                  <div class=\"checkbox\" ></div>\r\n                  <span>Tự làm mới</span>\r\n                  <span class=\"countdown\" >\r\n                    <span>(</span>\r\n                    <span>{{number}}</span>\r\n                    <span>)</span>\r\n                  </span>\r\n                </label>\r\n              </div>\r\n              <div class=\"matchInfo-line\" >\r\n                <div class=\"homeName\" title=\"Sport Club do Recife\">{{model.event.home}}</div>\r\n                <div class=\"awayName\" title=\"Botafogo RJ\" >{{model.event.away}}</div>\r\n                <div class=\"leagueName\" title=\"{{model.event.league_name}}\">{{model.event.league_name}}</div>\r\n              </div>\r\n              <form ng-submit=\"betSubmit()\" autocomplete=\"off\" novalidate>\r\n              <div>\r\n                <div class=\"stakeArea\" >\r\n                  <div class=\"entry\" >\r\n                    <span class=\"currency\" >UT</span>\r\n                    <span class=\"content\" >\r\n                      <input id=\"BPstake\" ng-model=\"model.bet_amount\" name=\"BPstake\">\r\n                      <button class=\"smallBtn special icon-clear\" title=\"Xóa nội dung\" ></button>\r\n                    </span>\r\n                  </div>\r\n                  <noscript ></noscript>\r\n                  <div class=\"entrySub\" >\r\n                    <label >\r\n                      <input type=\"checkbox\" checked=\"\" >\r\n                      <div class=\"checkbox\" ></div>\r\n                      <span >Tự nhận tỷ lệ cược tốt</span>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n                <div class=\"entryInfo\" >\r\n                  <div >\r\n                    <span class=\"title\" >Thanh Toán</span>\r\n                    <span class=\"content\" >\r\n                      <span>{{model | payAmount | number:2}}</span>\r\n                      <div class=\"smallBtn primary icon-scoreMap\" title=\"Bản đồ điểm\" ></div>\r\n                    </span>\r\n                  </div>\r\n                  <div >\r\n                    <span class=\"title\" >Tối Thiểu</span>\r\n                    <span class=\"content\">{{data.bongdamin | number}}</span>\r\n                  </div>\r\n                  <div >\r\n                    <span class=\"title\" >Tối đa</span>\r\n                    <span class=\"content\">{{data.bongdamax | number}}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"btnArea\" >\r\n                  <button class=\"largeBtn\" type=\"submit\">Đặt Cược</button>\r\n                  <button class=\"largeBtn secondary\" type=\"button\" ng-click=\"backToHome()\">Hủy</button>\r\n                </div>\r\n              </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n<!-- payload -->\r\n<div id=\"mainSection\" class=\"mainSection parlay\" ng-show=\"settings.type == \'multiple\' && !!model.event\">\r\n  <div class=\"scroll-panel\">\r\n    <div class=\"scroll-content\">\r\n      <div class=\"ticket\">\r\n        <div class=\"betInfoArea\">\r\n          <div class=\"betInfo\" ng-class=\"{\'betInfo-live\': valueX.event.time_status == 1}\" mul-bet-item-v2 on-delete=\"onDelete\" item=\"valueX\" ng-repeat=\"(key, valueX) in mulBets\"></div>\r\n        </div>\r\n        <div class=\"betOtherArea\" ng-show=\"mulBets.length > 2\">\r\n          <form name=\"betform\" id=\"betform\" ng-submit=\"betMulSubmit()\" autocomplete=\"off\" novalidate>\r\n          <div>\r\n            <div class=\"entryInfo\">\r\n              <label title=\"Tự động chấp nhận mọi tỷ lệ cược\">\r\n                <input type=\"checkbox\" value=\"on\">\r\n                  <div class=\"checkbox\"></div>\r\n                  <!-- react-text: 501 -->Tự động chấp nhận mọi tỷ lệ cược\r\n                  <!-- /react-text -->\r\n                </label>\r\n              </div>\r\n              <div class=\"betInfoSub\">\r\n                <div class=\"titleGroup\">\r\n                  <span class=\"title\">Parlay Tutorial</span>\r\n                  <div title=\"Help\" class=\"smallBtn primary icon-help\"></div>\r\n                </div>\r\n                </div>\r\n                <ul class=\"collapsible comboList\">\r\n                    <li class=\"active \">\r\n                        <div class=\"innerContent \">\r\n                          <div class=\"entry\">\r\n                            <span class=\"currency\">UT</span>\r\n                            <span class=\"content\">\r\n                              <input id=\"mulStake\" ng-model=\"mulModel.bet_amount\" autofocus name=\"tien\" autocomplete=\"off\">\r\n                                <div class=\"smallBtn special icon-clear\" title=\"Xóa nội dung\"></div>\r\n                              </span>\r\n                            </div>\r\n                            <div class=\"entryInfoMini\">\r\n                              <span class=\"title\">\r\n                                <!-- react-text: 568 -->Tối đa\r\n                                <!-- /react-text -->\r\n                                <!-- react-text: 569 -->: \r\n                                <!-- /react-text -->\r\n                              </span>\r\n                              <span class=\"content\">1,000</span>\r\n                            </div>\r\n                            <!-- react-empty: 591 -->\r\n                          </div>\r\n                        </li>\r\n                        </ul>\r\n                        <div>\r\n                            <div class=\"entryInfo\">\r\n                              <div>\r\n                                <span class=\"title\">Tổng đặt cược</span>\r\n                                <span class=\"content\">{{(mulModel.bet_total = (mulBets | betsValue)) | number:2}}</span>\r\n                              </div>\r\n                              <div>\r\n                                <span class=\"title\">Tổng tiền đặt cược</span>\r\n                                <span class=\"content\">{{mulModel.bet_amount | numberPretty:2}}</span>\r\n                              </div>\r\n                              <div>\r\n                                <span class=\"title\">Tiền thanh toán tối đa</span>\r\n                                <span class=\"content\">{{data.bongdamin | number}}</span>\r\n                              </div>\r\n                              <div>\r\n                                <span class=\"title\">Tối Thiểu</span>\r\n                                <span class=\"content\">{{data.bongdamax | number}}</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                        <div class=\"btnArea\">\r\n                          <button class=\"largeBtn\" type=\"submit\">Đặt Cược</button>\r\n                          <button class=\"largeBtn secondary\" type=\"button\" ng-click=\"backToHome()\">Hủy</button>\r\n                        </div>\r\n                      </div>\r\n                      <!-- react-empty: 460 -->\r\n                      <!-- react-empty: 461 -->\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                </form>\r\n              </div>\r\n              <!-- End payload -->\r\n  </div>\r\n  <div class=\"bottomArea\" ></div>\r\n</div>");
$templateCache.put("elements/v2/left-bet/mul-bet-item.html","<button class=\"glyphIcon icon-close\" title=\"Close\" ng-click=\"onDeleteItem();\"></button>\n<div class=\"betType\">\n	<!-- react-text: 2777 -->Bóng đá<!-- /react-text --><!-- react-text: 2778 --> / <!-- /react-text --><!-- react-text: 2779 -->{{item.odd_type}}<!-- /react-text -->\n</div>\n<div class=\"betDetial\">\n	<div class=\"name \">{{item.odd_name}}</div>\n	<div class=\"oddsDetail\"><span class=\"selectorName\">{{item | handicapValue}}</span>\n		<span class=\"selectorScore\">[{{item.event.ss}}]</span>\n		<span class=\"selectorOther\">@</span>\n		<span class=\"selectorOdds\">{{item.bet_value | numberOdd:2}}</span></div>\n</div>\n<div class=\"matchInfo-line\">\n	<div class=\"homeName\" title=\"Hiroshima Sanfrecce\">{{item.event.home}}</div>\n	<div class=\"awayName\" title=\"Vissel Kobe\">{{item.event.away}}</div>\n</div>");}]);