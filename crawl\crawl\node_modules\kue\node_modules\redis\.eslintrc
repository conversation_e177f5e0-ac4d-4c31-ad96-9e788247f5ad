env:
  node: true
  es6: false

rules:
  # Possible Errors
  # http://eslint.org/docs/rules/#possible-errors
  comma-dangle: [2, "only-multiline"]
  no-constant-condition: 2
  no-control-regex: 2
  no-debugger: 2
  no-dupe-args: 2
  no-dupe-keys: 2
  no-duplicate-case: 2
  no-empty: 2
  no-empty-character-class: 2
  no-ex-assign: 2
  no-extra-boolean-cast : 2
  no-extra-parens: [2, "functions"]
  no-extra-semi: 2
  no-func-assign: 2
  no-invalid-regexp: 2
  no-irregular-whitespace: 2
  no-negated-in-lhs: 2
  no-obj-calls: 2
  no-regex-spaces: 2
  no-sparse-arrays: 2
  no-inner-declarations: 2
  no-unexpected-multiline: 2
  no-unreachable: 2
  use-isnan: 2
  valid-typeof: 2

  # Best Practices
  # http://eslint.org/docs/rules/#best-practices
  array-callback-return: 2
  block-scoped-var: 2
  dot-notation: 2
  eqeqeq: 2
  no-else-return: 2
  no-extend-native: 2
  no-floating-decimal: 2
  no-extra-bind: 2
  no-fallthrough: 2
  no-labels: 2
  no-lone-blocks: 2
  no-loop-func: 2
  no-multi-spaces: 2
  no-multi-str: 2
  no-native-reassign: 2
  no-new-wrappers: 2
  no-octal: 2
  no-proto: 2
  no-redeclare: 2
  no-return-assign: 2
  no-self-assign: 2
  no-self-compare: 2
  no-sequences: 2
  no-throw-literal: 2
  no-useless-call: 2
  no-useless-concat: 2
  no-useless-escape: 2
  no-void: 2
  no-unmodified-loop-condition: 2
  yoda: 2

  # Strict Mode
  # http://eslint.org/docs/rules/#strict-mode
  strict: [2, "global"]

  # Variables
  # http://eslint.org/docs/rules/#variables
  no-delete-var: 2
  no-shadow-restricted-names: 2
  no-undef: 2
  no-unused-vars: [2, {"args": "none"}]

  # http://eslint.org/docs/rules/#nodejs-and-commonjs
  no-mixed-requires: 2
  no-new-require: 2
  no-path-concat: 2

  # Stylistic Issues
  # http://eslint.org/docs/rules/#stylistic-issues
  comma-spacing: 2
  eol-last: 2
  indent: [2, 4, {SwitchCase: 2}]
  keyword-spacing: 2
  max-len: [2, 200, 2]
  new-parens: 2
  no-mixed-spaces-and-tabs: 2
  no-multiple-empty-lines: [2, {max: 2}]
  no-trailing-spaces: 2
  quotes: [2, "single", "avoid-escape"]
  semi: 2
  space-before-blocks: [2, "always"]
  space-before-function-paren: [2, "always"]
  space-in-parens: [2, "never"]
  space-infix-ops: 2
  space-unary-ops: 2

globals:
  it: true
  describe: true
  before: true
  after: true
  beforeEach: true
  afterEach: true
