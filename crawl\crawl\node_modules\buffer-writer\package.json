{"name": "buffer-writer", "version": "1.0.1", "description": "a fast, efficient buffer writer", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-buffer-writer.git"}, "keywords": ["buffer", "writer", "builder"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"mocha": "~1.8.1", "benchmark": "~1.0.0", "microtime": "~0.3.3", "bench": "~0.3.5", "okay": "0.0.2", "cloned": "0.0.1", "rmdir": "~1.0.0", "async": "~0.2.6"}}